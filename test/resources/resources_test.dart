// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.
import 'package:evoapp/resources/input_borders.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/button_styles.dart';
import 'package:flutter_common_package/resources/text_styles.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCommonImageProvider extends Mock implements CommonImageProvider {}

class MockCommonTextStyles extends Mock implements CommonTextStyles {}

class MockEvoColors extends Mock implements EvoColors {}

class MockEvoColorsV2 extends Mock implements EvoColorsV2 {}

class MockEvoTextStylesV2 extends Mock implements EvoTextStylesV2 {}

class MockCommonButtonStyles extends Mock implements CommonButtonStyles {}

class MockEvoInputBorders extends Mock implements EvoInputBorders {}

void main() {
  group('Resources module tests', () {
    setUp(() {
      // Initialize the dependency injection
      getIt.registerSingleton<CommonImageProvider>(MockCommonImageProvider());
      getIt.registerSingleton<CommonTextStyles>(MockCommonTextStyles());
      getIt.registerSingleton<EvoColors>(MockEvoColors());
      getIt.registerSingleton<EvoColorsV2>(MockEvoColorsV2());
      getIt.registerSingleton<EvoTextStylesV2>(MockEvoTextStylesV2());
      getIt.registerSingleton<CommonButtonStyles>(MockCommonButtonStyles());
      getIt.registerSingleton<EvoInputBorders>(MockEvoInputBorders());
    });

    tearDown(() {
      // Clean up dependencies
      getIt.reset();
    });

    group('Global resource accessor tests', () {
      test('should provide valid CommonImageProvider instance', () {
        expect(evoImageProvider, isA<CommonImageProvider>());
        expect(evoImageProvider, isNotNull);
      });

      test('should provide valid CommonTextStyles instance', () {
        expect(evoTextStyles, isA<CommonTextStyles>());
        expect(evoTextStyles, isNotNull);
      });

      test('should provide valid EvoColors instance', () {
        expect(evoColors, isA<EvoColors>());
        expect(evoColors, isNotNull);
      });

      test('should provide valid EvoColorsV2 instance', () {
        expect(evoColorsV2, isA<EvoColorsV2>());
        expect(evoColorsV2, isNotNull);
      });

      test('should provide valid EvoTextStylesV2 instance', () {
        expect(evoTextStylesV2, isA<EvoTextStylesV2>());
        expect(evoTextStylesV2, isNotNull);
      });

      test('should provide valid CommonButtonStyles instance', () {
        expect(evoButtonStyles, isA<CommonButtonStyles>());
        expect(evoButtonStyles, isNotNull);
      });

      test('should provide valid EvoInputBorders instance', () {
        expect(evoInputBorders, isA<EvoInputBorders>());
        expect(evoInputBorders, isNotNull);
      });
    });
  });
}
