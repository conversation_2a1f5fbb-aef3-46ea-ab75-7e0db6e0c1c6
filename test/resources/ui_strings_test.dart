import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('verify EvoStrings', () {
    test('should have correct values', () {
      // Common
      expect(EvoStrings.footerLoadingText, 'Đang tải');
      expect(EvoStrings.confirmText, '<PERSON><PERSON>c nhận');
      expect(EvoStrings.notification, 'Thông báo');
      expect(EvoStrings.existWarning, 'Nhấn lần nữa để thoát');
      expect(EvoStrings.retry, 'Thử lại');
      expect(EvoStrings.active, '<PERSON><PERSON><PERSON> hoạt');
      expect(EvoStrings.hubLoadingText, 'Đợi chút nhé');
      expect(EvoStrings.groupHeaderMonthTitle, 'Tháng');
      expect(EvoStrings.ignore, 'Bỏ qua');
      expect(EvoStrings.review, 'Đánh giá');

      // Bottom navigation bar
      expect(EvoStrings.bottomBarHomeLabel, 'Trang chủ');
      expect(EvoStrings.bottomBarHistoryLabel, '<PERSON><PERSON>ch sử GD');
      expect(EvoStrings.bottomBarRewardLabel, 'Ưu đãi');
      expect(EvoStrings.bottomBarAccountLabel, 'Tài khoản');
      expect(EvoStrings.bottomBarLoginLabel, 'Đăng nhập');
      expect(EvoStrings.bottomBarScanQRLabel, 'Quét QR');

      // Tutorial flow
      expect(EvoStrings.tutorialTitle, 'Tiền thêm cho mọi nhà');
      expect(EvoStrings.tutorialDescription,
          'Từ mở thẻ tín dụng đến tận hưởng ưu đãi đều ngay tức thì.');
      expect(EvoStrings.tutorialLoginButtonText, 'Tiếp tục');
      expect(EvoStrings.tutorialSkipButtonText, 'Bỏ qua');

      // Login flow
      expect(EvoStrings.continueBtn, 'Tiếp tục');
      expect(EvoStrings.prefixTermAndCondition, 'Bằng việc chọn Tiếp tục, tôi đồng ý ');
      expect(EvoStrings.termAndCondition, 'điều khoản & điều kiện');
      expect(EvoStrings.suffixTermAndCondition,
          ' của EVO và cho phép dùng số điện thoại để nhận mã OTP');
      expect(EvoStrings.versionApp, 'v {0}');
      expect(EvoStrings.otpInvalidMsg, 'Mã OTP không đúng, vui lòng kiểm tra và nhập lại.');

      expect(EvoStrings.loginTitle, 'Đăng nhập/Đăng ký');
      expect(EvoStrings.loginDesc, 'Nhập số điện thoại đã đăng ký thẻ EVO của bạn để đăng nhập');

      expect(EvoStrings.wrongPhoneFormat, 'Số điện thoại chưa đúng định dạng');
      expect(EvoStrings.inputPhoneHint, 'Số điện thoại');

      expect(EvoStrings.loginDeactivatedAccountTitle, 'Bạn đã xoá tài khoản này');

      expect(EvoStrings.loginInvalidDeviceTokenTitle, 'Nhập OTP để đăng nhập bạn nhé');
      expect(EvoStrings.loginInvalidDeviceTokenContent,
          'Để đảm bảo an toàn cho tài khoản, bạn cần nhập lại OTP và PIN để đăng nhập EVO');
      expect(EvoStrings.loginInvalidDeviceTokenConfirm, 'Đồng ý');
      expect(EvoStrings.doeCompletedWithAnotherPhone,
          'Bạn vừa hoàn tất mở thẻ với số điện thoại khác');
      expect(EvoStrings.pleaseLoginToUseEvo,
          'Vui lòng đăng nhập lại để tiếp tục sử dụng ứng dụng EVO.');
      expect(EvoStrings.loginAgain, 'Đăng nhập lại');

      //OTP
      expect(EvoStrings.otpDescription, 'Nhập mã OTP được gửi tới số thuê bao');

      // Home page
      expect(EvoStrings.openCardSuggestion_1, 'Mở thẻ thành công\n');
      expect(EvoStrings.openCardSuggestion_2, 'giảm giá');
      expect(EvoStrings.openCardSuggestion_3, ' cực đỉnh');
      expect(EvoStrings.openCardBtn, 'Mở ngay');
      expect(EvoStrings.hotVoucherTitle, 'Ưu đãi hot');
      expect(EvoStrings.cardBenefitsTitle, 'Lợi ích thẻ');
      expect(EvoStrings.cardBenefit_1, 'Không phí mở thẻ & phí thường niên');
      expect(EvoStrings.cardBenefit_2, 'Nhận lại 10% hàng tháng lên đến 500K');
      expect(EvoStrings.cardBenefit_3, 'Lãi suất 0%, mua hàng trả trước 0đ');
      expect(EvoStrings.cardBenefit_4, 'Miễn lãi 45 ngày');
      expect(EvoStrings.allVoucherTitle, 'Tất cả ưu đãi');
      expect(EvoStrings.listVoucherTitle, 'EVO đang có gì?');

      // Profile Page
      expect(EvoStrings.myVoucher, 'Ưu đãi của tôi');
      expect(EvoStrings.transactionHistory, 'Lịch sử giao dịch');
      expect(EvoStrings.emiManagement, 'Quản lý trả góp');
      expect(EvoStrings.setting, 'Cài đặt');
      expect(EvoStrings.frequentlyQuestions, 'Câu hỏi thường gặp');
      expect(EvoStrings.cardUsageGuide, 'Cẩm nang sử dụng thẻ');
      expect(EvoStrings.aboutUs, 'Về chúng tôi');
      expect(EvoStrings.signOut, 'Đăng xuất');
      expect(EvoStrings.version, 'Phiên bản');
      expect(EvoStrings.moneyHide, 'xxxxxxđ');
      expect(EvoStrings.unKnowUser, 'Người dùng Evo');
      expect(EvoStrings.unKnowPhone, '09xxxxxxxx');
      expect(EvoStrings.prefixMale, 'Anh');
      expect(EvoStrings.prefixFemale, 'Chị');
      expect(EvoStrings.unknownGivenName, 'Bạn');
      expect(EvoStrings.defaultVersion, 'v1.0.0');
      expect(EvoStrings.doneNow, 'Hoàn tất ngay');
      expect(EvoStrings.connectEvoCardNow, 'Liên kết thẻ EVO ngay');
      expect(EvoStrings.tutorialActiveCard, 'Hướng dẫn kích hoạt');
      expect(EvoStrings.cardStatusTitleCreditLimit, 'Hạn mức thẻ');
      expect(EvoStrings.cardStatusTitleAwaitingForApproval, 'Hạn mức thẻ chờ phê duyệt');
      expect(EvoStrings.hiddenCreditLimit, '********');
      expect(EvoStrings.cardStatusTwoRemainingSteps, 'Chỉ 2 bước nữa để thanh toán');
      expect(EvoStrings.cardStatusOneRemainingStep, 'Chỉ 1 bước nữa để thanh toán');
      expect(EvoStrings.cardStatusNotLinked, 'Liên kết thẻ');
      expect(EvoStrings.cardStatusLinked, 'Đã liên kết');
      expect(EvoStrings.cardStatusNotActivated, 'Kích hoạt thẻ');
      expect(EvoStrings.cardStatusActivated, 'Đã kích hoạt');
      expect(EvoStrings.getCardStatusError, 'Chưa thể cập nhật thông tin của bạn');

      //Referral
      expect(EvoStrings.referralNewMemberProfile, 'Giới thiệu bạn mới có quà');
      expect(EvoStrings.referralIntroductionTitle, 'Chi tiết chương trình');

      // User Info
      expect(EvoStrings.fullName, 'Họ và tên');
      expect(EvoStrings.phoneNumber, 'Số điện thoại');
      expect(EvoStrings.identityCardNumber, 'Số CMND/CCCD');
      expect(EvoStrings.birthday, 'Ngày sinh');
      expect(EvoStrings.editInfo, 'Sửa thông tin');
      expect(EvoStrings.email, 'Email');
      expect(EvoStrings.female, 'Nữ');
      expect(EvoStrings.male, 'Nam');
      expect(EvoStrings.sendEmailTo, 'Hoặc gửi email về');
      expect(EvoStrings.feedbackAndContact, 'Góp ý và Liên hệ');
      expect(EvoStrings.sendEmail, 'Gửi email');
      expect(EvoStrings.feedbackScreenTitle, 'EVO luôn sẵn sàng hỗ trợ bạn');
      expect(EvoStrings.feedbackScreenDescription,
          'Mọi góp ý về ứng dụng và thẻ EVO bạn có thể gửi về địa chỉ email ');
      expect(EvoStrings.noEmailClientTitle, 'Chưa có ứng dụng email');
      expect(
          EvoStrings.noEmailClientDescription, 'Bạn có thể gửi câu hỏi, góp ý về địa chỉ email ');

      // Profile page
      expect(EvoStrings.userInfo, 'Thông tin cá nhân');

      // Profile detail page
      expect(EvoStrings.addImage, 'Thêm ảnh');
      expect(EvoStrings.deactivateAccount, 'Xoá tài khoản');
      expect(EvoStrings.camera, 'Máy ảnh');
      expect(EvoStrings.library, 'Thư viện');
      expect(EvoStrings.notHaveInfo, 'Chưa có thông tin');

      //Unsupported Phone Number
      expect(EvoStrings.createEvoCardTitle, 'Bạn chưa có tài khoản');
      expect(EvoStrings.createEvoCardDesc, 'Mở thẻ để có tài khoản dùng mọi dịch vụ trên ứng dụng');
      expect(EvoStrings.createEvoCardNow, 'Tiếp tục');

      // No eKYC data for faceOTP
      expect(EvoStrings.noEKYCDataForFaceOtpTitle, 'Bạn chưa mở thẻ TPBank EVO thành công');
      expect(EvoStrings.noEKYCDataForFaceOtpDesc,
          'Hoàn tất Mở thẻ để có thể sử dụng dịch vụ trên ứng dụng EVO bạn nhé!');

      // Terms & conditions
      expect(EvoStrings.termsAndConditions, 'Điều khoản & Điều kiện');

      // Button
      expect(EvoStrings.cancel, 'Huỷ bỏ');
      expect(EvoStrings.confirm, 'Xác nhận');
      expect(EvoStrings.close, 'Đóng');
      expect(EvoStrings.viewMore, 'Xem thêm');
      expect(EvoStrings.agree, 'Đồng ý');

      // Pin code
      expect(EvoStrings.createPin, 'Tạo mã PIN');
      expect(EvoStrings.descCreatePin, 'Mã PIN dùng để đăng nhập và xác nhận giao dịch');
      expect(EvoStrings.confirmPin, 'Xác nhận mã PIN (6 số)');
      expect(EvoStrings.desConfirmPin, 'Nhập lại mã PIN để xác nhận');
      expect(EvoStrings.errorConfirmPin, 'Mã PIN chưa khớp, vui lòng kiểm tra và nhập lại.');
      expect(EvoStrings.titleCreatePin, 'Nhập mã PIN (6 số)');
      expect(EvoStrings.titleCancelInputPin, 'Bạn có chắc muốn thoát?');
      expect(EvoStrings.descriptionCancelInputPin,
          'Thông tin bạn vừa nhập sẽ không được lưu lại, bạn cần nhập lại từ đầu.');
      expect(EvoStrings.cancelInputPin, 'Thoát');
      expect(EvoStrings.continueInputPin, 'Nhập tiếp');

      // Login
      expect(EvoStrings.loginScreenTitleWithBiometric, 'Đăng nhập');
      expect(EvoStrings.loginScreenTitleWithoutBiometric, 'Nhập mã PIN');
      expect(EvoStrings.loginScreenBiometricDescriptionPrefix, 'Đăng nhập tài khoản ');
      expect(EvoStrings.loginScreenDescriptionPrefix, 'Nhập mã PIN để đăng nhập tài khoản ');
      expect(EvoStrings.loginScreenDescriptionSuffix, ' bạn đã đăng ký tại EVO');
      expect(
          EvoStrings.loginScreenLoginWithAnotherPhoneNumber, 'Đăng nhập bằng số điện thoại khác');
      expect(EvoStrings.loginScreenLoginWithBiometric, 'Đăng nhập bằng');
      expect(EvoStrings.loginScreenConfirmButton, 'Đăng nhập');
      expect(EvoStrings.loginScreenPinCodeOption, 'Hoặc nhập mã PIN để đăng nhập');
      expect(EvoStrings.loginScreenResetPin, 'Đặt lại mã PIN');
      expect(EvoStrings.loginScreenBioFailMaxTime,
          'Bạn đã đăng nhập sai quá {0} lần bằng {1}. Vui lòng nhập PIN để tiếp tục');
      expect(EvoStrings.loginScreenBioLockedAndEnterPin, 'Vui lòng nhập mã PIN để đăng nhập');

      // Session timeout
      expect(EvoStrings.authorizationSessionTimeoutTitle, 'Phiên đăng nhập hết hạn');
      expect(EvoStrings.authorizationSessionTimeoutDesc,
          'Vui lòng đăng nhập lại để tiếp tục sử dụng các tính năng dành cho thành viên bạn nhé.');
      expect(EvoStrings.authorizationSessionTimeoutConfirmLogin, 'Đăng nhập');
      expect(EvoStrings.authorizationSessionTimeoutIgnoreLogin, 'Bỏ qua');
      expect(EvoStrings.localizedReasonForUsingBiometrics, 'EVO');
      expect(EvoStrings.termAndConditionTitle, 'Điều khoản & Điều kiện');
      expect(EvoStrings.enterPinTitle, 'Nhập PIN');
      expect(EvoStrings.enterPinToAuthenticate, 'Hãy nhập mã PIN để xác thực');
      expect(EvoStrings.settingTitle, 'Cài đặt');
      expect(EvoStrings.ignoreTitle, 'Bỏ qua');

      //bio metrics error;
      expect(EvoStrings.passCodeNotSetError, 'pass code not set');
      expect(EvoStrings.notEnrolledError, 'not enrolled biometrics');
      expect(EvoStrings.notAvailableError, 'not available biometrics');
      expect(EvoStrings.otherOperatingSystemError, 'other operating system error');
      expect(EvoStrings.lockedOutError, 'biometrics authentication is locked out');
      expect(
          EvoStrings.permanentlyLockedOutError, 'biometrics authentication permanently locked out');
      expect(EvoStrings.biometricOnlyNotSupportedError,
          'biometrics authentication biometric only not supported');
      expect(EvoStrings.otpTitle, 'Nhập mã OTP');
      expect(EvoStrings.limitOtp, 'Đăng nhập tạm khóa');
      expect(EvoStrings.descLimitOtp, 'Bạn đã đạt giới hạn số lần nhập mã xác thực');
      expect(EvoStrings.moveToHome, 'Về trang chủ');

      // Active biometric
      expect(EvoStrings.loginSuccess, 'Đăng nhập thành công');
      expect(EvoStrings.descSetUpPinSuccess, 'Từ lần sau bạn chỉ cần nhập mã PIN để đăng nhập EVO');
      expect(EvoStrings.titlePreFixActiveBiometric, 'Kích hoạt ');
      expect(EvoStrings.titleSubFixActiveBiometric,
          ' để xác nhận thanh toán và đăng nhập nhanh thay cho mã PIN');
      expect(EvoStrings.textPositiveActiveBiometric, 'Kích hoạt');
      expect(EvoStrings.descriptionActiveBiometric,
          'Dùng {0} để đăng nhập và xác thực thanh toán nhanh và bảo mật hơn');

      //Text for No Setup FaceId/FingerId Popup
      expect(EvoStrings.noSetupFaceFingerIdDescPart1, 'Cài đặt {0} trong ');
      expect(EvoStrings.noSetupFaceFingerIdDescPart2,
          ' của thiết bị để xác nhận giao dịch thanh toán và đăng nhập nhanh thay cho mật khẩu.');
      expect(EvoStrings.fail, 'Thất bại');
      expect(EvoStrings.signOutConfirm, 'Bạn có chắc chắn muốn đăng xuất khỏi ứng dụng EVO?');
      expect(EvoStrings.signOutFail, 'Bạn vừa đăng xuất không thành công');
      expect(EvoStrings.signOutSuccess, 'Bạn đã đăng xuất thành công');
      expect(EvoStrings.faceText, 'khuôn mặt');
      expect(EvoStrings.fingerText, 'vân tay');
      expect(EvoStrings.faceFingerText, 'khuôn mặt/ vân tay');
      expect(EvoStrings.authenticateText, 'Xác thực');
      expect(EvoStrings.notSetupText, 'Chưa cài đặt');
      expect(EvoStrings.enableText, 'Kích hoạt');
      expect(EvoStrings.prefixDisableBioAuthenticatorText, 'Đã tắt xác thực');
      expect(EvoStrings.biometricTokenUnUsableMessage,
          'Đăng nhập bằng {0} đã hết hạn. Vui lòng kích hoạt lại trong Cài đặt của ứng dụng');
      expect(EvoStrings.titleActiveBiometric, 'Xác thực bằng {0}?');
      expect(EvoStrings.biometricLocked, '{0} của bạn đã bị khóa. Vui lòng vào Cài Đặt để mở khóa');

      //Setting pages
      expect(EvoStrings.resetPin, 'Đặt lại mã PIN');
      expect(EvoStrings.deleteAccountPrompt, 'Nếu bạn muốn xoá tài khoản EVO, hãy');
      expect(EvoStrings.deleteAccountActionText, 'bấm vào đây');

      // Promotion pages
      expect(EvoStrings.campaignListPageTitle, 'Ưu đãi');
      expect(EvoStrings.promotionListPageTitle, 'Ưu đãi');
      expect(EvoStrings.allCampaigns, 'Tất cả ưu đãi');
      expect(EvoStrings.emptyCampaignText, 'Chưa có ưu đãi nào');
      expect(EvoStrings.emptyCampaignDescription,
          'Cùng chờ đón các chương trình ưu đãi hấp dẫn của EVO trong thời gian tới nhé');
      expect(EvoStrings.emptyMyPromotionText, 'Ưu đãi của bạn sẽ hiện ở đây');
      expect(EvoStrings.emptyMyPromotionDescription,
          'Tham gia các chương trình ưu đãi của EVO để nhận thêm nhiều ưu đãi nhé');
      expect(EvoStrings.promotionRunningOut, 'Còn');
      expect(EvoStrings.promotionDay, 'ngày');
      expect(EvoStrings.promotionHour, 'giờ');
      expect(EvoStrings.promotionMinute, 'phút');
      expect(EvoStrings.promotionUse, 'Sử dụng');
      expect(EvoStrings.promotionIsUsed, 'Đã sử dụng');
      expect(EvoStrings.voucherEndAt, 'HSD');
      expect(EvoStrings.campaignEndAt, 'Kết thúc vào');
      expect(EvoStrings.promotionStartAt, 'Bắt đầu vào');
      expect(EvoStrings.promotionTimeOut, 'Đã hết hạn');
      expect(EvoStrings.campaignTimeOut, 'Đã kết thúc');

      // Announcement
      expect(EvoStrings.announcementListTitle, 'Thông báo');
      expect(EvoStrings.transactionTitle, 'Giao dịch');
      expect(EvoStrings.todayText, 'Hôm nay');
      expect(EvoStrings.yesterdayText, 'Hôm qua');
      expect(EvoStrings.noAnnouncementDescription, 'Bạn chưa có thông báo');
      expect(EvoStrings.announcementDetailHeaderTitle, 'Nội dung chi tiết');

      // Transaction
      expect(EvoStrings.statusSuccess, 'Thành công');
      expect(EvoStrings.statusPending, 'Chờ thanh toán');
      expect(EvoStrings.statusFailed, 'Chưa thành công');
      expect(EvoStrings.tpBankEvo, 'TPBank EVO');
      expect(EvoStrings.transactionHistoryEmiLabel, 'Trả góp');
      expect(EvoStrings.refundInfoTitle, 'Hoàn tiền EVO');
      expect(EvoStrings.refundInfoDescription,
          'Khoản tiền hoàn này sẽ được cộng vào hạn mức tín dụng của Khách hàng');
      expect(EvoStrings.refundInfoTimeDescription,
          'Thời gian hoàn tiền chậm nhất là ngày 20 trong tháng kế tiếp của chu kỳ sao kê có chứa giao dịch gốc');

      //Web view
      expect(EvoStrings.webViewErrorTitle, 'Không tìm thấy trang');
      expect(EvoStrings.webViewErrorDescription,
          'Trang của bạn không tồn tại hoặc\nđang có lỗi xảy ra');

      // Promotion detail
      expect(EvoStrings.copyPromotionCodeSuccess, 'Đã sao chép mã ưu đãi vào bộ nhớ');
      expect(EvoStrings.promotionCopyAction, 'Sao chép và sử dụng ngay');
      expect(EvoStrings.copyPromotionUsingAction, 'Sử dụng mã ưu đãi');
      expect(EvoStrings.campaignDefaultAction, 'Tham gia ngay');
      expect(EvoStrings.promotionDetailTitle, 'Chi tiết ưu đãi');
      expect(EvoStrings.campaignDetailTitle, 'Chi tiết chương trình');
      expect(EvoStrings.voucherEarningSuccessCtaText, 'Xem ưu đãi ngay');
      expect(EvoStrings.voucherEarningErrorTitle,
          'Rất tiếc, hệ thống đang gián đoạn.\nBạn vui lòng thử lại sau nhé!');

      expect(EvoStrings.biometricDeviceChangeWarring,
          '{0} đã bị thay đổi. Vui lòng kiểm tra lại thiết lập {1} trong máy');

      /// Payment flow
      expect(EvoStrings.paymentTitle, 'Thanh toán');
      expect(EvoStrings.paymentAmountHint, 'Nhập số tiền');
      expect(EvoStrings.paymentNote, 'Ghi chú');
      expect(EvoStrings.paymentDescPrefix, 'Thanh toán cho');
      expect(EvoStrings.paymentAmountInvalid, 'Số tiền không hợp lệ');
      expect(EvoStrings.paymentAmountMinInvalid, 'Bạn cần thanh toán tối thiểu');
      expect(EvoStrings.paymentAmountMaxInvalid, 'Bạn chỉ có thể thanh toán tối đa');
      expect(EvoStrings.paymentContinue, 'Tiếp tục');
      expect(EvoStrings.paymentConfirmTitle, 'Xác nhận thanh toán');
      expect(EvoStrings.paymentSummaryTransactionCode, 'Mã giao dịch');
      expect(EvoStrings.paymentSummaryDesc, 'Mô tả');
      expect(EvoStrings.paymentSummaryDateTime, 'Thời gian');
      expect(EvoStrings.paymentSummaryAmount, 'Số tiền');
      expect(EvoStrings.paymentSummaryFee, 'Phí giao dịch');
      expect(EvoStrings.paymentSummaryPromotionAmount, 'Ưu đãi');
      expect(EvoStrings.paymentSummaryCashbackAmount, 'Hoàn tiền');
      expect(EvoStrings.paymentSummaryTotalAmount, 'Tổng thanh toán');
      expect(EvoStrings.paymentSummaryPayment, 'Thanh toán');
      expect(EvoStrings.paymentEmiSummaryPayment, 'Thanh toán trả góp');
      expect(EvoStrings.payment3DSTitle, 'Xác minh thẻ');
      expect(EvoStrings.paymentBackToHome, 'Về trang chủ');
      expect(EvoStrings.paymentRePayment, 'Thanh toán lại');
      expect(EvoStrings.paymentPollingTimeoutTitle, 'Để bạn đợi lâu thiệt là có lỗi');
      expect(EvoStrings.paymentPollingTimeoutDesc,
          'Thanh toán lại giúp EVO nhé, lần này EVO sẽ cố gắng xử lý nhanh hơn 🚀');
      expect(EvoStrings.paymentResultSuccessFullName, 'Giao dịch thành công');
      expect(EvoStrings.paymentResultFailFullName, 'Giao dịch thất bại');
      expect(EvoStrings.paymentResultPendingFullName, 'Giao dịch đang chờ xử lý');
      expect(EvoStrings.paymentSelectPromotion, 'Chọn ưu đãi');
      expect(EvoStrings.paymentPromotionEmptyTitle, 'Chưa có ưu đãi phù hợp');
      expect(EvoStrings.paymentPromotionEmptyDesc,
          'Chưa có ưu đãi nào phù hợp với giao dịch này, cùng đợi thêm các ưu đãi khác từ EVO nhé');
      expect(EvoStrings.paymentSelectPromotionTitle, 'Danh sách ưu đãi');
      expect(EvoStrings.paymentSelectPromotionButton, 'Áp dụng');
      expect(EvoStrings.paymentUnSelectPromotionButton, 'Bỏ chọn');
      expect(EvoStrings.paymentPromotionNotQualified, 'Không đủ điều kiện');
      expect(EvoStrings.paymentPromotionCanNotSelected, 'Chỉ được chọn 1 ưu đãi');
      expect(EvoStrings.paymentPromotionUnavailable,
          'Ưu đãi bạn đang chọn không còn sử dụng được, chọn ưu đãi khác nhé');
      expect(EvoStrings.paymentPromotionApplyTimeout, 'Bạn chưa áp dụng ưu đãi');
      expect(EvoStrings.paymentPromotionRemoveTimeout, 'Ưu đãi chưa được bỏ chọn, thử lại bạn nhé');
      expect(EvoStrings.paymentPromotionTimeout, 'Bạn chưa áp dụng ưu đãi');
      expect(EvoStrings.paymentPromotionApplyErrorTitle, 'Chưa thể áp dụng ưu đãi');
      expect(EvoStrings.paymentPromotionApplyErrorSelectOtherPromotion, 'Chọn ưu đãi khác');
      expect(
          EvoStrings.paymentPromotionApplyErrorPayWithoutPromotion, 'Thanh toán không dùng ưu đãi');
      expect(
          EvoStrings.paymentWithNoPromotionWarning, 'Áp dụng ưu đãi để thanh toán tiết kiệm hơn');
      expect(EvoStrings.poweredByLabel, 'Powered by');
      expect(EvoStrings.payment3DSConfirmDialogTitle, 'Bạn có chắc muốn quay lại?');
      expect(EvoStrings.payment3DSConfirmDialogContent,
          'Nếu bạn đã nhập mã OTP, giao dịch này có thể vẫn đang được xử lý. Kết quả giao dịch sẽ được cập nhật sau 15 phút.');
      expect(EvoStrings.payment3DSKeepWaiting, 'Tiếp tục đợi');
      expect(EvoStrings.payment3DSBackScreen, 'Quay lại');
      expect(EvoStrings.updateNewOrderWithInvalidVoucher, 'Ưu đãi chưa thể áp dụng được');
      expect(EvoStrings.promotionItemUnqualified, '(Chưa thể áp dụng ưu đãi)');
      expect(EvoStrings.invalidPromotionBottomSheetDefaultError,
          'Bạn không thể sử dụng ưu đãi này, kiểm tra và chọn ưu đãi khác nhé');
      expect(EvoStrings.transactionTooSoonBottomSheetTitle, 'Giao dịch trước đang được xử lý');
      expect(EvoStrings.transactionTooSoonBottomSheetDescription,
          'Giao dịch trước đó của bạn đang được xử lý, vui lòng chờ ít nhất 1 phút trước khi thanh toán lại nhé');

      // QR code screen
      expect(EvoStrings.scanQRCodeGuideline, 'Hướng khung Camera\nvào vị trí có mã QR');
      expect(EvoStrings.scanQRCodeTitle, 'Quét mã');
      expect(EvoStrings.cameraPermissionTitle, 'Quyền truy cập Camera');
      expect(EvoStrings.cameraPermissionDescription,
          'Kích hoạt Camera trong phần cài đặt để thực hiện tính năng scan QR');
      expect(EvoStrings.errorQrCodeDefaultErrorDescription,
          'EVO không thể quét mã QR này, bạn kiểm tra và thử lại nhé');
      expect(EvoStrings.scanQRClose, 'Đóng');
      expect(EvoStrings.scanQRRetry, 'Thử lại');
      expect(EvoStrings.storeNotFoundDefaultError, 'Cửa hàng không tồn tại');
      expect(EvoStrings.userCardNotSupportTitle, 'Chưa hỗ trợ với thẻ của bạn');
      expect(EvoStrings.userCardNotSupportContent,
          'EVO sẽ gửi thông báo cho bạn ngay khi thẻ được hỗ trợ, bạn cố đợi thêm nhé');
      expect(EvoStrings.scanFailBackToHome, 'Về trang chủ');
      expect(EvoStrings.cantCheckLinkedCardTitle, 'Ơ, có chút vấn đề rồi');
      expect(EvoStrings.cantCheckLinkedCardContent,
          'EVO đang kiểm tra và khắc phục, bạn đợi một chút rồi thử lại nhé.');
      expect(EvoStrings.cantCheckLinkedCardButton, 'Về trang chủ');
      expect(EvoStrings.defaultCantLinkedCartStatusContent,
          'Chưa đủ điều kiện liên kết thẻ. Thử lại sau bạn nhé.');
      expect(EvoStrings.titleErrorPopupInvalidFormat, 'Mã QR không hợp lệ');
      expect(EvoStrings.titleErrorPopupOrderInvalid, 'Đơn hàng không hợp lệ');
      expect(EvoStrings.titleErrorPopupOrderExpired, 'Đơn hàng đã hết hạn');
      expect(EvoStrings.titleErrorPopupOrderInPayment, 'Đơn hàng đang được xử lý');
      expect(EvoStrings.titleErrorPopupInvalidOrderStatus, 'Đơn hàng đang xử lý hoặc đã thất bại');
      expect(EvoStrings.titleErrorPopupPayTimesExceeded, 'Không thể thanh toán đơn hàng này');
      expect(EvoStrings.titleErrorPopupOrderSucceeded, 'Đơn hàng đã được thanh toán');
      expect(EvoStrings.titleErrorPopupOrderFailed, 'Đơn hàng đã thất bại');
      expect(EvoStrings.titleErrorPopupOrderCancelled, 'Đơn hàng đã bị hủy');
      expect(EvoStrings.descriptionEmiNotSupportForThisProduct,
          'EVO hiện chưa hỗ trợ trả góp với phương thức thanh toán này');
      expect(EvoStrings.descriptionEmiNotSupportForVNPay,
          'EVO hiện chưa hỗ trợ trả góp với phương thức thanh toán VNPay');

      //Home user menu
      expect(EvoStrings.menuUserGuideCardUsage, 'Hướng dẫn\nsử dụng thẻ');
      expect(EvoStrings.menuUserReward, 'Ưu đãi\ncủa tôi');
      expect(EvoStrings.menuUserScanner, 'Quét mã\nthanh toán');
      expect(EvoStrings.menuUserEmiPayment, 'Trả góp\nlinh hoạt');
      expect(EvoStrings.menuUserQuestion, 'Câu hỏi\nthường gặp');

      // Account deleting flow
      expect(EvoStrings.confirmDeactivateTitle, 'Khi xoá tài khoản, bạn sẽ:');
      expect(
          EvoStrings.confirmDeactivateNotice,
          '  \u2022  Huỷ liên kết thẻ trên ứng dụng EVO\n'
          '  \u2022  Không thể quét mã thanh toán và dùng ưu đãi trên EVO\n'
          '  \u2022  Không thể dùng số điện thoại này để tạo tài khoản EVO trong 30 ngày');
      expect(EvoStrings.confirmDeactivatePositiveButton, 'Xoá tài khoản');
      expect(EvoStrings.confirmDeactivateNegativeButton, 'Không xoá');
      expect(EvoStrings.deactivateAccountFailTitle, 'Chưa thể xoá tài khoản EVO');
      expect(EvoStrings.deactivateAccountFailNotice,
          'Lâu rồi chưa gặp trường hợp nào như thế này, bạn thử lại lần nữa nhé');
      expect(EvoStrings.deactivateAccountSuccessTitle, 'Đã xoá tài khoản EVO');
      expect(
          EvoStrings.deactivateAccountSuccessNotice,
          '  \u2022  Các giao dịch trên thẻ EVO bạn có thể theo dõi trên ứng dụng TPBank\n'
          '  \u2022  Bạn có thể đăng ký lại tài khoản EVO sau 30 ngày nữa. EVO đợi bạn 👋🏻');

      // Transaction history
      expect(EvoStrings.transactionHistoryEmptyTittle, 'Chưa có giao dịch nào thanh toán bằng EVO');
      expect(EvoStrings.transactionHistoryEmptyDesc,
          'Mọi giao dịch thanh toán bằng ứng dụng EVO của bạn sẽ hiện ở đây');
      expect(EvoStrings.transactionHistoryNotLogin, 'Đăng nhập để xem giao dịch');
      expect(EvoStrings.transactionHistoryNotLoginDesc,
          'Mọi giao dịch thanh toán bằng ứng dụng EVO của bạn sẽ hiện ở đây');
      expect(EvoStrings.transactionHistoryProcessing, 'Đang xử lý');
      expect(EvoStrings.transactionHistorySuccess, 'Thành công');
      expect(EvoStrings.transactionHistoryFailure, 'Thất bại');
      expect(EvoStrings.transactionHistoryTitle, 'Chi tiết giao dịch');
      expect(EvoStrings.transactionHistoryCashbackTitle, 'Ưu đãi hoàn tiền đã nhận');
      expect(EvoStrings.transactionHistoryCashbackPeriodTitle, 'Kỳ tháng {0}');
      expect(EvoStrings.transactionHistoryCashbackBottomSheetDesc,
          'Khoản tiền hoàn sẽ được ghi có vào tài khoản thẻ tín dụng chậm nhất vào ngày 20 của tháng kế tiếp kỳ sao kê có phát sinh giao dịch.');
      expect(EvoStrings.transactionHistoryCashbackNameBottomSheetTitle, 'Ứng dụng EVO');

      // Transaction detail
      expect(EvoStrings.transactionDetailTitleProcessing, 'Đang xử lý thanh toán tại {0}');
      expect(EvoStrings.transactionDetailOutrightPurchaseTitleSuccess, 'Bạn đã thanh toán tại {0}');
      expect(EvoStrings.transactionDetailEmiTitleSuccess, 'Đã ghi nhận đăng ký trả góp tại {0}');
      expect(EvoStrings.transactionDetailTitleErrorPreFix, 'Giao dịch thất bại tại {0}');

      // Linked card
      expect(EvoStrings.linkedCardDetailTitle, 'Tài khoản liên kết');
      expect(EvoStrings.linkedCardLimitation, 'Hạn mức thanh toán');
      expect(EvoStrings.linkedCardMinimumLimitation, 'Hạn mức tối thiểu');
      expect(EvoStrings.linkedCardMaximumLimitation, 'Hạn mức tối đa');
      expect(EvoStrings.linkedCardLimitPerTrans, ' / Giao dịch');

      // Intro
      expect(EvoStrings.introductionText1, 'Quét mã thanh toán không cần thẻ cứng');
      expect(EvoStrings.introductionText2, 'Mở thẻ online 24/7 chỉ trong 10 phút');
      expect(EvoStrings.introductionText3, 'Nhiều ưu đãi hấp dẫn từ hàng ngàn đối tác EVO');
      expect(EvoStrings.introductionSkip, 'Bỏ qua');

      // Non-authorized user with scan function
      expect(EvoStrings.nonAuthorizedUserScanPopupTitle, 'Đăng nhập để sử dụng EVO');
      expect(EvoStrings.nonAuthorizedUserScanPopupContent,
          'Quét mã thanh toán không cần thẻ cứng, quản lý chi tiêu hiệu quả ngay trên ứng dụng');
      expect(EvoStrings.nonAuthorizedUserScanPopupButton, 'Đăng nhập');

      //force update
      expect(EvoStrings.forceUpdateTitle, 'Thông báo');
      expect(EvoStrings.forceUpdateDescription,
          'Cập nhật ngay phiên bản mới để có trải nghiệm mượt mà hơn & sử dụng các tính năng mới nhất.');
      expect(EvoStrings.forceUpdateSubDesc, 'Đã có phiên bản mới');
      expect(EvoStrings.forceUpdateAgree, 'Cập nhật');
      expect(EvoStrings.forceUpdateSkip, 'Bỏ qua');

      //Reset Pin
      expect(EvoStrings.resetPinSuccess, 'Đã đặt mã PIN thành công');
      expect(EvoStrings.titleNationalId, 'Nhập số CCCD/CMND');
      expect(EvoStrings.descriptionNationalId, 'Số bạn đã dùng để đăng ký thẻ EVO');
      expect(EvoStrings.hintNationalId, 'Nhập ở đây nhé');
      expect(EvoStrings.titleLimitResetPin, 'Tạm khoá Đặt lại mã PIN');
      expect(EvoStrings.descriptionLimitResetPin,
          'Số CCCD/CMND sai quá số lần giới hạn, thử lại sau 24 giờ nữa bạn nhé');
      expect(EvoStrings.errorLimitResetPin,
          'Mã OTP sai quá số lần giới hạn, thử lại sau 24 giờ nữa bạn nhé');
      expect(EvoStrings.titleSessionTokenExpiredSignIn, 'Hết thời gian đăng nhập');
      expect(
          EvoStrings.contentSessionTokenExpiredSignIn, 'Đăng nhập lại để tiếp tục sử dụng EVO nhé');
      expect(EvoStrings.textSubmitSessionTokenExpiredSignIn, 'Đăng nhập lại');
      expect(EvoStrings.titleSessionTokenExpiredResetPin, 'Hết thời gian đổi mã PIN');
      expect(EvoStrings.contentSessionTokenExpiredResetPin,
          'Hãy quay lại trang trước và bấm đặt lại mã PIN bạn nhé');
      expect(EvoStrings.textSubmitSessionTokenExpiredResetPin, 'Quay lại');

      // DOP
      expect(EvoStrings.dopCardStatusDefaultTitle, 'Chưa thể liên kết thẻ');
      expect(
          EvoStrings.dopDuplicatedLinkRequestTitle, 'Bạn có một yêu cầu liên kết thẻ đang xử lý');
      expect(EvoStrings.dopUnqualifiedUserInformationTitle, 'Liên kết thẻ thất bại');
      expect(EvoStrings.dopUnqualifiedCardTitle, 'Chưa thể cài đặt phương thức thanh toán');
      expect(EvoStrings.dopUnfulfilledCardDescription,
          'Bạn cần hoàn tất mở thẻ EVO trước khi thực hiện liên kết thẻ');
      expect(EvoStrings.dopWaitingForIssuingCardDescription,
          'Hồ sơ mở thẻ đang được duyệt. Bạn có thể liên kết sau khi nhận thông báo mở thẻ thành công.');
      expect(EvoStrings.dopUnqualifiedCardDescription,
          'Bạn đã huỷ đăng ký mở thẻ hoặc hồ sơ mở thẻ của bạn không được duyệt');
      expect(EvoStrings.dopDuplicatedLinkRequestDescription,
          'EVO sẽ thông báo cho bạn khi có kết quả liên kết');
      expect(EvoStrings.dopUnqualifiedUserInformationDescription,
          'Chưa đủ điều kiện liên kết thẻ. Thử lại sau bạn nhé.');
      expect(EvoStrings.dopContinueManualLinkCard, 'Tiếp tục mở thẻ');

      /// manual link card result
      expect(EvoStrings.resultSuccessDescription, 'Thanh toán nhanh hơn khi quét mã QR trên EVO');
      expect(EvoStrings.resultWaitingForLinkingCardDescription,
          'EVO sẽ thông báo cho bạn khi có kết quả liên kết');
      expect(EvoStrings.resultLinkingCardDefaultDescription,
          'EVO chưa thể xử lý yêu cầu liên kết thẻ của bạn. Thử lại sau {0} phút nữa nhé.');
      expect(EvoStrings.resultClose, 'Đóng');
      expect(EvoStrings.resultReLinkCard, 'Liên kết lại');
      expect(EvoStrings.resultSuccessTitle, 'Liên kết thẻ thành công');
      expect(EvoStrings.resultProcessingTitle, 'Đang liên kết thẻ');
      expect(EvoStrings.resultBackToCheckout, 'Trở về trang thanh toán');

      // manual link card
      expect(EvoStrings.titleManualLinkCardCheckOut, 'Liên kết thẻ để thanh toán');
      expect(EvoStrings.descriptionManualLinkCardCheckOut,
          'Liên kết thẻ TPBank EVO để thanh toán với các ưu đãi cực hấp dẫn từ EVO và đối tác');
      expect(EvoStrings.manualLinkCardButtonTitleCheckOut, 'Liên kết ngay');
      expect(EvoStrings.titleManualLinkCardScanQr, 'Chưa cài đặt phương thức thanh toán');
      expect(EvoStrings.descriptionManualLinkCardScanQr,
          'Bạn cần liên kết thẻ với ứng dụng EVO để thanh toán trên ứng dụng');
      expect(EvoStrings.manualLinkCardButtonTitleScanQr, 'Cài đặt ngay');
      expect(EvoStrings.manualLinkCard3DSTitle, 'Xác minh thẻ');
      expect(EvoStrings.linkCardCancellationWarningContent,
          'Nếu rời màn hình này, bạn cần đợi thêm {0} phút để có thể liên kết lại.');
      expect(EvoStrings.linkCardCancellationWarningTitle, 'Bạn có chắc muốn thoát?');
      expect(EvoStrings.linkCardKeepCancellation, 'Ở lại');
      expect(EvoStrings.linkCardDiscardCancellation, 'Thoát');
      expect(EvoStrings.linkCardInProcessing, 'Đang liên kết thẻ');
      expect(EvoStrings.linkCardProcessingCanTakeSomeMinutes,
          'Quá trình liên kết có thể mất vài phút. Bạn đừng tắt ứng dụng nhé.');
      expect(EvoStrings.linkCardOtpTimeout,
          'Đã hết thời gian liên kết. Bạn có thể thử lại sau {0} phút nữa nhé ⏰');
      expect(EvoStrings.linkCardPollingTimeout, 'EVO sẽ thông báo cho bạn khi có kết quả liên kết');
      expect(EvoStrings.submitLinkCardWithPermissionDenied,
          'EVO chưa thể xử lý yêu cầu liên kết thẻ của bạn. Thử lại sau bạn nhé.');
      expect(EvoStrings.submitLinkCardWithRecordNotFoundAndFailed,
          'EVO chưa thể xử lý yêu cầu liên kết thẻ của bạn. Thử liên kết lại bạn nhé 😉');
      expect(EvoStrings.submitLinkCardWithDefaultDescription,
          'EVO chưa thể xử lý yêu cầu liên kết thẻ của bạn. Thử lại sau {0} phút nữa nhé.');

      // Detect Root/Jailbreak
      expect(EvoStrings.titleBlockInsecureDeviceDialog, 'EVO chưa hỗ trợ thiết bị này');
      expect(EvoStrings.descriptionBlockInsecureDeviceDialog,
          'EVO chưa hỗ trợ các thiết bị đã bẻ khoá để bảo đảm an toàn cho tài khoản của bạn');

      // FaceOTP
      expect(EvoStrings.noticeFaceOtpTitle, 'Các lưu ý khi chụp');
      expect(EvoStrings.verifyFaceOtpTitle, 'Bạn cần xác thực khuôn mặt');
      expect(EvoStrings.verifyFaceOtpSignInDescription,
          'Xác thực danh tính của bạn khi đăng nhập và sử dụng ứng dụng');
      expect(EvoStrings.verifyFaceOtpLinkCardDescription,
          'Xác thực danh tính của bạn để liên kết thẻ TPBank EVO');
      expect(EvoStrings.startFaceOTP, 'Bắt đầu chụp');
      expect(EvoStrings.instructFaceOTPInFrame, 'Luôn giữ đầu trong khung hình');
      expect(
          EvoStrings.instructFaceOTPNoGlasses, 'Không đeo kính râm, nón hoặc các phụ kiện che mặt');
      expect(EvoStrings.instructFaceOTPCleanEnvironment,
          'Môi trường chụp không quá tối hoặc chói sáng');
      expect(EvoStrings.cameraFaceOtpPermissionDescription,
          'Kích hoạt Camera trong phần cài đặt để thực hiện tính năng xác thực khuôn mặt');
      expect(EvoStrings.faceOtpExceedLimitErrorTitle, 'Chưa thể xác thực khuôn mặt');
      expect(EvoStrings.faceOtpExceedLimitationDescription,
          'Bạn đã xác thực khuôn mặt quá số lần giới hạn. Thử lại sau nhé.');
      expect(EvoStrings.faceOtpBackButtonTitle, 'Quay lại');
      expect(EvoStrings.faceOtpSuccess, 'Xác thực khuôn mặt thành công');
      expect(EvoStrings.faceOtpMatchingErrorTitle, 'Xác thực thất bại');
      expect(EvoStrings.faceOtpMatchingErrorDescription,
          'Bạn không được dùng ảnh có sẵn để xác thực khuôn mặt');
      expect(EvoStrings.faceOtpMatchingRetryTitle, 'Thử lại');
      expect(EvoStrings.faceOtpMatchingProcessing, 'Hệ thống đang xử lý,\n xin chờ trong giây lát');
      expect(EvoStrings.eKYCLoadingTitle, 'Bạn đợi EVO chút nhé');
      expect(EvoStrings.eKYCLoadingContent,
          'Tháo mũ, kính râm, chọn nơi đủ sáng để xác thực nhanh và chính xác');
      expect(EvoStrings.eKYCErrorTitle, 'Ơ sao lại thế nhỉ');
      expect(EvoStrings.eKYCSelfieCapturingErrorDescription,
          'EVO chưa thể xác thực khuôn mặt của bạn, thử lại lần nữa nhé');
      expect(EvoStrings.eKYCSelfieCapturingCannotRetryErrorDescription,
          'EVO chưa thể xác thực ảnh khuôn mặt của bạn');

      expect(EvoStrings.faceOtpSessionExpireTitle, 'Phiên làm việc hết hạn');
      expect(EvoStrings.faceOtpSessionExpireDescription,
          'Phiên làm việc của bạn đã hết hạn, vui lòng nhấn vào nút bên dưới để tiếp tục');

      // Private policy
      expect(EvoStrings.privatePolicy, 'Chính sách xử lý dữ liệu');
      expect(EvoStrings.policyRequestAcceptTitle, 'Bạn cần đồng ý với bản cập nhật ngày {0}');
      expect(EvoStrings.policyAcceptedTitle, 'Cập nhật ngày {0}');
      expect(EvoStrings.policyDialogTitle, 'Bạn cần đồng ý để tiếp tục sử dụng EVO');
      expect(EvoStrings.policyDialogContent,
          '{0} nhằm bảo vệ thông tin, dữ liệu do bạn cung cấp và để tối ưu trải nghiệm cho bạn khi sử dụng EVO');
      expect(EvoStrings.policyDialogNegative, 'Xem lại');
      expect(EvoStrings.policyAccept, 'Tôi đồng ý');
      expect(EvoStrings.downloadConsentSuccess, 'Tải xuống thành công');
      expect(EvoStrings.downloadConsentError, 'Tải xuống không thành công');
      expect(EvoStrings.storagePermissionTitle, 'Chưa thiết lập lưu trữ tập tin');
      expect(EvoStrings.storagePermissionDescription,
          'Hãy đồng ý cho EVO lưu trữ tập tin trong Cài đặt của thiết bị để tải tập tin này');
      expect(EvoStrings.errorLoadingPdf, 'Không tải được nội dung');
      expect(EvoStrings.privatePolicyFileName, 'Chinh sach xu ly du lieu EVO');

      // Pre Face Otp of Manual link card flow
      expect(EvoStrings.preFaceOtpTitleManualLinkCard, 'Xác thực khuôn mặt để liên kết');
      expect(EvoStrings.preFaceOtpButtonTitleManualLinkCard, 'Tiếp tục ({0}s)');
      expect(EvoStrings.preFaceOtpDescriptionManualLinkCard,
          'Chỉ 1 bước chụp ảnh khuôn mặt nữa để liên kết thẻ với ứng dụng EVO 📸');

      /// Detele Account flow
      expect(EvoStrings.attentionNotesTitle, 'Vài điều bạn cần lưu ý');
      expect(
          EvoStrings.attentionNoteContents,
          '\nBạn có thể khôi phục tài khoản trong vòng 30 ngày chỉ với việc đăng nhập EVO bằng số điện thoại này.'
          '\n\nBạn có thể tiếp tục theo dõi các giao dịch thẻ EVO trên ứng dụng TPBank Mobile.'
          '\n\nNếu có ghi nhận một yêu cầu đăng ký thẻ tín dụng TPBank EVO bằng số điện thoại này, thì tài khoản sẽ bị xoá ngay lập tức để bảo vệ an toàn thông tin cá nhân và thông tin thẻ nếu có.'
          '\n\nTài khoản sẽ bị xoá vĩnh viễn khi quá 30 ngày kể từ thời điểm đề nghị xoá tài khoản.'
          '\n\nSau khi tài khoản bị xoá vĩnh viễn:\n');
      expect(EvoStrings.attentionNoteAfterDeleteUnlinkEvoCard,
          'Thẻ của bạn sẽ bị huỷ liên kết với ứng dụng EVO');
      expect(EvoStrings.attentionNoteAfterDeleteCantUseEvoApp,
          'Bạn không thể dùng EVO để quét mã thanh toán và dùng ưu đãi');
      expect(EvoStrings.attentionNoteCarefullyConsider,
          '\nCân nhắc kỹ trước khi xoá tài khoản bạn nhé. EVO có rất nhiều tiện ích và ưu đãi hấp dẫn dành riêng cho bạn 🎁');
      expect(EvoStrings.attentionNotesLearnMore, 'Tìm hiểu thêm về EVO');
      expect(EvoStrings.attentionNotesCancelDelete, 'Không xoá');
      expect(EvoStrings.attentionNotesConfirmDelete, 'Xoá tài khoản');
      expect(EvoStrings.attentionNotesProcessing, 'Đang xử lý...');
      expect(EvoStrings.lockAccountToDeleteTitle, 'Tạm khoá Xoá tài khoản');
      expect(EvoStrings.lockAccountToDeleteContent,
          'Chưa hết 24 giờ tạm khoá tính năng Xoá tài khoản, bạn đợi thêm chút nữa nhé');
      expect(EvoStrings.confirmToDeleteAccountTitle, 'Bạn muốn xoá tài khoản?');
      expect(EvoStrings.confirmToDeleteAccountDescription,
          'Bạn sẽ không thể dùng tài khoản này để quét mã QR thanh toán và sử dụng ưu đãi trên ứng dụng EVO');
      expect(EvoStrings.cancelToDeleteAccountTextBtn, 'Không xoá');
      expect(EvoStrings.deleteAccountTextBtn, 'Xoá tài khoản');
      expect(EvoStrings.deleteAccountSuccessTitle, 'Xoá tài khoản thành công');
      expect(EvoStrings.deleteAccountSuccessDescription,
          'Bạn có thể khôi phục tài khoản bằng cách đăng nhập lại EVO trong vòng 30 ngày');
      expect(EvoStrings.confirmPinToDeleteAccountText, 'Xác nhận mã PIN');
      expect(
          EvoStrings.confirmPinToDeleteAccountDescription, 'Nhập mã PIN để xác nhận xoá tài khoản');
      expect(EvoStrings.lockAccountDueToVerifyPinLimitExceedDescription,
          'Mã PIN sai quá số lần giới hạn, thử lại sau 24 giờ nữa bạn nhé');
      expect(EvoStrings.deleteAccountSurveyTitle, 'Chia sẻ lý do của bạn nhé');
      expect(EvoStrings.deleteAccountSurveyDescription,
          'Điều này giúp EVO tốt hơn trong thời gian tới');
      expect(EvoStrings.deleteAccountSurveyFooter, 'EVO sẵn sàng lắng nghe bạn. ');
      expect(EvoStrings.deleteAccountContactSurveyEvo, 'Liên hệ EVO');
      expect(EvoStrings.deleteAccountRemindUserSelectReason,
          'Cho EVO biết lý do xoá tài khoản của bạn nhé');
      expect(EvoStrings.welcomeBackDialogTitle, 'Chào mừng bạn trở lại');
      expect(EvoStrings.welcomeBackDialogContent, 'Bạn đã khôi phục tài khoản thành công');
      expect(EvoStrings.welcomeBackDialogBtn, 'Đóng');
      expect(EvoStrings.deleteAccountSessionExpiredTitle, 'Yêu cầu hết hiệu lực');
      expect(EvoStrings.deleteAccountSessionExpiredContent,
          'Yêu cầu xoá tài khoản của bạn đã hết hiệu lực, thực hiện lại lần nữa nhé');
      expect(EvoStrings.deleteAccountSessionExpiredButton, 'Đóng');
      expect(
          EvoStrings.confirmPinToDeleteAccountDescription, 'Nhập mã PIN để xác nhận xoá tài khoản');
      expect(EvoStrings.lockAccountDueToVerifyPinLimitExceedDescription,
          'Mã PIN sai quá số lần giới hạn, thử lại sau 24 giờ nữa bạn nhé');
      expect(EvoStrings.sharingText, 'Chia sẻ');
      expect(EvoStrings.referralQrCodeTitle, 'Mã của bạn nè!');
      expect(EvoStrings.referralQrCodeDescription,
          'Đưa mã hoặc link này cho bạn bè mở thẻ EVO để nhận quà');
      expect(EvoStrings.referralLinkLoadingTitle, 'Bạn đợi EVO chút nhé');
      expect(EvoStrings.referralLinkLoadingDescription, 'EVO đang kiểm tra thông tin chương trình');
      expect(EvoStrings.referralLinkLoadingUserNotInCampaignTitle, 'Chương trình chưa phù hợp');
      expect(EvoStrings.referralLinkLoadingUserNotInCampaignDescription,
          'Cùng chờ đón các chương trình ưu đãi hấp dẫn từ EVO trong thời gian tới bạn nhé');
      expect(EvoStrings.referralLinkGetApiErrorTitle, 'Chưa thể tải thông tin chương trình');
      expect(EvoStrings.referralLinkGetApiErrorDescription,
          'Quay lại để lấy lại link nhé, chương trình rất hấp dẫn đang đợi bạn');
      expect(EvoStrings.referralLinkBackBtn, 'Quay lại');
      expect(EvoStrings.referralLinkConfirmCopy, 'Đã sao chép');

      // Download File
      expect(EvoStrings.downloadLinkFileSuccess, 'Tải file thành công');
      expect(EvoStrings.downloadLinkFileFail, 'Tải file không thành công');
      expect(EvoStrings.startDownloadLinkFile, 'Đang tải file, bạn đợi chút nhé');

      // Payment Input Amount V2
      expect(EvoStrings.fullPaymentInputAmountTitle, 'Bạn đang thanh toán tại');
      expect(EvoStrings.fullPaymentInputAmountUpdateNote, 'Thêm ghi chú');
      expect(EvoStrings.inputAmountUpdateNoteSuccess, 'Đã thêm ghi chú');
      expect(EvoStrings.inputAmountEmpty, 'Nhập số tiền cần thanh toán ở đây bạn nhé');
      expect(EvoStrings.paymentNotePrefix, 'Tôi muốn thanh toán số tiền này tại');
      expect(EvoStrings.paymentAmountV2Hint, 'nhập số tiền');

      //EMI
      expect(EvoStrings.suffixTitleEmi, 'tháng');
      expect(EvoStrings.payWithEMITitle, 'Bạn có thể trả góp tại đơn hàng này');
      expect(EvoStrings.payWithEMIDescription, 'Chia nhỏ số tiền trả mỗi tháng, lãi suất 0%');
      expect(EvoStrings.titlePayInAll, 'Chuyển sang trả thẳng');
      expect(EvoStrings.emiUnqualifiedCancelButton, 'Huỷ');
      expect(EvoStrings.titleChooseOtherPromotion, 'Chọn ưu đãi khác');
      expect(EvoStrings.titleEmiWithoutPromotion, 'Trả góp không dùng ưu đãi');
      expect(EvoStrings.paymentResultEmiTitleProcess, 'Đang xử lý giao dịch tại {0}');
      expect(EvoStrings.paymentResultOutrightOPurchaseTitleSuccess, 'Bạn đã thanh toán tại {0}');
      expect(EvoStrings.paymentResultEmiTitleSuccess, 'Đã ghi nhận đăng ký trả góp tại {0}');
      expect(EvoStrings.paymentResultEmiTitleError, 'Giao dịch thất bại tại {0}');
      expect(EvoStrings.paymentResultEmiDetail, 'Chi tiết');
      expect(EvoStrings.paymentResultEmiTransactionCode, 'Mã giao dịch');
      expect(EvoStrings.paymentPartnerReferenceLabel, 'Mã tham chiếu');
      expect(EvoStrings.paymentPartnerOrderId, 'Mã đơn hàng');
      expect(EvoStrings.paymentResultEmiTime, 'Thời gian');
      expect(EvoStrings.paymentResultEmiDescription, 'Mô tả');
      expect(EvoStrings.paymentResultEmiTenor, 'Kỳ hạn trả góp');
      expect(EvoStrings.paymentResultEmiMonthlyPay, 'Trả mỗi tháng');
      expect(EvoStrings.paymentResultEmiConversionFee, 'Phí chuyển đổi');
      expect(EvoStrings.paymentResultEmiRate, 'Lãi suất');
      expect(EvoStrings.paymentResultEmiVoucher, 'Ưu đãi');
      expect(EvoStrings.paymentResultEmiFee, 'Phí giao dịch');
      expect(EvoStrings.paymentResultEmiCheckPaymentMethod, 'Nguồn tiền');
      expect(EvoStrings.paymentResultEmiSupport, 'Liên hệ hỗ trợ');
      expect(EvoStrings.paymentResultEmiInterestRate, '{0} %');
      expect(EvoStrings.paymentResultEmiToday, 'Hôm nay - {0}');
      expect(EvoStrings.emiFeeInfoText, 'Tính vào kỳ sao kê tiếp theo');
      expect(EvoStrings.emiMonthlyInstallmentAmount, 'Trả mỗi tháng');
      expect(EvoStrings.emiConversionFee, 'Phí chuyển đổi');
      expect(EvoStrings.emiMonthlyInterestRate, 'Lãi suất');
      expect(EvoStrings.emiAmountDifferenceBetweenDirectAndEmiPayment, 'Chênh lệch với trả thẳng');
      expect(EvoStrings.emiOptionUWantEMIPaymentTitle, 'Bạn muốn trả góp tại {0}');
      expect(EvoStrings.emiCheckoutUWantEMIPaymentTitle, 'Đăng ký trả góp tại {0}');
      expect(EvoStrings.emiOptionSelectTenorTitle, 'trong bao lâu?');
      expect(EvoStrings.emiCheckoutSelectedTenorTitle, 'trong {0} tháng');
      expect(EvoStrings.emiOptionSeeMoreTenorInfoTitle, 'Xem thêm thông tin');
      expect(EvoStrings.emiPaymentSummaryTotalAmount, 'Tổng giá trị đơn hàng');
      expect(EvoStrings.outrightPurchaseTitle, 'Bạn đang thanh toán tại {0}');
      expect(EvoStrings.emiNotSupportTitle, 'Chưa hỗ trợ trả góp');
      expect(EvoStrings.emiGuideToOutrightPaymentDescription,
          'Bạn có thể chọn trả thẳng để tiếp tục thanh toán cho đơn hàng này');
      expect(EvoStrings.orderExpiredTitleBottomSheet,
          'Hết thời gian thanh toán, thực hiện lại giao dịch bạn nhé');
      expect(EvoStrings.orderExpiredDescriptionBottomSheet,
          'Giao dịch của bạn đã hết thời hạn thanh toán.\nVui lòng thực hiện lại giao dịch để hoàn tất quá trình mua hàng.');
      expect(EvoStrings.doAgain, 'Thực hiện lại');
      expect(EvoStrings.understand, 'Đã hiểu');
      expect(EvoStrings.groupHeaderMonthTitle, 'Tháng');
      expect(EvoStrings.mwgPromotionSuffixUnSelectedTitle, 'Chọn ngay');
      expect(EvoStrings.mwgPromotionSuffixSelectedTitle, 'Thay đổi');
      expect(EvoStrings.mwgPaymentSelectPromotion, 'Danh sách ưu đãi');
      expect(EvoStrings.mwgPaymentSummaryTotalAmount, 'Tổng giá trị đơn hàng');

      /// EMI confirm and payment
      expect(EvoStrings.emiUnqualifiedAfterApplyPromotionTitle, 'Chưa đủ điều kiện trả góp');
      expect(EvoStrings.emiUnqualifiedAfterApplyPromotionChangeToOutrightPayment,
          'Chuyển sang trả thẳng');
      expect(EvoStrings.emiUnqualifiedAfterApplyPromotionSelectOtherPromotion, 'Chọn ưu đãi khác');
      expect(EvoStrings.emiUnqualifiedAfterApplyPromotionDefaultError,
          'Số tiền thanh toán của bạn không đủ điều kiện trả góp.');

      // DOP on WebView
      expect(EvoStrings.cancelDOPJourneyTitle, 'Bạn có chắc muốn dừng hành trình mở thẻ không?');
      expect(EvoStrings.cancelDOPJourneyDescription,
          '“Tiếp tục” hành trình để không bỏ lỡ các ưu đãi hấp dẫn từ thẻ TPBank EVO');
      expect(EvoStrings.webViewDOPJourneyTitle, 'Đăng ký mở thẻ');
      expect(EvoStrings.webViewDOPWithErrorCameraPermission,
          'Vui lòng cho phép ứng dụng EVO sử dụng camera để tiếp tục hành trình mở thẻ nhé!');

      // Revamp home page for non-users
      expect(EvoStrings.homepageForNonUserTitle, 'Thẻ tín dụng\nđồng thương hiệu');
      expect(EvoStrings.homePageForNonUserDescription, 'Đồng hành cùng\nngân hàng Tiên Phong');
      expect(EvoStrings.storyCtaLogin, 'Đăng nhập');
      expect(EvoStrings.storyCtaViewDetail, 'Xem chi tiết');
      expect(EvoStrings.storyFooterDescription,
          'Điều gì khiến bạn chần chừ? Trò chuyện với \nEVO ngay nhé');

      /// POS limit
      expect(EvoStrings.posLimitTitle, 'Thanh toán thất bại do hạn mức quẹt thẻ chưa được cài đặt');
      expect(EvoStrings.posLimitDescription,
          'Thao tác 4 bước đơn giản để Cài đặt Hạn mức tại ứng dụng TPBank Mobile và tiếp tục mua sắm nhé!');
      expect(EvoStrings.enablePosLimitButton, 'Cách cài đặt');
      expect(EvoStrings.enablePosLimitWebTitle, 'Hướng dẫn cài đặt');

      /// Remind POS limit
      expect(EvoStrings.remindEnablePosLimitButton, 'Tiếp tục');
      expect(EvoStrings.remindPosLimitGuideIntroTitle, 'Bạn đã cài đặt hạn mức thanh toán chưa?');
      expect(EvoStrings.remindPosLimitGuideIntroDescription,
          'Cài đặt hạn mức quẹt thẻ/rút tiền theo các bước sau để tiếp tục thanh toán với ứng dụng EVO nhé!');
      expect(EvoStrings.remindPosLimitGuideTitle1, 'Bước 1');
      expect(EvoStrings.remindPosLimitGuideDescription1,
          'Đăng nhập Ứng dụng TPBank Mobile và chọn mục Quản lý thẻ');
      expect(EvoStrings.remindPosLimitGuideTitle2, 'Bước 2');
      expect(EvoStrings.remindPosLimitGuideDescription2,
          'Trên màn hình Quản lý thẻ, chọn mục\nXem thêm');
      expect(EvoStrings.remindPosLimitGuideTitle3, 'Bước 3');
      expect(EvoStrings.remindPosLimitGuideDescription3, 'Chọn Cài đặt hạn mức giao dịch');
      expect(EvoStrings.remindPosLimitGuideTitle4, 'Bước 4');
      expect(EvoStrings.remindPosLimitGuideDescription4, 'Chọn Cài đặt hạn mức quẹt thẻ/rút tiền');
      expect(EvoStrings.remindPosLimitGuideTitle5, 'Bước 5');
      expect(EvoStrings.remindPosLimitGuideDescription5,
          'Cài đặt hạn mức quẹt thẻ/rút tiền với giá trị lớn hơn giá trị đơn hàng');

      /// DOP Native - scan invalid QR code
      expect(EvoStrings.dopNativeScanInvalidQrCodeTitle, 'Mã QR chưa đúng');
      expect(EvoStrings.dopNativeScanInvalidQrCodeDescription,
          'EVO không thể quét mã QR này, bạn kiểm tra và thử lại nhé');
      expect(EvoStrings.dopNativeCameraPermissionDenyTitle, 'Quyền truy cập Camera');
      expect(EvoStrings.dopNativeCameraPermissionDenyDescription,
          'Kích hoạt Camera trong cài đặt để quét mã và mở thẻ nhanh chóng');

      /// Tern & condition
      expect(EvoStrings.termAndConditionPopupTitle, 'Đồng ý Điều khoản và Điều kiện');
      expect(EvoStrings.termAndConditionPopupContent1, 'Tôi xác nhận đã đọc, hiểu rõ và đồng ý');
      expect(EvoStrings.termAndConditionPopupContent2, 'Điều khoản và Điều kiện');
      expect(EvoStrings.termAndConditionPopupContent3, 'khi sử dụng ứng dụng EVO');
      expect(EvoStrings.termAndConditionPopupCTA, 'Tiếp tục');

      /// VNPay QR payment
      expect(EvoStrings.orderProcessedByTPB, 'Đơn hàng được xử lý bởi TPBank');

      /// Scan QR Go Evo URL
      expect(EvoStrings.scanQRGoEvoUrlWebViewTitle, 'Chi tiết chương trình');

      /// EMI Management
      expect(EvoStrings.emiManagementTitle, 'Quản lý trả góp');
      expect(EvoStrings.emiManagementEmptyTitle, 'Chưa có giao dịch trả góp');
      expect(EvoStrings.emiManagementEmptyDescription,
          'Mọi yêu cầu trả góp qua ứng dụng EVO của bạn hiện ở đây');

      /// EMI Management Detail
      expect(EvoStrings.emiManagementDetailTitle, 'Chi tiết trả góp');
      expect(EvoStrings.emiManagementTransactionDetailCTA, 'Chi tiết giao dịch');
      expect(EvoStrings.emiManagementRemainingAmount, 'Dư nợ trả góp còn lại');
      expect(EvoStrings.emiManagementRemainingPeriod, 'Số tháng còn lại');
      expect(EvoStrings.emiManagementPaid, 'Đã thanh toán');
      expect(EvoStrings.emiManagementDetailMonthTitle, 'tháng');

      /// Activated Card
      expect(EvoStrings.activatedCardIntroductionTitle,
          'Bạn cần kích hoạt thẻ và cài đặt hạn mức trước khi thanh toán');
      expect(EvoStrings.activatedCardIntroductionDescription,
          'Cài đặt hạn mức để kích hoạt thẻ trước khi thực hiện thanh toán đơn hàng');
      expect(EvoStrings.activatedCardIntroductionCTA, 'Tiếp tục');

      /// POS Limit
      expect(EvoStrings.posLimitIntroductionTitle, 'Thẻ của bạn chưa cài đặt hạn mức thanh toán');
      expect(EvoStrings.posLimitIntroductionDescription,
          'Bạn cần cài đặt hạn mức thanh toán tối đa trên 1 giao dịch để hoàn tất đơn hàng');
      expect(EvoStrings.posLimitIntroductionCTA, 'Cài đặt hạn mức');

      /// Activated Card Guidance
      expect(EvoStrings.activatedCardGuideTitle,
          'Bạn cần kích hoạt thẻ và cài đặt hạn mức trước khi thanh toán');
      expect(EvoStrings.activatedCardGuideDescription,
          'Xem hướng dẫn để kích hoạt thẻ và cài đặt hạn mức trên ứng dụng TPBank');
      expect(EvoStrings.activatedCardAcceptedDescription, 'Đã kích hoạt thẻ');

      /// POS Limit Guidance
      expect(EvoStrings.posLimitGuideTitle, 'Giá trị đơn hàng đang cao hơn hạn mức thanh toán');
      expect(EvoStrings.posLimitGuideDescription,
          'Bạn cần nâng hạn mức thanh toán trên ứng dụng TPBank để hoàn tất được đơn hàng');
      expect(EvoStrings.activePosLimitPayAgainCTA, 'Thanh toán lại đơn hàng');
      expect(EvoStrings.activePosLimitSetupTpBankCTA, 'Chuyển sang ứng dụng TPBank');
      expect(EvoStrings.posLimitAcceptedDescription, 'Đã cài đặt hạn mức POS');

      /// Insufficient Credit Limit
      expect(EvoStrings.creditLimitInsufficientBottomSheetTitle, 'Hạn mức tín dụng không đủ');
      expect(EvoStrings.creditLimitInsufficientBottomSheetDescription,
          'Tách đơn hàng với giá trị nhỏ hơn để thanh toán');

      // Setup pos limit
      expect(EvoStrings.setupPosLimitTitle, 'Cài đặt hạn mức giao dịch');
      expect(EvoStrings.posLimitNeedHigherThanOrderAmount,
          'Hạn mức thanh toán cần lớn hơn giá trị đơn hàng');
      expect(EvoStrings.setupPosLimitNote,
          'Bạn có thể điều chỉnh lại hạn mức thông qua ứng dụng TPBank Mobile');
      expect(EvoStrings.setupPosLimitTooltipContent,
          'Hạn mức tối đa là {0}, bạn có thể điều chỉnh lại trên ứng dụng TPBank Mobile');
      expect(EvoStrings.setupPosLimitEnterAmount, 'Nhập số tiền');

      /// Setup POS limit - Waiting popup
      expect(EvoStrings.systemNeedTimeToProcess, 'Hệ thống cần thời gian xử lý');
      expect(EvoStrings.youWaitAndRetryLater, 'Bạn vui lòng chờ và thử lại sau');
      expect(EvoStrings.activatePOSLimitCancellationWarningContent,
          'Nếu rời màn hình này, bạn cần đợi thêm 5 phút để có thể kích hoạt lại');
      expect(EvoStrings.activatePOSLimitCancellationWarningTitle, 'Bạn có chắc muốn thoát?');
      expect(EvoStrings.activatePOSLimitStayHere, 'Ở lại');
      expect(EvoStrings.activatePOSLimitDiscardCancellation, 'Thoát');

      // cashback result popup
      expect(EvoStrings.cashBackSuccessTitle, 'Hoàn tiền thành công');
      expect(EvoStrings.cashBackSuccessContentPar1, 'Bạn đã được hoàn ');
      expect(EvoStrings.cashBackSuccessContentPar2, ' từ giao dịch với ');
      expect(EvoStrings.viewCashBackCTA, 'Xem tiền hoàn');

      // Maintenance
      expect(EvoStrings.maintenanceTitle, 'Hệ thống đang bảo trì');
      expect(EvoStrings.systemDuringMaintain, 'Hệ thống đang trong quá trình bảo trì');
      expect(EvoStrings.pleaseComebackLater, 'Vui lòng quay lại sau thời gian này');
      expect(EvoStrings.from, 'từ');
      expect(EvoStrings.day, 'ngày');
      expect(EvoStrings.hour, 'giờ');
      expect(EvoStrings.minute, 'phút');
      expect(EvoStrings.to, 'đến');

      // Rating Review Popup
      expect(
          EvoStrings.cardIssuedCardActivatedRatingReviewPopupTitle, 'Bạn vừa mở thẻ thành công 🎉');
      expect(EvoStrings.cardIssuedCardActivatedRatingReviewPopupContent,
          'Cảm ơn bạn đã tin dùng EVO. Nếu hài lòng, hãy đánh giá 5 sao để EVO phục vụ bạn ngày càng tốt hơn nhé!');
      expect(EvoStrings.paymentResultRatingReviewPopupTitle, 'Giao dịch thành công rồi! ✅');
      expect(EvoStrings.paymentResultRatingReviewPopupContent,
          'Nếu hài lòng, hãy đánh giá 5 sao cho EVO nhé, mỗi đánh giá của bạn sẽ giúp EVO hoàn thiện trải nghiệm tốt hơn.');
    });
  });
}
