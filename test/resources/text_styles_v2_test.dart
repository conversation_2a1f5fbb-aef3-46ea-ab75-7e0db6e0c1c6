// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../util/flutter_test_config.dart';

void main() {
  late EvoTextStylesV2 textStylesV2;

  setUpAll(() {
    getItRegisterColorV2();
  });

  setUp(() {
    textStylesV2 = EvoTextStylesV2();
  });

  tearDownAll(() {
    getItUnregisterColorV2();
  });

  group('EvoTextStylesV2 initialization', () {
    test('should inherit from EvoTextStyles', () {
      expect(textStylesV2, isA<EvoTextStyles>());
    });

    test('should use Inter font family', () {
      expect(textStylesV2.fontFamily, 'Inter');
    });
  });

  group('heading() method', () {
    group('returns correct TextStyle properties', () {
      test('for h0 heading type', () {
        final TextStyle textStyle = textStylesV2.heading(HeadingTextType.h0);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w700);
        expect(textStyle.fontSize, 40);
        expect(textStyle.height, 48 / 40); // lineHeight / fontSize
        expect(textStyle.letterSpacing, -4 / 100 * 40); // figmaLetterSpacing / 100 * fontSize
      });

      test('for h1 heading type', () {
        final TextStyle textStyle = textStylesV2.heading(HeadingTextType.h1);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w600);
        expect(textStyle.fontSize, 28);
        expect(textStyle.height, 36 / 28);
        expect(textStyle.letterSpacing, 0); // default figmaLetterSpacing is 0
      });

      test('for h2 heading type', () {
        final TextStyle textStyle = textStylesV2.heading(HeadingTextType.h2);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w600);
        expect(textStyle.fontSize, 24);
        expect(textStyle.height, 28 / 24);
        expect(textStyle.letterSpacing, 0);
      });

      test('for h3 heading type', () {
        final TextStyle textStyle = textStylesV2.heading(HeadingTextType.h3);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w600);
        expect(textStyle.fontSize, 20);
        expect(textStyle.height, 28 / 20);
        expect(textStyle.letterSpacing, 0);
      });

      test('for h4 heading type', () {
        final TextStyle textStyle = textStylesV2.heading(HeadingTextType.h4);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w600);
        expect(textStyle.fontSize, 18);
        expect(textStyle.height, 24 / 18);
        expect(textStyle.letterSpacing, 0);
      });

      test('for h5 heading type', () {
        final TextStyle textStyle = textStylesV2.heading(HeadingTextType.h5);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w600);
        expect(textStyle.fontSize, 16);
        expect(textStyle.height, 24 / 16);
        expect(textStyle.letterSpacing, 0);
      });

      test('for h6 heading type', () {
        final TextStyle textStyle = textStylesV2.heading(HeadingTextType.h6);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w500);
        expect(textStyle.fontSize, 14);
        expect(textStyle.height, 20 / 14);
        expect(textStyle.letterSpacing, -1 / 100 * 14);
      });

      test('for h7 heading type', () {
        final TextStyle textStyle = textStylesV2.heading(HeadingTextType.h7);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w600);
        expect(textStyle.fontSize, 12);
        expect(textStyle.height, 18 / 12);
        expect(textStyle.letterSpacing, -1 / 100 * 12);
      });
    });

    test('uses default color when no color specified', () {
      final TextStyle textStyle = textStylesV2.heading(HeadingTextType.h1);
      expect(textStyle.color, evoColorsV2.textTitle);
    });

    test('uses custom color when specified', () {
      const Color customColor = Color(0xFF123456);
      final TextStyle textStyle = textStylesV2.heading(HeadingTextType.h1, color: customColor);
      expect(textStyle.color, customColor);
    });
  });

  group('body() method', () {
    group('returns correct TextStyle properties', () {
      test('for large body type', () {
        final TextStyle textStyle = textStylesV2.body(BodyTextType.large);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w400);
        expect(textStyle.fontSize, 16);
        expect(textStyle.height, 24 / 16);
      });

      test('for medium body type', () {
        final TextStyle textStyle = textStylesV2.body(BodyTextType.medium);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w400);
        expect(textStyle.fontSize, 14);
        expect(textStyle.height, 20 / 14);
      });

      test('for small body type', () {
        final TextStyle textStyle = textStylesV2.body(BodyTextType.small);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w400);
        expect(textStyle.fontSize, 12);
        expect(textStyle.height, 18 / 12);
      });

      test('for smallCap body type', () {
        final TextStyle textStyle = textStylesV2.body(BodyTextType.smallCap);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w500);
        expect(textStyle.fontSize, 12);
        expect(textStyle.height, 18 / 12);
      });
    });

    test('uses default color when no color specified', () {
      final TextStyle textStyle = textStylesV2.body(BodyTextType.large);
      expect(textStyle.color, evoColorsV2.textBody);
    });

    test('uses custom color when specified', () {
      const Color customColor = Color(0xFF654321);
      final TextStyle textStyle = textStylesV2.body(BodyTextType.large, color: customColor);
      expect(textStyle.color, customColor);
    });
  });

  group('caption() method', () {
    group('returns correct TextStyle properties', () {
      test('for medium caption type', () {
        final TextStyle textStyle = textStylesV2.caption(CaptionTextType.medium);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w400);
        expect(textStyle.fontSize, 14);
        expect(textStyle.height, 20 / 14);
      });

      test('for small caption type', () {
        final TextStyle textStyle = textStylesV2.caption(CaptionTextType.small);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w400);
        expect(textStyle.fontSize, 12);
        expect(textStyle.height, 18 / 12);
      });
    });

    test('uses default color when no color specified', () {
      final TextStyle textStyle = textStylesV2.caption(CaptionTextType.medium);
      expect(textStyle.color, evoColorsV2.textSubtitle);
    });

    test('uses custom color when specified', () {
      const Color customColor = Color(0xFF789ABC);
      final TextStyle textStyle = textStylesV2.caption(CaptionTextType.medium, color: customColor);
      expect(textStyle.color, customColor);
    });
  });

  group('label() method', () {
    group('returns correct TextStyle properties', () {
      test('for xLarge label type', () {
        final TextStyle textStyle = textStylesV2.label(LabelTextType.xLarge);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w500);
        expect(textStyle.fontSize, 18);
        expect(textStyle.height, 24 / 18);
        expect(textStyle.letterSpacing, 0); // default figmaLetterSpacing is 0
      });

      test('for large label type', () {
        final TextStyle textStyle = textStylesV2.label(LabelTextType.large);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w600);
        expect(textStyle.fontSize, 16);
        expect(textStyle.height, 20 / 16);
        expect(textStyle.letterSpacing, -1 / 100 * 16);
      });

      test('for medium label type', () {
        final TextStyle textStyle = textStylesV2.label(LabelTextType.medium);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w600);
        expect(textStyle.fontSize, 14);
        expect(textStyle.height, 18 / 14);
        expect(textStyle.letterSpacing, -1 / 100 * 14);
      });

      test('for small label type', () {
        final TextStyle textStyle = textStylesV2.label(LabelTextType.small);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w600);
        expect(textStyle.fontSize, 12);
        expect(textStyle.height, 18 / 12);
        expect(textStyle.letterSpacing, 0);
      });

      test('for xSmall label type', () {
        final TextStyle textStyle = textStylesV2.label(LabelTextType.xSmall);

        expect(textStyle.fontFamily, 'Inter');
        expect(textStyle.fontWeight, FontWeight.w600);
        expect(textStyle.fontSize, 10);
        expect(textStyle.height, 18 / 10);
        expect(textStyle.letterSpacing, 0);
      });
    });

    test('uses default color when no color specified', () {
      final TextStyle textStyle = textStylesV2.label(LabelTextType.large);
      expect(textStyle.color, evoColorsV2.textLabel);
    });

    test('uses custom color when specified', () {
      const Color customColor = Color(0xFFDEF012);
      final TextStyle textStyle = textStylesV2.label(LabelTextType.large, color: customColor);
      expect(textStyle.color, customColor);
    });
  });

  group('HeadingTextType enum', () {
    test('height getter calculates correctly', () {
      expect(HeadingTextType.h0.height, 48 / 40);
      expect(HeadingTextType.h1.height, 36 / 28);
      expect(HeadingTextType.h2.height, 28 / 24);
      expect(HeadingTextType.h3.height, 28 / 20);
      expect(HeadingTextType.h4.height, 24 / 18);
      expect(HeadingTextType.h5.height, 24 / 16);
      expect(HeadingTextType.h6.height, 20 / 14);
      expect(HeadingTextType.h7.height, 18 / 12);
    });

    test('letterSpacing getter calculates correctly', () {
      expect(HeadingTextType.h0.letterSpacing, -4 / 100 * 40);
      expect(HeadingTextType.h1.letterSpacing, 0 / 100 * 28);
      expect(HeadingTextType.h2.letterSpacing, 0 / 100 * 24);
      expect(HeadingTextType.h3.letterSpacing, 0 / 100 * 20);
      expect(HeadingTextType.h4.letterSpacing, 0 / 100 * 18);
      expect(HeadingTextType.h5.letterSpacing, 0 / 100 * 16);
      expect(HeadingTextType.h6.letterSpacing, -1 / 100 * 14);
      expect(HeadingTextType.h7.letterSpacing, -1 / 100 * 12);
    });
  });

  group('BodyTextType enum', () {
    test('height getter calculates correctly', () {
      expect(BodyTextType.large.height, 24 / 16);
      expect(BodyTextType.medium.height, 20 / 14);
      expect(BodyTextType.small.height, 18 / 12);
      expect(BodyTextType.smallCap.height, 18 / 12);
    });
  });

  group('CaptionTextType enum', () {
    test('height getter calculates correctly', () {
      expect(CaptionTextType.medium.height, 20 / 14);
      expect(CaptionTextType.small.height, 18 / 12);
    });
  });

  group('LabelTextType enum', () {
    test('height getter calculates correctly', () {
      expect(LabelTextType.xLarge.height, 24 / 18);
      expect(LabelTextType.large.height, 20 / 16);
      expect(LabelTextType.medium.height, 18 / 14);
      expect(LabelTextType.small.height, 18 / 12);
      expect(LabelTextType.xSmall.height, 18 / 10);
    });

    test('letterSpacing getter calculates correctly', () {
      expect(LabelTextType.xLarge.letterSpacing, 0 / 100 * 18);
      expect(LabelTextType.large.letterSpacing, -1 / 100 * 16);
      expect(LabelTextType.medium.letterSpacing, -1 / 100 * 14);
      expect(LabelTextType.small.letterSpacing, 0 / 100 * 12);
      expect(LabelTextType.xSmall.letterSpacing, 0 / 100 * 10);
    });
  });
}
