import 'package:evoapp/widget/pdf_view/pdf_view_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pdfrx/pdfrx.dart';

void main() {
  const String fakeUrl = 'test_url';

  testWidgets('displays error widget when url is null', (WidgetTester tester) async {
    final GlobalKey<PdfViewWidgetState> keyPdfViewWidgetState = GlobalKey<PdfViewWidgetState>();
    bool isOnErrorLoadedCalled = false;
    final PdfViewWidget widget = PdfViewWidget(
      key: keyPdfViewWidgetState,
      errorWidget: const Text('Error'),
      onPdfLoadFailed: (ErrorUIModel error) {
        isOnErrorLoadedCalled = true;
      },
    );

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: widget,
        ),
      ),
    );

    await tester.pumpAndSettle();

    expect(find.byType(PdfViewer), findsNothing);
    expect(isOnErrorLoadedCalled, true);
    expect(find.text('Error'), findsOneWidget);
  });

  testWidgets('displays loading widget when state is PdfWidgetLoading and show PdfViewer',
      (WidgetTester tester) async {
    final GlobalKey<PdfViewWidgetState> keyPdfViewWidgetState = GlobalKey<PdfViewWidgetState>();
    final PdfViewerController pdfController = PdfViewerController();
    const bool fakeEnableZoom = false;
    const double fakePageMargin = 5;
    final PdfViewWidget widget = PdfViewWidget(
      key: keyPdfViewWidgetState,
      controller: pdfController,
      url: fakeUrl,
      enableZoom: fakeEnableZoom,
      pageMargin: fakePageMargin,
      loadingWidget: const Text('Loading...'),
      errorWidget: const Text('Error'),
    );

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: widget,
        ),
      ),
    );

    await tester.pumpAndSettle();

    expect(find.text('Loading...'), findsOneWidget);
    final Finder pdfViewer = find.byType(PdfViewer);
    expect(pdfViewer, findsOneWidget);

    final PdfViewer pdfViewerWidget = tester.widget(pdfViewer) as PdfViewer;
    expect(pdfViewerWidget.controller, isA<PdfViewerController>());
    expect(pdfViewerWidget.params.scaleEnabled, fakeEnableZoom);
    expect(pdfViewerWidget.params.margin, fakePageMargin);
    expect(pdfViewerWidget.params.backgroundColor, Colors.transparent);
    expect(pdfViewerWidget.params.pageDropShadow, null);
  });
}
