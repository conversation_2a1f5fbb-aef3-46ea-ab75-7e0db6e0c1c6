import 'package:evoapp/widget/animation/lottie_animation_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:lottie/lottie.dart';

void main() {
  group('LottieAnimationWidget', () {
    testWidgets('should render with default size and fit', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LottieAnimationWidget('assets/animation.json'),
        ),
      );

      final Finder lottieFinder = find.byType(Lottie);
      expect(lottieFinder, findsOneWidget);

      final Lottie lottieWidget = tester.widget<Lottie>(lottieFinder);
      expect(lottieWidget.width, 55);
      expect(lottieWidget.height, 55);
      expect(lottieWidget.fit, BoxFit.fill);
    });

    testWidgets('should render with custom size', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LottieAnimationWidget(
            'assets/animation.json',
            size: 100,
          ),
        ),
      );

      final Finder lottieFinder = find.byType(<PERSON><PERSON>);
      expect(lottieFinder, findsOneWidget);

      final Lottie lottieWidget = tester.widget<Lottie>(lottieFinder);
      expect(lottieWidget.width, 100);
      expect(lottieWidget.height, 100);
    });

    testWidgets('should render with custom fit', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LottieAnimationWidget(
            'assets/animation.json',
            fit: BoxFit.contain,
          ),
        ),
      );

      final Finder lottieFinder = find.byType(Lottie);
      expect(lottieFinder, findsOneWidget);

      final Lottie lottieWidget = tester.widget<Lottie>(lottieFinder);
      expect(lottieWidget.fit, BoxFit.contain);
    });

    testWidgets('should render with size constructor', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LottieAnimationWidget.size(
            'assets/animation.json',
            width: 150,
            height: 150,
            fit: BoxFit.contain,
          ),
        ),
      );

      final Finder lottieFinder = find.byType(Lottie);
      expect(lottieFinder, findsOneWidget);

      final Lottie lottieWidget = tester.widget<Lottie>(lottieFinder);
      expect(lottieWidget.width, 150);
      expect(lottieWidget.height, 150);
      expect(lottieWidget.fit, BoxFit.contain);
    });

    testWidgets('should render with size constructor, default size and fit',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: LottieAnimationWidget.size('assets/animation.json'),
        ),
      );

      final Finder lottieFinder = find.byType(Lottie);
      expect(lottieFinder, findsOneWidget);

      final Lottie lottieWidget = tester.widget<Lottie>(lottieFinder);
      expect(lottieWidget.width, isNull);
      expect(lottieWidget.height, isNull);
      expect(lottieWidget.fit, BoxFit.fill);
    });
  });
}
