import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/widget/evo_image_provider_widget.dart';
import 'package:evoapp/widget/evo_inkwell_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../util/flutter_test_config.dart';
import 'dynamic_height_carousel_slider/size_reporting_widget_test.dart';

void main() {
  late CommonImageProvider imageProvider;

  setUpAll(() {
    registerFallbackValue(BoxFit.contain);

    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    imageProvider = getIt.get<CommonImageProvider>();
    when(
      () => imageProvider.network(
        any(),
        height: any(named: 'height'),
        width: any(named: 'width'),
        cornerRadius: any(named: 'cornerRadius'),
        fit: any(named: 'fit'),
        color: any(named: 'color'),
        placeholder: any(named: 'placeholder'),
        errorWidget: any(named: 'errorWidget'),
        onLoadError: any(named: 'onLoadError'),
      ),
    ).thenReturn(Resizable());
    when(
      () => imageProvider.asset(
        any(),
        height: any(named: 'height'),
        width: any(named: 'width'),
        cornerRadius: any(named: 'cornerRadius'),
        fit: any(named: 'fit'),
      ),
    ).thenReturn(SizedBox(width: 100, height: 100));
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('EvoInkWellNetworkImage', () {
    test('should init with default values when not provided', () async {
      final EvoInkWellNetworkImage widget = EvoInkWellNetworkImage('url');
      expect(widget.typeImage, TypeImage.thumbnail);
      expect(widget.fit, BoxFit.contain);
      expect(widget.cornerRadius, 0);
    });

    testWidgets('should trigger onTap callback when tapped', (WidgetTester tester) async {
      bool isTapped = false;
      await tester.pumpWidget(
        MaterialApp(
          home: EvoInkWellNetworkImage(
            'url',
            height: 300,
            width: 300,
            onTap: () => isTapped = true,
          ),
        ),
      );

      await tester.tap(find.byType(InkWell));
      await tester.pumpAndSettle();

      expect(isTapped, isTrue);
    });

    testWidgets('should change height of InkWell when image height is changed',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Center(
            child: EvoInkWellNetworkImage('url'),
          ),
        ),
      );
      await tester.pumpAndSettle();

      final ResizableState state = tester.state(find.byType(Resizable));
      final double initialHeight = state.size.height;
      SizedBox sizedBox = tester.widget(find.byType(SizedBox));
      expect(sizedBox.height, initialHeight);

      final double newHeight = 300;
      expect(newHeight, isNot(initialHeight));
      state.resize(Size(300, newHeight));
      await tester.pumpAndSettle();
      sizedBox = tester.widget(find.byType(SizedBox));
      expect(sizedBox.height, newHeight);
    });

    testWidgets('should pass down values to EvoNetworkImageProviderWidget',
        (WidgetTester tester) async {
      final String imageUrl = 'url';
      final TypeImage typeImage = TypeImage.banner;
      final double width = 20;
      final double height = 30;
      final MaterialColor color = Colors.red;
      final BoxFit fit = BoxFit.fitHeight;
      final double cornerRadius = 4;
      final SizedBox customPlaceholder = SizedBox();
      final SizedBox customErrorWidget = SizedBox();

      await tester.pumpWidget(
        MaterialApp(
          home: EvoInkWellNetworkImage(
            imageUrl,
            typeImage: typeImage,
            width: width,
            height: height,
            color: color,
            fit: fit,
            cornerRadius: cornerRadius,
            placeholder: customPlaceholder,
            errorWidget: customErrorWidget,
          ),
        ),
      );

      final EvoNetworkImageProviderWidget widget =
          tester.widget(find.byType(EvoNetworkImageProviderWidget));
      expect(widget.imageUrl, imageUrl);
      expect(widget.typeImage, typeImage);
      expect(widget.width, width);
      expect(widget.height, height);
      expect(widget.color, color);
      expect(widget.fit, fit);
      expect(widget.cornerRadius, cornerRadius);
      expect(widget.placeholder, customPlaceholder);
      expect(widget.errorWidget, customErrorWidget);
    });
  });
}
