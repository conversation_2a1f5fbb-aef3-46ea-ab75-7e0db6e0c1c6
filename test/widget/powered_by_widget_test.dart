import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/powered_by_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider commonImageProvider;

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    commonImageProvider = getIt.get<CommonImageProvider>();

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());
  });

  testWidgets('PoweredByWidget test', (WidgetTester tester) async {
    await tester.pumpWidget(const MaterialApp(
      home: Scaffold(
        body: PoweredByWidget(),
      ),
    ));

    final Finder poweredByLabelFinder = find.text(EvoStrings.poweredByLabel);
    expect(poweredByLabelFinder, findsOneWidget);

    final Element poweredByLabelElement = tester.element(poweredByLabelFinder);
    final Row? poweredByLabelRowWidget = poweredByLabelElement.findAncestorWidgetOfExactType<Row>();
    expect(poweredByLabelRowWidget, isNotNull);
    expect(poweredByLabelRowWidget?.mainAxisAlignment, MainAxisAlignment.center);

    verify(() => commonImageProvider.asset(
          EvoImages.icTPBankLogo,
        )).called(1);
  });
}
