import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/payment/widget/remind_enable_pos_limit_guide_widget/remind_enable_pos_limit_guide_model.dart';
import 'package:evoapp/feature/payment/widget/remind_enable_pos_limit_guide_widget/remind_enable_pos_limit_guide_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:evoapp/widget/evo_dialog/evo_dialog_remind_enable_pos_limit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockEventTrackingUtils extends Mock implements EventTrackingUtils {}

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

void main() {
  late EventTrackingUtils eventTrackingUtils;
  final MockBuildContext mockBuildContext = MockBuildContext();

  const List<RemindEnablePosLimitGuideModel> expectedModels = <RemindEnablePosLimitGuideModel>[
    RemindEnablePosLimitGuideModel(
      title: EvoStrings.remindPosLimitGuideIntroTitle,
      description: EvoStrings.remindPosLimitGuideIntroDescription,
      image: EvoImages.imgRemindPosEnableIntro,
    ),
    RemindEnablePosLimitGuideModel(
      title: EvoStrings.remindPosLimitGuideTitle1,
      description: EvoStrings.remindPosLimitGuideDescription1,
      image: EvoImages.imgRemindPosEnableStep1,
    ),
    RemindEnablePosLimitGuideModel(
      title: EvoStrings.remindPosLimitGuideTitle2,
      description: EvoStrings.remindPosLimitGuideDescription2,
      image: EvoImages.imgRemindPosEnableStep2,
    ),
    RemindEnablePosLimitGuideModel(
      title: EvoStrings.remindPosLimitGuideTitle3,
      description: EvoStrings.remindPosLimitGuideDescription3,
      image: EvoImages.imgRemindPosEnableStep3,
    ),
    RemindEnablePosLimitGuideModel(
      title: EvoStrings.remindPosLimitGuideTitle4,
      description: EvoStrings.remindPosLimitGuideDescription4,
      image: EvoImages.imgRemindPosEnableStep4,
    ),
    RemindEnablePosLimitGuideModel(
      title: EvoStrings.remindPosLimitGuideTitle5,
      description: EvoStrings.remindPosLimitGuideDescription5,
      image: EvoImages.imgRemindPosEnableStep5,
    ),
  ];

  Future<void> verifyEvoDialogRemindEnablePosLimit(
    WidgetTester tester,
    List<RemindEnablePosLimitGuideModel> expectedModels,
  ) async {
    final Finder remindEnablePosLimitGuideWidgetFinder =
        find.byType(RemindEnablePosLimitGuideWidget);
    expect(remindEnablePosLimitGuideWidgetFinder, findsOneWidget);
    final RemindEnablePosLimitGuideWidget remindEnablePosLimitGuideWidget =
        tester.widget(remindEnablePosLimitGuideWidgetFinder);
    expect(remindEnablePosLimitGuideWidget.models, expectedModels);

    /// Verify CTA
    final Finder ctaFinder = find.text(EvoStrings.remindEnablePosLimitButton);
    // Tap to goto the last page
    expect(ctaFinder, findsOneWidget);
    for (int i = 0; i < expectedModels.length - 1; i++) {
      await tester.tap(ctaFinder);
      await tester.pumpAndSettle();
    }
    // verify CTA text is changed
    expect(find.text(EvoStrings.remindEnablePosLimitButton), findsNothing);
    final Finder ctaAfterTapFinder = find.text(EvoStrings.close);
    expect(ctaAfterTapFinder, findsOneWidget);
    // verify tap the CTA at the last page will close the dialog
    await tester.tap(ctaAfterTapFinder);
    await tester.pumpAndSettle();
    verify(() => navigatorContext?.pop()).called(1);

    /// Verify button (x)
    final Finder closeFinder = find.byIcon(Icons.close);
    expect(closeFinder, findsOneWidget);
    await tester.tap(closeFinder);
    await tester.pumpAndSettle();
    verify(() => navigatorContext?.pop()).called(1);
  }

  setUpAll(() {
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();

    getIt.registerSingleton<AppState>(AppState());
    getIt.registerSingleton<FeatureToggle>(FeatureToggle());
    getIt.registerLazySingleton<EventTrackingUtils>(() => MockEventTrackingUtils());
    eventTrackingUtils = getIt<EventTrackingUtils>();
    setUtilsMockInstanceForTesting();

    when(
      () => eventTrackingUtils.sendUserActionEvent(
        eventId: any(named: 'eventId'),
        metaData: any(named: 'metaData'),
      ),
    ).thenAnswer(
      (_) => Future<void>.value(),
    );

    getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());

    getIt.registerSingleton<GlobalKeyProvider>(GlobalKeyProvider());

    registerFallbackValue(mockBuildContext);

    // when(() => mockLoggingRepo.logEvent(
    //       eventType: EventType.userAction,
    //       data: any(named: 'data'),
    //     )).thenAnswer((_) => Future<void>.value());

    when(() => evoImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(100);

    when(() => EvoUiUtils().calculateHorizontalSpace(
          context: any(named: 'context'),
          widthPercentage: any(named: 'widthPercentage'),
        )).thenReturn(50);
  });

  tearDownAll(() {
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  group('Test function show()', () {
    testWidgets('Test function show()', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  EvoDialogRemindEnablePosLimit.show();
                },
                child: const Text('show button'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('show button'));
      await tester.pumpAndSettle(const Duration(seconds: 1));

      final Finder finder = find.byType(EvoDialogRemindEnablePosLimit);
      expect(finder, findsOneWidget);

      await verifyEvoDialogRemindEnablePosLimit(tester, expectedModels);
    });
  });
}
