import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_images.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/feature/dop_native/widgets/custom_button/camera_icon_button_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class MockCommonImageProvider extends Mock implements CommonImageProvider {}

void main() {
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getItRegisterColor();

    getIt.registerLazySingleton<DopNativeButtonStyles>(() => DopNativeButtonStyles());
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());
    getIt.registerLazySingleton<CommonImageProvider>(() => MockCommonImageProvider());
    mockCommonImageProvider = getIt.get<CommonImageProvider>();
    when(() => mockCommonImageProvider.asset(
          any(),
          height: any(named: 'height'),
          width: any(named: 'width'),
          fit: any(named: 'fit'),
        )).thenReturn(const SizedBox());
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('CameraIconButtonWidget', () {
    testWidgets('should display icon and default title', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CameraIconButtonWidget(
              onPressed: () {},
            ),
          ),
        ),
      );

      final Finder textFinder = find.text(DOPNativeStrings.dopNativeRetry);
      expect(textFinder, findsOneWidget);

      verify(() => mockCommonImageProvider.asset(
            DOPNativeImages.icDOPCamera,
            height: 16,
            width: 16,
          )).called(1);
    });

    testWidgets('should display icon and custom title', (WidgetTester tester) async {
      const String customTitle = 'Custom Title';
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CameraIconButtonWidget(
              onPressed: () {},
              title: customTitle,
            ),
          ),
        ),
      );

      final Finder textFinder = find.text(customTitle);

      expect(textFinder, findsOneWidget);
    });

    testWidgets('should trigger onPressed callback when tapped', (WidgetTester tester) async {
      bool isPressed = false;
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CameraIconButtonWidget(
              onPressed: () {
                isPressed = true;
              },
            ),
          ),
        ),
      );

      await tester.tap(find.byType(CommonButton));
      await tester.pumpAndSettle();

      expect(isPressed, true);
    });
  });
}
