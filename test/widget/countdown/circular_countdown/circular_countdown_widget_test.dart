import 'package:evoapp/widget/countdown/circular_countdown/circular_countdown_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets(
    'CircularCountdownWidget starts countdown and finishes',
    (WidgetTester tester) async {
      bool finished = false;
      int updatedRemaining = -1;

      await tester.pumpWidget(
        MaterialApp(
          home: CircularCountdownWidget(
            countdownTotal: 3,
            remainingCountdown: 3,
            onUpdated: (CountdownUnit unit, int remaining) {
              updatedRemaining = remaining;
            },
            onFinished: () {
              finished = true;
            },
          ),
        ),
      );

      // Verify initial state
      expect(updatedRemaining, 3);
      expect(finished, false);

      // Let the countdown proceed
      await tester.pump(const Duration(seconds: 1));
      expect(updatedRemaining, 2);

      await tester.pump(const Duration(seconds: 1));
      expect(updatedRemaining, 1);

      await tester.pump(const Duration(seconds: 1));
      expect(finished, true);
    },
  );

  testWidgets(
    'CircularCountdownWidget cancels countdown on dispose',
    (WidgetTester tester) async {
      int canceledRemaining = -1;

      // Create the widget with initial values
      await tester.pumpWidget(
        MaterialApp(
          home: CircularCountdownWidget(
            countdownTotal: 3,
            remainingCountdown: 3,
            onCanceled: (CountdownUnit unit, int remaining) {
              canceledRemaining = remaining;
            },
          ),
        ),
      );

      // Dispose the widget
      await tester.pumpWidget(Container());

      // Verify that the countdown was canceled
      expect(canceledRemaining, 3);
    },
  );

  testWidgets(
    'CircularCountdownWidget initializes with default properties',
    (WidgetTester tester) async {
      // Create the widget with default values
      await tester.pumpWidget(
        MaterialApp(
          home: CircularCountdownWidget(),
        ),
      );

      // Verify default properties
      final CircularCountdownWidgetState state = tester.state(find.byType(CircularCountdownWidget));
      expect(state.widget.unit, CountdownUnit.seconds);
      expect(state.widget.countdownTotal, 1);
      expect(state.widget.remainingCountdown, 1);
      expect(state.widget.shouldDowngradeUnit, true);
      expect(state.widget.countdownTotalColor, Colors.white30);
      expect(state.widget.countdownRemainingColor, Colors.white);
      expect(state.widget.backgroundEdgeColor, Colors.white);
      expect(state.widget.gapFactor, 2);
      expect(state.widget.isClockwise, false);
    },
  );

  testWidgets(
    'CircularCountdownWidget repeats countdown when repeat is true',
    (WidgetTester tester) async {
      bool finished = false;
      int updatedRemaining = -1;

      // Create the widget with repeat set to true
      await tester.pumpWidget(
        MaterialApp(
          home: CircularCountdownWidget(
            countdownTotal: 2,
            remainingCountdown: 2,
            repeat: true,
            onUpdated: (CountdownUnit unit, int remaining) {
              updatedRemaining = remaining;
            },
            onFinished: () {
              finished = true;
            },
          ),
        ),
      );

      // Let the countdown proceed and finish
      await tester.pump(const Duration(seconds: 2));
      expect(finished, true);

      // Verify that countdown restarts due to repeat
      await tester.pump(const Duration(seconds: 1));
      expect(updatedRemaining, 1);
    },
  );
}
