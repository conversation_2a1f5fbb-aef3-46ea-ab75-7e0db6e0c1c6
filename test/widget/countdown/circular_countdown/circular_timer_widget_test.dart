import 'package:evoapp/widget/countdown/circular_countdown/circular_timer_painter.dart';
import 'package:evoapp/widget/countdown/circular_countdown/circular_timer_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets('CircularTimerWidget displays correctly', (WidgetTester tester) async {
    const CircularTimerWidget widget = CircularTimerWidget(
      countdownTotal: 10,
      countdownRemaining: 5,
      diameter: 100,
      countdownTotalColor: Colors.red,
      countdownRemainingColor: Colors.green,
      countdownCurrentColor: Colors.blue,
      gapFactor: 5,
      strokeWidth: 5,
      isClockwise: true,
      textStyle: TextStyle(fontSize: 20, color: Colors.black),
    );

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: widget,
        ),
      ),
    );

    expect(find.byType(CircularTimerWidget), findsOneWidget);

    final CustomPaint customPaint = tester.widget<CustomPaint>(find.byType(CustomPaint).last);
    expect(customPaint.size, const Size(100, 100));

    final CircularTimerPainter textPainter = customPaint.painter as CircularTimerPainter;
    expect(textPainter.textStyle, const TextStyle(fontSize: 20, color: Colors.black));

    expect(textPainter.countdownTotalColor, Colors.red);
    expect(textPainter.countdownRemainingColor, Colors.green);
    expect(textPainter.countdownCurrentColor, Colors.blue);
    expect(textPainter.gapFactor, 5);
    expect(textPainter.strokeWidth, 5);
    expect(textPainter.isClockwise, true);
  });

  testWidgets('CircularTimerWidget with default properties', (WidgetTester tester) async {
    const CircularTimerWidget widget = CircularTimerWidget(countdownTotal: 10);

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: SizedBox(
            width: 600,
            height: 600,
            child: widget,
          ),
        ),
      ),
    );

    expect(find.byType(CircularTimerWidget), findsOneWidget);

    final CustomPaint customPaint = tester.widget<CustomPaint>(find.byType(CustomPaint).last);

    final CircularTimerPainter textPainter = customPaint.painter as CircularTimerPainter;

    expect(customPaint.size.width, 600);
    expect(customPaint.size.height, 600);

    expect(textPainter.countdownTotal, 10);
    expect(textPainter.countdownRemaining, 10);
    expect(textPainter.countdownTotalColor, Colors.white30);
    expect(textPainter.countdownRemainingColor, Colors.white);
    expect(textPainter.countdownCurrentColor, null);
    expect(textPainter.gapFactor, 2);
    expect(textPainter.strokeWidth, 100);
    expect(textPainter.isClockwise, false);
  });

  testWidgets('CircularTimerWidget handles negative countdownRemaining',
      (WidgetTester tester) async {
    expect(
      () => CircularTimerWidget(
        countdownTotal: 10,
        countdownRemaining: -1,
        diameter: 100,
        countdownTotalColor: Colors.red,
        countdownRemainingColor: Colors.green,
        countdownCurrentColor: Colors.blue,
        strokeWidth: 5,
        isClockwise: true,
        textStyle: TextStyle(fontSize: 20, color: Colors.black),
      ),
      throwsAssertionError,
    );
  });
}
