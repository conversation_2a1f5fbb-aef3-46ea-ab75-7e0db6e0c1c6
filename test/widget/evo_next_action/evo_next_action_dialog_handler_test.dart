import 'package:evoapp/data/response/voucher_earning_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/main_screen/main_screen.dart';
import 'package:evoapp/feature/payment/utils/voucher_detail_action_helper.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/widget/evo_next_action/evo_next_action_dialog_handler.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/widget/common_dialog_bottom_sheet/common_dialog_bottom_sheet.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockVoucherDetailActionHelper extends Mock implements VoucherDetailActionHelper {}

void main() {
  final EvoNextActionDialogHandler evoNextActionDialogHandler = EvoNextActionDialogHandler();
  final BuildContext mockBuildContext = MockBuildContext();
  late CommonNavigator mockCommonNavigator;
  late VoucherDetailActionHelper mockVoucherDetailActionHelper;

  void verifyGotoMainScreen() {
    expect(
        verify(
          () => mockCommonNavigator.removeUntilAndPushReplacementNamed(
            mockBuildContext,
            captureAny(),
            any(),
            extra: captureAny(named: 'extra'),
          ),
        ).captured,
        <dynamic>[
          Screen.mainScreen.name,
          isA<MainScreenArg>()
              .having((MainScreenArg p0) => p0.isLoggedIn, 'verify isLoggedIn', true),
        ]);
  }

  void verifyGotoVoucherDetailScreen(VoucherEarningEntity voucherEarningResult) {
    expect(
      verify(
        () => mockVoucherDetailActionHelper.handleGotoVoucherDetailScreen(captureAny()),
      ).captured.single,
      isA<VoucherEntity>()
          .having(
            (VoucherEntity p0) => p0.action?.type,
            'verify action type',
            voucherEarningResult.action?.type,
          )
          .having(
            (VoucherEntity p0) => p0.action?.args?.nextAction?.type,
            'verify next action type',
            voucherEarningResult.action?.args?.nextAction?.type,
          ),
    );
  }

  setUpAll(() {
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getItRegisterColor();

    setUpMockGlobalKeyProvider(mockBuildContext);
    getIt.registerLazySingleton<CommonNavigator>(() => MockCommonNavigator());
    mockCommonNavigator = getIt.get<CommonNavigator>();

    getIt.registerLazySingleton<VoucherDetailActionHelper>(() => MockVoucherDetailActionHelper());
    mockVoucherDetailActionHelper = getIt.get<VoucherDetailActionHelper>();

    registerFallbackValue(EvoDialogId.voucherEarningSuccessBottomSheet);
    registerFallbackValue(mockBuildContext);

    setUtilsMockInstanceForTesting();
    when(() => EvoDialogHelper().showDialogBottomSheet(
          title: any(named: 'title'),
          content: any(named: 'content'),
          dialogId: any(named: 'dialogId'),
          isShowButtonClose: any(named: 'isShowButtonClose'),
          header: any(named: 'header'),
          buttonListOrientation: any(named: 'buttonListOrientation'),

          /// Positive button
          textPositive: any(named: 'textPositive'),
          positiveButtonStyle: any(named: 'positiveButtonStyle'),
          onClickPositive: any(named: 'onClickPositive'),
        )).thenAnswer((_) async => Future<void>.value());

    when(() => evoImageProvider.asset(
          any(),
          width: any(named: 'width'),
          fit: any(named: 'fit'),
        )).thenReturn(const SizedBox.shrink());

    when(() => mockCommonNavigator.maybePop(any(), result: any(named: 'result'))).thenReturn(true);
  });

  tearDownAll(() {
    resetUtilMockToOriginalInstance();
    getIt.reset();
  });

  test('Test showVoucherEarningSuccessDialog with action type = promotion_scan_to_pay', () {
    final VoucherEarningEntity voucherEarningResult = VoucherEarningEntity.fromBaseResponse(
      BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'user_message': 'fake_user_message',
            'user_message_title': 'fake_user_message_title',
            'action': <String, dynamic>{
              'type': 'open_web_view',
              'args': <String, dynamic>{
                'next_action': <String, dynamic>{
                  'type': 'promotion_scan_to_pay',
                  'args': <String, dynamic>{
                    'id': 'fake_voucher_id',
                  },
                },
              },
            },
          },
        },
      ),
    );

    evoNextActionDialogHandler.showVoucherEarningSuccessDialog(voucherEarningResult);
    verify(
      () => evoImageProvider.asset(
        EvoImages.imgVoucherEarningSuccess,
        width: double.infinity,
        fit: BoxFit.fitWidth,
      ),
    ).called(1);

    final VoidCallback onClickPositive = verify(
      () => EvoDialogHelper().showDialogBottomSheet(
        title: voucherEarningResult.userMessageTitle,
        content: voucherEarningResult.userMessage,
        dialogId: EvoDialogId.voucherEarningSuccessBottomSheet,
        isShowButtonClose: true,
        header: any(named: 'header'),
        buttonListOrientation: ButtonListOrientation.verticalDown,

        /// Positive button
        textPositive: EvoStrings.voucherEarningSuccessCtaText,
        positiveButtonStyle: any(named: 'positiveButtonStyle'),
        onClickPositive: captureAny(named: 'onClickPositive'),
      ),
    ).captured.single;

    onClickPositive.call();
    verifyGotoVoucherDetailScreen(voucherEarningResult);
  });

  test('Test showVoucherEarningErrorDialog', () {
    evoNextActionDialogHandler.showVoucherEarningErrorDialog();
    verify(
      () => evoImageProvider.asset(
        EvoImages.imgReSelectPromotion,
        width: double.infinity,
        fit: BoxFit.fitWidth,
      ),
    ).called(1);

    final VoidCallback onClickPositive = verify(
      () => EvoDialogHelper().showDialogBottomSheet(
        title: EvoStrings.voucherEarningErrorTitle,
        dialogId: EvoDialogId.voucherEarningErrorBottomSheet,
        isShowButtonClose: true,
        header: any(named: 'header'),
        buttonListOrientation: ButtonListOrientation.verticalDown,

        /// Positive button
        textPositive: EvoStrings.moveToHome,
        positiveButtonStyle: any(named: 'positiveButtonStyle'),
        onClickPositive: captureAny(named: 'onClickPositive'),
      ),
    ).captured.single;

    onClickPositive.call();
    verifyGotoMainScreen();
  });

  test('Test showVoucherEarningFailedDialog', () {
    final ErrorUIModel errorUIModel = ErrorUIModel(
      userMessage: 'fake_user_message',
      userMessageTitle: 'fake_user_message_title',
    );
    evoNextActionDialogHandler.showVoucherEarningFailedDialog(errorUIModel);
    verify(
      () => evoImageProvider.asset(
        EvoImages.imgEkycError,
        width: double.infinity,
        fit: BoxFit.fitWidth,
      ),
    ).called(1);

    final VoidCallback onClickPositive = verify(
      () => EvoDialogHelper().showDialogBottomSheet(
        title: errorUIModel.userMessageTitle,
        content: errorUIModel.userMessage,
        dialogId: EvoDialogId.voucherEarningFailedBottomSheet,
        isShowButtonClose: true,
        header: any(named: 'header'),
        buttonListOrientation: ButtonListOrientation.verticalDown,

        /// Positive button
        textPositive: EvoStrings.moveToHome,
        positiveButtonStyle: any(named: 'positiveButtonStyle'),
        onClickPositive: captureAny(named: 'onClickPositive'),
      ),
    ).captured.single;

    onClickPositive.call();
    verifyGotoMainScreen();
  });
}
