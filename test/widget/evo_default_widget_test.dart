import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/widget/evo_default_widget.dart';
import 'package:evoapp/widget/evo_error_web_view_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_common_package/widget/default_widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    final CommonImageProvider mockCommonImageProvider = getIt.get<CommonImageProvider>();
    when(() => mockCommonImageProvider.asset(any())).thenAnswer((_) => Container());
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('EvoDefaultWidget', () {
    test('should be type of CommonDefaultWidgets', () {
      expect(EvoDefaultWidget(), isA<CommonDefaultWidgets>());
    });

    testWidgets('should render EvoErrorWebViewWidget with onReload callback',
        (WidgetTester tester) async {
      bool isReloaded = false;
      await tester.pumpWidget(
        MaterialApp(
          home: EvoDefaultWidget().noInternetUI(
            'No internet connection',
            () => isReloaded = true,
          ),
        ),
      );

      final Finder webViewFinder = find.byType(EvoErrorWebViewWidget);
      expect(webViewFinder, findsOneWidget);

      final EvoErrorWebViewWidget webViewWidget =
          tester.widget<EvoErrorWebViewWidget>(webViewFinder);
      webViewWidget.onReloadWebView?.call();
      expect(isReloaded, isTrue);
    });

    testWidgets('should render EvoErrorWebViewWidget without onReload callback',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: EvoDefaultWidget().noInternetUI(''),
        ),
      );

      final Finder webViewFinder = find.byType(EvoErrorWebViewWidget);
      expect(webViewFinder, findsOneWidget);

      final EvoErrorWebViewWidget webViewWidget =
          tester.widget<EvoErrorWebViewWidget>(webViewFinder);
      expect(webViewWidget.onReloadWebView, isNull);
    });
  });
}
