import 'package:evoapp/widget/evo_overlay/evo_overlay.dart';
import 'package:evoapp/widget/evo_overlay/evo_overlay_widget.dart';
import 'package:evoapp/widget/evo_overlay/widgets/evo_overlay_container.dart';
import 'package:evoapp/widget/evo_overlay/widgets/evo_overlay_entry.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockEvoOverlayEntry extends Mock implements EvoOverlayEntry {}

class TestEvoOverlay extends EvoOverlay {}

class MockBuildContext extends Mock implements BuildContext {}

void main() {
  late TestEvoOverlay evoOverlay;
  late MockEvoOverlayEntry mockEvoOverlayEntry;

  setUp(() {
    evoOverlay = TestEvoOverlay();
    mockEvoOverlayEntry = MockEvoOverlayEntry();
    evoOverlay.overlayEntry = mockEvoOverlayEntry;
  });

  test('init returns a TransitionBuilder', () {
    final TransitionBuilder builder = evoOverlay.init();
    expect(builder, isA<TransitionBuilder>());
  });

  test('init with builder wraps child with EvoOverlayWidget', () {
    final Widget child = Container();
    BuildContext fakeContext = MockBuildContext();

    final TransitionBuilder builder = evoOverlay.init(
      builder: (BuildContext context, Widget? child) {
        fakeContext = context;
        return Container(child: child);
      },
    );

    final Widget result = builder(
      fakeContext,
      child,
    );

    expect(result, isA<Container>());
    final Container container = result as Container;
    expect(container.child, isA<EvoOverlayWidget>());
    final EvoOverlayWidget overlayWidget = container.child as EvoOverlayWidget;
    expect(overlayWidget.overlayChild, child);
  });

  test('show sets _container and calls _markNeedsBuild', () async {
    final Widget overlayWidget = Container();
    await evoOverlay.show(overlayWidget: overlayWidget);

    expect(evoOverlay.containerOverlay, isA<EvoOverlayContainer>());
    verify(() => mockEvoOverlayEntry.markNeedsBuild()).called(1);
  });

  test('dismiss resets _container and _key and calls _markNeedsBuild', () async {
    await evoOverlay.dismiss();

    expect(evoOverlay.containerOverlay, isNull);
    expect(evoOverlay.keyOverlay, isNull);
    verify(() => mockEvoOverlayEntry.markNeedsBuild()).called(1);
  });

  test('show calls handleLogOnShowEvent if not duplicated', () async {
    bool eventCalled = false;
    await evoOverlay.show(handleLogOnShowEvent: () {
      eventCalled = true;
    });

    expect(eventCalled, isTrue);
  });

  test('show does not call handleLogOnShowEvent if duplicated', () async {
    evoOverlay.keyOverlay = GlobalKey<EvoOverlayContainerState>();
    bool eventCalled = false;
    await evoOverlay.show(handleLogOnShowEvent: () {
      eventCalled = true;
    });

    expect(eventCalled, isFalse);
  });
}
