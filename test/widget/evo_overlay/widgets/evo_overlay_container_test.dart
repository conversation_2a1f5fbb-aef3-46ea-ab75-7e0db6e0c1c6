import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/widget/evo_overlay/widgets/evo_overlay_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeText = 'fakeText';
  const Color fakeColor = Colors.black;
  const AlignmentGeometry fakeAlignment = Alignment.center;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerSingleton<GlobalKeyProvider>(GlobalKeyProvider());
  });

  tearDownAll(() {
    getIt.reset();
  });

  testWidgets('test EvoOverlayContainer widget', (WidgetTester tester) async {
    await tester.pumpWidget(MaterialApp(
      navigatorKey: getIt.get<GlobalKeyProvider>().navigator<PERSON>ey,
      scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
      home: Scaffold(
        body: Builder(
          builder: (BuildContext context) {
            return const EvoOverlayContainer(
              overlayChild: Text(fakeText),
              maskColor: fakeColor,
              alignment: fakeAlignment,
            );
          },
        ),
      ),
    ));

    final Finder overlayChildFinder = find.text(fakeText);
    expect(overlayChildFinder, findsOneWidget);
  });
}
