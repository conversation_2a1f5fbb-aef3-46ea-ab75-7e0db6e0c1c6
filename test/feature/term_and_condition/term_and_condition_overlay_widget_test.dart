import 'package:evoapp/feature/logging/evo_logging_event.dart';
import 'package:evoapp/feature/term_and_condition/term_and_condition_overlay_widget.dart';
import 'package:evoapp/feature/term_and_condition/widget/term_and_condition_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/widget/evo_overlay/evo_overlay_util_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockEvoTextStyles extends Mock implements EvoTextStyles {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockNetworkManager extends Mock implements NetworkManager {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

class MockEvoOverlayUtilFunctions extends Mock implements EvoOverlayUtilFunctions {}

void main() {
  final LoggingRepo mockLoggingRepo = MockLoggingRepo();

  late CommonImageProvider evoImageProvider;
  late EvoLocalStorageHelper mockEvoLocalStorageHelper;
  late NetworkManager mockNetworkManager;
  late EvoSnackBar mockEvoSnackBar;
  late EvoOverlayUtilFunctions mockEvoOverlayUtilFunctions;

  setUpAll(() {
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterColor();
    getItRegisterButtonStyle();
    registerFallbackValue(EventType.userAction);
    registerFallbackValue(EvoEventType.consentAgreed);
    registerFallbackValue(SnackBarType.error);

    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());

    getIt.registerSingleton<NetworkManager>(MockNetworkManager());
    mockNetworkManager = getIt.get<NetworkManager>();

    getIt.registerSingleton<EvoSnackBar>(MockEvoSnackBar());
    mockEvoSnackBar = getIt.get<EvoSnackBar>();

    getIt.registerSingleton<EvoOverlayUtilFunctions>(MockEvoOverlayUtilFunctions());
    mockEvoOverlayUtilFunctions = getIt.get<EvoOverlayUtilFunctions>();

    getIt.registerSingleton<EvoLocalStorageHelper>(MockEvoLocalStorageHelper());
    mockEvoLocalStorageHelper = getIt.get<EvoLocalStorageHelper>();

    getIt.registerLazySingleton<LoggingRepo>(() => mockLoggingRepo);

    getIt.registerSingleton<GlobalKeyProvider>(GlobalKeyProvider());

    when(() => mockLoggingRepo.logEvent(
          eventType: any(named: 'eventType'),
          data: any(named: 'data'),
        )).thenAnswer((_) => Future<void>.value());

    when(() => mockEvoLocalStorageHelper.setConsentAgreed(any()))
        .thenAnswer((_) => Future<void>.value());
  });

  setUp(() {
    evoImageProvider = getIt.get<CommonImageProvider>();
    when(() => evoImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());

    when(() => mockNetworkManager.hasInternet).thenReturn(true);
    when(() => mockEvoSnackBar.show(
          any(),
          typeSnackBar: any(named: 'typeSnackBar'),
          durationInSec: any(named: 'durationInSec'),
        )).thenAnswer((_) => Future<bool?>.value());

    when(() => mockEvoOverlayUtilFunctions.hideTermAndConditionOverlay())
        .thenAnswer((_) => Future<void>.value());
  });

  tearDown(() {
    reset(evoImageProvider);
    reset(mockEvoSnackBar);
    reset(mockNetworkManager);
  });

  tearDownAll(() {
    getIt.reset();
  });

  void verifyTermCondition() {
    final Finder termAndConditionWidget = find.byType(TermAndConditionWidget);
    expect(termAndConditionWidget, findsOneWidget);
  }

  Future<void> verifyCTAButton(WidgetTester tester, {bool hasInternet = true}) async {
    final Finder ctaButtonFinder = find.text(EvoStrings.termAndConditionPopupCTA);
    expect(ctaButtonFinder, findsOneWidget);

    await tester.tap(ctaButtonFinder);
    await tester.pumpAndSettle();

    verify(() => mockNetworkManager.hasInternet).called(1);

    if (hasInternet) {
      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.consentAgreed,
            data: any(named: 'data'),
          )).called(1);

      verify(() => mockEvoLocalStorageHelper.setConsentAgreed(true)).called(1);
    } else {
      verify(() => mockEvoSnackBar.show(
            CommonStrings.genericNoInternetErrorMessage,
            typeSnackBar: SnackBarType.error,
            durationInSec: SnackBarDuration.short.value,
          )).called(1);

      verifyNever(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.consentAgreed,
            data: any(named: 'data'),
          ));

      verifyNever(() => mockEvoLocalStorageHelper.setConsentAgreed(true));
    }
  }

  void verifyTitle() {
    final Finder titleFinder = find.text(EvoStrings.termAndConditionPopupTitle);
    expect(titleFinder, findsOneWidget);
  }

  group('Test TermAndConditionOverlayWidget', () {
    testWidgets('Test TermAndConditionOverlayWidget with internet', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return const TermAndConditionOverlayWidget();
            },
          ),
        ),
      ));

      verifyTitle();
      verifyTermCondition();
      verifyCTAButton(tester);
    });

    testWidgets('Test TermAndConditionOverlayWidget and click CTA without internet',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return const TermAndConditionOverlayWidget();
            },
          ),
        ),
      ));

      verifyTitle();
      verifyTermCondition();

      when(() => mockNetworkManager.hasInternet).thenReturn(false);

      verifyCTAButton(tester, hasInternet: false);
    });
  });
}
