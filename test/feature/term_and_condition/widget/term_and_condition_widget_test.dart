import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/term_and_condition/widget/term_and_condition_checkbox_widget.dart';
import 'package:evoapp/feature/term_and_condition/widget/term_and_condition_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/button_styles.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/evo_checkbox_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockEvoImageProvider extends Mock implements CommonImageProvider {}

void main() {
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<CommonButtonStyles>(() => EvoButtonStyles());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<EvoColors>(() => EvoColors());
    getIt.registerLazySingleton<EvoTextStylesV2>(() => EvoTextStylesV2());
    getIt.registerLazySingleton<EvoColorsV2>(() => EvoColorsV2());
    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());
    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    mockCommonImageProvider = getIt.get<CommonImageProvider>();
  });

  setUp(() {
    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          fit: any(named: 'fit'),
        )).thenReturn(const SizedBox());
  });

  tearDown(() {
    reset(mockCommonImageProvider);
  });

  testWidgets('TermAndConditionWidget displays correctly', (WidgetTester tester) async {
    const Key key = Key('term_and_condition_widget');

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: TermAndConditionWidget(
            key: key,
            consentValue: true,
            onTap: () {},
          ),
        ),
      ),
    );

    if(getIt.get<FeatureToggle>().enableRevampUiFeature) {
      expect(find.byType(EvoCheckboxWidget), findsOneWidget);
    } else {
      expect(find.byKey(key), findsOneWidget);
      expect(find.byType(Row), findsOneWidget);
      expect(find.byType(TermAndConditionCheckBoxWidget), findsOneWidget);
      expect(find.byType(Expanded), findsOneWidget);
      expect(find.byType(RichText), findsOneWidget);
    }
  });

  testWidgets('TermAndConditionWidget onTap callback works', (WidgetTester tester) async {
    const Key key = Key('term_and_condition_widget');

    bool tapped = false;

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: TermAndConditionWidget(
            key: key,
            consentValue: true,
            onTap: () {
              tapped = true;
            },
          ),
        ),
      ),
    );
    if(getIt.get<FeatureToggle>().enableRevampUiFeature) {
      await tester.tap(find.byType(EvoCheckboxWidget));
    } else {
      await tester.tap(find.byType(TermAndConditionCheckBoxWidget));
    }
    await tester.pumpAndSettle();

    expect(tapped, true);
  });
}
