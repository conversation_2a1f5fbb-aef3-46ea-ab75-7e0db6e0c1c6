import 'package:evoapp/feature/ekyc/model/face_otp_payload.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FaceOtpPayload', () {
    test('should initialize with correct values', () {
      const String sessionToken = 'test_session_token';
      final BaseEntity entity = BaseEntity();
      final FaceOtpPayload faceOtpPayload = FaceOtpPayload(
        sessionToken: sessionToken,
        entity: entity,
      );

      expect(faceOtpPayload.sessionToken, sessionToken);
      expect(faceOtpPayload.entity, entity);
    });

    test('toString should return correct string representation', () {
      const String sessionToken = 'test_session_token';
      final BaseEntity entity = BaseEntity();
      final FaceOtpPayload faceOtpPayload = FaceOtpPayload(
        sessionToken: sessionToken,
        entity: entity,
      );

      expect(
        faceOtpPayload.toString(),
        'FaceOtpPayload{linkCardSession: $sessionToken, entity: $entity}',
      );
    });
  });
}
