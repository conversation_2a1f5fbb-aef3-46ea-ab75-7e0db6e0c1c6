import 'package:evoapp/feature/ekyc/face_otp/instruction/widgets/face_otp_instruction_content_widget.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../base/evo_page_state_base_test_config.dart';
import '../../../../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider commonImageProvider;
  const double expectedFaceOtpGuideImageHeightPercent = 188 / 812;
  const String verifyFaceOtpTitle = 'Bạn cần xác thực khuôn mặt';
  const String verifyFaceOtpDescription =
      '<PERSON><PERSON><PERSON> thực danh tính của bạn khi đăng nhập và sử dụng ứng dụng';
  const String noticeFaceOtpTitle = 'Các lưu ý khi chụp';
  const String instructFaceOTPInFrame = 'Luôn giữ đầu trong khung hình';
  const String instructFaceOTPNoGlasses = 'Không đeo kính râm, nón hoặc các phụ kiện che mặt';
  const String instructFaceOTPCleanEnvironment = 'Môi trường chụp không quá tối hoặc chói sáng';
  const String startFaceOTP = 'Bắt đầu chụp';

  Widget createTestWidget({VoidCallback? onPressStart}) {
    return MaterialApp(
      home: Scaffold(
        body: FaceOtpInstructionContentWidget(
          onPressStart: onPressStart,
          description: verifyFaceOtpDescription,
        ),
      ),
    );
  }

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(MockBuildContext());
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    commonImageProvider = getIt.get<CommonImageProvider>();

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());
  });

  tearDownAll(() {
    getItUnregisterColor();
    getItUnRegisterTextStyle();
    getItUnRegisterButtonStyle();
    getItUnRegisterMockCommonUtilFunctionAndImageProvider();
  });

  setUp(() {
    setUtilsMockInstanceForTesting();
    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(expectedFaceOtpGuideImageHeightPercent);
  });

  //reset the evoUtilFunction before each test to make sure the tests run independently
  tearDown(() {
    resetUtilMockToOriginalInstance();
    reset(evoUtilFunction);
  });

  group('FaceOtpInstructionSignInWidget', () {
    testWidgets('displays all expected texts correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // verify that all required texts present
      expect(find.text(verifyFaceOtpTitle), findsOneWidget);
      expect(find.text(verifyFaceOtpDescription), findsOneWidget);
      expect(find.text(noticeFaceOtpTitle), findsOneWidget);
      expect(find.text(instructFaceOTPInFrame), findsOneWidget);
      expect(find.text(instructFaceOTPNoGlasses), findsOneWidget);
      expect(find.text(instructFaceOTPCleanEnvironment), findsOneWidget);
      expect(find.text(startFaceOTP), findsOneWidget);
    });

    testWidgets('Start Face OTP button triggers callback when tapped', (WidgetTester tester) async {
      bool isButtonPressed = false;
      await tester.pumpWidget(createTestWidget(onPressStart: () {
        isButtonPressed = true;
      }));

      //find and tap the button
      final Finder startButton = find.text(startFaceOTP);
      expect(startButton, findsOneWidget);
      await tester.tap(startButton);
      await tester.pump();

      //verify that button pressed
      expect(isButtonPressed, isTrue);
    });

    testWidgets('evoUtilFunction.calculateVerticalSpace is called with exact value',
        (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      verify(() => EvoUiUtils().calculateVerticalSpace(
            context: any(named: 'context'),
            heightPercentage: expectedFaceOtpGuideImageHeightPercent,
          )).called(1);
    });
  });
}
