import 'package:evoapp/feature/ekyc/face_otp/mock_file/mock_face_otp_file_name.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('getMockFaceOtpFileName', () {
    test('should return correct filename for faceOTPMatchingForLinkCard', () {
      final String result = getMockFaceOtpFileName(MockFaceOtpFileName.faceOTPMatchingForLinkCard);
      expect(result, 'ekyc_face_otp_matching_link_card_value.json');
    });

    test('should return correct filename for faceOTPMatchingForFaceOtp', () {
      final String result = getMockFaceOtpFileName(MockFaceOtpFileName.faceOTPMatchingForSignIn);
      expect(result, 'ekyc_face_otp_matching_sign_in_value.json');
    });
  });
}
