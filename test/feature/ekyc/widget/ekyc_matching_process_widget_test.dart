import 'package:evoapp/feature/ekyc/widgets/ekyc_matching_process_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:evoapp/widget/animation/lottie_animation_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../app_state_test.dart';
import '../../../util/flutter_test_config.dart';

void main() {
  const double expectedHeightPercentage = 0.122;
  const double expectedPaddingTopPercentage = 0.25;
  const double expectedPaddingBottomPercentage = 0.068;
  const double spaceHeight10 = 10;
  const double spaceHeight20 = 20;
  const double spaceHeight30 = 30;

  void initWhenMethod() {
    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: expectedHeightPercentage,
        )).thenReturn(spaceHeight10);

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: expectedPaddingTopPercentage,
        )).thenReturn(spaceHeight20);

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: expectedPaddingBottomPercentage,
        )).thenReturn(spaceHeight30);
  }

  setUpAll(() {
    setUtilsMockInstanceForTesting();
    registerFallbackValue(MockBuildContext());
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    initWhenMethod();
  });

  tearDownAll(() {
    resetUtilMockToOriginalInstance();
    getIt.reset();
  });

  void verifySizeBox(double expectHeight) {
    final Finder sizeBoxFinder = find.byWidgetPredicate((Widget widget) {
      if (widget is SizedBox) {
        return widget.height == expectHeight;
      }
      return false;
    });
    expect(sizeBoxFinder, findsOneWidget);
  }

  testWidgets('EKYCMatchingProcessWidget should show correct UI',
      (WidgetTester widgetTester) async {
    await widgetTester.pumpWidget(
      const MaterialApp(
        home: EKYCMatchingProcessWidget(),
      ),
    );

    // verify percentage value
    final Finder matchingProcessFinder = find.byType(EKYCMatchingProcessWidget);
    expect(matchingProcessFinder, findsOneWidget);

    final EKYCMatchingProcessWidget widget = widgetTester.widget(matchingProcessFinder);
    expect(widget.loadingHeightPercentage, expectedHeightPercentage);
    expect(widget.loadingPaddingTopPercentage, expectedPaddingTopPercentage);
    expect(widget.loadingPaddingBottomPercentage, expectedPaddingBottomPercentage);

    // verify size box top
    verifySizeBox(spaceHeight20);

    // verify LottieAnimationWidget
    final Finder lottieAnimationWidgetFinder = find.byType(LottieAnimationWidget);
    expect(lottieAnimationWidgetFinder, findsOneWidget);

    final LottieAnimationWidget lottieAnimationWidget =
        widgetTester.widget(lottieAnimationWidgetFinder);
    expect(lottieAnimationWidget.animationFile, EvoAnimation.animationHubLoading);
    expect(lottieAnimationWidget.height, spaceHeight10);
    expect(lottieAnimationWidget.width, spaceHeight10);

    // verify size box bottom
    verifySizeBox(spaceHeight30);

    // verify text title
    final Finder textTitleFinder = find.text(EvoStrings.faceOtpMatchingProcessing);
    expect(textTitleFinder, findsOneWidget);

    final Text textTitle = widgetTester.widget(textTitleFinder);
    expect(textTitle.style, evoTextStyles.h300());
    expect(textTitle.textAlign, TextAlign.center);
  });
}
