import 'package:evoapp/feature/ekyc/ekyc_flow_callback.dart';
import 'package:evoapp/feature/ekyc/ekyc_flow_failed_reason.dart';
import 'package:evoapp/feature/ekyc/model/ekyc_for_flow_type.dart';
import 'package:evoapp/feature/ekyc/model/face_otp_payload.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EkycFlowCallback', () {
    test('should initialize with correct flow type', () {
      final EkycFlowCallback ekycFlowCallback = EkycFlowCallback(flow: EkycFlowType.linkCard);
      expect(ekycFlowCallback.flow, EkycFlowType.linkCard);
      expect(ekycFlowCallback.onSuccess, null);
      expect(ekycFlowCallback.onFailed, null);
    });

    test('should call onSuccess callback with correct parameters', () {
      bool successCalled = false;
      final EkycFlowCallback ekycFlowCallback = EkycFlowCallback(
        flow: EkycFlowType.linkCard,
        onSuccess: (BuildContext context, EkycFlowPayload? payload) {
          successCalled = true;
        },
      );

      final BuildContext context = Container().createElement();
      ekycFlowCallback.onSuccess?.call(context, FaceOtpPayload(sessionToken: '', entity: null));

      expect(successCalled, true);
    });

    test('should call onFailed callback with correct parameters', () {
      bool failedCalled = false;
      final EkycFlowCallback ekycFlowCallback = EkycFlowCallback(
        flow: EkycFlowType.linkCard,
        onFailed: (BuildContext context, EkycFlowFailedReason reason, String? userMessage) {
          failedCalled = true;
          expect(reason, EkycFlowFailedReason.userCancelled);
          expect(userMessage, 'Test message');
        },
      );

      final BuildContext context = Container().createElement();
      ekycFlowCallback.onFailed?.call(context, EkycFlowFailedReason.userCancelled, 'Test message');

      expect(failedCalled, isTrue);
    });
  });
}
