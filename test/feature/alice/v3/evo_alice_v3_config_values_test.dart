import 'package:evoapp/feature/alice/v3/evo_alice_v3_config_values.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Verify value of alice base url has the right value', () {
    expect(EvoAliceV3ConfigValues.aliceBaseUrlStag,
        'https://alice-ltv-uat-wv-wrp.tsengineering.io/?sdkCode=TPBEVO_MOBILE_SDK_EVOAPP');
    expect(EvoAliceV3ConfigValues.aliceBaseUrlUAT,
        'https://alice-ltv-uat-wv-wrp.tsengineering.io/?sdkCode=TPBEVO_MOBILE_SDK_EVOAPP');
    expect(EvoAliceV3ConfigValues.aliceBaseUrlProd,
        'https://alice-tpbank-wv-wrp.trustingsocial.com/?sdkCode=TPBEVO_MOBILE_SDK_EVOAPP');
  });
  group('verify getAliceV3BaseUrlBasedOnEnvironment', () {
    test('get STAG values', () {
      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );

      final String baseUrl = EvoAliceV3ConfigValues.getAliceV3BaseUrlBasedOnEnvironment();
      expect(baseUrl, EvoAliceV3ConfigValues.aliceBaseUrlStag);
    });

    test('get UAT values', () {
      FlavorConfig(
        flavor: FlavorType.uat.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.uat).getFlavorValue(),
      );

      final String baseUrl = EvoAliceV3ConfigValues.getAliceV3BaseUrlBasedOnEnvironment();
      expect(baseUrl, EvoAliceV3ConfigValues.aliceBaseUrlUAT);
    });

    test('get Prod values', () {
      FlavorConfig(
        flavor: FlavorType.prod.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.prod).getFlavorValue(),
      );

      final String baseUrl = EvoAliceV3ConfigValues.getAliceV3BaseUrlBasedOnEnvironment();
      expect(baseUrl, EvoAliceV3ConfigValues.aliceBaseUrlProd);
    });
  });
}
