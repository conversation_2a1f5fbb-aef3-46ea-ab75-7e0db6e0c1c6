import 'package:evoapp/feature/alice/v2/evo_alice_chatwoot_config_values.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Verify constant values', () {
    expect(EvoAliceChatwootConfigValues.nonEvoUserChatWootNamePrefix, 'EVO App user');
  });

  group('verify getChatwootBaseUrlBasedOnEnvironment', () {
    test('get STAG values', () {
      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );

      final String baseUrl = EvoAliceChatwootConfigValues.getChatwootBaseUrlBasedOnEnvironment();
      expect(baseUrl, EvoAliceChatwootConfigValues.chatwootBaseUrlStag);
    });

    test('get UAT values', () {
      FlavorConfig(
        flavor: FlavorType.uat.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.uat).getFlavorValue(),
      );

      final String baseUrl = EvoAliceChatwootConfigValues.getChatwootBaseUrlBasedOnEnvironment();
      expect(baseUrl, EvoAliceChatwootConfigValues.chatwootBaseUrlUat);
    });

    test('get Prod values', () {
      FlavorConfig(
        flavor: FlavorType.prod.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.prod).getFlavorValue(),
      );

      final String baseUrl = EvoAliceChatwootConfigValues.getChatwootBaseUrlBasedOnEnvironment();
      expect(baseUrl, EvoAliceChatwootConfigValues.chatwootBaseUrlProd);
    });
  });

  group('verify getChatwootWebsiteTokenBasedOnEnvironment', () {
    test('get STAG values', () {
      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );

      final String baseUrl =
          EvoAliceChatwootConfigValues.getChatwootWebsiteTokenBasedOnEnvironment();
      expect(baseUrl, EvoAliceChatwootConfigValues.chatwootWebsiteTokenStag);
    });

    test('get UAT values', () {
      FlavorConfig(
        flavor: FlavorType.uat.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.uat).getFlavorValue(),
      );

      final String baseUrl =
          EvoAliceChatwootConfigValues.getChatwootWebsiteTokenBasedOnEnvironment();
      expect(baseUrl, EvoAliceChatwootConfigValues.chatwootWebsiteTokenUat);
    });

    test('get Prod values', () {
      FlavorConfig(
        flavor: FlavorType.prod.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.prod).getFlavorValue(),
      );

      final String baseUrl =
          EvoAliceChatwootConfigValues.getChatwootWebsiteTokenBasedOnEnvironment();
      expect(baseUrl, EvoAliceChatwootConfigValues.chatwootWebsiteTokenProd);
    });
  });
}
