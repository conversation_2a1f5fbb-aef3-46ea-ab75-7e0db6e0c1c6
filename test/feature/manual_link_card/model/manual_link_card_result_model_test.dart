import 'package:evoapp/data/response/link_card_submission_status_entity.dart';
import 'package:evoapp/feature/manual_link_card/model/manual_link_card_result_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ManualLinkCardResultModel', () {
    test('getTypeResultStatus should return correct ManualLinkCardResultType', () {
      expect(ManualLinkCardResultModel(status: LinkCardSubmissionStatusEntity.statusSucceeded, verdict: '').getTypeResultStatus(), ManualLinkCardResultType.succeeded);
      expect(ManualLinkCardResultModel(status: LinkCardSubmissionStatusEntity.statusProcessing, verdict: '').getTypeResultStatus(), ManualLinkCardResultType.processing);
      expect(ManualLinkCardResultModel(status: LinkCardSubmissionStatusEntity.statusFailed, verdict: '').getTypeResultStatus(), ManualLinkCardResultType.failed);
      expect(ManualLinkCardResultModel(status: 'unknown_status', verdict: '').getTypeResultStatus(), ManualLinkCardResultType.unknown);
    });

    group('getVerdictForCTAButton', () {
      test('should return goToHome for specific verdicts', () {
        final List<String> goToHomeVerdicts = <String>[
          LinkCardSubmissionStatusEntity.verdictDuplicatedLinkRequest,
          LinkCardSubmissionStatusEntity.verdictLinkCardInvalidParameters,
          LinkCardSubmissionStatusEntity.verdictLinkCardLinkRequestNotExists,
          LinkCardSubmissionStatusEntity.verdictLinkCardInvalidBankCode,
          LinkCardSubmissionStatusEntity.verdictLinkCardBankProductNotSupported,
          LinkCardSubmissionStatusEntity.verdictLinkCardInvalidIDNumber,
          LinkCardSubmissionStatusEntity.verdictLinkCardInvalidPhoneNumber,
          LinkCardSubmissionStatusEntity.verdictLinkCardNotFoundLinkInfo,
          LinkCardSubmissionStatusEntity.verdictLinkCardNotExists,
          LinkCardSubmissionStatusEntity.verdictLinkCardUnsupportedCard,
          LinkCardSubmissionStatusEntity.verdictLinkCardTooManyValidCard,
          LinkCardSubmissionStatusEntity.verdictLinkCardInvalidCardStatus,
          LinkCardSubmissionStatusEntity.verdictLinkCardOnboardingRequestFailed,
          LinkCardSubmissionStatusEntity.verdictLinkCardNotFoundOnboardingRequest,
          LinkCardSubmissionStatusEntity.verdictLinkCardLinkRequestIsProcessing,
          LinkCardSubmissionStatusEntity.verdictLinkCardPermissionDenied,
        ];

        for (final String verdict in goToHomeVerdicts) {
          expect(ManualLinkCardResultModel(status: '', verdict: verdict).getVerdictForCTAButton(), ManualLinkCardResultVerdictCTAType.goToHome);
        }
      });

      test('should return close for verdictLinkCardAlreadyLinked', () {
        expect(ManualLinkCardResultModel(status: '', verdict: LinkCardSubmissionStatusEntity.verdictLinkCardAlreadyLinked).getVerdictForCTAButton(), ManualLinkCardResultVerdictCTAType.close);
      });

      test('should return retry for specific verdicts', () {
        final List<String> retryVerdicts = <String>[
          LinkCardSubmissionStatusEntity.verdictLinkCardFailure,
          LinkCardSubmissionStatusEntity.verdictLinkCardLinkTimeout,
          LinkCardSubmissionStatusEntity.verdictLinkCardRecordNotFound,
          LinkCardSubmissionStatusEntity.verdictLinkCardFailureAll
        ];
        for (final String verdict in retryVerdicts) {
          expect(ManualLinkCardResultModel(status: '', verdict: verdict).getVerdictForCTAButton(), ManualLinkCardResultVerdictCTAType.retry);
        }
      });

      test('should return goToHome for unknown verdicts', () {
        expect(ManualLinkCardResultModel(status: '', verdict: 'unknown_verdict').getVerdictForCTAButton(), ManualLinkCardResultVerdictCTAType.goToHome);
      });
    });
  });
}