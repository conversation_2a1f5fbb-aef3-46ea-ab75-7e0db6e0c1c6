import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/response/linked_card_status_checking_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/manual_link_card/result/manual_link_card_result_cubit.dart';
import 'package:evoapp/feature/manual_link_card/result/manual_link_card_result_state.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../constant.dart';
import '../../util/test_util.dart';

class MockUserRepo extends Mock implements UserRepo {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

void main() {
  late MockUserRepo mockUserRepo;
  late EvoUtilFunction mockEvoUtilFunction;
  late AppState appState;
  late ManualLinkCardResultCubit cubit;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    mockUserRepo = MockUserRepo();

    getIt.registerLazySingleton(() => AppState());
    appState = getIt.get<AppState>();

    getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());
    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() {
    cubit = ManualLinkCardResultCubit(userRepo: mockUserRepo);

    when(() => mockEvoUtilFunction.getFacialVerificationVersion())
        .thenReturn(FacialVerificationVersion.version_3);
  });

  tearDown(() {
    appState.manualLinkCardSharedData.clearTimeToWaitingForNextLinkCard();
    reset(mockUserRepo);
  });

  group('test retryManualLinkCard() function', () {
    blocTest<ManualLinkCardResultCubit, ManualLinkCardResultState>(
      'should call method retryManualLinkCard() success',
      setUp: () {
        when(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async {
          return LinkedCardStatusCheckingEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('check_linked_cards_success.json'),
          ));
        });
      },
      build: () => cubit,
      act: (ManualLinkCardResultCubit cubit) => cubit.retryManualLinkCard(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<ManualLinkCardResultLoadingState>(),
        isA<ManualLinkCardResultLoadedState>()
            .having(
              (ManualLinkCardResultLoadedState state) => state.linkedCardStatusCheckingEntity,
              'verify entity',
              isNotNull,
            )
            .having(
              (ManualLinkCardResultLoadedState state) =>
                  state.linkedCardStatusCheckingEntity.verdict,
              'verify verdict',
              LinkedCardStatusCheckingEntity.verdictSuccess,
            ),
      ],
      verify: (ManualLinkCardResultCubit cubit) {
        verify(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );

    blocTest<ManualLinkCardResultCubit, ManualLinkCardResultState>(
      'should call method retryManualLinkCard() success with verdict unfulfilled_card',
      setUp: () {
        when(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async {
          return LinkedCardStatusCheckingEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('check_linked_cards_unfulfilled.json'),
          ));
        });
      },
      build: () => cubit,
      act: (ManualLinkCardResultCubit cubit) => cubit.retryManualLinkCard(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<ManualLinkCardResultLoadingState>(),
        isA<ManualLinkCardResultLoadedState>()
            .having(
              (ManualLinkCardResultLoadedState state) => state.linkedCardStatusCheckingEntity,
              'verify entity',
              isNotNull,
            )
            .having(
              (ManualLinkCardResultLoadedState state) =>
                  state.linkedCardStatusCheckingEntity.verdict,
              'verify verdict',
              LinkedCardStatusCheckingEntity.verdictUnfulfilledCard,
            ),
      ],
      verify: (ManualLinkCardResultCubit cubit) {
        verify(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );

    blocTest<ManualLinkCardResultCubit, ManualLinkCardResultState>(
      'should call method retryManualLinkCard() success with verdict waiting_for_issuing',
      setUp: () {
        when(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async {
          return LinkedCardStatusCheckingEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('check_linked_cards_waiting_for_issuing.json'),
          ));
        });
      },
      build: () => cubit,
      act: (ManualLinkCardResultCubit cubit) => cubit.retryManualLinkCard(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<ManualLinkCardResultLoadingState>(),
        isA<ManualLinkCardResultLoadedState>()
            .having(
              (ManualLinkCardResultLoadedState state) => state.linkedCardStatusCheckingEntity,
              'verify entity',
              isNotNull,
            )
            .having(
              (ManualLinkCardResultLoadedState state) =>
                  state.linkedCardStatusCheckingEntity.verdict,
              'verify verdict',
              LinkedCardStatusCheckingEntity.verdictWaitingForCardIssuing,
            ),
      ],
      verify: (ManualLinkCardResultCubit cubit) {
        verify(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );

    blocTest<ManualLinkCardResultCubit, ManualLinkCardResultState>(
      'should call method retryManualLinkCard() success with verdict unqualified_card',
      setUp: () {
        when(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async {
          return LinkedCardStatusCheckingEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('check_linked_cards_unqualified.json'),
          ));
        });
      },
      build: () => cubit,
      act: (ManualLinkCardResultCubit cubit) => cubit.retryManualLinkCard(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<ManualLinkCardResultLoadingState>(),
        isA<ManualLinkCardResultLoadedState>()
            .having(
              (ManualLinkCardResultLoadedState state) => state.linkedCardStatusCheckingEntity,
              'verify entity',
              isNotNull,
            )
            .having(
              (ManualLinkCardResultLoadedState state) =>
                  state.linkedCardStatusCheckingEntity.verdict,
              'verify verdict',
              LinkedCardStatusCheckingEntity.verdictUnqualifiedCard,
            ),
      ],
      verify: (ManualLinkCardResultCubit cubit) {
        verify(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );

    blocTest<ManualLinkCardResultCubit, ManualLinkCardResultState>(
      'should call method retryManualLinkCard() success with verdict duplicated_link_request',
      setUp: () {
        when(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async {
          return LinkedCardStatusCheckingEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('check_linked_cards_duplicate_request.json'),
          ));
        });
      },
      build: () => cubit,
      act: (ManualLinkCardResultCubit cubit) => cubit.retryManualLinkCard(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<ManualLinkCardResultLoadingState>(),
        isA<ManualLinkCardResultLoadedState>()
            .having(
              (ManualLinkCardResultLoadedState state) => state.linkedCardStatusCheckingEntity,
              'verify entity',
              isNotNull,
            )
            .having(
              (ManualLinkCardResultLoadedState state) =>
                  state.linkedCardStatusCheckingEntity.verdict,
              'verify verdict',
              LinkedCardStatusCheckingEntity.verdictDuplicatedLinkRequest,
            ),
      ],
      verify: (ManualLinkCardResultCubit cubit) {
        verify(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );

    blocTest<ManualLinkCardResultCubit, ManualLinkCardResultState>(
      'should call method retryManualLinkCard() success with verdict unqualified_user_information',
      setUp: () {
        when(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async {
          return LinkedCardStatusCheckingEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock(
                'check_linked_cards_unqualified_user_information.json'),
          ));
        });
      },
      build: () => cubit,
      act: (ManualLinkCardResultCubit cubit) => cubit.retryManualLinkCard(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<ManualLinkCardResultLoadingState>(),
        isA<ManualLinkCardResultLoadedState>()
            .having(
              (ManualLinkCardResultLoadedState state) => state.linkedCardStatusCheckingEntity,
              'verify entity',
              isNotNull,
            )
            .having(
              (ManualLinkCardResultLoadedState state) =>
                  state.linkedCardStatusCheckingEntity.verdict,
              'verify verdict',
              LinkedCardStatusCheckingEntity.verdictUnqualifiedUserInformation,
            ),
      ],
      verify: (ManualLinkCardResultCubit cubit) {
        verify(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );

    blocTest<ManualLinkCardResultCubit, ManualLinkCardResultState>(
      'should call method retryManualLinkCard() success with verdict unknown',
      setUp: () {
        when(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async {
          return LinkedCardStatusCheckingEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('check_linked_cards_unknown.json'),
          ));
        });
      },
      build: () => cubit,
      act: (ManualLinkCardResultCubit cubit) => cubit.retryManualLinkCard(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<ManualLinkCardResultLoadingState>(),
        isA<ManualLinkCardResultLoadedState>()
            .having(
              (ManualLinkCardResultLoadedState state) => state.linkedCardStatusCheckingEntity,
              'verify entity',
              isNotNull,
            )
            .having(
              (ManualLinkCardResultLoadedState state) =>
                  state.linkedCardStatusCheckingEntity.verdict,
              'verify verdict',
              'unknown_verdict',
            ),
      ],
      verify: (ManualLinkCardResultCubit cubit) {
        verify(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );

    blocTest<ManualLinkCardResultCubit, ManualLinkCardResultState>(
      'should call method retryManualLinkCard() failed',
      setUp: () {
        when(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async {
          return LinkedCardStatusCheckingEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          ));
        });
      },
      build: () => cubit,
      act: (ManualLinkCardResultCubit cubit) => cubit.retryManualLinkCard(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<ManualLinkCardResultLoadingState>(),
        isA<ManualLinkCardResultErrorState>()
            .having(
              (ManualLinkCardResultErrorState state) => state.errorUIModel,
              'verify errorUIModel',
              isNotNull,
            )
            .having(
              (ManualLinkCardResultErrorState state) => state.errorUIModel.statusCode,
              'verify statusCode',
              CommonHttpClient.BAD_REQUEST,
            ),
      ],
      verify: (ManualLinkCardResultCubit cubit) {
        verify(() => mockUserRepo.checkLinkedCardsStatus(mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );
  });
}
