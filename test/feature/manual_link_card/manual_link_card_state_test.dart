import 'package:evoapp/data/response/linked_card_status_checking_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/feature/manual_link_card/manual_link_card_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ManualLinkCardState tests', () {
    test('ManualLinkCardInitState properties', () {
      final ManualLinkCardInitState state = ManualLinkCardInitState();
      expect(state, isA<ManualLinkCardState>());
    });

    test('ManualLinkCardLoadingState properties', () {
      final ManualLinkCardLoadingState state = ManualLinkCardLoadingState();
      expect(state, isA<ManualLinkCardState>());
    });

    test('ManualLinkCardErrorState properties', () {
      final ErrorUIModel errorUIModel = ErrorUIModel();
      final ManualLinkCardErrorState state = ManualLinkCardErrorState(errorUIModel);
      expect(state, isA<ManualLinkCardState>());
      expect(state.errorUIModel, errorUIModel);
    });

    test('PaymentMethodLoadedState properties', () {
      final OrderSessionEntity order = OrderSessionEntity();
      final PaymentMethodLoadedState state = PaymentMethodLoadedState(order: order);
      expect(state, isA<ManualLinkCardState>());
      expect(state.order, order);
    });

    test('PaymentMethodIsEmptyState properties', () {
      final PaymentMethodIsEmptyState state = PaymentMethodIsEmptyState();
      expect(state, isA<ManualLinkCardState>());
    });

    test('LinkedCardStatusCheckingLoadedState properties', () {
      final LinkedCardStatusCheckingEntity linkedCardStatusCheckingEntity =
          LinkedCardStatusCheckingEntity();
      final LinkedCardStatusCheckingLoadedState state =
          LinkedCardStatusCheckingLoadedState(linkedCardStatusCheckingEntity);
      expect(state, isA<ManualLinkCardState>());
      expect(state.linkedCardStatusCheckingEntity, linkedCardStatusCheckingEntity);
    });

    test('LinkedCardStatusCheckingErrorState properties', () {
      final ErrorUIModel errorUIModel = ErrorUIModel();
      final LinkedCardStatusCheckingErrorState state =
          LinkedCardStatusCheckingErrorState(errorUIModel);
      expect(state, isA<ManualLinkCardState>());
      expect(state.errorUIModel, errorUIModel);
    });
  });
}
