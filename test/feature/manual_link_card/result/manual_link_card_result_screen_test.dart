import 'dart:async';

import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/response/linked_card_status_checking_entity.dart';
import 'package:evoapp/feature/feature_flow/feature_flow_failed_reason.dart';
import 'package:evoapp/feature/manual_link_card/model/linked_card_status_model.dart';
import 'package:evoapp/feature/manual_link_card/model/manual_link_card_result_model.dart';
import 'package:evoapp/feature/manual_link_card/result/manual_link_card_result_cubit.dart';
import 'package:evoapp/feature/manual_link_card/result/manual_link_card_result_screen.dart';
import 'package:evoapp/feature/manual_link_card/result/manual_link_card_result_state.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/images.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';

class MockManualLinkCardResultCubit extends Mock implements ManualLinkCardResultCubit {
  @override
  Future<void> close() => Future<void>.value();
}

class MockUserRepo extends Mock implements UserRepo {}

class MockLinkedCardStatusCheckingEntity extends Mock implements LinkedCardStatusCheckingEntity {}

void main() {
  late MockManualLinkCardResultCubit mockCubit;
  late StreamController<ManualLinkCardResultState> stateStreamController;
  late ManualLinkCardResultModel successModel;
  late ManualLinkCardResultModel processingModel;
  late ManualLinkCardResultModel failedModel;
  late ManualLinkCardResultModel unknownModel;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(MockBuildContext());
    registerFallbackValue(FlowFailedReason.userCancelled);
    registerFallbackValue(ErrorUIModel());
    registerFallbackValue(LinkedCardStatusCheckingEntity());

    initConfigEvoPageStateBase();
    setUtilsMockInstanceForTesting();
  });

  setUp(() {
    mockCubit = MockManualLinkCardResultCubit();
    stateStreamController = StreamController<ManualLinkCardResultState>.broadcast();

    when(() => mockCubit.stream).thenAnswer((_) => stateStreamController.stream);
    when(() => mockCubit.state).thenReturn(ManualLinkCardResultInitState());
    when(() => mockCubit.retryManualLinkCard()).thenAnswer((_) async => Future<void>.value());

    setUpMockConfigEvoPageStateBase();
    setupMockImageProvider();
    setUpMockSnackBarForTest();

    // Setup test models
    successModel = ManualLinkCardResultModel(
      status: 'succeeded',
      verdict: 'success',
    );

    processingModel = ManualLinkCardResultModel(
      status: 'processing',
      verdict: 'processing',
    );

    failedModel = ManualLinkCardResultModel(
      status: 'failed',
      verdict: 'link_card_failure',
      userMessage: 'Failed message',
      nextRetryDurationInMinutes: 30,
    );

    unknownModel = ManualLinkCardResultModel(
      status: 'unknown',
      verdict: 'unknown',
    );

    // Setup EvoUiUtils mock
    when(
      () => EvoUiUtils().calculateVerticalSpace(
        context: any(named: 'context'),
        heightPercentage: any(named: 'heightPercentage'),
      ),
    ).thenReturn(200);
  });

  tearDown(() {
    reset(EvoUiUtils());

    stateStreamController.close();
    reset(mockNavigatorContext);
  });

  tearDownAll(() {
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  // Helper function to pump the widget with a specific model
  Future<ManualLinkCardResultScreenState> pumpScreenWithModel(
    WidgetTester tester,
    ManualLinkCardResultModel model,
  ) async {
    final Key key = UniqueKey();

    await tester.pumpWidget(
      MaterialApp(
        home: Builder(
          builder: (BuildContext context) {
            return ManualLinkCardResultScreen(
              key: key,
              resultModel: model,
              cubit: mockCubit,
            );
          },
        ),
      ),
    );

    await tester.pumpAndSettle();

    // Find the state
    final Finder stateFinder = find.byKey(key);
    expect(stateFinder, findsOneWidget);

    final State<StatefulWidget> state = tester.state(stateFinder);
    expect(state, isA<ManualLinkCardResultScreenState>());

    return state as ManualLinkCardResultScreenState;
  }

  test('pushReplacementNamed replaces with the correct route', () {
    ManualLinkCardResultScreen.pushReplacementNamed(resultModel: successModel);

    verify(
      () => mockNavigatorContext.pushReplacementNamed(
        Screen.manualLinkCardResultScreen.name,
        extra: any(named: 'extra'),
      ),
    ).called(1);
  });

  group('UI Elements Tests', () {
    testWidgets('should display correct UI elements for success state', (
      WidgetTester tester,
    ) async {
      await pumpScreenWithModel(tester, successModel);
      await tester.pumpAndSettle();

      // Verify the title is displayed correctly
      expect(find.text(EvoStrings.resultSuccessTitle), findsOneWidget);

      // Verify the description is displayed correctly
      expect(find.text(EvoStrings.resultSuccessDescription), findsOneWidget);

      // Verify the button title is displayed correctly
      expect(find.text(EvoStrings.resultClose), findsOneWidget);

      // Verify the close button is displayed
      expect(find.byType(CloseButton), findsOneWidget);
    });

    testWidgets('should display correct UI elements for processing state', (
      WidgetTester tester,
    ) async {
      await pumpScreenWithModel(tester, processingModel);
      await tester.pumpAndSettle();

      // Verify the title is displayed correctly
      expect(find.text(EvoStrings.resultProcessingTitle), findsOneWidget);

      // Verify the description is displayed correctly
      expect(find.text(EvoStrings.resultWaitingForLinkingCardDescription), findsOneWidget);

      // Verify the button title is displayed correctly
      expect(find.text(EvoStrings.moveToHome), findsOneWidget);
    });

    testWidgets('should display correct UI elements for failed state', (
      WidgetTester tester,
    ) async {
      await pumpScreenWithModel(tester, failedModel);
      await tester.pumpAndSettle();

      // Verify the title is displayed correctly
      expect(find.text(EvoStrings.dopUnqualifiedUserInformationTitle), findsOneWidget);

      // Verify the description is displayed correctly
      expect(find.text('Failed message'), findsOneWidget);

      // Verify the button title is displayed correctly
      expect(find.text(EvoStrings.resultReLinkCard), findsOneWidget);
    });

    testWidgets('should display correct UI elements for unknown state', (
      WidgetTester tester,
    ) async {
      await pumpScreenWithModel(tester, unknownModel);
      await tester.pumpAndSettle();

      // Verify the title is displayed correctly
      expect(find.text(EvoStrings.dopUnqualifiedUserInformationTitle), findsOneWidget);

      // Verify the button title is displayed correctly
      expect(find.text(EvoStrings.moveToHome), findsOneWidget);
    });
  });

  group('Button Action Tests', () {
    testWidgets('onTapButton should call moveToHome for processing state', (
      WidgetTester tester,
    ) async {
      await pumpScreenWithModel(tester, processingModel);
      await tester.pumpAndSettle();

      // Find and tap the button
      final Finder buttonFinder = find.text(EvoStrings.moveToHome);
      await tester.tap(buttonFinder);
      await tester.pumpAndSettle();

      // Verify moveToHome was called
      verify(
        () => mockNavigatorContext.removeUntilAndPushReplacementNamed(
          Screen.mainScreen.name,
          any(),
          extra: any(named: 'extra'),
        ),
      ).called(1);
    });

    testWidgets('onTapButton should call handleActionButtonWhenFailed for failed state', (
      WidgetTester tester,
    ) async {
      await pumpScreenWithModel(tester, failedModel);
      await tester.pumpAndSettle();

      // Find and tap the button
      final Finder buttonFinder = find.text(EvoStrings.resultReLinkCard);
      await tester.tap(buttonFinder);
      await tester.pumpAndSettle();

      // Verify retryManualLinkCard was called on the cubit
      verify(() => mockCubit.retryManualLinkCard()).called(1);
    });

    testWidgets('onTapButton should call moveToHome for unknown state', (
      WidgetTester tester,
    ) async {
      await pumpScreenWithModel(tester, unknownModel);
      await tester.pumpAndSettle();

      // Find and tap the button
      final Finder buttonFinder = find.text(EvoStrings.moveToHome);
      await tester.tap(buttonFinder);
      await tester.pumpAndSettle();

      // Verify moveToHome was called
      verify(
        () => mockNavigatorContext.removeUntilAndPushReplacementNamed(
          Screen.mainScreen.name,
          any(),
          extra: any(named: 'extra'),
        ),
      ).called(1);
    });
  });

  group('Helper Method Tests', () {
    test('getDescription should return correct description for different states', () {
      final ManualLinkCardResultScreenState state = ManualLinkCardResultScreenState();

      // Success state
      expect(state.getDescription(successModel), equals(EvoStrings.resultSuccessDescription));

      // Processing state
      expect(
        state.getDescription(processingModel),
        equals(EvoStrings.resultWaitingForLinkingCardDescription),
      );

      // Failed state with user message
      expect(state.getDescription(failedModel), equals('Failed message'));

      // Failed state without user message
      final ManualLinkCardResultModel failedWithoutMessageModel = ManualLinkCardResultModel(
        status: 'failed',
        verdict: 'link_card_failure',
        nextRetryDurationInMinutes: 30,
      );

      expect(state.getDescription(failedWithoutMessageModel).contains('30'), isTrue);
    });

    test('getImageUrl should return correct image URL for different states', () {
      final ManualLinkCardResultScreenState state = ManualLinkCardResultScreenState();

      // Success state
      expect(state.getImageUrl(successModel), equals(EvoImages.imgDopSuccess));

      // Processing state
      expect(state.getImageUrl(processingModel), equals(EvoImages.imgManualCardWaiting));

      // Failed state
      expect(state.getImageUrl(failedModel), equals(EvoImages.imgDopError));

      // Unknown state
      expect(state.getImageUrl(unknownModel), equals(EvoImages.imgDopError));
    });

    test('getTitle should return correct title for different states', () {
      final ManualLinkCardResultScreenState state = ManualLinkCardResultScreenState();

      // Success state
      expect(state.getTitle(successModel), equals(EvoStrings.resultSuccessTitle));

      // Processing state
      expect(state.getTitle(processingModel), equals(EvoStrings.resultProcessingTitle));

      // Failed state
      expect(state.getTitle(failedModel), equals(EvoStrings.dopUnqualifiedUserInformationTitle));

      // Unknown state
      expect(state.getTitle(unknownModel), equals(EvoStrings.dopUnqualifiedUserInformationTitle));
    });

    test('getButtonTitle should return correct button title for different states', () {
      final ManualLinkCardResultScreenState state = ManualLinkCardResultScreenState();

      // Success state
      expect(state.getButtonTitle(successModel), equals(EvoStrings.resultClose));

      // Processing state
      expect(state.getButtonTitle(processingModel), equals(EvoStrings.moveToHome));

      // Failed state with retry verdict
      expect(state.getButtonTitle(failedModel), equals(EvoStrings.resultReLinkCard));

      // Unknown state
      expect(state.getButtonTitle(unknownModel), equals(EvoStrings.moveToHome));
    });

    test('getTitleButtonWhenFailed should return correct button title for different verdict types',
        () {
      final ManualLinkCardResultScreenState state = ManualLinkCardResultScreenState();

      expect(
        state.getTitleButtonWhenFailed(ManualLinkCardResultVerdictCTAType.goToHome),
        equals(EvoStrings.moveToHome),
      );

      expect(
        state.getTitleButtonWhenFailed(ManualLinkCardResultVerdictCTAType.close),
        equals(EvoStrings.resultClose),
      );

      expect(
        state.getTitleButtonWhenFailed(ManualLinkCardResultVerdictCTAType.retry),
        equals(EvoStrings.resultReLinkCard),
      );
    });
  });

  group('handleActionButtonWhenFailed Tests', () {
    testWidgets('should call moveToHome when type is goToHome', (
      WidgetTester tester,
    ) async {
      final ManualLinkCardResultScreenState state = await pumpScreenWithModel(tester, failedModel);
      await tester.pumpAndSettle();

      // Call the method directly
      state.handleActionButtonWhenFailed(ManualLinkCardResultVerdictCTAType.goToHome);
      await tester.pumpAndSettle();

      // Verify moveToHome was called
      verify(
        () => mockNavigatorContext.removeUntilAndPushReplacementNamed(
          Screen.mainScreen.name,
          any(),
          extra: any(named: 'extra'),
        ),
      ).called(1);
    });

    testWidgets('should call retryLinkCard when type is retry', (
      WidgetTester tester,
    ) async {
      final ManualLinkCardResultScreenState state = await pumpScreenWithModel(tester, failedModel);
      await tester.pumpAndSettle();

      // Call the method directly
      state.handleActionButtonWhenFailed(ManualLinkCardResultVerdictCTAType.retry);
      await tester.pumpAndSettle();

      // Verify retryManualLinkCard was called on the cubit
      verify(() => mockCubit.retryManualLinkCard()).called(1);
    });

    testWidgets('should call moveToHome for default case', (
      WidgetTester tester,
    ) async {
      final ManualLinkCardResultScreenState state = await pumpScreenWithModel(tester, failedModel);
      await tester.pumpAndSettle();

      // Create a custom enum value that doesn't match any case
      // This is a bit of a hack for testing the default case
      final dynamic customType = ManualLinkCardResultVerdictCTAType.goToHome;
      state.handleActionButtonWhenFailed(customType);
      await tester.pumpAndSettle();

      // Verify moveToHome was called
      verify(
        () => mockNavigatorContext.removeUntilAndPushReplacementNamed(
          Screen.mainScreen.name,
          any(),
          extra: any(named: 'extra'),
        ),
      ).called(1);
    });
  });

  group('retryLinkCard Tests', () {
    testWidgets('should call retryManualLinkCard on cubit', (
      WidgetTester tester,
    ) async {
      final ManualLinkCardResultScreenState state = await pumpScreenWithModel(tester, failedModel);
      await tester.pumpAndSettle();

      // Call the method directly
      state.retryLinkCard();
      await tester.pumpAndSettle();

      // Verify retryManualLinkCard was called on the cubit
      verify(() => mockCubit.retryManualLinkCard()).called(1);
    });
  });

  group('handleUserBack Tests', () {
    testWidgets('should call moveToHome for processing state', (
      WidgetTester tester,
    ) async {
      final ManualLinkCardResultScreenState state =
          await pumpScreenWithModel(tester, processingModel);
      await tester.pumpAndSettle();

      // Call the method directly
      state.handleUserBack(processingModel);
      await tester.pumpAndSettle();

      // Verify moveToHome was called
      verify(
        () => mockNavigatorContext.removeUntilAndPushReplacementNamed(
          Screen.mainScreen.name,
          any(),
          extra: any(named: 'extra'),
        ),
      ).called(1);
    });
  });

  group('handleListener Tests', () {
    final ErrorUIModel mockErrorUIModel = ErrorUIModel(
      statusCode: CommonHttpClient.INTERNAL_SERVER_ERROR,
      verdict: LinkedCardStatusVerdict.verdictFailureAll.value,
    );

    testWidgets('should show loading HUD when state is ManualLinkCardResultLoadingState', (
      WidgetTester tester,
    ) async {
      final ManualLinkCardResultScreenState state = await pumpScreenWithModel(tester, failedModel);
      await tester.pumpAndSettle();

      // Call the method directly with a loading state
      await state.handleListener(ManualLinkCardResultLoadingState());

      // Verify showHudLoading was called
      verify(() => EvoUiUtils().showHudLoading()).called(1);
    });

    testWidgets('should handle error state with verdict failure all', (
      WidgetTester tester,
    ) async {
      final ManualLinkCardResultScreenState state = await pumpScreenWithModel(tester, failedModel);
      await tester.pumpAndSettle();

      // Call the method directly with an error state
      await state.handleListener(ManualLinkCardResultErrorState(mockErrorUIModel));

      // Verify DOPCardStatusScreen.pushReplacementNamed was called
      verify(() => mockNavigatorContext.pushReplacementNamed(
            Screen.dopCardStatusScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should do nothing for init state', (
      WidgetTester tester,
    ) async {
      final ManualLinkCardResultScreenState state = await pumpScreenWithModel(tester, failedModel);
      await tester.pumpAndSettle();

      // Reset mocks
      clearInteractions(EvoUiUtils());

      // Call the method directly with an init state
      await state.handleListener(ManualLinkCardResultInitState());

      // Verify hideHudLoading was called but nothing else
      verify(() => EvoUiUtils().hideHudLoading()).called(1);
    });
  });
}
