import 'package:evoapp/feature/deep_link/model/deep_link_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('should store screenName and params correctly', () {
    // Arrange
    const String testScreenName = 'home';
    const Map<String, String> testParams = <String, String>{'id': '123', 'name': '<PERSON>'};

    // Act
    final DeepLinkModel data = DeepLinkModel(
      screenName: testScreenName,
      params: testParams,
      isDeferred: true,
    );

    // Assert
    expect(data.screenName, testScreenName);
    expect(data.params, testParams);
    expect(data.isDeferred, true);
  });

  test('should handle null parameters correctly', () {
    // Act
    final DeepLinkModel data = DeepLinkModel(screenName: null, params: null);

    // Assert
    expect(data.screenName, null);
    expect(data.params, null);
    expect(data.isDeferred, null);
  });
}
