import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/home_screen/user/menu_user/menu_user_body_widget.dart';
import 'package:evoapp/feature/home_screen/user/menu_user/menu_user_item_widget.dart';
import 'package:evoapp/feature/home_screen/user/menu_user/menu_user_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockFeatureToggle extends Mock implements FeatureToggle {}

void main() {
  late FeatureToggle mockFeatureToggle;
  MenuUserType? menuUserType;

  void onTapMenuUser(MenuUserType type) {
    menuUserType = type;
  }

  Future<void> expectWidgetFound({
    required WidgetTester tester,
    required String text,
    required String icon,
    required MenuUserType expectMenuType,
  }) async {
    final Finder finder = find.byWidgetPredicate((Widget widget) {
      return widget is MenuUserItem && widget.title == text && widget.pathIcon == icon;
    });

    expect(finder, findsOneWidget);
    await tester.tap(finder);
    await tester.pump();
    expect(menuUserType, expectMenuType);
  }

  void expectWidgetNotFound({required String text, required String icon}) {
    final Finder finder = find.byWidgetPredicate((Widget widget) {
      return widget is MenuUserItem && widget.title == text && widget.pathIcon == icon;
    });

    expect(finder, findsNothing);
  }

  setUpAll(() {
    getIt.registerLazySingleton<AppState>(() => AppState());

    getIt.registerLazySingleton(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
    getIt.registerLazySingleton<CommonImageProvider>(() => CommonImageProviderImpl());

    getIt.registerLazySingleton<FeatureToggle>(() => MockFeatureToggle());
    mockFeatureToggle = getIt.get<FeatureToggle>();
  });

  tearDown(() {
    menuUserType = null;
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('Test createIconMenuUserScanQr', () {
    testWidgets(
      'enableEmiFeatureVersion is EmiFeatureVersion.version_1',
      (WidgetTester tester) async {
        when(() => mockFeatureToggle.enableEmiFeatureVersion).thenReturn(
          EmiFeatureVersion.version_1,
        );

        await tester.pumpWidget(MaterialApp(home: MenuUserBody(onTapMenuUser: onTapMenuUser)));
        await expectWidgetFound(
          tester: tester,
          text: EvoStrings.menuUserGuideCardUsage,
          icon: EvoImages.icGuideSearch,
          expectMenuType: MenuUserType.cardUsageGuide,
        );
        await expectWidgetFound(
          tester: tester,
          text: EvoStrings.menuUserReward,
          icon: EvoImages.icGift,
          expectMenuType: MenuUserType.reward,
        );
        await expectWidgetFound(
          tester: tester,
          text: EvoStrings.menuUserEmiPayment,
          icon: EvoImages.icMenuUserEmiPayment,
          expectMenuType: MenuUserType.scanQr,
        );
        await expectWidgetFound(
          tester: tester,
          text: EvoStrings.menuUserQuestion,
          icon: EvoImages.icFAQ,
          expectMenuType: MenuUserType.question,
        );

        expectWidgetNotFound(text: EvoStrings.menuUserScanner, icon: EvoImages.icScanner);
      },
    );

    testWidgets(
      'enableEmiFeatureVersion is EmiFeatureVersion.not_supported',
      (WidgetTester tester) async {
        when(() => mockFeatureToggle.enableEmiFeatureVersion).thenReturn(
          EmiFeatureVersion.notSupported,
        );

        await tester.pumpWidget(MaterialApp(home: MenuUserBody(onTapMenuUser: onTapMenuUser)));
        await expectWidgetFound(
          tester: tester,
          text: EvoStrings.menuUserGuideCardUsage,
          icon: EvoImages.icGuideSearch,
          expectMenuType: MenuUserType.cardUsageGuide,
        );
        await expectWidgetFound(
          tester: tester,
          text: EvoStrings.menuUserReward,
          icon: EvoImages.icGift,
          expectMenuType: MenuUserType.reward,
        );
        await expectWidgetFound(
          tester: tester,
          text: EvoStrings.menuUserScanner,
          icon: EvoImages.icScanner,
          expectMenuType: MenuUserType.scanQr,
        );
        await expectWidgetFound(
          tester: tester,
          text: EvoStrings.menuUserQuestion,
          icon: EvoImages.icFAQ,
          expectMenuType: MenuUserType.question,
        );

        expectWidgetNotFound(
            text: EvoStrings.menuUserEmiPayment, icon: EvoImages.icMenuUserEmiPayment);
      },
    );
  });
}
