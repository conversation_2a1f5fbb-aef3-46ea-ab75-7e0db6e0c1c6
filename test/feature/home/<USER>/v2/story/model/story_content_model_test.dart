import 'package:evoapp/data/response/action_entity.dart';
import 'package:evoapp/feature/home_screen/non_user/v2/story/model/story_content_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String expectedTitle = 'fake_title';
  const String expectedDescription = 'fake_description';
  const String expectedImage = 'fake_image';
  group('verify StoryContentModel', () {
    test('Initialization', () {
      final StoryContentModel storyContent = StoryContentModel(
        title: expectedTitle,
        description: expectedDescription,
        image: expectedImage,
      );

      expect(storyContent.title, expectedTitle);
      expect(storyContent.description, expectedDescription);
      expect(storyContent.image, expectedImage);
      expect(storyContent.navigation, isNull);
      expect(storyContent.footer, isNull);
    });

    test('Initialization with Action', () {
      const String navigationType = 'fake_navigation_type';
      const String footerType = 'fake_footer_type';
      final ActionEntity navigation = ActionEntity(
        type: navigationType,
      );
      final ActionEntity footer = ActionEntity(
        type: footerType,
      );
      final StoryContentModel storyContent = StoryContentModel(
        title: expectedTitle,
        description: expectedDescription,
        image: expectedImage,
        navigation: navigation,
        footer: footer,
      );

      expect(storyContent.title, expectedTitle);
      expect(storyContent.description, expectedDescription);
      expect(storyContent.image, expectedImage);
      expect(storyContent.navigation?.type, navigation.type);
      expect(storyContent.footer?.type, footer.type);
    });
  });
}
