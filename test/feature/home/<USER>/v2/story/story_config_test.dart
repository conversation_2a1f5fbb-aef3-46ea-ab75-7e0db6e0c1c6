import 'package:evoapp/feature/home_screen/non_user/v2/story/story_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('verify StoryConfig', () {
    test('verify constants correctly', () {
      expect(StoryConfig.durationInMs, 10000);
      expect(StoryConfig.progressBarHeight, 2);
      expect(StoryConfig.progressBarPadding, 4);
      expect(StoryConfig.aspectRatioStoryImage, 375 / 700);
    });
  });
}
