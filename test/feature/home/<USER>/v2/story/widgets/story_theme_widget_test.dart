import 'package:evoapp/data/response/action_entity.dart' as action_entity;
import 'package:evoapp/data/response/action_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/home_screen/non_user/v2/story/model/story_content_model.dart';
import 'package:evoapp/feature/home_screen/non_user/v2/story/story_config.dart';
import 'package:evoapp/feature/home_screen/non_user/v2/story/widgets/story_theme_widget.dart';
import 'package:evoapp/feature/home_screen/non_user/v2/story/widgets/story_web_view.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:evoapp/model/evo_action_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_action_handler.dart';
import 'package:evoapp/util/web_link_utils.dart';
import 'package:evoapp/widget/evo_image_provider_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/ui_model/action_model.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:html/dom.dart' as dom;
import 'package:mocktail/mocktail.dart';

import '../../../../../../util/flutter_test_config.dart';

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockFeatureToggle extends Mock implements FeatureToggle {}

void main() {
  const String expectedTitle = 'title';
  const String expectedDescription = 'description';
  const String expectedImage = 'image';
  final BuildContext mockNavigatorContext = MockBuildContext();
  final CommonNavigator commonNavigator = MockCommonNavigator();
  late FeatureToggle mockFeatureToggle;

  late CommonImageProvider commonImageProvider;

  setUpAll(() {
    registerFallbackValue(BoxFit.fill);
    registerFallbackValue(EvoActionModel());
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    setUpOneLinkDeepLinkRegExForTest();

    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();

    getIt.registerSingleton<WebLinkUtils>(WebLinkUtils());
    getIt.registerSingleton<CommonNavigator>(commonNavigator);
    setUpMockGlobalKeyProvider(mockNavigatorContext);

    getIt.registerSingleton<FeatureToggle>(MockFeatureToggle());
    mockFeatureToggle = getIt.get<FeatureToggle>();

    commonImageProvider = getIt.get<CommonImageProvider>();

    setUtilsMockInstanceForTesting();

    when(() => commonImageProvider.asset(
          any(),
          fit: any(named: 'fit'),
          width: any(named: 'width'),
          height: any(named: 'height'),
        )).thenReturn(Container());

    when(() => commonImageProvider.network(
          any(),
          fit: any(named: 'fit'),
          placeholder: any(named: 'placeholder'),
          errorWidget: any(named: 'errorWidget'),
          onLoadError: any(named: 'onLoadError'),
        )).thenReturn(Container());

    when(() => EvoActionHandler().openAuthenticationScreen())
        .thenAnswer((_) async => Future<void>.value());

    when(() => mockFeatureToggle.enableDOPNativeFeature).thenReturn(false);

    FlavorConfig(
      flavor: FlavorType.stag.name,
      values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
    );
  });

  tearDownAll(() {
    resetUtilMockToOriginalInstance();
    getIt.reset();
  });

  void verifySizedBox(int height) {
    final Finder sizedBoxFinder = find.byWidgetPredicate((Widget widget) {
      return widget is SizedBox && widget.height == height;
    });
    expect(sizedBoxFinder, findsOneWidget);
  }

  void verifyEvoNetworkImageProviderWidget(WidgetTester widgetTester) {
    final Finder networkImageFinder = find.byType(EvoNetworkImageProviderWidget);
    expect(networkImageFinder, findsOneWidget);

    final EvoNetworkImageProviderWidget networkImageProviderWidget =
        widgetTester.widget(networkImageFinder);
    expect(networkImageProviderWidget.imageUrl, expectedImage);
    expect(networkImageProviderWidget.fit, BoxFit.fitWidth);

    verify(() => commonImageProvider.network(
          expectedImage,
          fit: BoxFit.fitWidth,
          placeholder: any(named: 'placeholder'),
          errorWidget: any(named: 'errorWidget'),
          onLoadError: any(named: 'onLoadError'),
        )).called(1);

    verify(() => commonImageProvider.asset(
          EvoImages.bgNonUserLoading,
          fit: BoxFit.fill,
        )).called(2);
  }

  void verifyAspectRatio(WidgetTester widgetTester) {
    //verify AspectRatio
    final Finder aspectRatioFinder = find.byType(AspectRatio);
    expect(aspectRatioFinder, findsOneWidget);

    final AspectRatio aspectRatio = widgetTester.widget(aspectRatioFinder);
    expect(aspectRatio.aspectRatio, StoryConfig.aspectRatioStoryImage);
  }

  void verifyItemTitle(WidgetTester widgetTester, {required String title}) {
    final Finder itemTitleFinder = find.byWidgetPredicate(
      (Widget widget) => widget is HtmlWidget && widget.html == title,
    );
    expect(itemTitleFinder, findsOneWidget);

    final HtmlWidget itemTitle = widgetTester.widget(itemTitleFinder);
    // Verify textStyle
    expect(itemTitle.textStyle, evoTextStyles.h600(evoColors.storyTitleColor));
    // Verify customStylesBuilder
    final Map<String, String> expectedCustomStyles = <String, String>{'margin': '0px'};
    final Map<String, String>? customStyles =
        itemTitle.customStylesBuilder?.call(dom.Element.tag('p'));
    expect(customStyles, expectedCustomStyles);
  }

  void verifyItemDescription(WidgetTester widgetTester, {required String description}) {
    final Finder itemDescriptionFinder = find.byWidgetPredicate(
      (Widget widget) => widget is HtmlWidget && widget.html == description,
    );
    expect(itemDescriptionFinder, findsOneWidget);

    final HtmlWidget itemDescription = widgetTester.widget(itemDescriptionFinder);
    expect(itemDescription.textStyle, evoTextStyles.bodyLarge(evoColors.storyTitleColor));
  }

  group('test UI StoryThemeWidget', () {
    tearDown(() {
      reset(mockFeatureToggle);
    });
    testWidgets(
        'verify StoryThemeWidget with storyContentModel has navigation == null, footer == null',
        (WidgetTester widgetTester) async {
      const bool isEnableDOPNativeFeature = false;
      when(() => mockFeatureToggle.enableDOPNativeFeature).thenReturn(isEnableDOPNativeFeature);
      when(() => mockFeatureToggle.enableEventTrackingFeature).thenReturn(true);

      final StoryContentModel storyContentModel = StoryContentModel(
        title: expectedTitle,
        description: expectedDescription,
        image: expectedImage,
      );
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryThemeWidget(
              storyContentModel: storyContentModel,
            ),
          ),
        ),
      );

      // verify AspectRatio
      verifyAspectRatio(widgetTester);

      // verify EvoNetworkImageProviderWidget
      verifyEvoNetworkImageProviderWidget(widgetTester);

      //verify SizedBox
      verifySizedBox(100);
      verifySizedBox(8);

      //verify item title
      verifyItemTitle(widgetTester, title: storyContentModel.title ?? '');

      //verify item description
      verifyItemDescription(widgetTester, description: storyContentModel.description ?? '');
    });

    testWidgets(
        'verify StoryThemeWidget with storyContentModel has navigation != null, footer != null',
        (WidgetTester widgetTester) async {
      const bool isEnableDOPNativeFeature = true;
      when(() => mockFeatureToggle.enableDOPNativeFeature).thenReturn(isEnableDOPNativeFeature);
      when(() => mockFeatureToggle.enableEventTrackingFeature).thenReturn(true);

      final ActionEntity expectedFooterAction = ActionEntity(
        type: ActionModel.openInAppWebView,
        args: action_entity.ArgsEntity(
          link: 'fake_link_footer',
          screenName: 'fake_screen_name_footer',
          actionLabel: 'fake_action_label_footer',
          parameters: action_entity.ParametersEntity(id: 'fake_id_footer'),
        ),
      );
      final StoryContentModel storyContentModel = StoryContentModel(
        title: expectedTitle,
        description: expectedDescription,
        image: expectedImage,
        navigation: ActionEntity(
            type: ActionModel.openInAppWebView,
            args: action_entity.ArgsEntity(
              link: 'fake_link',
              screenName: 'fake_screen_name',
              actionLabel: 'fake_action_label',
              parameters: action_entity.ParametersEntity(id: 'fake_id'),
            )),
        footer: expectedFooterAction,
      );
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryThemeWidget(
              storyContentModel: storyContentModel,
            ),
          ),
        ),
      );

      verifyAspectRatio(widgetTester);

      // verify EvoNetworkImageProviderWidget
      verifyEvoNetworkImageProviderWidget(widgetTester);

      //verify SizedBox
      verifySizedBox(100);
      verifySizedBox(8);

      //verify item title
      verifyItemTitle(widgetTester, title: storyContentModel.title ?? '');

      //verify item description
      verifyItemDescription(widgetTester, description: storyContentModel.description ?? '');

      //verify item view detail
      final String actionTitle =
          storyContentModel.navigation?.args?.actionLabel ?? EvoStrings.storyCtaViewDetail;
      final Finder actionTitleFinder = find.text(actionTitle);
      expect(actionTitleFinder, findsOneWidget);

      final Text actionTitleText = widgetTester.widget(actionTitleFinder);
      expect(actionTitleText.style, evoTextStyles.h300(color: evoColors.storyViewDetailColor));

      final Finder iconArrowLeftFinder = find.byWidgetPredicate((Widget widget) {
        if (widget is RotatedBox) {
          return widget.quarterTurns == 2;
        }
        return false;
      });
      expect(iconArrowLeftFinder, findsOneWidget);

      await widgetTester.tap(actionTitleFinder);
      await widgetTester.pumpAndSettle();

      final String url = storyContentModel.navigation?.args?.link ?? '';

      expect(
          verify(
            () => mockNavigatorContext.pushNamed(
              any(),
              extra: captureAny(named: 'extra'),
            ),
          ).captured.single,
          isA<StoryWebViewArg>()
              .having(
                (StoryWebViewArg p0) => p0.evoWebViewArg?.title,
                'test in-app WebView title',
                '',
              )
              .having(
                (StoryWebViewArg p0) => p0.evoWebViewArg?.url,
                'test in-app WebView url',
                url,
              ));

      //verify icon arrow left
      verify(() => commonImageProvider.asset(
            EvoImages.icArrowLeft,
            width: 24,
            height: 24,
          )).called(1);
    });

    testWidgets('CTA Button in HEADER should open deeplink with action model type openDeepLink',
        (WidgetTester widgetTester) async {
      const String deeplink = 'evoappvn://deeplink';
      when(() => mockFeatureToggle.enableDOPNativeFeature).thenReturn(true);
      when(() => mockFeatureToggle.enableEventTrackingFeature).thenReturn(true);
      when(() => EvoActionHandler().openDeepLink(deeplink)).thenAnswer((_) async => true);

      final ActionEntity action = ActionEntity(
        type: EvoActionModel.openDeepLink,
        args: action_entity.ArgsEntity(link: deeplink),
      );
      final StoryContentModel content = StoryContentModel(
        title: null,
        description: null,
        image: null,
        navigation: action,
      );
      await widgetTester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryThemeWidget(storyContentModel: content),
          ),
        ),
      );

      final Finder actionTitleFinder = find.text(EvoStrings.storyCtaViewDetail);
      expect(actionTitleFinder, findsOneWidget);

      await widgetTester.tap(actionTitleFinder);
      await widgetTester.pumpAndSettle();

      verify(() => EvoActionHandler().openDeepLink(deeplink)).called(1);
    });
  });
}
