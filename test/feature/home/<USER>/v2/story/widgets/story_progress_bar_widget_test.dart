import 'package:evoapp/feature/home_screen/non_user/v2/story/widgets/story_progress_bar_widget.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../../../util/flutter_test_config.dart';

void main() {
  const Color expectedBackgroundColor = Colors.red;
  const Color expectedActiveColor = Colors.white;
  const double expectedProgressBarValue = 0.1;
  const double expectedProgressBarHeight = 5;
  const BorderRadius expectedBorderRadius = BorderRadius.all(Radius.circular(16));

  setUpAll(() {
    getItRegisterColor();
  });

  group('test StoryProgressBarWidget', () {
    testWidgets('verify with backgroundColor is null and activeColor is null',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: StoryProgressBarWidget(
              value: expectedProgressBarValue,
              progressBarHeight: expectedProgressBarHeight,
            ),
          ),
        ),
      );

      //verify SizedBox
      final Finder sizedBoxFinder = find.byWidgetPredicate((Widget widget) {
        if (widget is SizedBox) {
          return widget.height == expectedProgressBarHeight;
        }

        return false;
      });
      expect(sizedBoxFinder, findsOneWidget);

      //verify LinearProgressIndicator
      final Finder linearProgressIndicatorFinder = find.byType(LinearProgressIndicator);
      expect(linearProgressIndicatorFinder, findsOneWidget);

      final LinearProgressIndicator linearProgressIndicator =
          tester.widget(linearProgressIndicatorFinder);
      expect(linearProgressIndicator.value, expectedProgressBarValue);
      expect(linearProgressIndicator.backgroundColor, evoColors.storyProgressBarBackground);
      expect(linearProgressIndicator.color, evoColors.background);
      expect(linearProgressIndicator.borderRadius, expectedBorderRadius);
    });

    testWidgets('verify with backgroundColor is null and activeColor is null',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: StoryProgressBarWidget(
              value: expectedProgressBarValue,
              progressBarHeight: expectedProgressBarHeight,
              activeColor: expectedActiveColor,
              backgroundColor: expectedBackgroundColor,
            ),
          ),
        ),
      );

      //verify SizedBox
      final Finder sizedBoxFinder = find.byWidgetPredicate((Widget widget) {
        if (widget is SizedBox) {
          return widget.height == expectedProgressBarHeight;
        }

        return false;
      });
      expect(sizedBoxFinder, findsOneWidget);

      //verify LinearProgressIndicator
      final Finder linearProgressIndicatorFinder = find.byType(LinearProgressIndicator);
      expect(linearProgressIndicatorFinder, findsOneWidget);

      final LinearProgressIndicator linearProgressIndicator =
          tester.widget(linearProgressIndicatorFinder);
      expect(linearProgressIndicator.value, expectedProgressBarValue);
      expect(linearProgressIndicator.backgroundColor, expectedBackgroundColor);
      expect(linearProgressIndicator.color, expectedActiveColor);
      expect(linearProgressIndicator.borderRadius, expectedBorderRadius);
    });
  });
}
