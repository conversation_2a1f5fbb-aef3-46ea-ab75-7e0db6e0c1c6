import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/campaign_repo.dart';
import 'package:evoapp/data/response/campaign_list_entity.dart';
import 'package:evoapp/feature/home_screen/home_widgets/home_campaign_list/home_campaigns_cubit.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_component/ui_component_state.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';
import '../../../util/test_util.dart';

class MockCampaignRepo extends Mock implements CampaignRepo {}

void main() {
  late MockCampaignRepo mockCampaignRepo;
  late HomeCampaignsCubit cubit;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    mockCampaignRepo = MockCampaignRepo();
  });

  group('test getCampaigns() function', () {
    setUp(() {
      cubit = HomeCampaignsCubit(campaignRepo: mockCampaignRepo);
    });

    blocTest<HomeCampaignsCubit, UiComponentState>('test getCampaigns is success',
        setUp: () async {
          final Map<String, dynamic> responseData =
              await TestUtil.getResponseMock('campaigns_information.json');

          when(() => mockCampaignRepo.getOffers(
                flowType: FlowType.offers,
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async {
            return CampaignListEntity.fromBaseResponse(
                BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));
          });
        },
        build: () => cubit,
        act: (HomeCampaignsCubit cubit) => cubit.getCampaigns(),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<UiComponentLoading>(),
              isA<UiComponentDataLoaded<CampaignListEntity>>().having(
                  (UiComponentDataLoaded<CampaignListEntity> entity) =>
                      entity.data?.campaigns?.isNotEmpty,
                  'test CampaignListEntity has data',
                  isTrue),
            ]);

    blocTest<HomeCampaignsCubit, UiComponentState>('test getCampaigns is fail',
        setUp: () async {
          when(() => mockCampaignRepo.getOffers(
                flowType: FlowType.offers,
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async {
            return CampaignListEntity.fromBaseResponse(
                BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: <String, dynamic>{
              'status_code': CommonHttpClient.BAD_REQUEST,
            }));
          });
        },
        build: () => cubit,
        act: (HomeCampaignsCubit cubit) => cubit.getCampaigns(),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<UiComponentLoading>(),
              isA<UiComponentFailed>().having(
                  (UiComponentFailed error) => error.errorUIModel.statusCode,
                  'test status_code failed',
                  CommonHttpClient.BAD_REQUEST),
            ]);
  });
}
