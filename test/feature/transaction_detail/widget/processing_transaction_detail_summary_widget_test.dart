import 'package:evoapp/data/repository/checkout_repo.dart';
import 'package:evoapp/data/response/payment_result_transaction_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/payment/widget/item_text_order_summary_widget.dart';
import 'package:evoapp/feature/payment/widget/payment_tooltips_widget.dart';
import 'package:evoapp/feature/payment/widget/title_and_description_widget.dart';
import 'package:evoapp/feature/payment/widget/transaction_detail_summary_widget/processing_transaction_detail_summary_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';
import '../../payment/widget/order_summanry_widget/test_order_summary_util.dart';

class MockFeatureToggle extends Mock implements FeatureToggle {}

void main() {
  late CommonImageProvider commonImageProvider;
  late CommonUtilFunction mockCommonUtilFunction;
  late EvoUtilFunction mockEvoUtilFunction;
  const String descriptionExpect = 'fake_description';
  const String formatCurrencyExpect = 'fake_format_currency';
  late MockFeatureToggle mockFeatureToggle;

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    TestOrderSummaryUtil.instance.registerFallbackTransactionStatus();

    mockCommonUtilFunction = getIt.get<CommonUtilFunction>();
    commonImageProvider = getIt.get<CommonImageProvider>();
    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();

    mockFeatureToggle = MockFeatureToggle();
    getIt.registerSingleton<FeatureToggle>(mockFeatureToggle);

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());

    when(() => mockEvoUtilFunction.cropStringWithMaxLength(any(), any())).thenAnswer((_) {
      return descriptionExpect;
    });

    when(() => mockEvoUtilFunction.evoFormatCurrency(any(),
        currencySymbol: any(named: 'currencySymbol'))).thenAnswer((_) {
      return formatCurrencyExpect;
    });

    when(() => mockCommonUtilFunction.toDateTime(any())).thenReturn(DateTime(2023, 10, 10, 10, 10));

    when(() => mockFeatureToggle.enableInstantCashbackFeature).thenReturn(true);
  });

  void verifyFinderText(String? data) {
    final Finder finderText = find.text(data ?? '');
    expect(finderText, findsOneWidget);
  }

  Widget initTransactionDetailSummaryWidget(PaymentResultTransactionEntity? transactionEntity) {
    return MaterialApp(
      home: Material(
        child: ProcessingTransactionDetailSummaryWidget(
          transactionEntity: transactionEntity,
        ),
      ),
    );
  }

  group('test buildBodyEmi', () {
    final List<String> expectedListTextTitleWidgets = <String>[
      EvoStrings.paymentResultEmiTransactionCode,
      EvoStrings.paymentPartnerReferenceLabel,
      EvoStrings.paymentPartnerOrderId,
      EvoStrings.paymentResultEmiTime,
      EvoStrings.paymentResultEmiDescription,
      EvoStrings.paymentResultEmiConversionFee,
      EvoStrings.paymentResultEmiVoucher,
      EvoStrings.paymentSummaryCashbackAmount,
      EvoStrings.paymentResultEmiCheckPaymentMethod,
      EvoStrings.paymentResultEmiSupport,
    ];

    final PaymentResultTransactionEntity entity =
        TestOrderSummaryUtil.instance.getPaymentResultTransactionEntity(
      paymentService: PaymentService.emi,
    );

    testWidgets('verify order titleWidget', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(initTransactionDetailSummaryWidget(entity));

      final Finder finderBody = find.byType(TitleAndDescriptionWidget);
      expect(finderBody, findsNWidgets(10));

      for (int index = 0; index < expectedListTextTitleWidgets.length; index++) {
        final TitleAndDescriptionWidget widget = widgetTester.widget(finderBody.at(index));
        final Widget titleWidget = widget.titleWidget;

        if (titleWidget is ItemTextOrderSummaryWidget) {
          expect(titleWidget.text, expectedListTextTitleWidgets[index]);
        } else if (titleWidget is Row) {
          //verify ItemTextOrderSummaryWidget
          final Flexible itemFlexible = titleWidget.children.first as Flexible;
          final ItemTextOrderSummaryWidget? itemText =
              (itemFlexible.child) as ItemTextOrderSummaryWidget?;
          expect(itemText?.text, expectedListTextTitleWidgets[index]);

          //verify Tooltips widget
          final Widget lastWidget = titleWidget.children.last;
          expect(lastWidget, isA<PaymentTooltipsWidget>());
        }
      }
    });

    testWidgets('verify descriptionWidget', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(initTransactionDetailSummaryWidget(entity));

      //verify order number
      verifyFinderText('fake_order_number');

      //verify PartnerReferenceLabel
      verifyFinderText('fake_partner_reference');

      //verify partner order id
      verifyFinderText('fake_partner_order_id');

      //verify createAt
      verifyFinderText('10/10/2023 - 10:10');

      //verify sourceName
      verifyFinderText('fake_source_name');

      //verify promotionAmount
      verifyFinderText('-$formatCurrencyExpect');

      // verify cashbackAmount
      verifyFinderText('+$formatCurrencyExpect');

      //verify monthlyInstallmentAmount & conversionFee
      final Finder finderCurrency = find.text(formatCurrencyExpect);
      expect(finderCurrency, findsOneWidget);

      // verify cashbackAmount
      final Finder finderCashback = find.text('+$formatCurrencyExpect');
      expect(finderCashback, findsOneWidget);
    });
  });

  group('test buildBodyOutrightPurchase', () {
    final List<String> expectedListTextTitleWidgets = <String>[
      EvoStrings.paymentResultEmiTransactionCode,
      EvoStrings.paymentPartnerReferenceLabel,
      EvoStrings.paymentPartnerOrderId,
      EvoStrings.paymentResultEmiTime,
      EvoStrings.paymentResultEmiDescription,
      EvoStrings.paymentResultEmiVoucher,
      EvoStrings.paymentSummaryCashbackAmount,
      EvoStrings.paymentResultEmiFee,
      EvoStrings.paymentResultEmiCheckPaymentMethod,
      EvoStrings.paymentResultEmiSupport,
    ];

    final PaymentResultTransactionEntity entity =
        TestOrderSummaryUtil.instance.getPaymentResultTransactionEntity(
      paymentService: PaymentService.outrightPurchase,
    );

    testWidgets('verify order titleWidget', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(initTransactionDetailSummaryWidget(entity));

      final Finder finderBody = find.byType(TitleAndDescriptionWidget);
      expect(finderBody, findsNWidgets(10));

      for (int index = 0; index < expectedListTextTitleWidgets.length; index++) {
        final TitleAndDescriptionWidget widget = widgetTester.widget(finderBody.at(index));
        final Widget titleWidget = widget.titleWidget;

        if (titleWidget is ItemTextOrderSummaryWidget) {
          expect(titleWidget.text, expectedListTextTitleWidgets[index]);
        } else if (titleWidget is Row) {
          //verify ItemTextOrderSummaryWidget
          final Flexible itemFlexible = titleWidget.children.first as Flexible;
          final ItemTextOrderSummaryWidget? itemText =
              (itemFlexible.child) as ItemTextOrderSummaryWidget?;
          expect(itemText?.text, expectedListTextTitleWidgets[index]);
        }
      }
    });

    testWidgets('verify descriptionWidget', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(initTransactionDetailSummaryWidget(entity));

      //verify order number
      verifyFinderText('fake_order_number');

      //verify PartnerReferenceLabel
      verifyFinderText('fake_partner_reference');

      //verify partner order id
      verifyFinderText('fake_partner_order_id');

      //verify createAt
      verifyFinderText('10/10/2023 - 10:10');

      //verify description
      verifyFinderText(descriptionExpect);

      //verify sourceName
      verifyFinderText('fake_source_name');

      //verify promotionAmount
      verifyFinderText('-$formatCurrencyExpect');

      //verify cashbackAmount
      verifyFinderText('+$formatCurrencyExpect');
    });
  });
}
