import 'package:evoapp/data/response/cta_widget_config_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/profile/profile_screen/card_status/widget/card_cta_widget/card_cta_button.dart';
import 'package:evoapp/feature/profile/profile_screen/card_status/widget/card_cta_widget/card_cta_button_group.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../util/flutter_test_config.dart';

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

void main() {
  final CommonNavigator commonNavigator = MockCommonNavigator();
  final BuildContext mockNavigatorContext = MockBuildContext();
  bool isLinkCardClicked = false;
  onLinkCardClick() {
    isLinkCardClicked = true;
  }

  setUpAll(() {
    getItRegisterTextStyle();
    getItRegisterColor();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterButtonStyle();
    setUpOneLinkDeepLinkRegExForTest();

    getIt.registerSingleton<FeatureToggle>(FeatureToggle());

    getIt.registerSingleton<CommonNavigator>(commonNavigator);
    setUpMockGlobalKeyProvider(mockNavigatorContext);

    when(
      () => evoImageProvider.asset(
        any(),
        width: any(named: 'width'),
        fit: any(named: 'fit'),
      ),
    ).thenReturn(const SizedBox.shrink());

    FlavorConfig(
      flavor: FlavorType.stag.name,
      values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
    );
  });

  setUp(() {
    isLinkCardClicked = false;
  });

  tearDownAll(() {
    getIt.reset();
  });

  testWidgets(
    'Case linkedCard = true && activatedCard = false, '
    'should display an inactive link card button AND an active activate card button',
    (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: CardCtaButtonGroup(
          ctaWidgetConfig: CtaWidgetConfigEntity(
            display: true,
            linkedCard: true,
            activatedCard: false,
          ),
          onLinkCardClick: onLinkCardClick,
        ),
      ));

      /// Find the link card button
      final Finder linkCardBtnFinder = find.byWidgetPredicate((Widget widget) =>
          widget is CardCtaButton &&
          widget.doneText == EvoStrings.cardStatusLinked &&
          widget.notDoneText == EvoStrings.cardStatusNotLinked &&
          widget.isDoneAction &&
          widget.onPressed == onLinkCardClick);
      expect(linkCardBtnFinder, findsOneWidget);
      // Do nothing when the user tabs on the link card button
      expect(isLinkCardClicked, isFalse);
      await tester.tap(linkCardBtnFinder);
      expect(isLinkCardClicked, isFalse);

      /// Find the activate card button
      final Finder activateBtnFinder = find.byWidgetPredicate((Widget widget) =>
          widget is CardCtaButton &&
          widget.doneText == EvoStrings.cardStatusActivated &&
          widget.notDoneText == EvoStrings.cardStatusNotActivated &&
          !widget.isDoneAction &&
          widget.onPressed != null);
      expect(activateBtnFinder, findsOneWidget);
      // Open in-app WebView when the user taps on the activate card button
      await tester.tap(activateBtnFinder);
      expect(
          verify(
            () => mockNavigatorContext.pushNamed(
              any(),
              extra: captureAny(named: 'extra'),
            ),
          ).captured.single,
          isA<CommonWebViewArg>()
              .having(
                (CommonWebViewArg p0) => p0.title,
                'test in-app WebView title',
                EvoStrings.tutorialActiveCard,
              )
              .having(
                (CommonWebViewArg p0) => p0.url,
                'test in-app WebView url',
                WebsiteUrl.evoCardActivationTutorialUrl,
              ));
    },
  );

  testWidgets(
    'Case linkedCard = false && activatedCard = true, '
    'should display an active link card button AND an inactive activate card button',
    (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: CardCtaButtonGroup(
          ctaWidgetConfig: CtaWidgetConfigEntity(
            display: true,
            linkedCard: false,
            activatedCard: true,
          ),
          onLinkCardClick: onLinkCardClick,
        ),
      ));

      /// Find the link card button
      final Finder linkCardBtnFinder = find.byWidgetPredicate((Widget widget) =>
          widget is CardCtaButton &&
          widget.doneText == EvoStrings.cardStatusLinked &&
          widget.notDoneText == EvoStrings.cardStatusNotLinked &&
          !widget.isDoneAction &&
          widget.onPressed == onLinkCardClick);
      expect(linkCardBtnFinder, findsOneWidget);
      // Call the function `onLinkCardClick` when the user tabs on the link card button
      expect(isLinkCardClicked, isFalse);
      await tester.tap(linkCardBtnFinder);
      expect(isLinkCardClicked, isTrue);

      /// Find the activate card button
      final Finder activateBtnFinder = find.byWidgetPredicate((Widget widget) =>
          widget is CardCtaButton &&
          widget.doneText == EvoStrings.cardStatusActivated &&
          widget.notDoneText == EvoStrings.cardStatusNotActivated &&
          widget.isDoneAction &&
          widget.onPressed != null);
      expect(activateBtnFinder, findsOneWidget);
      // Do nothing when the user tabs on the activate card button
      await tester.tap(activateBtnFinder);
      verifyNever(
        () => mockNavigatorContext.pushNamed(
          any(),
          extra: any(named: 'extra'),
        ),
      );
    },
  );

  testWidgets(
    'Case linkedCard = false && activatedCard = false, '
    'should display an active link card button AND an active activate card button',
    (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: CardCtaButtonGroup(
          ctaWidgetConfig: CtaWidgetConfigEntity(
            display: true,
            linkedCard: false,
            activatedCard: false,
          ),
          onLinkCardClick: onLinkCardClick,
        ),
      ));

      /// Find the link card button
      final Finder linkCardBtnFinder = find.byWidgetPredicate((Widget widget) =>
          widget is CardCtaButton &&
          widget.doneText == EvoStrings.cardStatusLinked &&
          widget.notDoneText == EvoStrings.cardStatusNotLinked &&
          !widget.isDoneAction &&
          widget.onPressed == onLinkCardClick);
      expect(linkCardBtnFinder, findsOneWidget);
      // Call the function `onLinkCardClick` when the user tabs on the link card button
      expect(isLinkCardClicked, isFalse);
      await tester.tap(linkCardBtnFinder);
      expect(isLinkCardClicked, isTrue);

      /// Find the activate card button
      final Finder activateBtnFinder = find.byWidgetPredicate((Widget widget) =>
          widget is CardCtaButton &&
          widget.doneText == EvoStrings.cardStatusActivated &&
          widget.notDoneText == EvoStrings.cardStatusNotActivated &&
          !widget.isDoneAction &&
          widget.onPressed != null);
      expect(activateBtnFinder, findsOneWidget);
      // Open in-app WebView when the user taps on the activate card button
      await tester.tap(activateBtnFinder);
      expect(
          verify(
            () => mockNavigatorContext.pushNamed(
              any(),
              extra: captureAny(named: 'extra'),
            ),
          ).captured.single,
          isA<CommonWebViewArg>()
              .having(
                (CommonWebViewArg p0) => p0.title,
                'test in-app WebView title',
                EvoStrings.tutorialActiveCard,
              )
              .having(
                (CommonWebViewArg p0) => p0.url,
                'test in-app WebView url',
                WebsiteUrl.evoCardActivationTutorialUrl,
              ));
    },
  );
}
