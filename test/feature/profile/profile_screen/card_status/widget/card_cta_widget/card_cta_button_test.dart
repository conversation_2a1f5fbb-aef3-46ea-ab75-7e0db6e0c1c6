import 'package:evoapp/feature/profile/profile_screen/card_status/widget/card_cta_widget/card_cta_button.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../util/flutter_test_config.dart';

class MockCommonButtonStyles extends Mock implements CommonButtonStyles {}

void main() {
  const String doneText = 'doneText';
  const String notDoneText = 'notDoneText';
  const SizedBox expectImageHeader = SizedBox();
  final ButtonStyle mockButtonStyle = ButtonStyle(
    elevation: WidgetStateProperty.all(0),
  );
  final ButtonStyle expectedButtonStyle = mockButtonStyle.copyWith(
    padding: WidgetStateProperty.all(const EdgeInsets.all(12)),
  );

  bool isPressed = false;
  onPressed() {
    isPressed = true;
  }

  setUpAll(() {
    getItRegisterTextStyle();
    getItRegisterColor();

    getIt.registerLazySingleton<CommonButtonStyles>(() => MockCommonButtonStyles());
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    when(
      () => evoImageProvider.asset(
        any(),
        width: any(named: 'width'),
        fit: any(named: 'fit'),
      ),
    ).thenReturn(expectImageHeader);
    when(() => evoButtonStyles.tertiary(ButtonSize.large)).thenReturn(mockButtonStyle);
  });

  setUp(() {
    isPressed = false;
  });

  tearDownAll(() {
    getIt.reset();
  });

  testWidgets(
    'Case isDoneAction is false, '
    'the widget should display the NOT DONE state, '
    'and the user can interact with it',
    (WidgetTester tester) async {
      expect(isPressed, isFalse);

      await tester.pumpWidget(MaterialApp(
        home: CardCtaButton(
          doneText: doneText,
          notDoneText: notDoneText,
          onPressed: onPressed,
        ),
      ));

      /// Check CommonButton
      final Finder commonButtonFinder = find.byType(CommonButton);
      expect(commonButtonFinder, findsOneWidget);
      final CommonButton commonButton = tester.widget(commonButtonFinder);
      expect(commonButton.style.elevation, expectedButtonStyle.elevation);
      expect(commonButton.style.padding.toString(), expectedButtonStyle.padding.toString());
      expect(commonButton.onPressed, onPressed);

      await tester.tap(commonButtonFinder);
      expect(isPressed, isTrue);
      // Check button's icon
      verifyNever(
        () => evoImageProvider.asset(
          any(),
          width: any(named: 'width'),
          fit: any(named: 'fit'),
        ),
      );
      // Check button's text
      expect(find.text(doneText), findsNothing);
      expect(find.text(notDoneText), findsOneWidget);
    },
  );

  testWidgets(
    'Case isDoneAction is true, '
    'the widget should display the DONE state, '
    'and the user cannot interact with it',
    (WidgetTester tester) async {
      expect(isPressed, isFalse);

      await tester.pumpWidget(MaterialApp(
        home: CardCtaButton(
          doneText: doneText,
          notDoneText: notDoneText,
          onPressed: onPressed,
          isDoneAction: true,
        ),
      ));

      /// Check CommonButton
      final Finder commonButtonFinder = find.byType(CommonButton);
      expect(commonButtonFinder, findsOneWidget);
      final CommonButton commonButton = tester.widget(commonButtonFinder);
      expect(commonButton.style.elevation, expectedButtonStyle.elevation);
      expect(commonButton.style.padding.toString(), expectedButtonStyle.padding.toString());
      expect(commonButton.onPressed, isNull);

      await tester.tap(commonButtonFinder);
      expect(isPressed, isFalse);
      // Check button's icon
      verify(
        () => evoImageProvider.asset(
          EvoImages.icCardStatusCheck,
          width: 16,
          fit: BoxFit.fitWidth,
        ),
      ).called(1);
      // Check button's text
      expect(find.text(doneText), findsOneWidget);
      expect(find.text(notDoneText), findsNothing);
    },
  );
}
