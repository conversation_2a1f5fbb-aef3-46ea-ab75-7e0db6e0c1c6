import 'dart:async';

import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/response/card_status_entity.dart';
import 'package:evoapp/data/response/credit_limit_widget_config_entity.dart';
import 'package:evoapp/data/response/cta_widget_config_entity.dart';
import 'package:evoapp/data/response/linked_card_status_checking_entity.dart';
import 'package:evoapp/feature/manual_link_card/manual_link_card_cubit.dart';
import 'package:evoapp/feature/manual_link_card/manual_link_card_flow_callback.dart';
import 'package:evoapp/feature/profile/profile_screen/card_status/card_status_widget.dart';
import 'package:evoapp/feature/profile/profile_screen/card_status/cubit/card_status_cubit.dart';
import 'package:evoapp/feature/profile/profile_screen/card_status/cubit/card_status_state.dart';
import 'package:evoapp/feature/profile/profile_screen/card_status/widget/card_cta_widget/card_cta_widget.dart';
import 'package:evoapp/feature/profile/profile_screen/card_status/widget/credit_limit_widget/credit_limit_amount/credit_limit_amount_cubit.dart';
import 'package:evoapp/feature/profile/profile_screen/card_status/widget/credit_limit_widget/credit_limit_amount/credit_limit_amount_state.dart';
import 'package:evoapp/feature/profile/profile_screen/card_status/widget/credit_limit_widget/credit_limit_widget.dart';
import 'package:evoapp/feature/profile/profile_screen/card_status/widget/skeleton_card_status_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/widget/shimmer/shimmer_animation.dart';
import 'package:flutter_common_package/widget/shimmer/shimmer_widget.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';
import '../../../../util/flutter_test_config.dart';

class MockCardStatusCubit extends Mock implements CardStatusCubit {
  @override
  Future<void> close() => Future<void>.value();
}

class MockCreditLimitAmountCubit extends Mock implements CreditLimitAmountCubit {
  @override
  Future<void> close() => Future<void>.value();
}

class MockManualLinkCardCubit extends Mock implements ManualLinkCardCubit {
  @override
  Future<void> close() => Future<void>.value();
}

class MockUserRepo extends Mock implements UserRepo {}

class MockManualLinkCardFlowCallback extends Mock implements ManualLinkCardFlowCallback {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

class MockLinkedCardStatusCheckingEntity extends Mock implements LinkedCardStatusCheckingEntity {}

void main() {
  late MockCreditLimitAmountCubit mockCreditLimitAmountCubit;
  late StreamController<CreditLimitAmountState> creditAmountStateStreamController;

  late MockCardStatusCubit mockCardStatusCubit;
  late StreamController<CardStatusState> cardStatusStateStreamController;

  late MockManualLinkCardCubit mockManualLinkCardCubit;
  late StreamController<ManualLinkCardState> manualLinkCardStateStreamController;

  late EvoSnackBar mockEvoSnackBar;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    initConfigEvoPageStateBase();
    setUtilsMockInstanceForTesting();
  });

  setUp(() {
    mockCreditLimitAmountCubit = MockCreditLimitAmountCubit();
    creditAmountStateStreamController = StreamController<CreditLimitAmountState>.broadcast();

    when(() => mockCreditLimitAmountCubit.stream)
        .thenAnswer((_) => creditAmountStateStreamController.stream);
    when(() => mockCreditLimitAmountCubit.loadMaskedStatus())
        .thenAnswer((_) async => Future<void>.value());
    when(() => mockCreditLimitAmountCubit.state).thenReturn(
      CreditLimitAmountLoadedState(isMasked: false),
    );
    getIt.registerSingleton<CreditLimitAmountCubit>(mockCreditLimitAmountCubit);

    mockCardStatusCubit = MockCardStatusCubit();
    cardStatusStateStreamController = StreamController<CardStatusState>.broadcast();

    when(() => mockCardStatusCubit.stream)
        .thenAnswer((_) => cardStatusStateStreamController.stream);
    when(() => mockCardStatusCubit.state).thenReturn(CardStatusInitState());
    when(() => mockCardStatusCubit.loadCardStatus(renovateStatus: any(named: 'renovateStatus')))
        .thenAnswer((_) async => Future<void>.value());

    mockManualLinkCardCubit = MockManualLinkCardCubit();
    manualLinkCardStateStreamController = StreamController<ManualLinkCardState>.broadcast();

    when(() => mockManualLinkCardCubit.stream)
        .thenAnswer((_) => manualLinkCardStateStreamController.stream);
    when(() => mockManualLinkCardCubit.state).thenReturn(ManualLinkCardInitState());
    when(() => mockManualLinkCardCubit.checkDopCardStatus())
        .thenAnswer((_) async => Future<void>.value());

    setUpMockConfigEvoPageStateBase();
    setupMockImageProvider();
    setUpMockSnackBarForTest();

    // Setup EvoUiUtils mock
    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(200);

    // Setup SnackBar mock
    mockEvoSnackBar = getIt<EvoSnackBar>();
  });

  tearDown(() {
    manualLinkCardStateStreamController.close();
    cardStatusStateStreamController.close();
    creditAmountStateStreamController.close();

    getIt.unregister<CreditLimitAmountCubit>();
  });

  tearDownAll(() {
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  // Helper function to pump the widget
  Future<CardStatusWidgetState> pumpCardStatusWidget(
    WidgetTester tester, {
    bool forceHideCTAs = false,
  }) async {
    final Key key = UniqueKey();

    await tester.pumpWidget(
      MaterialApp(
        home: Builder(
          builder: (BuildContext context) {
            return ShimmerAnimation(
              child: CardStatusWidget(
                key: key,
                entryPointScreen: Screen.profileScreen,
                createManualLinkCardCallbackFn: () => ManualLinkCardFlowCallback(),
                createFaceOtpCallbackFn: (_) => null,
                createFaceAuthCallbackFn: (_) => null,
                manualLinkCardCubit: mockManualLinkCardCubit,
                cardStatusCubit: mockCardStatusCubit,
                forceHideCTAs: forceHideCTAs,
              ),
            );
          },
        ),
      ),
    );

    await tester.pump(Duration(seconds: 1));

    // Find the state
    final Finder stateFinder = find.byKey(key);
    expect(stateFinder, findsOneWidget);

    final State<StatefulWidget> state = tester.state(stateFinder);
    expect(state, isA<CardStatusWidgetState>());

    return state as CardStatusWidgetState;
  }

  CardStatusEntity createCardStatusEntity({
    bool? display,
    CreditLimitWidgetConfigEntity? creditLimitWidgetConfig,
    CtaWidgetConfigEntity? ctaWidgetConfig,
  }) {
    return CardStatusEntity(
      display: display,
      creditLimitWidgetConfig: creditLimitWidgetConfig,
      ctaWidgetConfig: ctaWidgetConfig,
    );
  }

  group('Initial State and Loading State Tests', () {
    testWidgets('Should show skeleton loading state when in CardStatusInitState',
        (WidgetTester tester) async {
      // Arrange
      when(() => mockCardStatusCubit.state).thenReturn(CardStatusInitState());

      // Act
      await pumpCardStatusWidget(tester);

      // Assert
      expect(find.byType(ShimmerWidget), findsOneWidget);
      expect(find.byType(SkeletonCardStatusWidget), findsOneWidget);
      expect(find.byType(CreditLimitWidget), findsNothing);
      expect(find.byType(CardCtaWidget), findsNothing);
    });

    testWidgets('Should show skeleton loading state when in CardStatusLoadingState',
        (WidgetTester tester) async {
      // Arrange
      when(() => mockCardStatusCubit.state).thenReturn(CardStatusLoadingState());

      // Act
      await pumpCardStatusWidget(tester);

      // Assert
      expect(find.byType(ShimmerWidget), findsOneWidget);
      expect(find.byType(SkeletonCardStatusWidget), findsOneWidget);
      expect(find.byType(CreditLimitWidget), findsNothing);
      expect(find.byType(CardCtaWidget), findsNothing);
    });
  });

  group('CardStatusLoadedState Tests', () {
    testWidgets('Should show empty widget when CardStatusEntity has display=false',
        (WidgetTester tester) async {
      // Arrange
      final CardStatusEntity cardStatus = createCardStatusEntity(display: false);
      when(() => mockCardStatusCubit.state)
          .thenReturn(CardStatusLoadedState(cardStatus: cardStatus));

      // Act
      await pumpCardStatusWidget(tester);

      // Assert
      expect(find.byType(ShimmerWidget), findsOneWidget);
      expect(find.byType(SkeletonCardStatusWidget), findsNothing);
      expect(find.byType(CreditLimitWidget), findsNothing);
      expect(find.byType(CardCtaWidget), findsNothing);
    });
  });

  group('Error State Tests', () {
    testWidgets('Should handle CardStatusErrorState correctly', (WidgetTester tester) async {
      // Arrange
      final ErrorUIModel errorModel = ErrorUIModel(userMessage: 'Test error message');
      when(() => mockCardStatusCubit.state)
          .thenReturn(CardStatusErrorState(errorUiModel: errorModel));

      // Assert - Verify the snack-bar is shown with the error message
      await pumpCardStatusWidget(tester);
      cardStatusStateStreamController.add(CardStatusErrorState(errorUiModel: errorModel));
      await tester.pump();

      verify(() => mockEvoSnackBar.show(
            'Test error message',
            typeSnackBar: any(named: 'typeSnackBar'),
            durationInSec: any(named: 'durationInSec'),
          )).called(1);
    });

    testWidgets('Should handle CardStatusDOPRenovateErrorState correctly',
        (WidgetTester tester) async {
      // Arrange
      final ErrorUIModel errorModel = ErrorUIModel(userMessage: 'Test renovate error message');
      when(() => mockCardStatusCubit.state)
          .thenReturn(CardStatusDOPRenovateErrorState(errorUiModel: errorModel));

      // Assert - Verify the snack-bar is shown with the error message
      await pumpCardStatusWidget(tester);
      cardStatusStateStreamController
          .add(CardStatusDOPRenovateErrorState(errorUiModel: errorModel));
      await tester.pump();

      verify(() => mockEvoSnackBar.show(
            'Test renovate error message',
            typeSnackBar: any(named: 'typeSnackBar'),
            durationInSec: any(named: 'durationInSec'),
          )).called(1);
    });
  });

  group('User Interaction Tests', () {
    testWidgets('Should call loadCardStatus when initialized', (WidgetTester tester) async {
      // Arrange
      when(() => mockCardStatusCubit.state).thenReturn(CardStatusInitState());

      // Act
      await pumpCardStatusWidget(tester);

      // Assert
      verify(() => mockCardStatusCubit.loadCardStatus()).called(1);
    });

    testWidgets('Should call loadCardStatus with renovateStatus=true when reload is called',
        (WidgetTester tester) async {
      // Arrange
      when(() => mockCardStatusCubit.state).thenReturn(CardStatusInitState());

      // Act
      final CardStatusWidgetState state = await pumpCardStatusWidget(tester);

      // Call the method
      state.loadCardStatus(renovateStatus: true);

      // Assert
      verify(() => mockCardStatusCubit.loadCardStatus(renovateStatus: true)).called(1);
    });

    testWidgets('Should call checkDopCardStatus when onLinkCardClick is called',
        (WidgetTester tester) async {
      // Arrange
      when(() => mockCardStatusCubit.state).thenReturn(CardStatusInitState());

      // Act
      final CardStatusWidgetState state = await pumpCardStatusWidget(tester);

      // Call the method
      state.onLinkCardClick();

      // Assert
      verify(() => mockManualLinkCardCubit.checkDopCardStatus()).called(1);
    });
  });

  group('ManualLinkCardCubit Handling Tests', () {
    testWidgets('Should show loading when ManualLinkCardLoadingState is emitted',
        (WidgetTester tester) async {
      // Arrange
      final CardStatusEntity cardStatus = createCardStatusEntity(display: true);
      when(() => mockCardStatusCubit.state)
          .thenReturn(CardStatusLoadedState(cardStatus: cardStatus));

      // Act
      await pumpCardStatusWidget(tester);

      // Emit loading state
      manualLinkCardStateStreamController.add(ManualLinkCardLoadingState());
      await tester.pump();

      // Assert
      verify(() => EvoUiUtils().showHudLoading()).called(1);
    });

    testWidgets('Should handle LinkedCardStatusCheckingLoadedState correctly',
        (WidgetTester tester) async {
      // Arrange
      final CardStatusEntity cardStatus = createCardStatusEntity(display: true);
      when(() => mockCardStatusCubit.state)
          .thenReturn(CardStatusLoadedState(cardStatus: cardStatus));

      final MockLinkedCardStatusCheckingEntity mockEntity = MockLinkedCardStatusCheckingEntity();
      when(() => mockEntity.statusCode).thenReturn(CommonHttpClient.SUCCESS);

      // Act
      await pumpCardStatusWidget(tester);

      // Emit loaded state
      manualLinkCardStateStreamController.add(LinkedCardStatusCheckingLoadedState(mockEntity));
      await tester.pump();

      // Assert
      verify(() => EvoUiUtils().hideHudLoading()).called(1);
    });

    testWidgets('Should handle LinkedCardStatusCheckingErrorState correctly',
        (WidgetTester tester) async {
      // Arrange
      final CardStatusEntity cardStatus = createCardStatusEntity(display: true);
      when(() => mockCardStatusCubit.state)
          .thenReturn(CardStatusLoadedState(cardStatus: cardStatus));

      final ErrorUIModel errorModel = ErrorUIModel(userMessage: 'Test link card error');

      // Act
      await pumpCardStatusWidget(tester);

      // Emit error state
      manualLinkCardStateStreamController.add(LinkedCardStatusCheckingErrorState(errorModel));
      await tester.pump();

      // Assert
      verify(() => EvoUiUtils().hideHudLoading()).called(1);
      verify(() => mockEvoSnackBar.show(
            'Test link card error',
            typeSnackBar: any(named: 'typeSnackBar'),
            durationInSec: any(named: 'durationInSec'),
          )).called(1);
    });
  });
}
