import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/response/user_entity.dart';
import 'package:evoapp/feature/profile/profile_detail_screen/profile_detail_page_cubit.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/test_util.dart';

class UserRepoMock extends Mock implements UserRepo {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late UserRepoMock userRepoMock;

  setUpAll(() {
    userRepoMock = UserRepoMock();
  });

  ProfileDetailPageCubit createCubit() {
    return ProfileDetailPageCubit(userRepoMock);
  }

  group('test getUserInfo', () {
    blocTest<ProfileDetailPageCubit, ProfileDetailPageState>(
      'test getUserInfo fail',
      setUp: () {
        when(() => userRepoMock.getUserInfo(
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return UserEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: null,
            ),
          );
        });
      },
      build: () => createCubit(),
      act: (ProfileDetailPageCubit cubit) => cubit.getUserInfo(),
      expect: () => <dynamic>[
        isA<ProfileDetailPageFail>().having(
          (ProfileDetailPageFail state) => state.error?.statusCode,
          'verify error code',
          CommonHttpClient.BAD_REQUEST,
        ),
      ],
    );

    blocTest<ProfileDetailPageCubit, ProfileDetailPageState>(
      'test getUserInfo success',
      setUp: () {
        when(() => userRepoMock.getUserInfo(
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return UserEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: await TestUtil.getResponseMock('user_information.json'),
            ),
          );
        });
      },
      build: () => createCubit(),
      act: (ProfileDetailPageCubit cubit) => cubit.getUserInfo(),
      expect: () => <dynamic>[
        isA<ProfileDetailPageInfoLoaded>().having(
          (ProfileDetailPageInfoLoaded state) => state.user,
          'verify user data',
          isNotNull,
        ),
      ],
    );
  });
}
