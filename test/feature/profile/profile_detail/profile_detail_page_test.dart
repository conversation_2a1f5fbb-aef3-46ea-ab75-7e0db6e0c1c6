// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/response/user_entity.dart';
import 'package:evoapp/data/response/user_information_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/profile/model/gender.dart';
import 'package:evoapp/feature/profile/profile_detail_screen/deactivate_account/deactivate_account_cubit.dart';
import 'package:evoapp/feature/profile/profile_detail_screen/profile_detail_page.dart';
import 'package:evoapp/feature/profile/profile_detail_screen/widget/profile_detail_text_field.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_dialog_widget_test.dart';
import '../../../base/evo_page_state_base_test.dart';
import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';
import '../../activated_pos_limit/activate_card_introduction/activate_card_introduction_screen_test.dart';

void main() {
  late MockUserRepo mockUserRepo;
  late MockEvoLocalStorageHelper mockEvoLocalStorageHelper;

  void mock() {
    EvoAuthenticationHelper.setInstanceForTesting(MockEvoAuthenticationHelper());
    when(() => mockEvoLocalStorageHelper.delete(key: any(named: 'key'))).thenAnswer((_) async {
      return Future<void>.value();
    });

    when(() => getIt<FeatureToggle>().enableRevampUiFeature).thenReturn(true);

    when(() => getIt<FeatureToggle>().enableDeleteAccountFeatureVersion)
        .thenReturn(DeleteAccountFeatureVersion.version_2);

    when(() => mockUserRepo.getUserInfo(
        mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
      return UserEntity();
    });
  }

  setUpAll(() {});

  setUp(() {
    mockUserRepo = MockUserRepo();
    mockEvoLocalStorageHelper = MockEvoLocalStorageHelper();
    getIt.registerSingleton<UserRepo>(mockUserRepo);
    getIt.registerSingleton<EvoLocalStorageHelper>(mockEvoLocalStorageHelper);
    getIt.registerSingleton<FeatureToggle>(MockFeatureToggle());
    mock();
    setupMockDialogHelper();
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();
    setupMockImageProvider();
    setUpMockSnackBarForTest();
    when(() => EvoUiUtils().showHudLoading(loadingText: any(named: 'loadingText')))
        .thenAnswer((_) => Future<void>.value());
    when(() => EvoDialogHelper().showMultiActionBottomSheet(
          any(),
          any(),
          onSelectedItem: any(named: 'onSelectedItem'),
        )).thenAnswer((_) async {});
    getIt<AppState>().userInfo.value =
        const UserInformationEntity(avatarUrl: '', fullName: 'fullName', gender: 'male');
  });

  tearDown(() {
    getIt.reset();
  });

  Future<ProfileDetailPageScreenState> pumpAndGetState(WidgetTester tester) async {
    await tester.runAsync(() async {
      await tester.pumpWidget(MaterialApp(
        home: ProfileDetailPage(isFinishRegisterDOP: false),
      ));
    });
    return tester.state(find.byType(ProfileDetailPage)) as ProfileDetailPageScreenState;
  }

  group('profile detail page test', () {
    testWidgets('test ', (WidgetTester tester) async {
      final ProfileDetailPageScreenState screenState = await pumpAndGetState(tester);
    });
    test('pushName', () {
      ProfileDetailPage.pushNamed(isFinishRegisterDOP: false);
      verify(() => getIt<CommonNavigator>().pushNamed(
            any(),
            Screen.profileDetailScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });
    testWidgets('test handleDeactivateAccountFail', (WidgetTester tester) async {
      final ProfileDetailPageScreenState screenState = await pumpAndGetState(tester);
      screenState.handleDeactivateAccountState(DeactivateFailState());
      verify(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: any(named: 'dialogId'),
            title: EvoStrings.deactivateAccountFailTitle,
            content: EvoStrings.deactivateAccountFailNotice,
            textPositive: EvoStrings.close,
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            titleTextStyle: any(named: 'titleTextStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });

    testWidgets('test handleConfirmDeactivateAccount', (WidgetTester tester) async {
      final ProfileDetailPageScreenState screenState = await pumpAndGetState(tester);
      screenState.handleDeactivateAccountState(ConfirmDeactivateState());

      verify(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: EvoDialogId.confirmDeactivateAccountBottomSheet,
            isShowButtonClose: true,
            header: any(named: 'header'),
            title: EvoStrings.confirmDeactivateTitle,
            content: EvoStrings.confirmDeactivateNotice,
            textPositive: EvoStrings.confirmDeactivatePositiveButton,
            textNegative: EvoStrings.confirmDeactivateNegativeButton,
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            titleTextStyle: any(named: 'titleTextStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });

    testWidgets('test handleDeactivateAccountSuccess', (WidgetTester tester) async {
      final ProfileDetailPageScreenState screenState = await pumpAndGetState(tester);
      screenState.handleDeactivateAccountState(DeactivateSuccessState(''));
      verify(
        () => commonNavigator.removeUntilAndPushReplacementNamed(
            mockNavigatorContext, Screen.mainScreen.name, any(),
            extra: any(named: 'extra')),
      ).called(1);
    });

    testWidgets('test handleDeactivateAccountState', (WidgetTester tester) async {
      final ProfileDetailPageScreenState screenState = await pumpAndGetState(tester);
      screenState.handleDeactivateAccountState(LoadingState());
      verify(() => EvoUiUtils().showHudLoading(loadingText: any(named: 'loadingText'))).called(1);
    });

    testWidgets('test onMoreMenuClick', (WidgetTester tester) async {
      final ProfileDetailPageScreenState screenState = await pumpAndGetState(tester);
      screenState.onMoreMenuClick();
      verify(() => EvoDialogHelper().showMultiActionBottomSheet(
            any(),
            any(),
            onSelectedItem: any(named: 'onSelectedItem'),
          )).called(1);
    });

    testWidgets('test genderGroup', (WidgetTester tester) async {
      final ProfileDetailPageScreenState screenState = await pumpAndGetState(tester);
      when(() => getIt<FeatureToggle>().enableRevampUiFeature).thenReturn(true);
      final Widget widget = screenState.genderGroup(Gender.male);
      expect(widget, isA<Padding>());
      when(() => getIt<FeatureToggle>().enableRevampUiFeature).thenReturn(false);
      final Widget widgetRevamp = screenState.genderGroup(Gender.male);
      expect(widgetRevamp, isA<Padding>());
    });

    testWidgets('test fullNameWidget', (WidgetTester tester) async {
      final ProfileDetailPageScreenState screenState = await pumpAndGetState(tester);
      final Widget fullNameWidget = screenState.fullNameWidget();
      expect(fullNameWidget, isA<ProfileDetailTextField>());
    });

    testWidgets('test onRefreshData', (WidgetTester tester) async {
      final ProfileDetailPageScreenState screenState = await pumpAndGetState(tester);
      screenState.onRefreshData();
      await tester.pump(Duration(milliseconds: 100));
      verify(() => mockUserRepo.getUserInfo(mockConfig: any(named: 'mockConfig'))).called(1);
    });
  });
}
