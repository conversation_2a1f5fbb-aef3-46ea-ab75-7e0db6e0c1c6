import 'package:evoapp/feature/profile/profile_detail_screen/other_widgets/list_action_bottom_sheet.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';
import '../../../../util/flutter_test_config.dart';

void main() {
  late CommonNavigator commonNavigator;
  late BuildContext mockNavigatorContext;

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();

    getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());
    commonNavigator = getIt<CommonNavigator>();

    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);
  });

  group('ListActionBottomSheet', () {
    testWidgets('displays list of actions', (WidgetTester tester) async {
      final List<ActionBottomSheet> actions = <ActionBottomSheet>[
        ActionBottomSheet(title: 'Action 1', icon: Icon(Icons.add)),
        ActionBottomSheet(title: 'Action 2', icon: Icon(Icons.remove)),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ListActionBottomSheet(
              list: actions,
              onPress: (_) {},
            ),
          ),
        ),
      );

      expect(find.text('Action 1'), findsOneWidget);
      expect(find.text('Action 2'), findsOneWidget);
      expect(find.byIcon(Icons.add), findsOneWidget);
      expect(find.byIcon(Icons.remove), findsOneWidget);
    });

    testWidgets('calls onPress when an action is tapped', (WidgetTester tester) async {
      final List<ActionBottomSheet> actions = <ActionBottomSheet>[
        ActionBottomSheet(title: 'Action 1', icon: Icon(Icons.add)),
      ];

      bool onPressCalled = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ListActionBottomSheet(
              list: actions,
              onPress: (ActionBottomSheet action) {
                onPressCalled = true;
                expect(action.title, 'Action 1');
              },
            ),
          ),
        ),
      );

      await tester.tap(find.text('Action 1'));
      await tester.pumpAndSettle();

      expect(onPressCalled, true);
      verify(() => commonNavigator.pop(mockNavigatorContext)).called(1);
    });

    testWidgets('displays nothing when list is empty', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ListActionBottomSheet(
              list: <ActionBottomSheet>[],
              onPress: (_) {},
            ),
          ),
        ),
      );

      expect(find.byType(ListView), findsNothing);
    });
  });
}
