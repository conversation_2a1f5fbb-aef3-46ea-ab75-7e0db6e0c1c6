import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/profile/profile_settings/profile_settings_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';

class BiometricsAuthenticateMock extends Mock implements BiometricsAuthenticate {}

class EvoLocalStorageHelperMock extends Mock implements EvoLocalStorageHelper {}

class BiometricsTokenModuleMock extends Mock implements BiometricsTokenModule {}

void main() {
  late BiometricsAuthenticateMock biometricsAuthenticateMock;
  late EvoLocalStorageHelperMock evoLocalStorageHelperMock;
  late BiometricsTokenModuleMock biometricsTokenModuleMock;
  late AppState appState;

  setUpAll(() {
    biometricsAuthenticateMock = BiometricsAuthenticateMock();
    evoLocalStorageHelperMock = EvoLocalStorageHelperMock();
    biometricsTokenModuleMock = BiometricsTokenModuleMock();
    appState = AppState();

    getIt.registerLazySingleton<AppState>(() => appState);
    getIt.registerLazySingleton<BiometricsTokenModule>(() => biometricsTokenModuleMock);
  });

  tearDown(() {
    reset(biometricsAuthenticateMock);
  });

  tearDownAll(() {
    getIt.reset();
  });

  ProfileSettingCubit createCubit() {
    return ProfileSettingCubit(
      bioAuth: biometricsAuthenticateMock,
      secureStorageHelper: evoLocalStorageHelperMock,
    );
  }

  group('test constructor()', () {
    test(
        'create new instance ProfileSettingCubit with ProfileSettingsBioState state and correct data',
        () {
      final ProfileSettingCubit cubit = createCubit();

      expect(cubit.state is ProfileSettingsBioState, isTrue);
      expect((cubit.state as ProfileSettingsBioState).isEnableAuthByBiometrics, false);
      expect((cubit.state as ProfileSettingsBioState).isDeviceSupportBiometrics, false);
      expect((cubit.state as ProfileSettingsBioState).isBiometricTokenUnUsable, false);
    });
  });

  group('test getBioAuthInfo function', () {
    blocTest<ProfileSettingCubit, ProfileSettingsState>(
      'test getBioAuthInfo with device is not support biometric',
      setUp: () {
        when(() => biometricsAuthenticateMock.isDeviceSupportBiometrics())
            .thenAnswer((_) async => false);
      },
      build: () => createCubit(),
      act: (ProfileSettingCubit cubit) => cubit.getBioAuthInfo(),
      expect: () => <dynamic>[
        isA<ProfileSettingsBioState>()
            .having((ProfileSettingsBioState state) => state.isDeviceSupportBiometrics,
                'verify isDeviceSupportBiometrics value must be false', false)
            .having((ProfileSettingsBioState state) => state.isBiometricTokenUnUsable,
                'verify isBiometricTokenUnUsable value must be false', false)
            .having((ProfileSettingsBioState state) => state.isEnableAuthByBiometrics,
                'verify isEnableAuthByBiometrics value must be false', false),
      ],
    );

    blocTest<ProfileSettingCubit, ProfileSettingsState>(
      'test getBioAuthInfo with device is support biometric, '
      'isBiometricTokenUnUsable is true, isEnableAuthByBiometrics is true',
      setUp: () {
        when(() => biometricsAuthenticateMock.isDeviceSupportBiometrics())
            .thenAnswer((_) async => true);

        when(() => biometricsTokenModuleMock.isEnableBiometricAuthenticator())
            .thenAnswer((_) async => true);

        when(() => biometricsTokenModuleMock.isBiometricTokenUsable())
            .thenAnswer((_) async => false);

        when(() => biometricsTokenModuleMock.disableBiometricAuthenticatorFeature())
            .thenAnswer((_) async {});

        when(() => evoLocalStorageHelperMock.isEnableBiometricAuthenticator())
            .thenAnswer((_) async => true);
      },
      build: () => createCubit(),
      act: (ProfileSettingCubit cubit) => cubit.getBioAuthInfo(),
      expect: () => <dynamic>[
        isA<ProfileSettingsBioState>()
            .having((ProfileSettingsBioState state) => state.isDeviceSupportBiometrics,
                'verify isDeviceSupportBiometrics value must be true', true)
            .having((ProfileSettingsBioState state) => state.isBiometricTokenUnUsable,
                'verify isBiometricTokenUnUsable value must be true', true)
            .having((ProfileSettingsBioState state) => state.isEnableAuthByBiometrics,
                'verify isEnableAuthByBiometrics value must be true', true),
      ],
    );

    blocTest<ProfileSettingCubit, ProfileSettingsState>(
      'test getBioAuthInfo with device is support biometric, '
      'isBiometricTokenUnUsable is false, isEnableAuthByBiometrics is false',
      setUp: () {
        when(() => biometricsAuthenticateMock.isDeviceSupportBiometrics())
            .thenAnswer((_) async => true);

        when(() => biometricsTokenModuleMock.isEnableBiometricAuthenticator())
            .thenAnswer((_) async => true);

        when(() => biometricsTokenModuleMock.isBiometricTokenUsable())
            .thenAnswer((_) async => true);

        when(() => evoLocalStorageHelperMock.isEnableBiometricAuthenticator())
            .thenAnswer((_) async => false);
      },
      build: () => createCubit(),
      act: (ProfileSettingCubit cubit) => cubit.getBioAuthInfo(),
      expect: () => <dynamic>[
        isA<ProfileSettingsBioState>()
            .having((ProfileSettingsBioState state) => state.isDeviceSupportBiometrics,
                'verify isDeviceSupportBiometrics value must be true', true)
            .having((ProfileSettingsBioState state) => state.isBiometricTokenUnUsable,
                'verify isBiometricTokenUnUsable value must be false', false)
            .having((ProfileSettingsBioState state) => state.isEnableAuthByBiometrics,
                'verify isEnableAuthByBiometrics value must be false', false),
      ],
    );
  });

  group('test showBiometricAuthAlertMessage function', () {
    blocTest<ProfileSettingCubit, ProfileSettingsState>(
      'test showBiometricAuthAlertMessage with isEnableSuccess is true',
      setUp: () {},
      build: () => createCubit(),
      act: (ProfileSettingCubit cubit) =>
          cubit.showBiometricAuthAlertMessage(isEnableSuccess: true),
      expect: () => <dynamic>[
        isA<EnableAuthBioSuccess>().having((EnableAuthBioSuccess state) => state.isEnable,
            'verify isEnable value must be true', true)
      ],
    );

    blocTest<ProfileSettingCubit, ProfileSettingsState>(
      'test showBiometricAuthAlertMessage with isEnableSuccess is false',
      setUp: () {},
      build: () => createCubit(),
      act: (ProfileSettingCubit cubit) =>
          cubit.showBiometricAuthAlertMessage(isEnableSuccess: false),
      expect: () => <dynamic>[
        isA<EnableAuthBioSuccess>().having((EnableAuthBioSuccess state) => state.isEnable,
            'verify isEnable value must be false', false)
      ],
    );
  });

  group('test reloadBiometricInfo function', () {
    blocTest<ProfileSettingCubit, ProfileSettingsState>('test reloadBiometricInfo',
        setUp: () {
          when(() => biometricsAuthenticateMock.isDeviceSupportBiometrics())
              .thenAnswer((_) async => false);
        },
        build: () => createCubit(),
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (ProfileSettingCubit cubit) => cubit.reloadBiometricInfo(true),
        expect: () => <dynamic>[
              isA<EnableAuthBioSuccess>().having((EnableAuthBioSuccess state) => state.isEnable,
                  'verify isEnable must be true', true),
              isA<ProfileSettingsBioState>()
                  .having((ProfileSettingsBioState state) => state.isDeviceSupportBiometrics,
                      'verify isDeviceSupportBiometrics value must be false', false)
                  .having((ProfileSettingsBioState state) => state.isBiometricTokenUnUsable,
                      'verify isBiometricTokenUnUsable value must be false', false)
                  .having((ProfileSettingsBioState state) => state.isEnableAuthByBiometrics,
                      'verify isEnableAuthByBiometrics value must be false', false),
            ],
        verify: (_) {
          verify(() => biometricsAuthenticateMock.isDeviceSupportBiometrics()).called(1);
        });
  });
}
