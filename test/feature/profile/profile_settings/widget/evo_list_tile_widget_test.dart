// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.
import 'package:evoapp/feature/profile/profile_settings/widget/evo_list_tile_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';

import '../../../../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('EvoListTile Tests', () {
    testWidgets('should render with only title', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Title';

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(title: title),
          ),
        ),
      );

      // Assert
      expect(find.byType(EvoListTile), findsOneWidget);
      expect(find.text(title), findsOneWidget);
      expect(find.byType(Container), findsOneWidget);
      expect(find.byType(Row), findsOneWidget);
      expect(find.byType(Expanded), findsOneWidget);
      expect(find.byType(Text), findsOneWidget);
    });

    testWidgets('should render with leading widget', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Title';
      const Icon leadingIcon = Icon(Icons.settings);

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(
              title: title,
              leadingWidget: leadingIcon,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(EvoListTile), findsOneWidget);
      expect(find.text(title), findsOneWidget);
      expect(find.byIcon(Icons.settings), findsOneWidget);

      // Verify the Row has correct children count (leading + spacing + expanded text)
      final Finder rowFinder = find.byType(Row);
      expect(rowFinder, findsOneWidget);

      final Row row = tester.widget<Row>(rowFinder);
      expect(row.children.length, 3); // Icon, SizedBox, Expanded
    });

    testWidgets('should render with trailing widget', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Title';
      const Icon trailingIcon = Icon(Icons.arrow_forward);

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(
              title: title,
              trailingWidget: trailingIcon,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(EvoListTile), findsOneWidget);
      expect(find.text(title), findsOneWidget);
      expect(find.byIcon(Icons.arrow_forward), findsOneWidget);

      // Verify the Row has correct children count (expanded text + spacing + trailing)
      final Finder rowFinder = find.byType(Row);
      expect(rowFinder, findsOneWidget);

      final Row row = tester.widget<Row>(rowFinder);
      expect(row.children.length, 3); // Expanded, SizedBox, Icon
    });

    testWidgets('should render with both leading and trailing widgets',
        (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Title';
      const Icon leadingIcon = Icon(Icons.settings);
      const Icon trailingIcon = Icon(Icons.arrow_forward);

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(
              title: title,
              leadingWidget: leadingIcon,
              trailingWidget: trailingIcon,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(EvoListTile), findsOneWidget);
      expect(find.text(title), findsOneWidget);
      expect(find.byIcon(Icons.settings), findsOneWidget);
      expect(find.byIcon(Icons.arrow_forward), findsOneWidget);

      // Verify the Row has correct children count (leading + spacing + expanded text + spacing + trailing)
      final Finder rowFinder = find.byType(Row);
      expect(rowFinder, findsOneWidget);

      final Row row = tester.widget<Row>(rowFinder);
      expect(row.children.length, 5); // Icon, SizedBox, Expanded, SizedBox, Icon
    });

    testWidgets('should apply custom background color', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Title';
      const Color backgroundColor = Colors.red;

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(
              title: title,
              backgroundColor: backgroundColor,
            ),
          ),
        ),
      );

      // Assert
      final Finder containerFinder = find.byType(Container);
      expect(containerFinder, findsOneWidget);

      final Container container = tester.widget<Container>(containerFinder);
      final BoxDecoration decoration = container.decoration as BoxDecoration;
      expect(decoration.color, backgroundColor);
    });

    testWidgets('should apply default transparent background when backgroundColor is null',
        (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Title';

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(title: title),
          ),
        ),
      );

      // Assert
      final Finder containerFinder = find.byType(Container);
      expect(containerFinder, findsOneWidget);

      final Container container = tester.widget<Container>(containerFinder);
      final BoxDecoration decoration = container.decoration as BoxDecoration;
      expect(decoration.color, Colors.transparent);
    });

    testWidgets('should apply custom padding', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Title';
      const EdgeInsets customPadding = EdgeInsets.all(20);

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(
              title: title,
              padding: customPadding,
            ),
          ),
        ),
      );

      // Assert
      final Finder containerFinder = find.byType(Container);
      expect(containerFinder, findsOneWidget);

      final Container container = tester.widget<Container>(containerFinder);
      expect(container.padding, customPadding);
    });

    testWidgets('should apply default zero padding when padding is null',
        (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Title';

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(title: title),
          ),
        ),
      );

      // Assert
      final Finder containerFinder = find.byType(Container);
      expect(containerFinder, findsOneWidget);

      final Container container = tester.widget<Container>(containerFinder);
      expect(container.padding, EdgeInsets.zero);
    });

    testWidgets('should apply custom border radius', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Title';
      const BorderRadius customBorderRadius = BorderRadius.all(Radius.circular(12));

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(
              title: title,
              borderRadius: customBorderRadius,
            ),
          ),
        ),
      );

      // Assert
      final Finder containerFinder = find.byType(Container);
      expect(containerFinder, findsOneWidget);

      final Container container = tester.widget<Container>(containerFinder);
      final BoxDecoration decoration = container.decoration as BoxDecoration;
      expect(decoration.borderRadius, customBorderRadius);
    });

    testWidgets('should apply default zero border radius when borderRadius is null',
        (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Title';

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(title: title),
          ),
        ),
      );

      // Assert
      final Finder containerFinder = find.byType(Container);
      expect(containerFinder, findsOneWidget);

      final Container container = tester.widget<Container>(containerFinder);
      final BoxDecoration decoration = container.decoration as BoxDecoration;
      expect(decoration.borderRadius, const BorderRadius.all(Radius.zero));
    });

    testWidgets('should have correct text styling', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Title';

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(title: title),
          ),
        ),
      );

      // Assert
      final Finder textFinder = find.byType(Text);
      expect(textFinder, findsOneWidget);

      final Text text = tester.widget<Text>(textFinder);
      expect(text.data, title);
      expect(text.style?.height, 1.42);
    });

    testWidgets('should have correct spacing between widgets', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Title';
      const Icon leadingIcon = Icon(Icons.settings);
      const Icon trailingIcon = Icon(Icons.arrow_forward);

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(
              title: title,
              leadingWidget: leadingIcon,
              trailingWidget: trailingIcon,
            ),
          ),
        ),
      );

      // Assert - Find SizedBox widgets that are direct children of Row and used for spacing
      final Finder rowFinder = find.byType(Row);
      final Row row = tester.widget<Row>(rowFinder);

      // Count spacing SizedBox widgets (those with width 12 and no height)
      int spacingSizedBoxCount = 0;
      for (final Widget child in row.children) {
        if (child is SizedBox && child.width == 12 && child.height == null) {
          spacingSizedBoxCount++;
        }
      }

      expect(spacingSizedBoxCount, 2); // Two spacing widgets
    });

    testWidgets('should render with complex widgets as leading and trailing',
        (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Title';
      const Widget complexLeading = Column(
        children: <Widget>[
          Icon(Icons.settings),
          Text('Settings'),
        ],
      );
      const Widget complexTrailing = Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text('Value'),
          Icon(Icons.arrow_forward),
        ],
      );

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(
              title: title,
              leadingWidget: complexLeading,
              trailingWidget: complexTrailing,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(EvoListTile), findsOneWidget);
      expect(find.text(title), findsOneWidget);
      expect(find.text('Settings'), findsOneWidget);
      expect(find.text('Value'), findsOneWidget);
      expect(find.byIcon(Icons.settings), findsOneWidget);
      expect(find.byIcon(Icons.arrow_forward), findsOneWidget);
      expect(find.byType(Column), findsOneWidget);
      expect(find.byType(Row), findsNWidgets(2)); // Main Row + trailing Row
    });

    testWidgets('should handle empty title string', (WidgetTester tester) async {
      // Arrange
      const String title = '';

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(title: title),
          ),
        ),
      );

      // Assert
      expect(find.byType(EvoListTile), findsOneWidget);
      expect(find.text(title), findsOneWidget);
      expect(find.byType(Text), findsOneWidget);
    });

    testWidgets('should handle very long title text', (WidgetTester tester) async {
      // Arrange
      const String longTitle =
          'This is a very long title that should be handled properly by the widget and should expand to take available space';

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              width: 300,
              child: const EvoListTile(title: longTitle),
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(EvoListTile), findsOneWidget);
      expect(find.text(longTitle), findsOneWidget);

      // Verify the text is in an Expanded widget
      final Finder expandedFinder = find.byType(Expanded);
      expect(expandedFinder, findsOneWidget);

      final Expanded expanded = tester.widget<Expanded>(expandedFinder);
      expect(expanded.child, isA<Text>());
    });

    testWidgets('should maintain proper widget hierarchy', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Title';
      const Icon leadingIcon = Icon(Icons.settings);

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(
              title: title,
              leadingWidget: leadingIcon,
            ),
          ),
        ),
      );

      // Assert widget hierarchy
      expect(find.byType(EvoListTile), findsOneWidget);

      // Container should be the root widget
      expect(
        find.descendant(
          of: find.byType(EvoListTile),
          matching: find.byType(Container),
        ),
        findsOneWidget,
      );

      // Row should be inside Container
      expect(
        find.descendant(
          of: find.byType(Container),
          matching: find.byType(Row),
        ),
        findsOneWidget,
      );

      // Expanded should be inside Row
      expect(
        find.descendant(
          of: find.byType(Row),
          matching: find.byType(Expanded),
        ),
        findsOneWidget,
      );

      // Text should be inside Expanded
      expect(
        find.descendant(
          of: find.byType(Expanded),
          matching: find.byType(Text),
        ),
        findsOneWidget,
      );
    });

    testWidgets('should apply all custom properties simultaneously', (WidgetTester tester) async {
      // Arrange
      const String title = 'Test Title';
      const Icon leadingIcon = Icon(Icons.settings);
      const Icon trailingIcon = Icon(Icons.arrow_forward);
      const Color backgroundColor = Colors.blue;
      const EdgeInsets customPadding = EdgeInsets.symmetric(horizontal: 16, vertical: 8);
      const BorderRadius customBorderRadius = BorderRadius.all(Radius.circular(8));

      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoListTile(
              title: title,
              leadingWidget: leadingIcon,
              trailingWidget: trailingIcon,
              backgroundColor: backgroundColor,
              padding: customPadding,
              borderRadius: customBorderRadius,
            ),
          ),
        ),
      );

      // Assert all properties are applied
      expect(find.byType(EvoListTile), findsOneWidget);
      expect(find.text(title), findsOneWidget);
      expect(find.byIcon(Icons.settings), findsOneWidget);
      expect(find.byIcon(Icons.arrow_forward), findsOneWidget);

      final Finder containerFinder = find.byType(Container);
      final Container container = tester.widget<Container>(containerFinder);
      final BoxDecoration decoration = container.decoration as BoxDecoration;

      expect(decoration.color, backgroundColor);
      expect(decoration.borderRadius, customBorderRadius);
      expect(container.padding, customPadding);
    });
  });
}
