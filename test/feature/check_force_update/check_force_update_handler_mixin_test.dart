import 'package:evoapp/data/repository/common_repo.dart';
import 'package:evoapp/data/response/force_update_entity.dart';
import 'package:evoapp/feature/check_force_update/check_force_update_handler_mixin.dart';
import 'package:evoapp/feature/check_force_update/force_update_ui_model.dart';
import 'package:evoapp/feature/logging/evo_logging_event.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/widget/evo_overlay/evo_overlay_util_functions.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/in_app_update/common_in_app_update.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';
import '../../util/test_util.dart';

class MockBuildContext extends Mock implements BuildContext {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockCommonRepo extends Mock implements CommonRepo {}

class MockAppState extends Mock implements AppState {}

class TestCheckForceUpdateHandler with CheckForceUpdateHandlerMixin {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockEvoPlatformWrapper extends Mock implements EvoFlutterWrapper {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockEvoImageProvider extends Mock implements CommonImageProvider {}

class MockEvoOverlayUtilFunctions extends Mock implements EvoOverlayUtilFunctions {}

class FakeForceUpdateUIModel extends Fake implements ForceUpdateUIModel {}

class MockInAppUpdateWrapper extends Mock implements InAppUpdateWrapper {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late TestCheckForceUpdateHandler checkForceUpdateHandler;
  late EvoLocalStorageHelper localStorageHelper;
  late CommonRepo commonRepo;
  late AppState appState;
  late CommonNavigator commonNavigator;
  late EvoFlutterWrapper evoPlatformWrapper;
  late EvoUtilFunction evoUtilFunction;
  late CommonImageProvider commonImageProvider;
  late CommonUtilFunction commonUtilFunction;
  late BuildContext mockNavigatorContext;
  late MockEvoOverlayUtilFunctions mockEvoOverlayUtilFunctions;
  late MockLoggingRepo mockLoggingRepo;
  late MockInAppUpdateWrapper mockInAppUpdateWrapper;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(FakeForceUpdateUIModel());

    localStorageHelper = MockEvoLocalStorageHelper();
    commonRepo = MockCommonRepo();
    appState = MockAppState();
    commonNavigator = MockCommonNavigator();
    evoPlatformWrapper = MockEvoPlatformWrapper();
    evoUtilFunction = MockEvoUtilFunction();
    commonImageProvider = MockEvoImageProvider();
    commonUtilFunction = MockCommonUtilFunction();
    mockEvoOverlayUtilFunctions = MockEvoOverlayUtilFunctions();
    mockLoggingRepo = MockLoggingRepo();
    mockInAppUpdateWrapper = MockInAppUpdateWrapper();
    setUtilsMockInstanceForTesting();

    /// Inject the mocked dependencies
    getIt.registerSingleton<EvoLocalStorageHelper>(localStorageHelper);
    getIt.registerSingleton<CommonRepo>(commonRepo);
    getIt.registerSingleton<AppState>(appState);
    getIt.registerSingleton<CommonNavigator>(commonNavigator);
    getIt.registerSingleton<EvoFlutterWrapper>(evoPlatformWrapper);
    getIt.registerSingleton<EvoUtilFunction>(evoUtilFunction);
    getIt.registerSingleton<CommonImageProvider>(commonImageProvider);
    getIt.registerSingleton<CommonUtilFunction>(commonUtilFunction);
    getIt.registerLazySingleton<EvoOverlayUtilFunctions>(() => mockEvoOverlayUtilFunctions);
    getIt.registerSingleton<LoggingRepo>(mockLoggingRepo);
    getIt.registerSingleton<InAppUpdateWrapper>(mockInAppUpdateWrapper);

    /// Register a fallback value
    registerFallbackValue(MockBuildContext());
    registerFallbackValue(EvoDialogId.newAppVersionBottomSheet);
    registerFallbackValue(CommonLaunchUrlMode.externalApplication);
    registerFallbackValue(EvoEventType.appUpdate);

    checkForceUpdateHandler = TestCheckForceUpdateHandler();

    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);
  });

  tearDownAll(() {
    resetUtilMockToOriginalInstance();
  });

  setUp(() {
    when(() => commonImageProvider.asset(
          any(),
          fit: any(named: 'fit'),
        )).thenReturn(Container());
  });

  tearDown(() {
    reset(commonImageProvider);
  });

  void stubShowDialogBottomSheet() {
    when(() => EvoDialogHelper().showDialogBottomSheet(
          dialogId: any(named: 'dialogId'),
          header: any(named: 'header'),
          title: any(named: 'title'),
          isDismissible: any(named: 'isDismissible'),
          textNegative: any(named: 'textNegative'),
          textPositive: any(named: 'textPositive'),
          content: any(named: 'content'),
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
        )).thenAnswer((_) => Future<void>.value());

    when(() => commonImageProvider.asset(any(), width: any(named: 'width'), fit: any(named: 'fit')))
        .thenReturn(const SizedBox());
  }

  void stubShowForceUpdateOverlay() {
    when(() => mockEvoOverlayUtilFunctions.showForceUpdateOverlay(any())).thenAnswer((_) {
      return Future<void>.value();
    });
  }

  group('test AppleStoreUrl & GooglePlayStoreUrl', () {
    test('test AppleStoreUrl', () {
      expect(CheckForceUpdateHandlerMixin.urlStoreIos, 'https://apps.apple.com/app/id/**********');
    });

    test('test GooglePlayStoreUrl', () {
      expect(CheckForceUpdateHandlerMixin.urlStoreAndroid,
          'https://play.google.com/store/apps/details?id=vn.goevo.evo');
    });
  });

  group('test checkForceUpdate() ', () {
    setUp(() {
      stubShowDialogBottomSheet();
      stubShowForceUpdateOverlay();

      when(() => appState.appVersion).thenReturn('1.0.0');
    });

    tearDown(() {
      reset(commonImageProvider);
      reset(evoUtilFunction);
      reset(mockEvoOverlayUtilFunctions);
    });

    test(
        'delete EvoSecureStorageHelperImpl.latestVersionIgnore in localStorage'
        'if forceUpdateEntity.hasNewerVersion == FALSE', () async {
      /// Arrange
      final Map<String, dynamic> responseData =
          await TestUtil.getResponseMock('check_force_update_has_new_version_false.json');

      when(() => commonRepo.getForceUpdate(mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) async {
        return ForceUpdateEntity.fromBaseResponse(
            BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));
      });

      when(() => localStorageHelper.delete(key: any(named: 'key')))
          .thenAnswer((_) => Future<void>.value());

      /// Act
      await checkForceUpdateHandler.checkForceUpdate();

      /// Assert
      verify(() => localStorageHelper.delete(key: EvoSecureStorageHelperImpl.latestVersionIgnore))
          .called(1);
    });

    testWidgets(
        'forceUpdateEntity.hasNewerVersion == TRUE'
        ' && forceUpdateEntity.forceToUpdate == FALSE', (WidgetTester widgetTester) async {
      /// Arrange
      /// mock API return version 1.0.1 & hasForceUpdate = true
      ///
      when(() => evoPlatformWrapper.isIOS()).thenReturn(true);
      final Map<String, dynamic> responseData =
          await TestUtil.getResponseMock('check_force_update_is_true.json');

      when(() => commonRepo.getForceUpdate(mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) async {
        return ForceUpdateEntity.fromBaseResponse(
            BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));
      });

      /// Act
      await checkForceUpdateHandler.checkForceUpdate();

      /// Assert
      verify(() => mockEvoOverlayUtilFunctions.showForceUpdateOverlay(any())).called(1);

      verifyNever(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: EvoDialogId.newAppVersionBottomSheet,
            header: any(named: 'header'),
            title: EvoStrings.forceUpdateSubDesc,
            textPositive: EvoStrings.forceUpdateAgree,
            content: EvoStrings.forceUpdateDescription,
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
          ));
    });
  });

  group('test handleIgnoreUpdate method', () {
    test('should call handleIgnoreUpdate correctly', () async {
      /// Arrange
      when(() => localStorageHelper.setLatestVersionIgnore(any()))
          .thenAnswer((_) => Future<void>.value());

      when(() => commonNavigator.pop(mockNavigatorContext, result: any(named: 'result')))
          .thenAnswer((_) {});

      const String version = '1.0.0';

      /// Act
      await checkForceUpdateHandler.handleIgnoreUpdate(version);

      /// Assert
      verify(() => localStorageHelper.setLatestVersionIgnore(version)).called(1);
      verify(() => mockNavigatorContext.pop()).called(1);
    });
  });

  group('test handleOpenStore method', () {
    test('handleOpenStore should open iOS store', () async {
      /// Arrange
      when(() => evoPlatformWrapper.isIOS()).thenReturn(true);
      when(() => evoPlatformWrapper.isAndroid()).thenReturn(false);
      when(() => commonUtilFunction.commonLaunchUrlString(any(), mode: any(named: 'mode')))
          .thenAnswer((_) => Future<void>.value());

      /// Act
      await checkForceUpdateHandler.handleOpenStore();

      /// Assert
      verify(() => commonUtilFunction.commonLaunchUrlString(
          CheckForceUpdateHandlerMixin.urlStoreIos,
          mode: CommonLaunchUrlMode.externalApplication)).called(1);
      verifyNever(() => commonUtilFunction.commonLaunchUrlString(
          CheckForceUpdateHandlerMixin.urlStoreAndroid,
          mode: any(named: 'mode')));
    });

    test('handleOpenStore should open Android store', () async {
      /// Arrange
      when(() => evoPlatformWrapper.isIOS()).thenReturn(false);
      when(() => evoPlatformWrapper.isAndroid()).thenReturn(true);
      when(() => commonUtilFunction.commonLaunchUrlString(any()))
          .thenAnswer((_) => Future<void>.value());

      /// Act
      await checkForceUpdateHandler.handleOpenStore();

      /// Assert
      verify(() => commonUtilFunction.commonLaunchUrlString(
          CheckForceUpdateHandlerMixin.urlStoreAndroid,
          mode: CommonLaunchUrlMode.externalApplication)).called(1);
      verifyNever(() => commonUtilFunction.commonLaunchUrlString(
          CheckForceUpdateHandlerMixin.urlStoreIos,
          mode: any(named: 'mode')));
    });
  });

  group('test showRequestUpdateUI method', () {
    setUp(() {
      stubShowDialogBottomSheet();
      stubShowForceUpdateOverlay();
    });

    testWidgets('should show dialog correctly', (WidgetTester widgetTester) async {
      /// Act
      await checkForceUpdateHandler.showRequestUpdateUI(isForceUpdate: true);

      /// Assert
      verify(() => commonImageProvider.asset(
            EvoImages.bgAppUpdate,
            fit: BoxFit.fitWidth,
          )).called(1);

      verify(() => mockEvoOverlayUtilFunctions.showForceUpdateOverlay(any())).called(1);

      verifyNever(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: EvoDialogId.newAppVersionBottomSheet,
            header: any(named: 'header'),
            title: EvoStrings.forceUpdateSubDesc,
            isDismissible: false,
            textPositive: EvoStrings.forceUpdateAgree,
            content: EvoStrings.forceUpdateDescription,
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
          ));
    });
  });

  group('test handleAppUpdate method', () {
    setUp(() {
      stubShowDialogBottomSheet();
      stubShowForceUpdateOverlay();
    });

    tearDown(() {
      reset(mockEvoOverlayUtilFunctions);
    });

    testWidgets('do nothing if currentVersion and ForceUpdateEntity.latestVersion is same',
        (WidgetTester widgetTester) async {
      /// Arrange
      final ForceUpdateEntity forceUpdateEntity = ForceUpdateEntity(
        hasNewerVersion: true,
        forceToUpdate: true,
        latestVersion: '2.0.0',
      );

      when(() => appState.appVersion).thenReturn('2.0.0');

      /// Act
      await checkForceUpdateHandler.handleAppUpdate(forceUpdateEntity);

      //Assert
      verifyNever(() => mockEvoOverlayUtilFunctions.showForceUpdateOverlay(any()));

      verifyNever(
        () => EvoDialogHelper().showDialogBottomSheet(
          dialogId: EvoDialogId.newAppVersionBottomSheet,
          header: any(named: 'header'),
          title: any(named: 'title'),
          isDismissible: any(named: 'isDismissible'),
          textNegative: any(named: 'textNegative'),
          textPositive: any(named: 'textPositive'),
          content: any(named: 'content'),
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
        ),
      );
    });

    testWidgets(
        'should show overlay if ForceUpdateEntity.isForceUpdate is true and platform is iOS',
        (WidgetTester widgetTester) async {
      /// Arrange
      when(() => evoPlatformWrapper.isIOS()).thenReturn(true);
      final ForceUpdateEntity forceUpdateEntity = ForceUpdateEntity(
        hasNewerVersion: true,
        forceToUpdate: true,
        latestVersion: '2.0.0',
      );
      when(() => appState.appVersion).thenReturn('1.0.0');

      /// Act
      await checkForceUpdateHandler.handleAppUpdate(forceUpdateEntity);

      /// Assert
      verify(() => mockEvoOverlayUtilFunctions.showForceUpdateOverlay(any())).called(1);
      verifyNever(() => mockInAppUpdateWrapper.isUpdateAvailable());

      verifyNever(
        () => EvoDialogHelper().showDialogBottomSheet(
          dialogId: EvoDialogId.newAppVersionBottomSheet,
          header: any(named: 'header'),
          title: EvoStrings.forceUpdateSubDesc,
          textPositive: EvoStrings.forceUpdateAgree,
          content: EvoStrings.forceUpdateDescription,
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
        ),
      );
    });

    testWidgets(
        'do nothing if ForceUpdateEntity.isForceUpdate is true and platform is android and update is not available',
        (WidgetTester widgetTester) async {
      /// Arrange
      when(() => evoPlatformWrapper.isIOS()).thenReturn(false);
      when(() => mockInAppUpdateWrapper.isUpdateAvailable())
          .thenAnswer((_) => Future<bool>.value(false));

      final ForceUpdateEntity forceUpdateEntity = ForceUpdateEntity(
        hasNewerVersion: true,
        forceToUpdate: true,
        latestVersion: '2.0.0',
      );
      when(() => appState.appVersion).thenReturn('1.0.0');

      /// Act
      await checkForceUpdateHandler.handleAppUpdate(forceUpdateEntity);

      /// Assert
      verify(() => mockInAppUpdateWrapper.isUpdateAvailable());
      verifyNever(() => mockInAppUpdateWrapper.startImmediateUpdate());
    });

    testWidgets(
        'should call startImmediateUpdate if ForceUpdateEntity.isForceUpdate is true and platform is android and update success',
        (WidgetTester widgetTester) async {
      /// Arrange
      when(() => evoPlatformWrapper.isIOS()).thenReturn(false);
      when(() => mockInAppUpdateWrapper.isUpdateAvailable())
          .thenAnswer((_) => Future<bool>.value(true));
      when(() => mockInAppUpdateWrapper.startImmediateUpdate())
          .thenAnswer((_) => Future<AppUpdateResult>.value(AppUpdateResult.success));

      final ForceUpdateEntity forceUpdateEntity = ForceUpdateEntity(
        hasNewerVersion: true,
        forceToUpdate: true,
        latestVersion: '2.0.0',
      );
      when(() => appState.appVersion).thenReturn('1.0.0');

      /// Act
      await checkForceUpdateHandler.handleAppUpdate(forceUpdateEntity);

      /// Assert
      verify(() => mockInAppUpdateWrapper.isUpdateAvailable()).called(1);
      verify(() => mockInAppUpdateWrapper.startImmediateUpdate()).called(1);
    });

    testWidgets(
        'should log event if ForceUpdateEntity.isForceUpdate is true and platform is android, and update result is fail',
        (WidgetTester widgetTester) async {
      /// Arrange
      when(() => evoPlatformWrapper.isIOS()).thenReturn(false);
      when(() => mockInAppUpdateWrapper.isUpdateAvailable())
          .thenAnswer((_) => Future<bool>.value(true));
      when(() => mockInAppUpdateWrapper.startImmediateUpdate())
          .thenAnswer((_) => Future<AppUpdateResult>.value(AppUpdateResult.inAppUpdateFailed));
      when(
        () => mockLoggingRepo.logEvent(
          eventType: any(named: 'eventType'),
          data: any(named: 'data'),
        ),
      ).thenAnswer((_) async => Future<void>.value());

      final ForceUpdateEntity forceUpdateEntity = ForceUpdateEntity(
        hasNewerVersion: true,
        forceToUpdate: true,
        latestVersion: '2.0.0',
      );
      when(() => appState.appVersion).thenReturn('1.0.0');

      /// Act
      await checkForceUpdateHandler.handleAppUpdate(forceUpdateEntity);

      /// Assert
      verify(() => mockInAppUpdateWrapper.isUpdateAvailable()).called(1);
      verify(() => mockInAppUpdateWrapper.startImmediateUpdate()).called(1);
      expect(
          verify(() => mockLoggingRepo.logEvent(
                eventType: captureAny(named: 'eventType'),
                data: any(named: 'data'),
              )).captured.single,
          EvoEventType.appUpdate);
    });

    testWidgets(
        'should show force update dialog if ForceUpdateEntity.isForceUpdate is true and platform is android, and user by pass in app update',
        (WidgetTester widgetTester) async {
      /// Arrange
      when(() => evoPlatformWrapper.isIOS()).thenReturn(false);
      when(() => mockInAppUpdateWrapper.isUpdateAvailable())
          .thenAnswer((_) => Future<bool>.value(true));
      when(() => mockInAppUpdateWrapper.startImmediateUpdate())
          .thenAnswer((_) => Future<AppUpdateResult>.value(AppUpdateResult.userDeniedUpdate));

      final ForceUpdateEntity forceUpdateEntity = ForceUpdateEntity(
        hasNewerVersion: true,
        forceToUpdate: true,
        latestVersion: '2.0.0',
      );
      when(() => appState.appVersion).thenReturn('1.0.0');

      /// Act
      await checkForceUpdateHandler.handleAppUpdate(forceUpdateEntity);

      /// Assert
      verify(() => mockInAppUpdateWrapper.isUpdateAvailable()).called(1);
      verify(() => mockInAppUpdateWrapper.startImmediateUpdate()).called(1);
      verify(() => mockEvoOverlayUtilFunctions.showForceUpdateOverlay(any())).called(1);
    });

    testWidgets(
        'should show dialog if ForceUpdateEntity.isForceUpdate is FALSE and ForceUpdateEntity.latestVersion is NOT IGNORED and Platform is iOS',
        (WidgetTester widgetTester) async {
      /// Arrange

      when(() => evoPlatformWrapper.isIOS()).thenReturn(true);
      final ForceUpdateEntity forceUpdateEntity = ForceUpdateEntity(
        hasNewerVersion: true,
        forceToUpdate: false,
        latestVersion: '2.0.0',
      );

      when(() => appState.appVersion).thenReturn('1.0.0');

      when(() => localStorageHelper.getLatestVersionIgnore()).thenAnswer(
        (_) async {
          return '1.0.1';
        },
      );

      /// Act
      await checkForceUpdateHandler.handleAppUpdate(forceUpdateEntity);

      //Assert
      verifyNever(() => mockEvoOverlayUtilFunctions.showForceUpdateOverlay(any()));
      verifyNever(() => mockInAppUpdateWrapper.isUpdateAvailable());

      verify(
        () => EvoDialogHelper().showDialogBottomSheet(
          dialogId: EvoDialogId.newAppVersionBottomSheet,
          header: any(named: 'header'),
          title: EvoStrings.forceUpdateSubDesc,
          textNegative: EvoStrings.forceUpdateSkip,
          textPositive: EvoStrings.forceUpdateAgree,
          content: EvoStrings.forceUpdateDescription,
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
        ),
      ).called(1);
    });

    testWidgets(
        'should show dialog if ForceUpdateEntity.isForceUpdate is FALSE and ForceUpdateEntity.latestVersion is NOT IGNORED and Platform is Android',
        (WidgetTester widgetTester) async {
      /// Arrange

      when(() => evoPlatformWrapper.isIOS()).thenReturn(false);
      when(() => mockInAppUpdateWrapper.isUpdateAvailable())
          .thenAnswer((_) => Future<bool>.value(true));
      when(() => mockInAppUpdateWrapper.completeFlexibleUpdate())
          .thenAnswer((_) => Future<void>.value());
      when(() => mockInAppUpdateWrapper.startFlexibleUpdate())
          .thenAnswer((_) => Future<AppUpdateResult>.value(AppUpdateResult.success));
      final ForceUpdateEntity forceUpdateEntity = ForceUpdateEntity(
        hasNewerVersion: true,
        forceToUpdate: false,
        latestVersion: '2.0.0',
      );

      when(() => appState.appVersion).thenReturn('1.0.0');

      when(() => localStorageHelper.getLatestVersionIgnore()).thenAnswer(
        (_) async {
          return '1.0.1';
        },
      );

      /// Act
      await checkForceUpdateHandler.handleAppUpdate(forceUpdateEntity);

      //Assert
      verifyNever(() => mockEvoOverlayUtilFunctions.showForceUpdateOverlay(any()));
      verify(() => mockInAppUpdateWrapper.isUpdateAvailable()).called(1);
      verify(() => mockInAppUpdateWrapper.startFlexibleUpdate()).called(1);
      verify(() => mockInAppUpdateWrapper.completeFlexibleUpdate()).called(1);
    });

    testWidgets(
        'do nothing if ForceUpdateEntity.isForceUpdate is FALSE and ForceUpdateEntity.latestVersion is IGNORED',
        (WidgetTester widgetTester) async {
      /// Arrange
      final ForceUpdateEntity forceUpdateEntity = ForceUpdateEntity(
        hasNewerVersion: true,
        forceToUpdate: false,
        latestVersion: '2.0.0',
      );

      when(() => appState.appVersion).thenReturn('1.0.0');

      when(() => localStorageHelper.getLatestVersionIgnore()).thenAnswer(
        (_) async {
          return '2.0.0';
        },
      );

      /// Act
      await checkForceUpdateHandler.handleAppUpdate(forceUpdateEntity);

      //Assert
      verifyNever(() => mockEvoOverlayUtilFunctions.showForceUpdateOverlay(any()));

      verifyNever(
        () => EvoDialogHelper().showDialogBottomSheet(
          dialogId: EvoDialogId.newAppVersionBottomSheet,
          header: any(named: 'header'),
          title: any(named: 'title'),
          isDismissible: any(named: 'isDismissible'),
          textNegative: any(named: 'textNegative'),
          textPositive: any(named: 'textPositive'),
          content: any(named: 'content'),
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
        ),
      );
    });
  });

  group('verify createForceUpdateUIModel()', () {
    setUp(() {
      when(() => evoPlatformWrapper.isIOS()).thenReturn(true);
      when(() => commonUtilFunction.commonLaunchUrlString(
            any(),
            mode: any(named: 'mode'),
          )).thenAnswer((_) => Future<void>.value());
    });

    tearDown(() {
      reset(evoPlatformWrapper);
      reset(commonUtilFunction);
    });

    test('createForceUpdateUIModel returns correct model when isForceUpdate is false', () {
      bool onNegativeCalled = false;

      final ForceUpdateUIModel model = checkForceUpdateHandler.createForceUpdateUIModel(
        onNegativeClick: () {
          onNegativeCalled = true;
        },
      );

      expect(model.imageHeader, isNotNull);
      verify(() => commonImageProvider.asset(
            EvoImages.bgAppUpdate,
            fit: BoxFit.fitWidth,
          )).called(1);

      expect(model.dialogId, EvoDialogId.newAppVersionBottomSheet);
      expect(model.title, EvoStrings.forceUpdateSubDesc);
      expect(model.content, EvoStrings.forceUpdateDescription);
      expect(model.textNegative, EvoStrings.forceUpdateSkip);
      expect(model.textPositive, EvoStrings.forceUpdateAgree);

      model.onClickNegative?.call();
      expect(onNegativeCalled, true);

      model.onClickPositive?.call();
      verify(() => commonUtilFunction.commonLaunchUrlString(
          CheckForceUpdateHandlerMixin.urlStoreIos,
          mode: any(named: 'mode'))).called(1);
    });

    test('createForceUpdateUIModel returns correct model when isForceUpdate is true', () {
      final ForceUpdateUIModel model =
          checkForceUpdateHandler.createForceUpdateUIModel(isForceUpdate: true);

      expect(model.imageHeader, isNotNull);
      verify(() => commonImageProvider.asset(
            EvoImages.bgAppUpdate,
            fit: BoxFit.fitWidth,
          )).called(1);

      expect(model.dialogId, EvoDialogId.newAppVersionBottomSheet);
      expect(model.title, EvoStrings.forceUpdateSubDesc);
      expect(model.content, EvoStrings.forceUpdateDescription);
      expect(model.textNegative, null);
      expect(model.textPositive, EvoStrings.forceUpdateAgree);

      model.onClickPositive?.call();
      verify(() => commonUtilFunction.commonLaunchUrlString(
          CheckForceUpdateHandlerMixin.urlStoreIos,
          mode: any(named: 'mode'))).called(1);
    });
  });

  group('verify checkInAppUpdateIfNeed()', () {
    tearDown(() {
      reset(mockInAppUpdateWrapper);
    });

    test('should return true directly when platform is iOS', () async {
      when(() => evoPlatformWrapper.isIOS()).thenReturn(true);
      when(() => mockInAppUpdateWrapper.isUpdateAvailable())
          .thenAnswer((_) => Future<bool>.value(true));

      final bool result = await checkForceUpdateHandler.checkInAppUpdateIfNeed();

      expect(result, true);
    });

    test('should delegate to inAppUpdateWrapper when platform is Android', () async {
      when(() => evoPlatformWrapper.isIOS()).thenReturn(false);
      when(() => mockInAppUpdateWrapper.isUpdateAvailable())
          .thenAnswer((_) => Future<bool>.value(true));

      final bool result = await checkForceUpdateHandler.checkInAppUpdateIfNeed();

      expect(result, true);
      verify(() => mockInAppUpdateWrapper.isUpdateAvailable()).called(1);
    });

    test('should return false when update is not available on Android', () async {
      when(() => evoPlatformWrapper.isIOS()).thenReturn(false);
      when(() => mockInAppUpdateWrapper.isUpdateAvailable())
          .thenAnswer((_) => Future<bool>.value(false));

      final bool result = await checkForceUpdateHandler.checkInAppUpdateIfNeed();

      expect(result, false);
      verify(() => mockInAppUpdateWrapper.isUpdateAvailable()).called(1);
    });
  });

  group('verify handleForceUpdate()', () {
    setUp(() {
      stubShowForceUpdateOverlay();
      stubShowDialogBottomSheet();
    });

    tearDown(() {
      reset(evoPlatformWrapper);
    });

    test('should call showRequestUpdateUI when platform is iOS', () async {
      // Arrange
      when(() => evoPlatformWrapper.isIOS()).thenReturn(true);

      await checkForceUpdateHandler.handleForceUpdate();

      verify(() => mockEvoOverlayUtilFunctions.showForceUpdateOverlay(any())).called(1);

      verifyNever(
        () => EvoDialogHelper().showDialogBottomSheet(
          dialogId: EvoDialogId.newAppVersionBottomSheet,
          header: any(named: 'header'),
          title: EvoStrings.forceUpdateSubDesc,
          textPositive: EvoStrings.forceUpdateAgree,
          content: EvoStrings.forceUpdateDescription,
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
        ),
      );
    });

    test('should call handleAndroidForceUpdate when platform is not iOS', () async {
      // Arrange
      when(() => evoPlatformWrapper.isIOS()).thenReturn(false);
      when(() => mockInAppUpdateWrapper.startImmediateUpdate())
          .thenAnswer((_) => Future<AppUpdateResult>.value(AppUpdateResult.success));

      // Act
      await checkForceUpdateHandler.handleForceUpdate();

      /// Assert
      verify(() => mockInAppUpdateWrapper.startImmediateUpdate()).called(1);
    });
  });

  group('verify processAndroidFlexibleUpdateResult()', () {
    final String latestVersion = '2.0.0';

    tearDown(() {
      reset(mockLoggingRepo);
      reset(mockInAppUpdateWrapper);
      reset(localStorageHelper);
    });

    test('should call handleIgnoreUpdate when result is userDeniedUpdate', () async {
      // Arrange
      when(() => localStorageHelper.setLatestVersionIgnore(any()))
          .thenAnswer((_) => Future<void>.value());

      // Act
      await checkForceUpdateHandler.processAndroidFlexibleUpdateResult(
          AppUpdateResult.userDeniedUpdate, latestVersion);

      // Assert
      verify(() => localStorageHelper.setLatestVersionIgnore(latestVersion)).called(1);
      verify(() => mockNavigatorContext.pop()).called(1);
      verifyNever(() =>
          mockLoggingRepo.logEvent(eventType: any(named: 'eventType'), data: any(named: 'data')));
      verifyNever(() => mockInAppUpdateWrapper.completeFlexibleUpdate());
    });

    test('should call logUpdateFailure when result is inAppUpdateFailed', () async {
      // Arrange
      when(() => mockLoggingRepo.logEvent(
            eventType: any(named: 'eventType'),
            data: any(named: 'data'),
          )).thenAnswer((_) => Future<void>.value());

      // Act
      await checkForceUpdateHandler.processAndroidFlexibleUpdateResult(
          AppUpdateResult.inAppUpdateFailed, latestVersion);

      // Assert
      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.appUpdate,
            data: <String, dynamic>{
              'error': 'In app update failed',
            },
          )).called(1);

      verifyNever(() => localStorageHelper.setLatestVersionIgnore(any()));
      verifyNever(() => mockInAppUpdateWrapper.completeFlexibleUpdate());
    });

    test('should call completeFlexibleUpdate when result is success', () async {
      // Arrange
      when(() => mockInAppUpdateWrapper.completeFlexibleUpdate())
          .thenAnswer((_) => Future<void>.value());

      // Act
      await checkForceUpdateHandler.processAndroidFlexibleUpdateResult(
          AppUpdateResult.success, latestVersion);

      // Assert
      verify(() => mockInAppUpdateWrapper.completeFlexibleUpdate()).called(1);
      verifyNever(() => localStorageHelper.setLatestVersionIgnore(any()));
      verifyNever(() =>
          mockLoggingRepo.logEvent(eventType: any(named: 'eventType'), data: any(named: 'data')));
    });
  });

  group('handleFlexibleUpdate', () {
    const String latestVersion = '2.0.0';

    test('should do nothing if latest version is ignored', () async {
      // Arrange
      when(() => localStorageHelper.getLatestVersionIgnore())
          .thenAnswer((_) async => latestVersion);

      // Act
      await checkForceUpdateHandler.handleFlexibleUpdate(latestVersion);

      // Assert
      verify(() => localStorageHelper.getLatestVersionIgnore()).called(1);
      verifyNever(() => mockEvoOverlayUtilFunctions.showForceUpdateOverlay(any()));
      verifyNever(() => mockInAppUpdateWrapper.startFlexibleUpdate());
    });

    test('should show request update UI if platform is iOS', () async {
      // Arrange
      stubShowDialogBottomSheet();
      when(() => evoFlutterWrapper.isIOS()).thenReturn(true);
      when(() => localStorageHelper.getLatestVersionIgnore()).thenAnswer((_) async => '1.0.0');

      // Act
      await checkForceUpdateHandler.handleFlexibleUpdate(latestVersion);

      // Assert
      verify(() => localStorageHelper.getLatestVersionIgnore()).called(1);
      verifyNever(() => mockInAppUpdateWrapper.startFlexibleUpdate());
      verify(
        () => EvoDialogHelper().showDialogBottomSheet(
          dialogId: EvoDialogId.newAppVersionBottomSheet,
          header: any(named: 'header'),
          title: EvoStrings.forceUpdateSubDesc,
          textNegative: EvoStrings.forceUpdateSkip,
          textPositive: EvoStrings.forceUpdateAgree,
          content: EvoStrings.forceUpdateDescription,
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
        ),
      ).called(1);
    });

    test('should start flexible update if platform is Android', () async {
      // Arrange
      when(() => evoFlutterWrapper.isIOS()).thenReturn(false);
      when(() => localStorageHelper.getLatestVersionIgnore()).thenAnswer((_) async => '1.0.0');
      when(() => mockInAppUpdateWrapper.startFlexibleUpdate())
          .thenAnswer((_) async => AppUpdateResult.success);
      when(() => mockInAppUpdateWrapper.completeFlexibleUpdate())
          .thenAnswer((_) => Future<void>.value());

      // Act
      await checkForceUpdateHandler.handleFlexibleUpdate(latestVersion);

      // Assert
      verify(() => localStorageHelper.getLatestVersionIgnore()).called(1);
      verify(() => mockInAppUpdateWrapper.startFlexibleUpdate()).called(1);
      verify(() => mockInAppUpdateWrapper.completeFlexibleUpdate()).called(1);
    });

    test('should process flexible update result correctly', () async {
      // Arrange
      when(() => evoFlutterWrapper.isIOS()).thenReturn(false);
      when(() => localStorageHelper.getLatestVersionIgnore()).thenAnswer((_) async => '1.0.0');
      when(() => mockInAppUpdateWrapper.startFlexibleUpdate())
          .thenAnswer((_) async => AppUpdateResult.userDeniedUpdate);
      when(() => localStorageHelper.setLatestVersionIgnore(any()))
          .thenAnswer((_) => Future<void>.value());
      // Act
      await checkForceUpdateHandler.handleFlexibleUpdate(latestVersion);

      // Assert
      verify(() => localStorageHelper.getLatestVersionIgnore()).called(1);
      verify(() => mockInAppUpdateWrapper.startFlexibleUpdate()).called(1);
      verify(() => localStorageHelper.setLatestVersionIgnore(latestVersion)).called(1);
    });
  });

  group('processAndroidFlexibleUpdateResult', () {
    const String latestVersion = '2.0.0';

    test('should call handleIgnoreUpdate when result is userDeniedUpdate', () async {
      // Arrange
      when(() => localStorageHelper.setLatestVersionIgnore(any()))
          .thenAnswer((_) => Future<void>.value());

      // Act
      await checkForceUpdateHandler.processAndroidFlexibleUpdateResult(
          AppUpdateResult.userDeniedUpdate, latestVersion);

      // Assert
      verify(() => localStorageHelper.setLatestVersionIgnore(latestVersion)).called(1);
      verifyNever(() =>
          mockLoggingRepo.logEvent(eventType: any(named: 'eventType'), data: any(named: 'data')));
      verifyNever(() => mockInAppUpdateWrapper.completeFlexibleUpdate());
    });

    test('should call logUpdateFailure when result is inAppUpdateFailed', () async {
      // Arrange
      when(() => mockLoggingRepo.logEvent(
            eventType: any(named: 'eventType'),
            data: any(named: 'data'),
          )).thenAnswer((_) => Future<void>.value());

      // Act
      await checkForceUpdateHandler.processAndroidFlexibleUpdateResult(
          AppUpdateResult.inAppUpdateFailed, latestVersion);

      // Assert
      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.appUpdate,
            data: <String, dynamic>{'error': 'In app update failed'},
          )).called(1);
      verifyNever(() => localStorageHelper.setLatestVersionIgnore(any()));
      verifyNever(() => mockInAppUpdateWrapper.completeFlexibleUpdate());
    });

    test('should call completeFlexibleUpdate when result is success', () async {
      // Arrange
      when(() => mockInAppUpdateWrapper.completeFlexibleUpdate())
          .thenAnswer((_) => Future<void>.value());

      // Act
      await checkForceUpdateHandler.processAndroidFlexibleUpdateResult(
          AppUpdateResult.success, latestVersion);

      // Assert
      verify(() => mockInAppUpdateWrapper.completeFlexibleUpdate()).called(1);
      verifyNever(() => localStorageHelper.setLatestVersionIgnore(any()));
      verifyNever(() =>
          mockLoggingRepo.logEvent(eventType: any(named: 'eventType'), data: any(named: 'data')));
    });
  });

  group('processAndroidForceUpdateResult', () {
    test('should show force update UI when result is userDeniedUpdate', () async {
      stubShowForceUpdateOverlay();
      await checkForceUpdateHandler
          .processAndroidForceUpdateResult(AppUpdateResult.userDeniedUpdate);

      verify(() => mockEvoOverlayUtilFunctions.showForceUpdateOverlay(any())).called(1);
    });

    test('should call logUpdateFailure when result is inAppUpdateFailed', () async {
      when(() => mockLoggingRepo.logEvent(
            eventType: any(named: 'eventType'),
            data: any(named: 'data'),
          )).thenAnswer((_) => Future<void>.value());
      stubShowForceUpdateOverlay();

      await checkForceUpdateHandler
          .processAndroidForceUpdateResult(AppUpdateResult.inAppUpdateFailed);

      verify(() => mockLoggingRepo.logEvent(
            eventType: EvoEventType.appUpdate,
            data: <String, dynamic>{'error': 'In app update failed'},
          )).called(1);

      verify(() => mockEvoOverlayUtilFunctions.showForceUpdateOverlay(any())).called(1);
    });

    test('should log success message when result is success', () async {
      await checkForceUpdateHandler.processAndroidForceUpdateResult(AppUpdateResult.success);

      // Assert
      verifyNever(() => mockEvoOverlayUtilFunctions.showForceUpdateOverlay(any()));
      verifyNever(() =>
          mockLoggingRepo.logEvent(eventType: any(named: 'eventType'), data: any(named: 'data')));
    });
  });

  group('test logUpdateFailure', () {
    test('call logUpdateFailure correctly', () {
      when(
        () => mockLoggingRepo.logEvent(
          eventType: any(named: 'eventType'),
          data: any(named: 'data'),
        ),
      ).thenAnswer((_) async => Future<void>.value());

      checkForceUpdateHandler.logUpdateFailure();

      expect(
          verify(() => mockLoggingRepo.logEvent(
                eventType: captureAny(named: 'eventType'),
                data: any(named: 'data'),
              )).captured.single,
          EvoEventType.appUpdate);
    });
  });
}
