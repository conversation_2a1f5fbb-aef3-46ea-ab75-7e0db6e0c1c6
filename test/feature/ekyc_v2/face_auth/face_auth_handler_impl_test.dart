import 'package:evoapp/feature/ekyc_v2/ekyc_v2_flow_callback.dart';
import 'package:evoapp/feature/ekyc_v2/ekyc_v2_flow_type.dart';
import 'package:evoapp/feature/ekyc_v2/face_auth/face_auth_handler_impl.dart';
import 'package:evoapp/feature/ekyc_v2/face_auth/starter_screen/face_auth_starter_screen.dart';
import 'package:evoapp/feature/login/login_shared_data.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';

class MockAppState extends Mock implements AppState {}

class MockLoginSharedData extends Mock implements LoginSharedData {}

void main() {
  final String fakeSessionToken = 'fake_session_token';

  late AppState mockAppState;
  late LoginSharedData mockLoginSharedData;
  late FaceAuthHandlerImpl faceAuthHandler;

  final EkycV2FlowCallback mockFlowCallback = EkycV2FlowCallback(
    flowType: EkycV2FlowType.signIn,
  );

  setUpAll(() {
    registerFallbackValue(MockBuildContext());
    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);

    getIt.registerSingleton<CommonNavigatorObserver>(MockCommonNavigatorObserver());
    getIt.registerLazySingleton<CommonNavigator>(() => MockCommonNavigator());
    commonNavigator = getIt.get<CommonNavigator>();

    getIt.registerLazySingleton<AppState>(() => MockAppState());
    mockAppState = getIt.get<AppState>();
  });

  setUp(() {
    mockLoginSharedData = MockLoginSharedData();

    when(() => mockAppState.loginSharedData).thenReturn(mockLoginSharedData);

    faceAuthHandler = FaceAuthHandlerImpl(flowType: EkycV2FlowType.signIn);
  });

  tearDown(() {
    reset(mockLoginSharedData);
    reset(mockAppState);
    reset(commonNavigator);
  });

  test('should return correct flow type', () {
    expect(faceAuthHandler.getFlowType, EkycV2FlowType.signIn);
  });

  group('verify startFaceAuth()', () {
    setUpAll(() {
      when(
        () => commonNavigator.pushNamed(
          any(),
          any(),
          extra: any(named: 'extra'),
        ),
      ).thenAnswer((_) => Future<void>.value());

      when(
        () => commonNavigator.pushReplacementNamed(
          any(),
          any(),
          extra: any(named: 'extra'),
        ),
      ).thenAnswer((_) => Future<void>.value());
    });

    test('should save face auth flow callback', () {
      faceAuthHandler.startFaceAuth(
        sessionToken: fakeSessionToken,
        callback: mockFlowCallback,
      );

      verify(() => mockLoginSharedData.faceAuthCallback = mockFlowCallback).called(1);
    });

    test('should push replacement named screen when isReplaceScreen is true', () async {
      await faceAuthHandler.startFaceAuth(
        sessionToken: fakeSessionToken,
        callback: mockFlowCallback,
      );

      final List<dynamic> capturedData = verify(
        () => commonNavigator.pushReplacementNamed(
          any(),
          Screen.faceAuthStarterScreenName,
          extra: captureAny(named: 'extra'),
        ),
      ).captured;

      expect(capturedData[0], isA<FaceAuthStarterScreenArg>());
      final FaceAuthStarterScreenArg arg = capturedData[0] as FaceAuthStarterScreenArg;
      expect(arg.flowType, EkycV2FlowType.signIn);
      expect(arg.callback, mockFlowCallback);
      expect(arg.sessionToken, fakeSessionToken);
    });

    test('should push named screen when isReplaceScreen is false', () async {
      await faceAuthHandler.startFaceAuth(
        sessionToken: fakeSessionToken,
        callback: mockFlowCallback,
        isReplaceScreen: false,
      );

      final List<dynamic> capturedData = verify(
        () => commonNavigator.pushNamed(
          any(),
          Screen.faceAuthStarterScreenName,
          extra: captureAny(named: 'extra'),
        ),
      ).captured;

      expect(capturedData[0], isA<FaceAuthStarterScreenArg>());
      final FaceAuthStarterScreenArg arg = capturedData[0] as FaceAuthStarterScreenArg;
      expect(arg.flowType, EkycV2FlowType.signIn);
      expect(arg.callback, mockFlowCallback);
      expect(arg.sessionToken, fakeSessionToken);
    });
  });
}
