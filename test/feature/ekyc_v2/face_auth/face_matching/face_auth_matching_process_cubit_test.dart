import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/ekyc_repo.dart';
import 'package:evoapp/data/request/face_auth_request.dart';
import 'package:evoapp/data/response/ekyc_face_matching_result_entity.dart';
import 'package:evoapp/data/response/sign_in_otp_entity.dart';
import 'package:evoapp/feature/ekyc_v2/ekyc_v2_state.dart';
import 'package:evoapp/feature/ekyc_v2/face_auth/face_matching/face_auth_matching_process_cubit.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_liveness_mode.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_start_capturing_result.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/test_util.dart';

class MockEkycRepo extends Mock implements EKYCRepo {}

class MockAppState extends Mock implements AppState {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

void main() {
  final String fakeSessionToken = 'fake_session_token';

  TestWidgetsFlutterBinding.ensureInitialized();

  late AppState mockAppState;
  late MockEkycRepo mockEKYCRepo;
  late MockAuthenticationRepo mockAuthenticationRepo;
  late EvoUtilFunction mockEvoUtilFunction;

  final FacialVerificationStartCapturingSuccessResult facialVerificationResult =
      FacialVerificationStartCapturingSuccessResult(
    imageIds: <String>['imageId'],
    videoIds: <String>['videoId'],
    livenessMode: EkycBridgeLivenessMode.flash_16,
  );

  setUpAll(() {
    registerFallbackValue(TypeLogin.faceAuth);
    registerFallbackValue(
      FaceAuthRequest(
        imageIds: <String>['imageId'],
        videoIds: <String>['videoId'],
        selfieType: 'selfieType',
      ),
    );

    getIt.registerSingleton<EvoUtilFunction>(MockEvoUtilFunction());
    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();

    getIt.registerSingleton<AppState>(MockAppState());
    mockAppState = getIt.get<AppState>();

    when(() => mockEvoUtilFunction.updateProcessUserStatus(any()))
        .thenAnswer((_) => Future<void>.value());

    when(() => mockEvoUtilFunction.getFacialVerificationVersion()).thenReturn(
      FacialVerificationVersion.version_3,
    );

    when(() => mockAppState.ekycV2State).thenReturn(
      EkycV2State()..sessionToken = fakeSessionToken,
    );
  });

  setUp(() async {
    mockEKYCRepo = MockEkycRepo();
    mockAuthenticationRepo = MockAuthenticationRepo();
  });

  tearDown(() {
    reset(mockEKYCRepo);
    reset(mockAuthenticationRepo);
  });

  group('test constructor', () {
    test(
      'should init FaceAuthMatchingInit state',
      () {
        final FaceAuthMatchingProcessCubit cubit = FaceAuthMatchingProcessCubit(
          ekycRepo: mockEKYCRepo,
          appState: mockAppState,
          authenticationRepo: mockAuthenticationRepo,
        );
        expect(cubit.state, isA<FaceAuthMatchingProcessInitialState>());
      },
    );
  });

  group('test faceMatchingForLinkCard()', () {
    blocTest<FaceAuthMatchingProcessCubit, FaceAuthMatchingProcessState>(
      'should emit FaceAuthMatchingCompleted when faceAuth is successful',
      setUp: () async {
        final Map<String, dynamic> faceAuthResponseData =
            await TestUtil.getResponseMock('ekyc_face_auth_matching_link_card.json');

        when(
          () => mockEKYCRepo.faceAuth(
            faceAuthRequest: any(named: 'faceAuthRequest'),
            sessionToken: any(named: 'sessionToken'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer(
          (_) async => EkycFaceMatchingResultEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: faceAuthResponseData,
            ),
          ),
        );
      },
      build: () {
        return FaceAuthMatchingProcessCubit(
          ekycRepo: mockEKYCRepo,
          appState: mockAppState,
          authenticationRepo: mockAuthenticationRepo,
        );
      },
      act: (FaceAuthMatchingProcessCubit cubit) => cubit.faceMatchingForLinkCard(
        facialVerificationResult: facialVerificationResult,
      ),
      expect: () => <dynamic>[isA<FaceAuthMatchingSuccessState>()],
      verify: (FaceAuthMatchingProcessCubit cubit) {
        // assert capture data
        final List<dynamic> capturedData = verify(
          () => mockEKYCRepo.faceAuth(
            faceAuthRequest: captureAny(named: 'faceAuthRequest'),
            sessionToken: captureAny(named: 'sessionToken'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).captured;

        final FaceAuthRequest faceAuthRequest = capturedData[0] as FaceAuthRequest;
        expect(faceAuthRequest.videoIds, facialVerificationResult.videoIds);
        expect(faceAuthRequest.imageIds, facialVerificationResult.imageIds);
        expect(faceAuthRequest.selfieType, facialVerificationResult.livenessMode.value);

        expect(capturedData[1], fakeSessionToken);
      },
    );

    blocTest<FaceAuthMatchingProcessCubit, FaceAuthMatchingProcessState>(
      'should emit FaceAuthMatchingError when faceAuth is not successful',
      setUp: () async {
        final Map<String, dynamic> faceAuthResponseData =
            await TestUtil.getResponseMock('ekyc_face_auth_unknown_error.json');

        when(
          () => mockEKYCRepo.faceAuth(
            faceAuthRequest: any(named: 'faceAuthRequest'),
            sessionToken: any(named: 'sessionToken'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer(
          (_) async => EkycFaceMatchingResultEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: faceAuthResponseData,
            ),
          ),
        );
      },
      build: () {
        return FaceAuthMatchingProcessCubit(
          ekycRepo: mockEKYCRepo,
          appState: mockAppState,
          authenticationRepo: mockAuthenticationRepo,
        );
      },
      act: (FaceAuthMatchingProcessCubit cubit) => cubit.faceMatchingForLinkCard(
        facialVerificationResult: facialVerificationResult,
      ),
      expect: () => <dynamic>[
        isA<FaceAuthMatchingErrorState>(),
      ],
      verify: (_) {
        // assert capture data
        final List<dynamic> capturedData = verify(
          () => mockEKYCRepo.faceAuth(
            faceAuthRequest: captureAny(named: 'faceAuthRequest'),
            sessionToken: captureAny(named: 'sessionToken'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).captured;

        final FaceAuthRequest faceAuthRequest = capturedData[0] as FaceAuthRequest;
        expect(faceAuthRequest.videoIds, facialVerificationResult.videoIds);
        expect(faceAuthRequest.imageIds, facialVerificationResult.imageIds);
        expect(faceAuthRequest.selfieType, facialVerificationResult.livenessMode.value);

        expect(capturedData[1], fakeSessionToken);
      },
    );

    blocTest<FaceAuthMatchingProcessCubit, FaceAuthMatchingProcessState>(
      'should emit FaceAuthMatchingLimitExceed when faceAuth result is limit exceeded',
      setUp: () async {
        final Map<String, dynamic> faceAuthResponseData =
            await TestUtil.getResponseMock('ekyc_face_auth_limited_exceed.json');

        when(
          () => mockEKYCRepo.faceAuth(
            faceAuthRequest: any(named: 'faceAuthRequest'),
            sessionToken: any(named: 'sessionToken'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer(
          (_) async => EkycFaceMatchingResultEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.LIMIT_EXCEEDED,
              response: faceAuthResponseData,
            ),
          ),
        );
      },
      build: () {
        return FaceAuthMatchingProcessCubit(
          ekycRepo: mockEKYCRepo,
          appState: mockAppState,
          authenticationRepo: mockAuthenticationRepo,
        );
      },
      act: (FaceAuthMatchingProcessCubit cubit) => cubit.faceMatchingForLinkCard(
        facialVerificationResult: facialVerificationResult,
      ),
      expect: () => <dynamic>[
        isA<FaceAuthMatchingLimitExceedState>(),
      ],
      verify: (_) {
        // assert capture data
        final List<dynamic> capturedData = verify(
          () => mockEKYCRepo.faceAuth(
            faceAuthRequest: captureAny(named: 'faceAuthRequest'),
            sessionToken: captureAny(named: 'sessionToken'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).captured;

        final FaceAuthRequest faceAuthRequest = capturedData[0] as FaceAuthRequest;
        expect(faceAuthRequest.videoIds, facialVerificationResult.videoIds);
        expect(faceAuthRequest.imageIds, facialVerificationResult.imageIds);
        expect(faceAuthRequest.selfieType, facialVerificationResult.livenessMode.value);

        expect(capturedData[1], fakeSessionToken);
      },
    );
  });

  group('test faceMatchingForSignIn()', () {
    blocTest<FaceAuthMatchingProcessCubit, FaceAuthMatchingProcessState>(
      'should emit FaceAuthMatchingCompleted when faceAuth is successful',
      setUp: () async {
        final Map<String, dynamic> faceAuthResponseData =
            await TestUtil.getResponseMock('ekyc_face_auth_matching_sign_in.json');

        when(
          () => mockAuthenticationRepo.login(
            TypeLogin.faceAuth,
            faceAuthRequest: any(named: 'faceAuthRequest'),
            sessionToken: any(named: 'sessionToken'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer(
          (_) async => SignInOtpEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: faceAuthResponseData,
            ),
          ),
        );
      },
      build: () {
        return FaceAuthMatchingProcessCubit(
          ekycRepo: mockEKYCRepo,
          appState: mockAppState,
          authenticationRepo: mockAuthenticationRepo,
        );
      },
      act: (FaceAuthMatchingProcessCubit cubit) => cubit.faceMatchingForSignIn(
        facialVerificationResult: facialVerificationResult,
      ),
      expect: () => <dynamic>[
        isA<FaceAuthMatchingSuccessState>(),
      ],
      verify: (FaceAuthMatchingProcessCubit cubit) {
        // assert capture data
        final List<dynamic> capturedData = verify(
          () => mockAuthenticationRepo.login(
            TypeLogin.faceAuth,
            faceAuthRequest: captureAny(named: 'faceAuthRequest'),
            sessionToken: captureAny(named: 'sessionToken'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).captured;

        final FaceAuthRequest faceAuthRequest = capturedData[0] as FaceAuthRequest;
        expect(faceAuthRequest.videoIds, facialVerificationResult.videoIds);
        expect(faceAuthRequest.imageIds, facialVerificationResult.imageIds);
        expect(faceAuthRequest.selfieType, facialVerificationResult.livenessMode.value);

        expect(capturedData[1], fakeSessionToken);

        verify(() => mockEvoUtilFunction.updateProcessUserStatus(any())).called(1);
      },
    );

    blocTest<FaceAuthMatchingProcessCubit, FaceAuthMatchingProcessState>(
      'should emit FaceAuthMatchingError when faceAuth is not successful',
      setUp: () async {
        final Map<String, dynamic> faceAuthResponseData =
            await TestUtil.getResponseMock('ekyc_face_auth_unknown_error.json');

        when(
          () => mockAuthenticationRepo.login(
            TypeLogin.faceAuth,
            faceAuthRequest: any(named: 'faceAuthRequest'),
            sessionToken: any(named: 'sessionToken'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer(
          (_) async => SignInOtpEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: faceAuthResponseData,
            ),
          ),
        );
      },
      build: () {
        return FaceAuthMatchingProcessCubit(
          ekycRepo: mockEKYCRepo,
          appState: mockAppState,
          authenticationRepo: mockAuthenticationRepo,
        );
      },
      act: (FaceAuthMatchingProcessCubit cubit) => cubit.faceMatchingForSignIn(
        facialVerificationResult: facialVerificationResult,
      ),
      expect: () => <dynamic>[
        isA<FaceAuthMatchingErrorState>(),
      ],
      verify: (_) {
        // assert capture data
        final List<dynamic> capturedData = verify(
          () => mockAuthenticationRepo.login(
            TypeLogin.faceAuth,
            selfieImageId: captureAny(named: 'faceAuthRequest'),
            sessionToken: captureAny(named: 'sessionToken'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).captured;

        final FaceAuthRequest faceAuthRequest = capturedData[0] as FaceAuthRequest;
        expect(faceAuthRequest.videoIds, facialVerificationResult.videoIds);
        expect(faceAuthRequest.imageIds, facialVerificationResult.imageIds);
        expect(faceAuthRequest.selfieType, facialVerificationResult.livenessMode.value);

        expect(capturedData[1], fakeSessionToken);

        verifyNever(() => mockEvoUtilFunction.updateProcessUserStatus(any()));
      },
    );

    blocTest<FaceAuthMatchingProcessCubit, FaceAuthMatchingProcessState>(
      'should emit FaceAuthMatchingLimitExceed when faceAuth result is limit exceeded',
      setUp: () async {
        final Map<String, dynamic> faceAuthResponseData = await TestUtil.getResponseMock(
          'ekyc_face_auth_limited_exceed.json',
        );

        when(
          () => mockAuthenticationRepo.login(
            TypeLogin.faceAuth,
            faceAuthRequest: any(named: 'faceAuthRequest'),
            sessionToken: any(named: 'sessionToken'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer(
          (_) async => SignInOtpEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.LIMIT_EXCEEDED,
              response: faceAuthResponseData,
            ),
          ),
        );
      },
      build: () {
        return FaceAuthMatchingProcessCubit(
          ekycRepo: mockEKYCRepo,
          appState: mockAppState,
          authenticationRepo: mockAuthenticationRepo,
        );
      },
      act: (FaceAuthMatchingProcessCubit cubit) => cubit.faceMatchingForSignIn(
        facialVerificationResult: facialVerificationResult,
      ),
      expect: () => <dynamic>[
        isA<FaceAuthMatchingLimitExceedState>(),
      ],
      verify: (_) {
        // assert capture data
        final List<dynamic> capturedData = verify(
          () => mockAuthenticationRepo.login(
            TypeLogin.faceAuth,
            faceAuthRequest: captureAny(named: 'faceAuthRequest'),
            sessionToken: captureAny(named: 'sessionToken'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).captured;

        final FaceAuthRequest faceAuthRequest = capturedData[0] as FaceAuthRequest;
        expect(faceAuthRequest.videoIds, facialVerificationResult.videoIds);
        expect(faceAuthRequest.imageIds, facialVerificationResult.imageIds);
        expect(faceAuthRequest.selfieType, facialVerificationResult.livenessMode.value);

        expect(capturedData[1], fakeSessionToken);

        verifyNever(() => mockEvoUtilFunction.updateProcessUserStatus(any()));
      },
    );
  });

  group('verify isLimitedExceeded()', () {
    late FaceAuthMatchingProcessCubit cubit;

    setUp(() {
      cubit = FaceAuthMatchingProcessCubit(
        ekycRepo: mockEKYCRepo,
        appState: mockAppState,
        authenticationRepo: mockAuthenticationRepo,
      );
    });

    test('true when statusCode is LIMIT_EXCEEDED and verdict is verdictFaceAuthExceeded', () {
      final bool result = cubit.isLimitedExceeded(
        statusCode: CommonHttpClient.LIMIT_EXCEEDED,
        verdict: EkycFaceMatchingResultEntity.verdictFaceAuthExceeded,
      );
      expect(result, true);
    });

    test('false when statusCode is not LIMIT_EXCEEDED', () {
      final bool result = cubit.isLimitedExceeded(
        statusCode: 200,
        verdict: null,
      );
      expect(result, false);
    });

    test('false when verdict is not verdictFaceAuthExceeded', () {
      final bool result = cubit.isLimitedExceeded(
        statusCode: CommonHttpClient.LIMIT_EXCEEDED,
        verdict: 'some_other_verdict',
      );
      expect(result, false);
    });

    test('false when and statusCode is null and verdict is null', () {
      final bool result = cubit.isLimitedExceeded(
        statusCode: null,
        verdict: null,
      );
      expect(result, false);
    });
  });
}
