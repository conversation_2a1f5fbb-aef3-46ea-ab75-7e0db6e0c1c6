import 'package:evoapp/feature/ekyc_v2/face_auth/models/face_auth_matching_error_type.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FaceAuthMatchingErrorType', () {
    test('should contain limitExceed', () {
      expect(
          FaceAuthMatchingErrorType.values.contains(FaceAuthMatchingErrorType.limitExceed), isTrue);
    });

    test('should contain sessionExpired', () {
      expect(FaceAuthMatchingErrorType.values.contains(FaceAuthMatchingErrorType.sessionExpired),
          isTrue);
    });

    test('should contain other', () {
      expect(FaceAuthMatchingErrorType.values.contains(FaceAuthMatchingErrorType.other), isTrue);
    });

    test('should have correct number of values', () {
      expect(FaceAuthMatchingErrorType.values.length, 3);
    });

    test('should have correct index for each value', () {
      expect(FaceAuthMatchingErrorType.limitExceed.index, 0);
      expect(FaceAuthMatchingErrorType.sessionExpired.index, 1);
      expect(FaceAuthMatchingErrorType.other.index, 2);
    });
  });
}
