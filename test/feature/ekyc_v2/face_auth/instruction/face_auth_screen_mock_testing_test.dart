import 'package:evoapp/feature/ekyc_v2/face_auth/instruction/face_auth_screen_mock_testing.dart';
import 'package:evoapp/feature/mock_test/mock_test_feature_type.dart';
import 'package:evoapp/feature/mock_test/mock_test_helper.dart';
import 'package:evoapp/feature/mock_test/model/mock_test_face_auth_data.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/util/permission/permission_handler_mixin.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockingMockTestHelper extends Mock implements MockTestHelper {}

class MockPermissionHandlerCallback extends Mock implements PermissionHandlerCallback {}

class FakeManualLinkCardFaceAuthInstructionScreen with FaceAuthInstructionScreenMockTesting {
  @override
  void onIgnoreMockTest() {
    /// do nothing
  }

  @override
  void onMockDataIsReady() {
    /// do nothing
  }

  @override
  MockTestFeatureType get mockTestFeatureType => MockTestFeatureType.manualLinkCard;
}

class FakeSignInFaceAuthInstructionScreen with FaceAuthInstructionScreenMockTesting {
  @override
  void onIgnoreMockTest() {
    /// do nothing
  }

  @override
  void onMockDataIsReady() {
    /// do nothing
  }

  @override
  MockTestFeatureType get mockTestFeatureType => MockTestFeatureType.signIn;
}

void main() {
  late FakeManualLinkCardFaceAuthInstructionScreen fakeManualLinkCardFaceAuthInstructionScreen;
  late FakeSignInFaceAuthInstructionScreen fakeSignInFaceAuthInstructionScreen;
  late MockTestHelper mockTestHelper;

  final MockTestFaceAuthData mockFaceAuthData = MockTestFaceAuthData(
    imageIds: <String>['imageId'],
    videoIds: <String>['videoId'],
  );

  setUpAll(() {
    getIt.registerSingleton<MockTestHelper>(MockingMockTestHelper());
    mockTestHelper = getIt.get<MockTestHelper>();

    fakeManualLinkCardFaceAuthInstructionScreen = FakeManualLinkCardFaceAuthInstructionScreen();
    fakeSignInFaceAuthInstructionScreen = FakeSignInFaceAuthInstructionScreen();

    /// register instance of PermissionHandlerCallback
    registerFallbackValue(fakeManualLinkCardFaceAuthInstructionScreen);
    registerFallbackValue(fakeSignInFaceAuthInstructionScreen);
  });

  tearDown(() {
    //reset value
    fakeManualLinkCardFaceAuthInstructionScreen.mockFaceAuthData = null;
    fakeManualLinkCardFaceAuthInstructionScreen.isIgnoredMockTest = false;

    fakeSignInFaceAuthInstructionScreen.mockFaceAuthData = null;
    fakeSignInFaceAuthInstructionScreen.isIgnoredMockTest = false;
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('verify isEnableMockFaceAuthTestFlow variable - flow manual link card', () {
    test('isEnableMockFaceAuthTestFlow returns true when conditions are met', () {
      when(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.manualLinkCard))
          .thenReturn(true);

      expect(fakeManualLinkCardFaceAuthInstructionScreen.isEnableMockFaceAuthTestFlow, true);
      verify(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.manualLinkCard))
          .called(1);
    });

    test('isEnableMockFaceAuthTestFlow returns false when conditions are not met', () {
      when(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.manualLinkCard))
          .thenReturn(false);

      expect(fakeManualLinkCardFaceAuthInstructionScreen.isEnableMockFaceAuthTestFlow, false);
      verify(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.manualLinkCard))
          .called(1);
    });

    test('isEnableMockFaceAuthTestFlow returns false when isIgnoredMockTest = TRUE', () {
      /// arrange
      fakeManualLinkCardFaceAuthInstructionScreen.isIgnoredMockTest = true;

      expect(fakeManualLinkCardFaceAuthInstructionScreen.isEnableMockFaceAuthTestFlow, false);
      verifyNever(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.manualLinkCard));
    });
  });

  group('verify isEnableMockFaceAuthTestFlow variable - flow sign in', () {
    test('isEnableMockFaceAuthTestFlow returns true when conditions are met', () {
      when(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.signIn)).thenReturn(true);

      expect(fakeSignInFaceAuthInstructionScreen.isEnableMockFaceAuthTestFlow, true);
      verify(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.signIn)).called(1);
    });

    test('isEnableMockFaceAuthTestFlow returns false when conditions are not met', () {
      when(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.signIn)).thenReturn(false);

      expect(fakeSignInFaceAuthInstructionScreen.isEnableMockFaceAuthTestFlow, false);
      verify(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.signIn)).called(1);
    });

    test('isEnableMockFaceAuthTestFlow returns false when isIgnoredMockTest = TRUE', () {
      /// arrange
      fakeSignInFaceAuthInstructionScreen.isIgnoredMockTest = true;

      expect(fakeSignInFaceAuthInstructionScreen.isEnableMockFaceAuthTestFlow, false);
      verifyNever(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.signIn));
    });
  });

  group('verify isMockDataAvailable variable', () {
    test('isMockDataAvailable returns true when mockFaceAuthData is not NULL', () {
      /// arrange
      fakeManualLinkCardFaceAuthInstructionScreen.mockFaceAuthData = mockFaceAuthData;
      when(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.manualLinkCard))
          .thenReturn(true);

      expect(fakeManualLinkCardFaceAuthInstructionScreen.isMockDataAvailable, true);
    });

    test('isMockDataAvailable returns false when mockFaceAuthData is NULL', () {
      when(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.manualLinkCard))
          .thenReturn(true);

      fakeManualLinkCardFaceAuthInstructionScreen.mockFaceAuthData = null;
      expect(fakeManualLinkCardFaceAuthInstructionScreen.isMockDataAvailable, false);
    });

    test(
        'isMockDataAvailable returns false when isEnableMockFaceAuthTestFlow == FALSE & mockFaceAuthImage has any value',
        () {
      when(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.manualLinkCard))
          .thenReturn(false);

      fakeManualLinkCardFaceAuthInstructionScreen.mockFaceAuthData = mockFaceAuthData;
      expect(fakeManualLinkCardFaceAuthInstructionScreen.isMockDataAvailable, false);

      fakeManualLinkCardFaceAuthInstructionScreen.mockFaceAuthData = null;
      expect(fakeManualLinkCardFaceAuthInstructionScreen.isMockDataAvailable, false);
    });
  });

  group('verify requestStoragePermissionForMockTest()', () {
    test(
        'requestStoragePermissionForMockTest calls requestStoragePermission when MockTestFlow enabled',
        () {
      when(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.manualLinkCard))
          .thenReturn(true);
      when(() => mockTestHelper.requestStoragePermission(any())).thenAnswer((_) async {});

      expect(fakeManualLinkCardFaceAuthInstructionScreen.isEnableMockFaceAuthTestFlow, true);

      fakeManualLinkCardFaceAuthInstructionScreen.requestStoragePermissionForMockTest();

      verify(
        () => mockTestHelper.requestStoragePermission(fakeManualLinkCardFaceAuthInstructionScreen),
      ).called(1);
    });

    test(
        'requestStoragePermissionForMockTest calls requestStoragePermission when MockTestFlow disabled',
        () {
      when(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.manualLinkCard))
          .thenReturn(false);
      when(() => mockTestHelper.requestStoragePermission(any())).thenAnswer((_) async {});

      expect(fakeManualLinkCardFaceAuthInstructionScreen.isEnableMockFaceAuthTestFlow, false);

      fakeManualLinkCardFaceAuthInstructionScreen.requestStoragePermissionForMockTest();

      verifyNever(
        () => mockTestHelper.requestStoragePermission(fakeManualLinkCardFaceAuthInstructionScreen),
      );
    });
  });

  group('verify startMockTestFaceAuthIfCan - flow manual link card', () {
    test('cannot start Mock Flow if isEnableMockFaceAuthTestFlow = FALSE', () {
      /// arrange
      when(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.manualLinkCard))
          .thenReturn(false);
      expect(fakeManualLinkCardFaceAuthInstructionScreen.isEnableMockFaceAuthTestFlow, false);

      /// action
      fakeManualLinkCardFaceAuthInstructionScreen.startMockTestFaceAuthIfCan();

      /// assert
      verifyNever(
          () => mockTestHelper.initMockTestFaceAuthFlow(MockTestFeatureType.manualLinkCard));
    });

    test('start Mock flow with valid data', () async {
      /// arrange
      when(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.manualLinkCard))
          .thenReturn(true);
      when(() => mockTestHelper.initMockTestFaceAuthFlow(MockTestFeatureType.manualLinkCard))
          .thenAnswer((_) async {
        return mockFaceAuthData;
      });
      expect(fakeManualLinkCardFaceAuthInstructionScreen.isEnableMockFaceAuthTestFlow, true);

      /// action
      await fakeManualLinkCardFaceAuthInstructionScreen.startMockTestFaceAuthIfCan();

      /// assert
      expect(fakeManualLinkCardFaceAuthInstructionScreen.mockFaceAuthData, mockFaceAuthData);
      expect(fakeManualLinkCardFaceAuthInstructionScreen.isIgnoredMockTest, false);
    });

    test('start Mock flow with invalid data', () async {
      /// arrange
      when(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.manualLinkCard))
          .thenReturn(true);
      when(() => mockTestHelper.initMockTestFaceAuthFlow(MockTestFeatureType.manualLinkCard))
          .thenAnswer((_) async {
        return null;
      });
      expect(fakeManualLinkCardFaceAuthInstructionScreen.isEnableMockFaceAuthTestFlow, true);

      /// action
      await fakeManualLinkCardFaceAuthInstructionScreen.startMockTestFaceAuthIfCan();

      /// assert
      expect(fakeManualLinkCardFaceAuthInstructionScreen.mockFaceAuthData, null);
      expect(fakeManualLinkCardFaceAuthInstructionScreen.isIgnoredMockTest, true);
    });
  });

  group('verify startMockTestFaceAuthIfCan - flow sign in', () {
    test('cannot start Mock Flow if isEnableMockFaceAuthTestFlow = FALSE', () {
      /// arrange
      when(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.signIn)).thenReturn(false);
      expect(fakeSignInFaceAuthInstructionScreen.isEnableMockFaceAuthTestFlow, false);

      /// action
      fakeSignInFaceAuthInstructionScreen.startMockTestFaceAuthIfCan();

      /// assert
      verifyNever(() => mockTestHelper.initMockTestFaceAuthFlow(MockTestFeatureType.signIn));
    });

    test('start Mock flow with valid data', () async {
      /// arrange
      when(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.signIn)).thenReturn(true);
      when(() => mockTestHelper.initMockTestFaceAuthFlow(MockTestFeatureType.signIn))
          .thenAnswer((_) async {
        return mockFaceAuthData;
      });
      expect(fakeSignInFaceAuthInstructionScreen.isEnableMockFaceAuthTestFlow, true);

      /// action
      await fakeSignInFaceAuthInstructionScreen.startMockTestFaceAuthIfCan();

      /// assert
      expect(fakeSignInFaceAuthInstructionScreen.mockFaceAuthData, mockFaceAuthData);
      expect(fakeSignInFaceAuthInstructionScreen.isIgnoredMockTest, false);
    });

    test('start Mock flow with invalid data', () async {
      /// arrange
      when(() => mockTestHelper.canExecutedMockTest(MockTestFeatureType.signIn)).thenReturn(true);
      when(() => mockTestHelper.initMockTestFaceAuthFlow(MockTestFeatureType.signIn))
          .thenAnswer((_) async {
        return null;
      });
      expect(fakeSignInFaceAuthInstructionScreen.isEnableMockFaceAuthTestFlow, true);

      /// action
      await fakeSignInFaceAuthInstructionScreen.startMockTestFaceAuthIfCan();

      /// assert
      expect(fakeSignInFaceAuthInstructionScreen.mockFaceAuthData, null);
      expect(fakeSignInFaceAuthInstructionScreen.isIgnoredMockTest, true);
    });
  });
}
