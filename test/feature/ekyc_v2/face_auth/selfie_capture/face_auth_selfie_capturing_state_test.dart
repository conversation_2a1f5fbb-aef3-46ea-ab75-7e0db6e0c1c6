import 'package:evoapp/feature/ekyc_v2/face_auth/selfie_capture/face_auth_selfie_capturing_cubit.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_liveness_mode.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_start_capturing_result.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FaceAuthSelfieCapturingState', () {
    test('FaceAuthSelfieCapturingInitialState should be created correctly', () {
      final FaceAuthSelfieCapturingInitialState state = FaceAuthSelfieCapturingInitialState();
      expect(state, isA<FaceAuthSelfieCapturingInitialState>());
    });

    test('FaceAuthSelfieCapturingInProgressState should be created correctly', () {
      final FaceAuthSelfieCapturingInProgressState state = FaceAuthSelfieCapturingInProgressState();
      expect(state, isA<FaceAuthSelfieCapturingInProgressState>());
    });

    test('FaceAuthSelfieCapturingErrorNotInitializedState should be created correctly', () {
      final FaceAuthSelfieCapturingErrorNotInitializedState state =
          FaceAuthSelfieCapturingErrorNotInitializedState();
      expect(state, isA<FaceAuthSelfieCapturingErrorNotInitializedState>());
    });

    test('FaceAuthSelfieCapturingSuccessState should be created with correct properties', () {
      final FacialVerificationStartCapturingSuccessResult successResult =
          FacialVerificationStartCapturingSuccessResult(
        livenessMode: EkycBridgeLivenessMode.flash_16,
      ); // Replace with actual initialization
      final FaceAuthSelfieCapturingSuccessState state =
          FaceAuthSelfieCapturingSuccessState(successResult: successResult);
      expect(state, isA<FaceAuthSelfieCapturingSuccessState>());
      expect(state.successResult, successResult);
    });

    test('FaceAuthSelfieCapturingErrorState should be created with correct properties', () {
      final FacialVerificationStartCapturingErrorResult errorResult =
          FacialVerificationStartCapturingErrorResult(
        livenessMode: EkycBridgeLivenessMode.flash_16,
      ); // Replace with actual initialization
      final FaceAuthSelfieCapturingErrorState state =
          FaceAuthSelfieCapturingErrorState(errorResult: errorResult);
      expect(state, isA<FaceAuthSelfieCapturingErrorState>());
      expect(state.errorResult, errorResult);
    });
  });
}
