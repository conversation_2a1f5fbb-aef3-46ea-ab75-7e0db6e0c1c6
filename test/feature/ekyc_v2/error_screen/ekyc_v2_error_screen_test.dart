import 'package:evoapp/feature/ekyc_v2/error_screen/ekyc_v2_error_screen.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// ignore: depend_on_referenced_packages
import 'package:visibility_detector/visibility_detector.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider mockCommonImageProvider;
  late CommonNavigatorObserver mockNavigatorObserver;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    initConfigEvoPageStateBase();

    mockCommonImageProvider = getIt.get<CommonImageProvider>();
    when(
      () => mockCommonImageProvider.asset(
        any(),
        width: any(named: 'width'),
        height: any(named: 'height'),
        color: any(named: 'color'),
        fit: any(named: 'fit'),
        cornerRadius: any(named: 'cornerRadius'),
        cacheWidth: any(named: 'cacheWidth'),
        cacheHeight: any(named: 'cacheHeight'),
        package: any(named: 'package'),
      ),
    ).thenAnswer((_) => Container());

    mockNavigatorObserver = getIt.get<CommonNavigatorObserver>();
    when(() => mockNavigatorObserver.topStackIsAPageRoute()).thenAnswer((_) {
      return true;
    });

    VisibilityDetectorController.instance.updateInterval = Duration.zero;

    setUtilsMockInstanceForTesting();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    when(() => EvoUiUtils().calculateHorizontalSpace(
          context: any(named: 'context'),
          widthPercentage: any(named: 'widthPercentage'),
        )).thenReturn(10);

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(10);
  });

  tearDownAll(() {
    getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  testWidgets(
    'EkycV2ErrorScreen displays correct title and description when cannot retry',
    (WidgetTester tester) async {
      final ValueNotifier<bool> onTapRetryButtonCalled = ValueNotifier<bool>(false);
      final ValueNotifier<bool> onTapCloseButtonCalled = ValueNotifier<bool>(false);

      await tester.pumpWidget(
        MaterialApp(
          home: EkycV2ErrorScreen(
            isAbleToRetry: false,
            onTapRetryButton: () => onTapRetryButtonCalled.value = true,
            onTapCloseButton: () => onTapCloseButtonCalled.value = true,
          ),
          navigatorObservers: <NavigatorObserver>[mockNavigatorObserver],
        ),
      );

      expect(find.text(EvoStrings.eKYCErrorTitle), findsOneWidget);
      expect(find.text(EvoStrings.eKYCSelfieCapturingCannotRetryErrorDescription), findsOneWidget);
      expect(find.text(EvoStrings.close), findsOneWidget);

      await tester.tap(find.text(EvoStrings.close));
      await tester.pumpAndSettle();
      expect(onTapCloseButtonCalled.value, isTrue);
    },
  );

  testWidgets(
    'EkycV2ErrorScreen displays correct title and description when can retry',
    (WidgetTester tester) async {
      final ValueNotifier<bool> onTapRetryButtonCalled = ValueNotifier<bool>(false);
      final ValueNotifier<bool> onTapCloseButtonCalled = ValueNotifier<bool>(false);

      await tester.pumpWidget(
        MaterialApp(
          home: EkycV2ErrorScreen(
            isAbleToRetry: true,
            onTapRetryButton: () => onTapRetryButtonCalled.value = true,
            onTapCloseButton: () => onTapCloseButtonCalled.value = true,
          ),
          navigatorObservers: <NavigatorObserver>[mockNavigatorObserver],
        ),
      );

      expect(find.text(EvoStrings.eKYCErrorTitle), findsOneWidget);
      expect(find.text(EvoStrings.eKYCSelfieCapturingErrorDescription), findsOneWidget);
      expect(find.text(EvoStrings.retry), findsOneWidget);

      await tester.tap(find.text(EvoStrings.retry));
      await tester.pumpAndSettle();
      expect(onTapRetryButtonCalled.value, isTrue);
    },
  );
}
