import 'package:evoapp/data/response/private_policy_entity.dart';
import 'package:evoapp/feature/privacy_policy/privacy_policy_state.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Privacy Policy State', () {
    test('PrivacyPolicyState should have correct type', () {
      final PrivacyPolicyState state = PrivacyPolicyState();
      expect(state, isA<BlocState>());
    });

    test('PrivacyPolicyLoadingState should have correct type', () {
      final PrivacyPolicyLoadingState state = PrivacyPolicyLoadingState();
      expect(state, isA<PrivacyPolicyState>());
    });

    test('PrivacyPolicyLoadedState should have correct type and properties', () {
      final PrivacyPolicyEntity entity = PrivacyPolicyEntity();
      final PrivacyPolicyLoadedState state =
          PrivacyPolicyLoadedState(entity: entity, enableDownload: true);
      expect(state, isA<PrivacyPolicyState>());
      expect(state.entity, entity);
      expect(state.enableDownload, true);
    });

    test('PrivacyPolicyRejectedState should have correct type', () {
      final PrivacyPolicyRejectedState state = PrivacyPolicyRejectedState();
      expect(state, isA<PrivacyPolicyState>());
    });

    test('PrivacyPolicyAcceptedState should have correct type', () {
      final PrivacyPolicyAcceptedState state = PrivacyPolicyAcceptedState();
      expect(state, isA<PrivacyPolicyState>());
    });

    test('PrivacyPolicyErrorState should have correct type and properties', () {
      final ErrorUIModel errorUIModel = ErrorUIModel(userMessage: 'Test error message');
      final PrivacyPolicyErrorState state = PrivacyPolicyErrorState(errorUIModel);
      expect(state, isA<PrivacyPolicyState>());
      expect(state.errorUIModel, errorUIModel);
    });
  });
}
