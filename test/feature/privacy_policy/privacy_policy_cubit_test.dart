import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/decree_consent_repo.dart';
import 'package:evoapp/data/response/decree_version_entity.dart';
import 'package:evoapp/data/response/private_policy_entity.dart';
import 'package:evoapp/feature/privacy_policy/privacy_policy_cubit.dart';
import 'package:evoapp/feature/privacy_policy/privacy_policy_state.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/mock_file_name_utils/mock_common_file_name.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../constant.dart';
import '../../util/flutter_test_config.dart';
import '../../util/test_util.dart';

class EvoLocalStorageHelperMock extends Mock implements EvoLocalStorageHelper {}

class EvoUtilFunctionMock extends Mock implements EvoUtilFunction {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockDecreeConsentRepo extends Mock implements DecreeConsentRepo {}

void main() {
  late EvoLocalStorageHelperMock mockEvoLocalStorageHelper;
  late PrivacyPolicyCubit cubit;
  late MockAuthenticationRepo mockAuthenticationRepo;
  late MockDecreeConsentRepo mockDecreeConsentRepo;

  final PrivacyPolicyEntity privatePolicyEntity = PrivacyPolicyEntity(
      consented: true,
      privacyPolicyTitle: 'privacyPolicyTitle',
      createdAt: 'createdAt',
      decreeVersion: DecreeVersionEntity(
        url: 'url',
        version: 1,
        id: 1,
        effectiveFrom: 'effectiveFrom',
      ));

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    mockEvoLocalStorageHelper = EvoLocalStorageHelperMock();
    mockAuthenticationRepo = MockAuthenticationRepo();
    mockDecreeConsentRepo = MockDecreeConsentRepo();
    setUtilsMockInstanceForTesting();
  });
  
  tearDownAll(() {
    resetUtilMockToOriginalInstance();
  });

  setUp(() {
    cubit = PrivacyPolicyCubit(
        localStorageHelper: mockEvoLocalStorageHelper,
        decreeConsentRepo: mockDecreeConsentRepo,
        authenticationRepo: mockAuthenticationRepo);
  });

  group('test logout() function', () {
    setUpAll(() {
      when(() => EvoAuthenticationHelper().clearDataOnTokenInvalid()).thenAnswer((_) async {});
      when(() => EvoAuthenticationHelper().clearUserInfoAppState()).thenAnswer((_) async {});
    });

    blocTest<PrivacyPolicyCubit, PrivacyPolicyState>(
        'test logout() is success and verify emit state [PrivatePolicyLoadingState, PrivatePolicyDataDeleted]',
        setUp: () {
          when(() => mockAuthenticationRepo.logout(mockConfig: any(named: 'mockConfig')))
              .thenAnswer(
            (_) async => BaseEntity.fromBaseResponse(
                BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
              'verdict': 'success',
            })),
          );
        },
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (PrivacyPolicyCubit cubit) => cubit.logout(),
        expect: () => <dynamic>[
              isA<PrivacyPolicyLoadingState>(),
              isA<PrivacyPolicyRejectedState>(),
            ],
        verify: (_) {
          verify(() => mockAuthenticationRepo.logout(mockConfig: any(named: 'mockConfig')))
              .called(1);
          verify(() => EvoAuthenticationHelper().clearDataOnTokenInvalid()).called(1);
          verify(() => EvoAuthenticationHelper().clearUserInfoAppState()).called(1);
        });

    blocTest<PrivacyPolicyCubit, PrivacyPolicyState>(
        'test logout() is failure and verify emit state [PrivatePolicyLoadingState, PrivatePolicyErrorState]',
        setUp: () {
          when(() => mockAuthenticationRepo.logout(mockConfig: any(named: 'mockConfig')))
              .thenAnswer(
            (_) async => BaseEntity.fromBaseResponse(
                BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: <String, dynamic>{
              'verdict': 'failure',
            })),
          );
        },
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (PrivacyPolicyCubit cubit) => cubit.logout(),
        expect: () => <dynamic>[
              isA<PrivacyPolicyLoadingState>(),
              isA<PrivacyPolicyErrorState>().having(
                  (PrivacyPolicyErrorState state) => state.errorUIModel.statusCode,
                  'verify status code when failure',
                  CommonHttpClient.BAD_REQUEST),
            ],
        verify: (_) {
          verify(() => mockAuthenticationRepo.logout(mockConfig: any(named: 'mockConfig')))
              .called(1);
          verifyNever(() => EvoAuthenticationHelper().clearDataOnTokenInvalid());
          verifyNever(() => EvoAuthenticationHelper().clearUserInfoAppState());
        });
  });

  group('test createDecreeConsent() function', () {
    const int decreeVersionId = 1;
    blocTest<PrivacyPolicyCubit, PrivacyPolicyState>(
        'test createDecreeConsent() is success and verify emit state [PrivatePolicyLoadingState, PrivatePolicyAcceptedState]',
        setUp: () async {
          final Map<String, dynamic> responseData =
              await TestUtil.getResponseMock('check_decree_consent_13_success.json');

          when(() => mockDecreeConsentRepo.createDecreeConsent(any(),
              mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
            return BaseEntity.fromBaseResponse(
                BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));
          });
        },
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (PrivacyPolicyCubit cubit) => cubit.createDecreeConsent(decreeVersionId),
        expect: () => <dynamic>[
              isA<PrivacyPolicyLoadingState>(),
              isA<PrivacyPolicyAcceptedState>(),
            ],
        verify: (_) {
          verify(() => mockDecreeConsentRepo.createDecreeConsent(decreeVersionId,
              mockConfig: any(named: 'mockConfig'))).called(1);
        });

    blocTest<PrivacyPolicyCubit, PrivacyPolicyState>(
      'test createDecreeConsent() is failure and verify emit state [PrivatePolicyLoadingState, PrivatePolicyErrorState]',
      setUp: () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('check_decree_consent_13_failure.json');

        when(() => mockDecreeConsentRepo.createDecreeConsent(any(),
            mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
          return BaseEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: responseData));
        });
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (PrivacyPolicyCubit cubit) => cubit.createDecreeConsent(decreeVersionId),
      expect: () => <dynamic>[
        isA<PrivacyPolicyLoadingState>(),
        isA<PrivacyPolicyErrorState>()
            .having((PrivacyPolicyErrorState state) => state.errorUIModel.statusCode,
                'verify status code when failure', CommonHttpClient.BAD_REQUEST)
            .having((PrivacyPolicyErrorState state) => state.errorUIModel.verdict,
                'verify verdict when failure', PrivacyPolicyEntity.verdictFailure),
      ],
      verify: (_) {
        verify(() => mockDecreeConsentRepo.createDecreeConsent(decreeVersionId,
            mockConfig: any(named: 'mockConfig'))).called(1);
      },
    );
  });

  group('test getPrivatePolicyLatest() function', () {
    blocTest<PrivacyPolicyCubit, PrivacyPolicyState>(
      'test getPrivatePolicyLatest() is success and verify emit state [PrivatePolicyLoadedState]',
      setUp: () async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock(latestPrivatePolicyFileName());

        when(() => mockDecreeConsentRepo.latestDecreeConsent(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async {
          return PrivacyPolicyEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));
        });
      },
      build: () => cubit,
      act: (PrivacyPolicyCubit cubit) => cubit.getPrivacyPolicyLatest(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<PrivacyPolicyLoadingState>(),
        isA<PrivacyPolicyLoadedState>()
            .having((PrivacyPolicyLoadedState state) => state.entity?.statusCode,
                'verify status code when success', CommonHttpClient.SUCCESS)
            .having((PrivacyPolicyLoadedState state) => state.entity?.verdict,
                'verify verdict when success', PrivacyPolicyEntity.verdictSuccess)
            .having(
              (PrivacyPolicyLoadedState state) => state.enableDownload,
              'verify enableDownload value',
              true,
            ),
      ],
      verify: (_) {
        verify(() =>
                mockDecreeConsentRepo.latestDecreeConsent(mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );

    blocTest<PrivacyPolicyCubit, PrivacyPolicyState>(
      'test getPrivatePolicyLatest() is failure and verify emit state [PrivatePolicyErrorState]',
      setUp: () async {
        when(() => mockDecreeConsentRepo.latestDecreeConsent(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async {
          return PrivacyPolicyEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: null));
        });
      },
      build: () => cubit,
      act: (PrivacyPolicyCubit cubit) => cubit.getPrivacyPolicyLatest(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<PrivacyPolicyLoadingState>(),
        isA<PrivacyPolicyErrorState>().having(
            (PrivacyPolicyErrorState state) => state.errorUIModel.statusCode,
            'verify status code when failure',
            CommonHttpClient.BAD_REQUEST),
      ],
      verify: (_) {
        verify(() =>
                mockDecreeConsentRepo.latestDecreeConsent(mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );
  });

  group('test showPrivacyPolicyLatest() function', () {
    blocTest<PrivacyPolicyCubit, PrivacyPolicyState>(
      'verify emit state [PrivatePolicyLoadedState] with correct value',
      build: () => cubit,
      act: (PrivacyPolicyCubit cubit) => cubit.showPrivacyPolicyLatest(privatePolicyEntity),
      expect: () => <dynamic>[
        isA<PrivacyPolicyLoadedState>()
            .having(
              (PrivacyPolicyLoadedState state) => state.entity,
              'verify entity correct',
              privatePolicyEntity,
            )
            .having(
              (PrivacyPolicyLoadedState state) => state.enableDownload,
              'verify enableDownload value',
              false,
            ),
      ],
    );
  });

  group('test updateDecreeConsentStatus() function', () {
    test('should mockEvoLocalStorageHelper.setDecreeConsentStatus called once', () {
      when(() => mockEvoLocalStorageHelper.setDecreeConsentStatus(any()))
          .thenAnswer((_) => Future<void>.value());

      cubit.updateDecreeConsentStatus(true);

      verify(() => mockEvoLocalStorageHelper.setDecreeConsentStatus(true)).called(1);
    });
  });
}
