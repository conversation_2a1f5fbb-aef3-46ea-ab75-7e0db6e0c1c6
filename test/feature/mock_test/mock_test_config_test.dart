import 'package:evoapp/feature/mock_test/mock_test_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('returns the expected config directory name', () {
    expect(MockTestConfig.configDirectorName, 'EvoMockTest');
  });

  test('returns the expected prefix for config file names', () {
    expect(MockTestConfig.prefixConfigFileName, 'evo_mock_test');
  });

  test('returns the expected file extension for config files', () {
    expect(MockTestConfig.extensionsConfigFile, '.json');
  });
}
