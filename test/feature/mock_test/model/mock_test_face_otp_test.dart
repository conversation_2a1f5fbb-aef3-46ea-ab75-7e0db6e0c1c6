import 'package:evoapp/feature/mock_test/model/mock_test_face_otp.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MockTestFaceOtp', () {
    test('Default constructor should set enable to false and imageId to null', () {
      final MockTestFaceOtp mockTestFaceOtp = MockTestFaceOtp();

      expect(mockTestFaceOtp.enable, isFalse);
      expect(mockTestFaceOtp.imageId, isNull);
    });

    test('Constructor with parameters should initialize fields correctly', () {
      final MockTestFaceOtp mockTestFaceOtp = MockTestFaceOtp(enable: true, imageId: '123');

      expect(mockTestFaceOtp.enable, isTrue);
      expect(mockTestFaceOtp.imageId, equals('123'));
    });

    test('fromJson should correctly parse a valid JSON', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'enable': true,
        'image_id': '456',
      };

      final MockTestFaceOtp mockTestFaceOtp = MockTestFaceOtp.fromJson(json);

      expect(mockTestFaceOtp.enable, isTrue);
      expect(mockTestFaceOtp.imageId, equals('456'));
    });

    test('fromJson should handle missing imageId', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'enable': false,
      };

      final MockTestFaceOtp mockTestFaceOtp = MockTestFaceOtp.fromJson(json);

      expect(mockTestFaceOtp.enable, isFalse);
      expect(mockTestFaceOtp.imageId, isNull);
    });
  });
}
