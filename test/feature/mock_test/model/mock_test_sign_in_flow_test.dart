import 'package:evoapp/feature/mock_test/model/mock_test_face_auth.dart';
import 'package:evoapp/feature/mock_test/model/mock_test_face_auth_data.dart';
import 'package:evoapp/feature/mock_test/model/mock_test_face_otp.dart';
import 'package:evoapp/feature/mock_test/model/mock_test_sign_in_flow.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MockTestSignInFlow faceOtp', () {
    test('Default constructor should set faceOtp to null', () {
      final MockTestSignInFlow mockTestSignInFlow = MockTestSignInFlow();

      expect(mockTestSignInFlow.faceOtp, isNull);
    });

    test('Constructor with parameters should initialize fields correctly', () {
      final MockTestFaceOtp mockTestFaceOtp = MockTestFaceOtp(enable: true, imageId: 'image123');
      final MockTestSignInFlow mockTestSignInFlow = MockTestSignInFlow(faceOtp: mockTestFaceOtp);

      expect(mockTestSignInFlow.faceOtp, isNotNull);
      expect(mockTestSignInFlow.faceOtp!.enable, isTrue);
      expect(mockTestSignInFlow.faceOtp!.imageId, equals('image123'));
    });

    test('fromJson should correctly parse a valid JSON with faceOtp', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'face_otp': <String, dynamic>{
          'enable': true,
          'image_id': 'image456',
        },
      };

      final MockTestSignInFlow mockTestSignInFlow = MockTestSignInFlow.fromJson(json);

      expect(mockTestSignInFlow.faceOtp, isNotNull);
      expect(mockTestSignInFlow.faceOtp!.enable, isTrue);
      expect(mockTestSignInFlow.faceOtp!.imageId, equals('image456'));
    });

    test('fromJson should handle missing faceOtp gracefully', () {
      final Map<String, dynamic> json = <String, dynamic>{};

      final MockTestSignInFlow mockTestSignInFlow = MockTestSignInFlow.fromJson(json);

      expect(mockTestSignInFlow.faceOtp, isNull);
    });

    test('fromJson should handle null faceOtp field gracefully', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'face_otp': null,
      };

      final MockTestSignInFlow mockTestSignInFlow = MockTestSignInFlow.fromJson(json);

      expect(mockTestSignInFlow.faceOtp, isNull);
    });
  });

  group('MockTestSignInFlow faceAuth', () {
    final MockTestFaceAuthData faceAuthData = MockTestFaceAuthData(
      imageIds: <String>['imageId'],
      videoIds: <String>['videoId'],
    );

    test('Default constructor should set faceAuth to null', () {
      final MockTestSignInFlow mockTestSignInFlow = MockTestSignInFlow();

      expect(mockTestSignInFlow.faceAuth, isNull);
    });

    test('Constructor with parameters should initialize fields correctly', () {
      final MockTestFaceAuth mockTestFaceAuth = MockTestFaceAuth(
        enable: true,
        data: faceAuthData,
      );
      final MockTestSignInFlow mockTestSignInFlow = MockTestSignInFlow(faceAuth: mockTestFaceAuth);

      expect(mockTestSignInFlow.faceAuth, isNotNull);
      expect(mockTestSignInFlow.faceAuth!.enable, isTrue);
      expect(mockTestSignInFlow.faceAuth!.data, equals(faceAuthData));
    });

    test('fromJson should correctly parse a valid JSON with faceAuth', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'face_auth': <String, dynamic>{
          'enable': true,
          'data': <String, List<String>>{
            'image_ids': <String>['imageId'],
            'video_ids': <String>['videoId']
          },
        },
      };

      final MockTestSignInFlow mockTestSignInFlow = MockTestSignInFlow.fromJson(json);

      expect(mockTestSignInFlow.faceAuth, isNotNull);
      expect(mockTestSignInFlow.faceAuth!.enable, isTrue);
      expect(mockTestSignInFlow.faceAuth!.data!.imageIds, faceAuthData.imageIds);
      expect(mockTestSignInFlow.faceAuth!.data!.videoIds, faceAuthData.videoIds);
    });

    test('fromJson should handle missing faceAuth gracefully', () {
      final Map<String, dynamic> json = <String, dynamic>{};

      final MockTestSignInFlow mockTestSignInFlow = MockTestSignInFlow.fromJson(json);

      expect(mockTestSignInFlow.faceAuth, isNull);
    });

    test('fromJson should handle null faceAuth field gracefully', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'face_otp': null,
      };

      final MockTestSignInFlow mockTestSignInFlow = MockTestSignInFlow.fromJson(json);

      expect(mockTestSignInFlow.faceAuth, isNull);
    });
  });
}
