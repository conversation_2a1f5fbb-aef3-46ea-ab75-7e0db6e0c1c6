import 'package:evoapp/data/response/dop_native/dop_native_application_state_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_data_entity.dart';
import 'package:evoapp/feature/dop_native/features/e_success/dop_native_e_success_ui_version.dart';
import 'package:evoapp/feature/dop_native/features/introduction/dop_native_introduction_screen.dart';
import 'package:evoapp/feature/dop_native/features/status_screen/dop_native_status_screen.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/feature/dop_native/util/dop_native_navigation_utils.dart' as utils;
import 'package:evoapp/feature/dop_native/util/dop_native_navigation_utils.dart';
import 'package:evoapp/feature/main_screen/main_screen.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

class MockAppState extends Mock implements AppState {}

void main() {
  final DOPNativeNavigationUtils dopNativeNavigationUtils = DOPNativeNavigationUtils();
  final BuildContext mockNavigatorContext = MockBuildContext();
  late AppState mockAppState;

  setUpAll(() {
    mockAppState = MockAppState();
    getIt.registerLazySingleton<AppState>(() => mockAppState);

    getIt.registerLazySingleton<DOPUtilFunctions>(() => MockDOPUtilFunctions());

    setUpMockGlobalKeyProvider(mockNavigatorContext);
    getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());

    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getItRegisterColor();

    getIt.registerLazySingleton<DopNativeButtonStyles>(() => DopNativeButtonStyles());
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());

    getIt.registerSingleton<DOPNativeNavigationUtils>(dopNativeNavigationUtils);
  });

  tearDown(() {
    reset(mockAppState);
  });

  test('getIt', () {
    // reset and mock
    final DOPNativeNavigationUtils instance = DOPNativeNavigationUtils();
    getIt.unregister<DOPNativeNavigationUtils>();
    getIt.registerSingleton<DOPNativeNavigationUtils>(instance);

    expect(utils.dopNativeNavigationUtils, instance);

    // reset
    getIt.unregister<DOPNativeNavigationUtils>();
    getIt.registerSingleton<DOPNativeNavigationUtils>(dopNativeNavigationUtils);
  });

  group('DOPNativeNavigationStep tests', () {
    test('Test the value of DOPNativeNavigationStep enum members', () {
      expect(DOPNativeNavigationStep.failure.value, 'failure');
      expect(DOPNativeNavigationStep.otp.value, 'otp');
      expect(DOPNativeNavigationStep.ekycIdCard.value, 'ekyc.id_card');
      expect(DOPNativeNavigationStep.ekycConfirm.value, 'ekyc.confirm');
      expect(DOPNativeNavigationStep.appraisingQuickApproval.value, 'appraising.quick_approval');
      expect(DOPNativeNavigationStep.appraisingSecondApproval.value, 'appraising.second_approval');
      expect(DOPNativeNavigationStep.ekycSelfieFlash.value, 'ekyc.selfie.flash');
      expect(DOPNativeNavigationStep.appraisingThirdApproval.value, 'appraising.third_approval');
      expect(DOPNativeNavigationStep.appraisingFourthApproval.value, 'appraising.fourth_approval');
      expect(DOPNativeNavigationStep.appraisingNFCCheck.value, 'appraising.nfc_check');
      expect(DOPNativeNavigationStep.informSuccess.value, 'inform.success');
      expect(DOPNativeNavigationStep.appFormAdditionalInfo.value, 'app_form.additional_info');
      expect(DOPNativeNavigationStep.esignIntro.value, 'esign.intro');
      expect(DOPNativeNavigationStep.esignReview.value, 'esign.review');
      expect(DOPNativeNavigationStep.esignOtp.value, 'esign.otp');
      expect(DOPNativeNavigationStep.appFormCardDesign.value, 'app_form.card_design');
      expect(DOPNativeNavigationStep.appFormStatementDate.value, 'app_form.statement_date');
      expect(DOPNativeNavigationStep.cifConfirm.value, 'cif.confirm');
      expect(DOPNativeNavigationStep.appraisingCif.value, 'appraising.cif');
      expect(DOPNativeNavigationStep.locked.value, 'locked');
      expect(DOPNativeNavigationStep.success.value, 'success');
      expect(DOPNativeNavigationStep.underwritingInProgress.value, 'underwriting.in_progress');
      expect(DOPNativeNavigationStep.underwritingCardIssued.value, 'underwriting.card_issued');
      expect(DOPNativeNavigationStep.underwritingCanceled.value, 'underwriting.canceled');
      expect(DOPNativeNavigationStep.underwritingRejected.value, 'underwriting.rejected');
      expect(
          DOPNativeNavigationStep.cardStatusWaitingDelivery.value, 'card_status.waiting_delivery');
      expect(DOPNativeNavigationStep.cardStatusInDelivery.value, 'card_status.in_delivery');
      expect(DOPNativeNavigationStep.cardStatusCardDelivered.value, 'card_status.card_delivered');
      expect(DOPNativeNavigationStep.cardStatusInformation.value, 'card_status.information');
      expect(DOPNativeNavigationStep.ekycNFCScan.value, 'ekyc.nfc_scan');
      expect(DOPNativeNavigationStep.appFormSalesman.value, 'app_form.saleman');
      expect(DOPNativeNavigationStep.appFormSalesmanConfirm.value, 'app_form.saleman_confirm');
      expect(DOPNativeNavigationStep.appFormAcquisitionReward.value, 'app_form.acquisition_reward');
    });
  });

  group('Screen Configurations', () {
    test('Verify failure config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('failure'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['failure'], Screen.dopNativeFailureScreen);
    });

    test('Verify otp config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('otp'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['otp']?.containsKey('v9.4.1.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['otp']?['v9.4.1.0'],
          Screen.dopNativeVerifyOtpScreen);
    });

    test('Verify ekyc.id_card config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('ekyc.id_card'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['ekyc.id_card']?.containsKey('v9.4.1.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['ekyc.id_card']?['v9.4.1.0'],
          Screen.dopNativeIdCardCaptureIntroductionScreen);
    });

    test('Verify ekyc.confirm config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('ekyc.confirm'), isTrue);

      expect(
          dopNativeNavigationUtils.screenConfigs['ekyc.confirm']?.containsKey('v9.4.1.0'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['ekyc.confirm']?.containsKey('v9.4.1.1'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['ekyc.confirm']?.containsKey('v9.4.2.0'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['ekyc.confirm']?.containsKey('v9.4.3.0'), isTrue);

      expect(dopNativeNavigationUtils.screenConfigs['ekyc.confirm']?['v9.4.1.0'],
          Screen.dopNativeEKYCConfirmScreen);
      expect(dopNativeNavigationUtils.screenConfigs['ekyc.confirm']?['v9.4.1.1'],
          Screen.dopNativeSingleEKYCConfirmScreen);
      expect(dopNativeNavigationUtils.screenConfigs['ekyc.confirm']?['v9.4.2.0'],
          Screen.dopNativeSingleEKYCConfirmScreen);
      expect(dopNativeNavigationUtils.screenConfigs['ekyc.confirm']?['v9.4.3.0'],
          Screen.dopNativeSingleEKYCConfirmScreen);
    });

    test('Verify appraising.quick_approval config', () {
      expect(
          dopNativeNavigationUtils.screenConfigs.containsKey('appraising.quick_approval'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['appraising.quick_approval']
              ?.containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['appraising.quick_approval']?['v9.4.1.0'],
          Screen.dopNativeThreeStepAppraisingVerificationScreen);
    });

    test('Verify appraising.second_approval config', () {
      expect(
          dopNativeNavigationUtils.screenConfigs.containsKey('appraising.second_approval'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['appraising.second_approval']
              ?.containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['appraising.second_approval']?['v9.4.1.0'],
          Screen.dopNativeThreeStepAppraisingVerificationScreen);
    });

    test('Verify ekyc.selfie.flash config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('ekyc.selfie.flash'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['ekyc.selfie.flash']?.containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['ekyc.selfie.flash']?['v9.4.1.0'],
          Screen.dopNativeSelfieFlashIntroductionScreen);
    });

    test('Verify ekyc.selfie.active config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('ekyc.selfie.active'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['ekyc.selfie.active']?.containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['ekyc.selfie.active']?['v9.4.1.0'],
          Screen.dopNativeSelfieActiveIntroductionScreen);
    });

    test('Verify appraising.third_approval config', () {
      expect(
          dopNativeNavigationUtils.screenConfigs.containsKey('appraising.third_approval'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['appraising.third_approval']
              ?.containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['appraising.third_approval']?['v9.4.1.0'],
          Screen.dopNativeThreeStepAppraisingVerificationScreen);
    });

    test('Verify appraising.fourth_approval config', () {
      expect(
          dopNativeNavigationUtils.screenConfigs.containsKey('appraising.fourth_approval'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['appraising.fourth_approval']
              ?.containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['appraising.fourth_approval']?['v9.4.1.0'],
          Screen.dopNativeAppraisingVerificationScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['appraising.fourth_approval']
              ?.containsKey('v9.4.2.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['appraising.fourth_approval']?['v9.4.2.0'],
          Screen.dopNativeAppraisingVerificationScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['appraising.fourth_approval']
              ?.containsKey('v9.4.3.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['appraising.fourth_approval']?['v9.4.3.0'],
          Screen.dopNativeAppraisingVerificationScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['appraising.fourth_approval']
              ?.containsKey('v9.4.1.1'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['appraising.fourth_approval']?['v9.4.1.1'],
          Screen.dopNativeFourthAppraisingScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['appraising.fourth_approval']
              ?.containsKey('v9.4.2.1'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['appraising.fourth_approval']?['v9.4.2.1'],
          Screen.dopNativeFourthAppraisingScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['appraising.fourth_approval']
              ?.containsKey('v9.4.3.1'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['appraising.fourth_approval']?['v9.4.3.1'],
          Screen.dopNativeFourthAppraisingScreen);
    });

    test('Verify appraising.nfc_check config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('appraising.nfc_check'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['appraising.nfc_check']?.containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['appraising.nfc_check']?['v9.4.1.0'],
          Screen.dopNativeAppraisingVerificationScreen);
    });

    test('Verify inform.success config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('inform.success'), isTrue);

      expect(dopNativeNavigationUtils.screenConfigs['inform.success']?.containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['inform.success']?['v9.4.1.0'],
          Screen.dopNativeInformSuccessSemiScreen);

      expect(dopNativeNavigationUtils.screenConfigs['inform.success']?.containsKey('v9.4.2.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['inform.success']?['v9.4.2.0'],
          Screen.dopNativeInformSuccessSemiScreen);

      expect(dopNativeNavigationUtils.screenConfigs['inform.success']?.containsKey('v9.4.3.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['inform.success']?['v9.4.3.0'],
          Screen.dopNativeInformSuccessSemiScreen);

      expect(dopNativeNavigationUtils.screenConfigs['inform.success']?.containsKey('v9.4.1.2'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['inform.success']?['v9.4.1.2'],
          Screen.dopNativeInformSuccessSophiaScreen);

      expect(dopNativeNavigationUtils.screenConfigs['inform.success']?.containsKey('v9.4.2.2'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['inform.success']?['v9.4.2.2'],
          Screen.dopNativeInformSuccessSophiaScreen);

      expect(dopNativeNavigationUtils.screenConfigs['inform.success']?.containsKey('v9.4.3.2'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['inform.success']?['v9.4.3.2'],
          Screen.dopNativeInformSuccessSophiaScreen);
    });

    test('Verify app_form.additional_info config', () {
      expect(
          dopNativeNavigationUtils.screenConfigs.containsKey('app_form.additional_info'), isTrue);

      expect(
          dopNativeNavigationUtils.screenConfigs['app_form.additional_info']
              ?.containsKey('v9.4.1.0'),
          isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['app_form.additional_info']
              ?.containsKey('v9.4.2.0'),
          isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['app_form.additional_info']
              ?.containsKey('v9.4.3.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.additional_info']?['v9.4.1.0'],
          Screen.dopNativeAppFormAdditionalInfoScreen);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.additional_info']?['v9.4.2.0'],
          Screen.dopNativeAppFormAdditionalInfoScreen);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.additional_info']?['v9.4.3.0'],
          Screen.dopNativeAppFormAdditionalInfoScreen);
    });

    test('Verify esign.intro config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('esign.intro'), isTrue);

      expect(
          dopNativeNavigationUtils.screenConfigs['esign.intro']?.containsKey('v9.4.1.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['esign.intro']?['v9.4.1.0'],
          Screen.dopNativeESignIntroScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['esign.intro']?.containsKey('v9.4.2.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['esign.intro']?['v9.4.2.0'],
          Screen.dopNativeESignIntroScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['esign.intro']?.containsKey('v9.4.3.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['esign.intro']?['v9.4.3.0'],
          Screen.dopNativeESignIntroScreen);
    });

    test('Verify esign.review config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('esign.review'), isTrue);

      expect(
          dopNativeNavigationUtils.screenConfigs['esign.review']?.containsKey('v9.4.1.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['esign.review']?['v9.4.1.0'],
          Screen.dopNativeESignReviewScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['esign.review']?.containsKey('v9.4.2.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['esign.review']?['v9.4.2.0'],
          Screen.dopNativeESignReviewScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['esign.review']?.containsKey('v9.4.3.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['esign.review']?['v9.4.3.0'],
          Screen.dopNativeESignReviewScreen);
    });

    test('Verify esign.otp config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('esign.otp'), isTrue);

      expect(dopNativeNavigationUtils.screenConfigs['esign.otp']?.containsKey('v9.4.1.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['esign.otp']?['v9.4.1.0'],
          Screen.dopNativeESignOTPScreen);

      expect(dopNativeNavigationUtils.screenConfigs['esign.otp']?.containsKey('v9.4.2.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['esign.otp']?['v9.4.2.0'],
          Screen.dopNativeESignOTPScreen);

      expect(dopNativeNavigationUtils.screenConfigs['esign.otp']?.containsKey('v9.4.3.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['esign.otp']?['v9.4.3.0'],
          Screen.dopNativeESignOTPScreen);
    });

    test('Verify app_form.card_design config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('app_form.card_design'), isTrue);

      expect(
          dopNativeNavigationUtils.screenConfigs['app_form.card_design']?.containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.card_design']?['v9.4.1.0'],
          Screen.dopNativeAppFormCardDesignSemiScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['app_form.card_design']?.containsKey('v9.4.2.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.card_design']?['v9.4.2.0'],
          Screen.dopNativeAppFormCardDesignAutoPCBScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['app_form.card_design']?.containsKey('v9.4.3.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.card_design']?['v9.4.3.0'],
          Screen.dopNativeAppFormCardDesignAutoCICScreen);
    });

    test('Verify app_form.statement_date config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('app_form.statement_date'), isTrue);

      expect(
          dopNativeNavigationUtils.screenConfigs['app_form.statement_date']
              ?.containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.statement_date']?['v9.4.1.0'],
          Screen.dopNativeAppFormStatementDateSemiScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['app_form.statement_date']
              ?.containsKey('v9.4.2.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.statement_date']?['v9.4.2.0'],
          Screen.dopNativeAppFormStatementDateAutoPCBScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['app_form.statement_date']
              ?.containsKey('v9.4.3.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.statement_date']?['v9.4.3.0'],
          Screen.dopNativeAppFormStatementDateAutoCICScreen);
    });

    test('Verify cif.confirm config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('cif.confirm'), isTrue);

      expect(
          dopNativeNavigationUtils.screenConfigs['cif.confirm']?.containsKey('v9.4.4.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['cif.confirm']?['v9.4.4.0'],
          Screen.dopNativeCifConfirmScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['cif.confirm']?.containsKey('v9.4.5.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['cif.confirm']?['v9.4.5.0'],
          Screen.dopNativeCifConfirmScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['cif.confirm']?.containsKey('v9.4.6.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['cif.confirm']?['v9.4.6.0'],
          Screen.dopNativeCifConfirmScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['cif.confirm']?.containsKey('v9.4.7.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['cif.confirm']?['v9.4.7.0'],
          Screen.dopNativeCifConfirmScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['cif.confirm']?.containsKey('v9.4.8.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['cif.confirm']?['v9.4.8.0'],
          Screen.dopNativeCifConfirmScreen);
      expect(dopNativeNavigationUtils.screenConfigs['cif.confirm']?['v9.4.9.0'],
          Screen.dopNativeCifNoBranchScreen);
    });

    test('Verify appraising.cif config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('appraising.cif'), isTrue);

      expect(dopNativeNavigationUtils.screenConfigs['appraising.cif']?.containsKey('v9.4.4.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['appraising.cif']?['v9.4.4.0'],
          Screen.dopNativeAppraisingVerificationScreen);

      expect(dopNativeNavigationUtils.screenConfigs['appraising.cif']?.containsKey('v9.4.5.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['appraising.cif']?['v9.4.5.0'],
          Screen.dopNativeAppraisingVerificationScreen);

      expect(dopNativeNavigationUtils.screenConfigs['appraising.cif']?.containsKey('v9.4.6.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['appraising.cif']?['v9.4.6.0'],
          Screen.dopNativeAppraisingVerificationScreen);

      expect(dopNativeNavigationUtils.screenConfigs['appraising.cif']?.containsKey('v9.4.7.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['appraising.cif']?['v9.4.7.0'],
          Screen.dopNativeAppraisingVerificationScreen);

      expect(dopNativeNavigationUtils.screenConfigs['appraising.cif']?.containsKey('v9.4.8.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['appraising.cif']?['v9.4.8.0'],
          Screen.dopNativeAppraisingVerificationScreen);
    });

    test('Verify locked config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('locked'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['locked'], Screen.dopNativeEKYCLimitExceedScreen);
    });

    test('Verify success config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('success'), isTrue);

      expect(
          dopNativeNavigationUtils.screenConfigs['success']
              .containsKey(DOPNativeESuccessUIVersion.autoCic.value),
          isTrue);

      expect(
          dopNativeNavigationUtils.screenConfigs['success']
              ?[DOPNativeESuccessUIVersion.autoCic.value],
          Screen.dopNativeESuccessScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['success']
              .containsKey(DOPNativeESuccessUIVersion.semi.value),
          isTrue);

      expect(
        dopNativeNavigationUtils.screenConfigs['success']?[DOPNativeESuccessUIVersion.semi.value],
        Screen.dopNativeESuccessSemiScreen,
      );

      // verify autoPcb config
      expect(
          dopNativeNavigationUtils.screenConfigs['success']
              .containsKey(DOPNativeESuccessUIVersion.autoPcb.value),
          isTrue);

      expect(
          dopNativeNavigationUtils.screenConfigs['success']
              ?[DOPNativeESuccessUIVersion.autoPcb.value],
          isA<Map<String, Screen>>());

      expect(
          dopNativeNavigationUtils.screenConfigs['success']
                  ?[DOPNativeESuccessUIVersion.autoPcb.value]
              ?.containsKey(DOPNativeApplicationStatus.pending.value),
          isTrue);

      expect(
          dopNativeNavigationUtils.screenConfigs['success']
                  ?[DOPNativeESuccessUIVersion.autoPcb.value]
              ?[DOPNativeApplicationStatus.pending.value],
          Screen.dopNativeESuccessScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['success']
                  ?[DOPNativeESuccessUIVersion.autoPcb.value]
              ?.containsKey(DOPNativeApplicationStatus.pushing.value),
          isTrue);

      expect(
          dopNativeNavigationUtils.screenConfigs['success']
                  ?[DOPNativeESuccessUIVersion.autoPcb.value]
              ?[DOPNativeApplicationStatus.pushing.value],
          Screen.dopNativeESuccessScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['success']
                  ?[DOPNativeESuccessUIVersion.autoPcb.value]
              ?.containsKey(DOPNativeApplicationStatus.pushed.value),
          isTrue);

      expect(
          dopNativeNavigationUtils.screenConfigs['success']
              ?[DOPNativeESuccessUIVersion.autoPcb.value]?[DOPNativeApplicationStatus.pushed.value],
          Screen.dopNativeESuccessScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['success']
                  ?[DOPNativeESuccessUIVersion.autoPcb.value]
              ?.containsKey(DOPNativeApplicationStatus.skipped.value),
          isTrue);

      expect(
          dopNativeNavigationUtils.screenConfigs['success']
                  ?[DOPNativeESuccessUIVersion.autoPcb.value]
              ?[DOPNativeApplicationStatus.skipped.value],
          Screen.dopNativeESuccessScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['success']
                  ?[DOPNativeESuccessUIVersion.autoPcb.value]
              ?.containsKey(DOPNativeApplicationStatus.exceedFailure.value),
          isTrue);

      expect(
          dopNativeNavigationUtils.screenConfigs['success']
                  ?[DOPNativeESuccessUIVersion.autoPcb.value]
              ?[DOPNativeApplicationStatus.exceedFailure.value],
          Screen.dopNativeESuccessScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['success']
                  ?[DOPNativeESuccessUIVersion.autoPcb.value]
              ?.containsKey(DOPNativeApplicationStatus.holding.value),
          isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['success']
                  ?[DOPNativeESuccessUIVersion.autoPcb.value]
              ?[DOPNativeApplicationStatus.holding.value],
          Screen.dopNativeESuccessCICHoldingScreen);
    });

    test('Verify underwritingInProgress config', () {
      expect(
          dopNativeNavigationUtils.screenConfigs.containsKey('underwriting.in_progress'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['underwriting.in_progress']
              .containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['underwriting.in_progress']?['v9.4.1.0'],
          Screen.dopNativeUnderwritingInProgressScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['underwriting.in_progress']
              .containsKey('v9.4.2.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['underwriting.in_progress']?['v9.4.2.0'],
          Screen.dopNativeUnderwritingInProgressScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['underwriting.in_progress']
              .containsKey('v9.4.3.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['underwriting.in_progress']?['v9.4.3.0'],
          Screen.dopNativeUnderwritingInProgressScreen);
    });

    test('Verify underwritingCardIssued config', () {
      expect(
          dopNativeNavigationUtils.screenConfigs.containsKey('underwriting.card_issued'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['underwriting.card_issued']
              .containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['underwriting.card_issued']?['v9.4.1.0'],
          Screen.dopNativeUnderwritingCardIssuedScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['underwriting.card_issued']
              .containsKey('v9.4.2.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['underwriting.card_issued']?['v9.4.2.0'],
          Screen.dopNativeUnderwritingCardIssuedScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['underwriting.card_issued']
              .containsKey('v9.4.3.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['underwriting.card_issued']?['v9.4.3.0'],
          Screen.dopNativeUnderwritingCardIssuedScreen);
    });

    test('Verify underwritingCanceled config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('underwriting.canceled'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['underwriting.canceled'].containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['underwriting.canceled']?['v9.4.1.0'],
          Screen.dopNativeFailureScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['underwriting.canceled'].containsKey('v9.4.2.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['underwriting.canceled']?['v9.4.2.0'],
          Screen.dopNativeFailureScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['underwriting.canceled'].containsKey('v9.4.3.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['underwriting.canceled']?['v9.4.3.0'],
          Screen.dopNativeFailureScreen);
    });

    test('Verify underwritingRejected config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('underwriting.rejected'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['underwriting.rejected'].containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['underwriting.rejected']?['v9.4.1.0'],
          Screen.dopNativeFailureScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['underwriting.rejected'].containsKey('v9.4.2.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['underwriting.rejected']?['v9.4.2.0'],
          Screen.dopNativeFailureScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['underwriting.rejected'].containsKey('v9.4.3.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['underwriting.rejected']?['v9.4.3.0'],
          Screen.dopNativeFailureScreen);
    });

    test('Verify cardStatusWaitingDelivery config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('card_status.waiting_delivery'),
          isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['card_status.waiting_delivery']
              .containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['card_status.waiting_delivery']?['v9.4.1.0'],
          Screen.dopNativeUnderwritingCardStatusScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['card_status.waiting_delivery']
              .containsKey('v9.4.2.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['card_status.waiting_delivery']?['v9.4.2.0'],
          Screen.dopNativeUnderwritingCardStatusScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['card_status.waiting_delivery']
              .containsKey('v9.4.3.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['card_status.waiting_delivery']?['v9.4.3.0'],
          Screen.dopNativeUnderwritingCardStatusScreen);
    });

    test('Verify cardStatusInDelivery config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('card_status.in_delivery'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['card_status.in_delivery'].containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['card_status.in_delivery']?['v9.4.1.0'],
          Screen.dopNativeUnderwritingCardStatusScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['card_status.in_delivery'].containsKey('v9.4.2.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['card_status.in_delivery']?['v9.4.2.0'],
          Screen.dopNativeUnderwritingCardStatusScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['card_status.in_delivery'].containsKey('v9.4.3.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['card_status.in_delivery']?['v9.4.3.0'],
          Screen.dopNativeUnderwritingCardStatusScreen);
    });

    test('Verify cardStatusCardDelivered config', () {
      expect(
          dopNativeNavigationUtils.screenConfigs.containsKey('card_status.card_delivered'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['card_status.card_delivered']
              .containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['card_status.card_delivered']?['v9.4.1.0'],
          Screen.dopNativeUnderwritingCardStatusScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['card_status.card_delivered']
              .containsKey('v9.4.2.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['card_status.card_delivered']?['v9.4.2.0'],
          Screen.dopNativeUnderwritingCardStatusScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['card_status.card_delivered']
              .containsKey('v9.4.3.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['card_status.card_delivered']?['v9.4.3.0'],
          Screen.dopNativeUnderwritingCardStatusScreen);
    });

    test('Verify cardStatusInformation config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('card_status.information'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['card_status.information'].containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['card_status.information']?['v9.4.1.0'],
          Screen.dopNativeCardStatusInformationScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['card_status.information'].containsKey('v9.4.2.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['card_status.information']?['v9.4.2.0'],
          Screen.dopNativeCardStatusInformationScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['card_status.information'].containsKey('v9.4.3.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['card_status.information']?['v9.4.3.0'],
          Screen.dopNativeCardStatusInformationScreen);
    });

    test('Verify ekycNFCScan config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('ekyc.nfc_scan'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['ekyc.nfc_scan'].containsKey('v9.4.1.0'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['ekyc.nfc_scan']?['v9.4.1.0'],
          Screen.dopNativeNFCReaderIntroductionScreen);
    });

    test('Verify app_form.saleman config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('app_form.saleman'), isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.saleman'].containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.saleman']?['v9.4.1.0'],
          Screen.dopNativeSalesmanScreen);
    });

    test('Verify app_form.saleman_confirm config', () {
      expect(
          dopNativeNavigationUtils.screenConfigs.containsKey('app_form.saleman_confirm'), isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['app_form.saleman_confirm']
              .containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.saleman_confirm']?['v9.4.1.0'],
          Screen.dopNativeSalesmanConfirmScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['app_form.saleman_confirm']
              .containsKey('v9.4.2.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.saleman_confirm']?['v9.4.2.0'],
          Screen.dopNativeSalesmanConfirmScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['app_form.saleman_confirm']
              .containsKey('v9.4.3.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.saleman_confirm']?['v9.4.3.0'],
          Screen.dopNativeSalesmanConfirmScreen);
    });

    test('Verify app_form.acquisition_reward config', () {
      expect(dopNativeNavigationUtils.screenConfigs.containsKey('app_form.acquisition_reward'),
          isTrue);
      expect(
          dopNativeNavigationUtils.screenConfigs['app_form.acquisition_reward']
              .containsKey('v9.4.1.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.acquisition_reward']?['v9.4.1.0'],
          Screen.dopNativeAcquisitionRewardScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['app_form.acquisition_reward']
              .containsKey('v9.4.2.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.acquisition_reward']?['v9.4.2.0'],
          Screen.dopNativeAcquisitionRewardScreen);

      expect(
          dopNativeNavigationUtils.screenConfigs['app_form.acquisition_reward']
              .containsKey('v9.4.3.0'),
          isTrue);
      expect(dopNativeNavigationUtils.screenConfigs['app_form.acquisition_reward']?['v9.4.3.0'],
          Screen.dopNativeAcquisitionRewardScreen);
    });
  });

  group('Test getNavigationScreen', () {
    test('Verify that currentScreen and uiVersion DO NOT exist in the configs', () {
      final Screen? navigationScreen = dopNativeNavigationUtils.getNavigationScreen(
        currentScreen: 'fakeCurrentScreen',
        uiVersion: 'fakeUiVersion',
        currentStatus: 'fakeStatus',
      );
      expect(navigationScreen, isNull);
    });

    test('Verify that currentScreen exists and uiVersion does not exist in the configs', () {
      final Screen? navigationScreen = dopNativeNavigationUtils.getNavigationScreen(
        currentScreen: 'otp',
        uiVersion: 'fakeUiVersion',
        currentStatus: 'fakeStatus',
      );
      expect(navigationScreen, isNull);
    });

    test('Verify that currentScreen and uiVersion exist in the configs', () {
      final Screen? navigationScreen = dopNativeNavigationUtils.getNavigationScreen(
        currentScreen: 'otp',
        uiVersion: 'v9.4.1.0',
        currentStatus: 'fakeStatus',
      );
      expect(navigationScreen, Screen.dopNativeVerifyOtpScreen);
    });

    test(
        'Verify that currentScreen and uiVersion NOT exist in the configs, and currentScreen is appraising state',
        () {
      final Screen? navigationScreen = dopNativeNavigationUtils.getNavigationScreen(
        currentScreen: 'appraising.new_state',
        uiVersion: 'v9.4.1.0',
        currentStatus: 'fakeStatus',
      );

      expect(navigationScreen, Screen.dopNativeAppraisingVerificationScreen);
    });

    group('test if screen need mapping with current_status', () {
      test('if current screen is success, ui version = v9.4.2.0 and currentStatus is holding', () {
        final Screen? navigationScreen = dopNativeNavigationUtils.getNavigationScreen(
          currentScreen: 'success',
          uiVersion: 'v9.4.2.0',
          currentStatus: 'holding',
        );
        expect(navigationScreen, Screen.dopNativeESuccessCICHoldingScreen);
      });

      test('if current screen is success, ui version = v9.4.2.0 and currentStatus is pending', () {
        final Screen? navigationScreen = dopNativeNavigationUtils.getNavigationScreen(
          currentScreen: 'success',
          uiVersion: 'v9.4.2.0',
          currentStatus: 'pending',
        );

        expect(navigationScreen, Screen.dopNativeESuccessScreen);
      });

      test('if current screen is success, ui version = v9.4.2.0 and currentStatus is other', () {
        final Screen? navigationScreen = dopNativeNavigationUtils.getNavigationScreen(
          currentScreen: 'success',
          uiVersion: 'v9.4.2.0',
          currentStatus: 'other',
        );

        expect(navigationScreen, null);
      });

      test('if current screen is success, ui version = v9.4.2.0, currentStatus is null', () {
        final Screen? navigationScreen = dopNativeNavigationUtils.getNavigationScreen(
          currentScreen: 'success',
          uiVersion: 'v9.4.2.0',
          currentStatus: null,
        );

        expect(navigationScreen, Screen.dopNativeESuccessScreen);
      });

      test('if current screen is success, ui version != v9.4.2.0 and currentStatus is other', () {
        final Screen? navigationScreen = dopNativeNavigationUtils.getNavigationScreen(
          currentScreen: 'success',
          uiVersion: 'v9.4.1.0',
          currentStatus: 'other',
        );

        expect(navigationScreen, Screen.dopNativeESuccessSemiScreen);
      });

      test('if screen is success and ui version != v9.4.2.0', () {
        final Screen? navigationScreen = dopNativeNavigationUtils.getNavigationScreen(
          currentScreen: 'success',
          uiVersion: 'v9.4.1.0',
          currentStatus: 'pending',
        );
        expect(navigationScreen, Screen.dopNativeESuccessSemiScreen);
      });

      test('if screen is success and ui version != v9.4.2.0', () {
        final Screen? navigationScreen = dopNativeNavigationUtils.getNavigationScreen(
          currentScreen: 'success',
          uiVersion: 'v9.4.3.0',
          currentStatus: 'pending',
        );
        expect(navigationScreen, Screen.dopNativeESuccessScreen);
      });
    });

    group('Test locked state', () {
      test('Verify that currentScreen is `locked` and ui version is null', () {
        final String state = DOPNativeNavigationStep.locked.value;
        final Screen? navigationScreen = dopNativeNavigationUtils.getNavigationScreen(
          currentScreen: state,
          uiVersion: null,
          currentStatus: null,
        );
        expect(navigationScreen, Screen.dopNativeEKYCLimitExceedScreen);
      });

      test('Verify that currentScreen is `locked` and ui version have random value', () {
        final String state = DOPNativeNavigationStep.locked.value;
        final Screen? navigationScreen = dopNativeNavigationUtils.getNavigationScreen(
          currentScreen: state,
          uiVersion: 'v9.4.1.0',
          currentStatus: 'fakeStatus',
        );
        expect(navigationScreen, Screen.dopNativeEKYCLimitExceedScreen);
      });
    });

    group('Test failure state', () {
      test('Verify that currentScreen is `failure` and ui version is null', () {
        final String state = DOPNativeNavigationStep.failure.value;
        final Screen? navigationScreen = dopNativeNavigationUtils.getNavigationScreen(
          currentScreen: state,
          uiVersion: null,
          currentStatus: 'fakeStatus',
        );
        expect(navigationScreen, Screen.dopNativeFailureScreen);
      });

      test('Verify that currentScreen is `failure` and ui version is any value', () {
        final String state = DOPNativeNavigationStep.failure.value;
        final Screen? navigationScreen = dopNativeNavigationUtils.getNavigationScreen(
          currentScreen: state,
          uiVersion: 'v9.4.1.0',
          currentStatus: 'fakeStatus',
        );
        expect(navigationScreen, Screen.dopNativeFailureScreen);
      });
    });
  });

  group('Test navigateToScreen', () {
    test('Verify that currentScreen and uiVersion DO NOT exist in the configs', () async {
      await dopNativeNavigationUtils.navigateToScreen(
        currentScreen: 'fakeCurrentScreen',
        uiVersion: 'fakeUiVersion',
        currentStatus: 'fakeStatus',
      );

      verify(() => mockNavigatorContext.pushReplacementNamed(Screen.dopNativeStatusScreen.name,
          extra: any(named: 'extra'))).called(1);
    });

    test('Verify that currentScreen exists and uiVersion does not exist in the configs', () async {
      await dopNativeNavigationUtils.navigateToScreen(
        currentScreen: 'otp',
        uiVersion: 'fakeUiVersion',
        currentStatus: 'fakeStatus',
      );

      verify(() => mockNavigatorContext.pushReplacementNamed(Screen.dopNativeStatusScreen.name,
          extra: any(named: 'extra'))).called(1);
    });

    test('Verify that currentScreen and uiVersion exist in the configs', () async {
      await dopNativeNavigationUtils.navigateToScreen(
        currentScreen: 'otp',
        uiVersion: 'v9.4.1.0',
        currentStatus: 'fakeStatus',
      );

      verify(() => mockNavigatorContext.pushReplacementNamed(Screen.dopNativeVerifyOtpScreen.name))
          .called(1);
    });

    test('Verify that currentScreen, uiVersion and currentStatus exist in the configs', () async {
      await dopNativeNavigationUtils.navigateToScreen(
        currentScreen: 'success',
        uiVersion: 'v9.4.2.0',
        currentStatus: 'holding',
      );

      verify(() => mockNavigatorContext
          .pushReplacementNamed(Screen.dopNativeESuccessCICHoldingScreen.name)).called(1);
    });

    test('Verify that currentScreen, uiVersion exist in the configs, currentStatus = null',
        () async {
      await dopNativeNavigationUtils.navigateToScreen(
        currentScreen: 'success',
        uiVersion: 'v9.4.2.0',
        currentStatus: null,
      );

      verify(() => mockNavigatorContext.pushReplacementNamed(Screen.dopNativeESuccessScreen.name))
          .called(1);
    });

    test('Verify that currentScreen and uiVersion exist in the configs but currentStatus not exist',
        () async {
      await dopNativeNavigationUtils.navigateToScreen(
        currentScreen: 'success',
        uiVersion: 'v9.4.2.0',
        currentStatus: 'other',
      );

      verify(() => mockNavigatorContext.pushReplacementNamed(Screen.dopNativeStatusScreen.name,
          extra: any(named: 'extra'))).called(1);
    });
  });

  group('Test navigateToLandingPage', () {
    test('Test navigateToLandingPage without arg', () {
      dopNativeNavigationUtils.navigateToLandingPage();

      verify(() => dopUtilFunction.clearDOPNativeAccessTokenHeader()).called(1);
      verify(() => dopUtilFunction.clearDOPSessionData()).called(1);
      verify(() => mockNavigatorContext.popUntilNamed(Screen.mainScreen.name)).called(1);
      verify(() => mockNavigatorContext.pushNamed(Screen.dopNativeIntroductionScreen.name))
          .called(1);
    });

    test('Test navigateToLandingPage with uniqueToken arg', () {
      final DOPNativeIntroductionScreenArg arg = DOPNativeIntroductionScreenArg(
        uniqueToken: 'test_unique_token',
      );

      dopNativeNavigationUtils.navigateToLandingPage(arg: arg);

      verify(() => dopUtilFunction.clearDOPNativeAccessTokenHeader()).called(1);
      verify(() => dopUtilFunction.clearDOPSessionData()).called(1);
      verify(() => mockNavigatorContext.popUntilNamed(Screen.mainScreen.name)).called(1);
      verify(
        () => mockNavigatorContext.pushNamed(
          Screen.dopNativeIntroductionScreen.name,
          extra: arg,
        ),
      ).called(1);
    });

    test('Test navigateToLandingPage with phoneNumber and autoRequestOTP arg', () {
      final DOPNativeIntroductionScreenArg arg = DOPNativeIntroductionScreenArg(
        phoneNumber: '0123456789',
        autoRequestOTP: true,
      );

      dopNativeNavigationUtils.navigateToLandingPage(arg: arg);

      verify(() => dopUtilFunction.clearDOPNativeAccessTokenHeader()).called(1);
      verify(() => dopUtilFunction.clearDOPSessionData()).called(1);
      verify(() => mockNavigatorContext.popUntilNamed(Screen.mainScreen.name)).called(1);
      verify(
        () => mockNavigatorContext.pushNamed(
          Screen.dopNativeIntroductionScreen.name,
          extra: arg,
        ),
      ).called(1);
    });

    test('Test navigateToLandingPage with dopNativeDataEntity arg', () {
      final DOPNativeIntroductionScreenArg arg = DOPNativeIntroductionScreenArg(
        dopNativeDataEntity: DOPNativeDataEntity(),
      );

      dopNativeNavigationUtils.navigateToLandingPage(arg: arg);

      verify(() => dopUtilFunction.clearDOPNativeAccessTokenHeader()).called(1);
      verify(() => dopUtilFunction.clearDOPSessionData()).called(1);
      verify(() => mockNavigatorContext.popUntilNamed(Screen.mainScreen.name)).called(1);
      verify(
        () => mockNavigatorContext.pushNamed(
          Screen.dopNativeIntroductionScreen.name,
          extra: arg,
        ),
      ).called(1);
    });
  });

  group('verify popUntilToLandingPage', () {
    test('Test popUntilToLandingPage', () {
      dopNativeNavigationUtils.popUntilToLandingPage();

      verify(() => mockNavigatorContext.popUntilNamed(Screen.dopNativeIntroductionScreen.name))
          .called(1);
    });
  });

  group('Test navigateToHomePage()', () {
    test('should navigate to NonUserHomePage when user not login', () {
      when(() => mockAppState.isUserLogIn).thenReturn(false);

      dopNativeNavigationUtils.navigateToHomePage();

      verify(() => dopUtilFunction.clearDOPNativeAccessTokenHeader()).called(1);
      verify(() => dopUtilFunction.clearDOPSessionData()).called(1);

      final VerificationResult verification = verify(() =>
          mockNavigatorContext.goNamed(Screen.mainScreen.name, extra: captureAny(named: 'extra')));
      final dynamic captured = verification.captured.single;

      verification.called(1);
      expect(captured, isA<MainScreenArg>());
      expect((captured as MainScreenArg).isLoggedIn, isFalse);
    });

    test('should navigate to HomeUserPage when user already login', () {
      when(() => mockAppState.isUserLogIn).thenReturn(true);

      dopNativeNavigationUtils.navigateToHomePage();

      verify(() => dopUtilFunction.clearDOPNativeAccessTokenHeader()).called(1);
      verify(() => dopUtilFunction.clearDOPSessionData()).called(1);

      final VerificationResult verification = verify(() =>
          mockNavigatorContext.goNamed(Screen.mainScreen.name, extra: captureAny(named: 'extra')));
      final dynamic captured = verification.captured.single;

      verification.called(1);
      expect(captured, isA<MainScreenArg>());
      expect((captured as MainScreenArg).isLoggedIn, isTrue);
    });
  });

  test(
      'verify clearDOPNativeState() should call clearDOPNativeAccessTokenHeader and clearDOPSessionData',
      () {
    dopNativeNavigationUtils.clearDOPNativeState();

    verify(() => dopUtilFunction.clearDOPNativeAccessTokenHeader()).called(1);
    verify(() => dopUtilFunction.clearDOPSessionData()).called(1);
  });

  test('Verify isAppraisingState returns correct result for appraising state', () {
    expect(dopNativeNavigationUtils.isAppraisingState('appraising.quick_approval'), isTrue);
    expect(dopNativeNavigationUtils.isAppraisingState('appraising.second_approval'), isTrue);
    expect(dopNativeNavigationUtils.isAppraisingState('appraising.third_approval'), isTrue);
    expect(dopNativeNavigationUtils.isAppraisingState('appraising.new_state'), isTrue);
    expect(dopNativeNavigationUtils.isAppraisingState('appraising'), isTrue);

    expect(dopNativeNavigationUtils.isAppraisingState('appraising_state.quick_approval'), isFalse);
    expect(dopNativeNavigationUtils.isAppraisingState('otp'), isFalse);
    expect(dopNativeNavigationUtils.isAppraisingState('locked'), isFalse);
    expect(dopNativeNavigationUtils.isAppraisingState('failure'), isFalse);

    expect(dopNativeNavigationUtils.isAppraisingState(null), isFalse);
    expect(dopNativeNavigationUtils.isAppraisingState(''), isFalse);
  });

  test('navigateCommonErrorScreen', () {
    // Act
    dopNativeNavigationUtils.navigateCommonErrorScreen(
      errorReason: 'fake_error_reason',
    );

    final DOPNativeStatusScreenArg arg = verify(
      () => mockNavigatorContext.pushReplacementNamed(
        Screen.dopNativeStatusScreen.name,
        extra: captureAny(named: 'extra'),
      ),
    ).captured.single;

    expect(arg.ctaWidget, isA<CommonButton>());
    (arg.ctaWidget as CommonButton).onPressed?.call();
    verify(
      () => mockNavigatorContext.pushNamed(
        Screen.dopNativeIntroductionScreen.name,
        extra: any(named: 'extra'),
      ),
    );
  });
}
