import 'package:evoapp/feature/dop_native/util/dop_native_submit_status_polling/dop_native_submit_status_polling_impl.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const Duration testDuration = Duration(seconds: 1);
  late DOPNativeSubmitStatusPollingImpl pollingImpl;
  late bool pollingCalled;

  setUp(() {
    pollingImpl = DOPNativeSubmitStatusPollingImpl(intervalDuration: testDuration);
    pollingCalled = false;
  });

  test('delayToPolling should call onDoPolling after a delay', () async {
    pollingImpl.delayToPolling(onDoPolling: () {
      pollingCalled = true;
    });

    // Wait for the delay
    await Future<void>.delayed(const Duration(seconds: 2));
    expect(pollingCalled, isTrue);
  });

  test('cancel should stop the polling', () async {
    pollingImpl.delayToPolling(onDoPolling: () {
      pollingCalled = true;
    });

    pollingImpl.cancel();

    // Wait for the delay
    await Future<void>.delayed(const Duration(seconds: 2));
    expect(pollingCalled, isFalse);
  });
}
