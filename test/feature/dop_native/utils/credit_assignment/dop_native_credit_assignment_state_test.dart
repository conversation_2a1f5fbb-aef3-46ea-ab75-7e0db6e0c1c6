import 'package:evoapp/feature/dop_native/util/credit_assignment/cubit/dop_native_credit_assignment_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeErrorMessage = 'fakeErrorMessage';
  const int fakeCreditAmount = 5000000;

  group('DOPNative inform success state test', () {
    test('DOPNativeCreditAssignmentInitial should be an instance of DOPNativeCreditAssignmentState',
        () {
      final DOPNativeCreditAssignmentInitial state = DOPNativeCreditAssignmentInitial();
      expect(state, isA<DOPNativeCreditAssignmentState>());
    });

    test('DOPNativeCreditAssignmentLoading should be an instance of DOPNativeCreditAssignmentState',
        () {
      final DOPNativeCreditAssignmentLoading state = DOPNativeCreditAssignmentLoading();
      expect(state, isA<DOPNativeCreditAssignmentState>());
    });

    test(
        'DOPNativeCreditAssignmentLoadFail should be an instance of DOPNativeCreditAssignmentState',
        () {
      final ErrorUIModel errorModel = ErrorUIModel(userMessage: fakeErrorMessage);
      final DOPNativeCreditAssignmentLoadFail state = DOPNativeCreditAssignmentLoadFail(errorModel);
      expect(state, isA<DOPNativeCreditAssignmentState>());
      expect(state.error, equals(errorModel));
    });

    test(
        'DOPNativeCreditAssignmentLoadSuccess should be an instance of DOPNativeCreditAssignmentState',
        () {
      final DOPNativeCreditAssignmentLoadSuccess state =
          DOPNativeCreditAssignmentLoadSuccess(fakeCreditAmount);
      expect(state, isA<DOPNativeCreditAssignmentState>());
      expect(state.creditAmount, isA<int>());
      expect(state.creditAmount, fakeCreditAmount);
    });
  });
}
