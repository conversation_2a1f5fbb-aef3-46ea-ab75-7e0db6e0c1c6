import 'package:evoapp/feature/dop_native/util/mock_file/mock_dop_native_log_event_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('enum value MockDOPNativeESignStateUseCase', () {
    expect(
      MockDOPNativeLogEventUseCase.logEventSuccess.value,
      'dop_native_log_event_success.json',
    );
  });

  test('test getMockDOPNativeLogEvent returns correct value for logEventSuccess', () {
    final String result = getMockDOPNativeLogEvent(MockDOPNativeLogEventUseCase.logEventSuccess);
    expect(result, 'dop_native_log_event_success.json');
  });
}
