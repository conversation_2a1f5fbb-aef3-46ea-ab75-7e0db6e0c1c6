import 'package:evoapp/feature/dop_native/util/mock_file/mock_dop_native_esign_state_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('enum value MockDOPNativeESignStateUseCase', () {
    expect(
      MockDOPNativeESignStateUseCase.statePending.value,
      'dop_native_esign_pending.json',
    );
    expect(
      MockDOPNativeESignStateUseCase.stateReady.value,
      'dop_native_esign_ready.json',
    );
    expect(
      MockDOPNativeESignStateUseCase.stateSigned.value,
      'dop_native_esign_signed.json',
    );
    expect(
      MockDOPNativeESignStateUseCase.stateSignedFakePdfUrl.value,
      'dop_native_esign_signed_fake_pdf_url.json',
    );
    expect(
      MockDOPNativeESignStateUseCase.failed.value,
      'dop_native_esign_failed.json',
    );
  });

  test('getMockDOPNativeESignState returns correct values', () {
    expect(
      getMockDOPNativeESignState(MockDOPNativeESignStateUseCase.stateReady),
      'dop_native_esign_ready.json',
    );
    expect(
      getMockDOPNativeESignState(MockDOPNativeESignStateUseCase.statePending),
      'dop_native_esign_pending.json',
    );
    expect(
      getMockDOPNativeESignState(MockDOPNativeESignStateUseCase.stateSigned),
      'dop_native_esign_signed.json',
    );
    expect(
      getMockDOPNativeESignState(MockDOPNativeESignStateUseCase.stateSignedFakePdfUrl),
      'dop_native_esign_signed_fake_pdf_url.json',
    );
    expect(
      getMockDOPNativeESignState(MockDOPNativeESignStateUseCase.failed),
      'dop_native_esign_failed.json',
    );
  });
}
