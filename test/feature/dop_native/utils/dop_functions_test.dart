import 'package:evoapp/data/constants.dart';
import 'package:evoapp/data/response/dop_native/dop_native_application_state_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_cif_info_entity.dart';
import 'package:evoapp/feature/deep_link/deep_link_utils.dart';
import 'package:evoapp/feature/dop_native/dialogs/input_phone_number/dop_native_input_phone_number_dialog.dart';
import 'package:evoapp/feature/dop_native/dop_native_constants.dart';
import 'package:evoapp/feature/dop_native/features/cif_confirm/dop_native_cif_confirm_case.dart';
import 'package:evoapp/feature/dop_native/features/cif_confirm/dop_native_cif_confirm_ui_version.dart';
import 'package:evoapp/feature/dop_native/features/pdf_view/dop_native_pdf_screen.dart';
import 'package:evoapp/feature/dop_native/features/web_view/dop_native_webview_screen.dart';
import 'package:evoapp/feature/dop_native/models/dop_native_dialog_id.dart';
import 'package:evoapp/feature/dop_native/models/dop_native_state.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_website_url.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/feature/dop_native/util/validation/dop_native_validation_case_model.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/sharing/sharing_feature.dart';
import 'package:evoapp/feature/user_journey/user_journey_handler.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/colors.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/text_styles.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/util/url_launcher_uri_wrapper.dart';
import 'package:evoapp/widget/evo_dialog/dialog_confirm.dart';
import 'package:evoapp/widget/pdf_view/pdf_view_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/common_package/url_launcher.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/dio_http_client_impl.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/button_styles.dart';
import 'package:flutter_common_package/resources/colors.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/resources/text_styles.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_dialog_bottom_sheet/common_dialog_bottom_sheet.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';
import '../../home/<USER>/v2/story/widgets/story_web_view_test.dart';

class MockAppState extends Mock implements AppState {}

class MockDOPNativeState extends Mock implements DOPNativeState {}

class MockUrlLauncherWrapper extends Mock implements UrlLauncherWrapper {}

class FakeUri extends Fake implements Uri {}

class FakeWebViewConfiguration extends Fake implements WebViewConfiguration {}

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockEvoFlutterWrapper extends Mock implements EvoFlutterWrapper {}

class MockCommonButtonStyles extends Mock implements CommonButtonStyles {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

class MockSignOutFunction extends Mock {
  void signOut();
}

class MockSharingFeature extends Mock implements SharingFeature {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockDeepLinkUtils extends Mock implements DeepLinkUtils {}

void main() {
  late DOPUtilFunctions dopUtilFunctions;
  const String fakeAccessToken = 'fakeAccessToken';
  const String fakeCompanyNameTooLong =
      'zQzNe3pEBpRRoaKFSityesEK0IQDuZP8Hj2hqTPWn9SNz1X41MAtiHNAVEFsQINo5vMKcA9aY0RZcH5aHeLtL23GRM3A2myIIpCESMpevbxZ9D2UN0XQbI9aRDg2BFS4QAYIqkUJNHiHjmiLuVlagXPot7oS8HCkkcGJtnm9FJuTOfvmzFrrK7qoRA8rA6iZvZ0Z3O9oW';

  late BuildContext mockNavigatorContext;
  late CommonNavigator commonNavigator;

  late Dio dopHttpClient;
  late DioClientImpl dioClientImpl;

  final AppState mockAppState = MockAppState();
  final DOPNativeState mockDOPNativeState = MockDOPNativeState();

  final MockEvoFlutterWrapper mockEvoFlutterWrapper = MockEvoFlutterWrapper();
  final MockCommonButtonStyles mockCommonButtonStyles = MockCommonButtonStyles();

  const ButtonStyle defaultPositiveButtonStyle = ButtonStyle();
  const ButtonStyle defaultNegativeButtonStyle = ButtonStyle();

  late MockSharingFeature mockSharingFeature;

  setUpAll(() {
    registerFallbackValue(EvoDialogId.activateCameraPermissionDialog);
    setUtilsMockInstanceForTesting();
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());

    getIt.registerSingleton<DOPUtilFunctions>(DOPUtilFunctions());
    getIt.registerSingleton<FeatureToggle>(FeatureToggle());

    mockNavigatorContext = MockBuildContext();
    registerFallbackValue(mockNavigatorContext);

    getIt.registerSingleton<AppState>(mockAppState);
    when(() => mockAppState.dopNativeState).thenReturn(mockDOPNativeState);

    registerFallbackValue(DOPNativeDialogId.confirmCloseDOPNativeFlowDialog);

    // Register EvoFlutterWrapper
    getIt.registerLazySingleton<EvoFlutterWrapper>(() => mockEvoFlutterWrapper);
    when(() => evoFlutterWrapper.showDialog<void>(
          barrierDismissible: any(named: 'barrierDismissible'),
          builder: any(named: 'builder'),
        )).thenAnswer((_) => Future<void>.value());

    // Register CommonButtonStyles
    getIt.registerLazySingleton<CommonButtonStyles>(() => mockCommonButtonStyles);
    when(() => mockCommonButtonStyles.primary(ButtonSize.xLarge))
        .thenReturn(defaultPositiveButtonStyle);
    when(() => mockCommonButtonStyles.tertiary(ButtonSize.xLarge))
        .thenReturn(defaultNegativeButtonStyle);

    mockSharingFeature = MockSharingFeature();
    getIt.registerSingleton<SharingFeature>(mockSharingFeature);

    getIt.registerSingleton<EvoUtilFunction>(MockEvoUtilFunction());

    when(() => EvoDialogHelper().showDialogConfirm(
          onClickPositive: any(named: 'onClickPositive'),
          title: any(named: 'title'),
          content: any(named: 'content'),
          textPositive: any(named: 'textPositive'),
          textNegative: any(named: 'textNegative'),
          dialogId: any(named: 'dialogId'),
        )).thenAnswer((_) => Future<void>.value());

    getIt.registerSingleton<DeepLinkUtils>(MockDeepLinkUtils());
  });

  setUp(() {
    dopHttpClient = Dio();
    getIt.registerSingleton<Dio>(
      dopHttpClient,
      instanceName: getItInstanceNameForDOPNative,
    );
    getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());
    commonNavigator = getIt.get<CommonNavigator>();
    setUpMockGlobalKeyProvider(mockNavigatorContext);
    dioClientImpl = DioClientImpl(dopHttpClient);
    getIt.registerSingleton<CommonHttpClient>(
      dioClientImpl,
      instanceName: getItInstanceNameForDOPNative,
    );

    dopUtilFunctions = getIt.get<DOPUtilFunctions>();
  });

  tearDown(() {
    getIt.unregister(instance: dopHttpClient);
    getIt.unregister(instance: dioClientImpl);
    getIt.unregister<GlobalKeyProvider>();
    getIt.unregister<CommonNavigator>();
    reset(mockDOPNativeState);
  });

  tearDownAll(() {
    resetUtilMockToOriginalInstance();
    getIt.reset();
  });

  group('verify getDOPNativeAccessTokenHeader()', () {
    test('returns correct header when accessToken is not null and not empty', () {
      const String accessToken = 'some_access_token';

      final Map<String, String?> result =
          dopUtilFunction.getDOPNativeAccessTokenHeader(accessToken);

      expect(result, containsPair(HeaderKey.dopNativeAuthorization, 'Bearer $accessToken'));
    });

    test('returns null for header value when accessToken is null', () {
      final Map<String, String?> result = dopUtilFunction.getDOPNativeAccessTokenHeader(null);

      expect(result, containsPair(HeaderKey.dopNativeAuthorization, null));
    });

    test('returns null for header value when accessToken is empty', () {
      const String accessToken = '';

      final Map<String, String?> result =
          dopUtilFunction.getDOPNativeAccessTokenHeader(accessToken);

      expect(result, containsPair(HeaderKey.dopNativeAuthorization, null));
    });
  });

  group('Test setDOPNativeAccessTokenHeader', () {
    test('Give accessToken NOT null', () {
      expect(dopHttpClient.options.headers.containsKey(HeaderKey.dopNativeAuthorization), false);

      const String fakeAccessToken = 'fakeAccessToken';
      dopUtilFunctions.setDOPNativeAccessTokenHeader(fakeAccessToken);

      expect(dopHttpClient.options.headers.containsKey(HeaderKey.dopNativeAuthorization), true);
      expect(dopHttpClient.options.headers[HeaderKey.dopNativeAuthorization],
          'Bearer $fakeAccessToken');
    });

    test('Give accessToken null', () {
      expect(dopHttpClient.options.headers.containsKey(HeaderKey.dopNativeAuthorization), false);

      dopUtilFunctions.setDOPNativeAccessTokenHeader(null);

      expect(dopHttpClient.options.headers.containsKey(HeaderKey.dopNativeAuthorization), true);
      expect(dopHttpClient.options.headers[HeaderKey.dopNativeAuthorization], null);
    });
  });

  group('queryDiacriticsFilter tests', () {
    test('Returns all items when query is empty', () {
      final List<String> testData = <String>['Apple', 'Banana', 'Cherry'];
      final List<String> results = dopUtilFunctions.queryDiacriticsFilter(
        query: '',
        data: testData,
        filterBy: (String item) => item,
      );
      expect(results.length, testData.length);
      expect(results, testData);
    });

    test('Returns matching items', () {
      final List<String> testData = <String>['Apple', 'Banana', 'Cherry'];
      final List<String> results = dopUtilFunctions.queryDiacriticsFilter(
        query: 'an',
        data: testData,
        filterBy: (String item) => item,
      );
      expect(results.length, 1);
      expect(results, contains('Banana'));
    });

    test('Returns no items when no matches', () {
      final List<String> testData = <String>['Apple', 'Banana', 'Cherry'];
      final List<String> results = dopUtilFunctions.queryDiacriticsFilter(
        query: 'xyz',
        data: testData,
        filterBy: (String item) => item,
      );
      expect(results.isEmpty, true);
    });

    test('Ignores diacritics and case in query', () {
      final List<String> testData = <String>['Café', 'Cafe', 'Car'];
      final List<String> results = dopUtilFunctions.queryDiacriticsFilter(
        query: 'CAFÉ',
        data: testData,
        filterBy: (String item) => item,
      );
      expect(results.length, 2);
      expect(results, contains('Café'));
      expect(results, contains('Cafe'));
    });

    test('Handles custom objects and filterBy function', () {
      final List<Map<String, String>> testData = <Map<String, String>>[
        <String, String>{'name': 'Apple', 'color': 'Green'},
        <String, String>{'name': 'Banana', 'color': 'Yellow'},
        <String, String>{'name': 'Cherry', 'color': 'Red'}
      ];
      final List<Map<String, dynamic>> results =
          dopUtilFunctions.queryDiacriticsFilter<Map<String, dynamic>>(
        query: 'red',
        data: testData,
        filterBy: (Map<String, dynamic> item) => item['color'] as String,
      );
      expect(results.length, 1);
      expect(results.first, equals(testData[2]));
    });
  });

  group('Test clearDOPNativeAccessTokenHeader', () {
    test('Can clear previous dop-authorization in http client header', () {
      dopUtilFunctions.setDOPNativeAccessTokenHeader(fakeAccessToken);

      dopUtilFunctions.clearDOPNativeAccessTokenHeader();
      expect(dopHttpClient.options.headers.containsKey(HeaderKey.dopNativeAuthorization), false);
    });
  });

  group('Test clearDOPNativeData', () {
    test(
        'clearDOPNativeData clears all DOPNativeState data and dop-authorization in http client header',
        () {
      dopUtilFunctions.setDOPNativeAccessTokenHeader(fakeAccessToken);

      dopUtilFunctions.clearDOPNativeData();

      verify(() => mockAppState.dopNativeState).called(1);
      verify(() => mockDOPNativeState.clear()).called(1);
      expect(dopHttpClient.options.headers.containsKey(HeaderKey.dopNativeAuthorization), false);
    });
  });

  group('isVietnameseCitizenIdCardBefore2016', () {
    test('should return false if idCard is null', () {
      final bool result = dopUtilFunctions.isVietnameseCitizenIdCardBefore2016(null);
      expect(result, false);
    });

    test('should return true if CommonValidator returns before_2016', () {
      const String idCard = '123456789';
      final bool result = dopUtilFunctions.isVietnameseCitizenIdCardBefore2016(idCard);
      expect(result, true);
    });

    test('should return false if CommonValidator returns any type other than after_2016', () {
      const String idCard = '123456789123';
      final bool result = dopUtilFunctions.isVietnameseCitizenIdCardBefore2016(idCard);
      expect(result, false);
    });
  });

  group('isVietnameseCitizenIdCardAfter2016', () {
    test('should return false if idCard is null', () {
      final bool result = dopUtilFunctions.isVietnameseCitizenIdCardAfter2016(null);
      expect(result, false);
    });

    test('should return true if CommonValidator returns after_2016', () {
      const String idCard = '123456789101';
      final bool result = dopUtilFunctions.isVietnameseCitizenIdCardAfter2016(idCard);
      expect(result, true);
    });

    test('should return false if CommonValidator returns any type other than before_2016', () {
      const String idCard = '123456789';
      final bool result = dopUtilFunctions.isVietnameseCitizenIdCardAfter2016(idCard);
      expect(result, false);
    });
  });

  group('isValidDOPAddress', () {
    test('should return false if address has special character', () {
      const String address = '123 @ Street';
      final bool result = dopUtilFunctions.isValidDOPAddress(address: address);
      expect(result, false);
    });

    test('should return false if address has not special character', () {
      const String address = '123/45 Nguyễn Văn A, P.1, Q.2';
      final bool result = dopUtilFunctions.isValidDOPAddress(address: address);
      expect(result, true);
    });
  });

  group('formatDOPBirthday', () {
    test('should return empty with empty birthday', () {
      const String birthday = '';
      final String? result = dopUtilFunctions.formatDOPBirthday(birthday);
      expect(result, null);
    });

    test('should return birthday Formatted', () {
      const String birthday = '1989-02-01';
      final String? result = dopUtilFunctions.formatDOPBirthday(birthday);
      expect(result, '01/02/1989');
    });
  });

  group('formatIncomeAmount utils', () {
    test('returns double when input is a valid number with dot', () {
      final double? result = dopUtilFunctions.formatIncomeAmount(text: '1234.56');
      expect(result, equals(1234.56));
    });

    test('returns double when input is a valid number with commas ', () {
      final double? result = dopUtilFunctions.formatIncomeAmount(text: '1234,56');
      expect(result, equals(1234.56));
    });

    test('returns null when input is not a number', () {
      final double? result = dopUtilFunctions.formatIncomeAmount(text: 'not a number');
      expect(result, isNull);
    });

    test('returns null when input is empty', () {
      final double? result = dopUtilFunctions.formatIncomeAmount(text: '');
      expect(result, isNull);
    });
  });

  group('IncomeCase test', () {
    test('returns IncomeCase.empty when input is empty', () {
      final IncomeCase result = dopUtilFunctions.getIncomeCase('');
      expect(result, equals(IncomeCase.empty));
    });

    test('returns IncomeCase.tooSmall when input is too small', () {
      IncomeCase result = dopUtilFunctions.getIncomeCase('0');
      expect(result, equals(IncomeCase.tooSmall));

      result = dopUtilFunctions.getIncomeCase('0.0');
      expect(result, equals(IncomeCase.tooSmall));

      result = dopUtilFunctions.getIncomeCase('-1');
      expect(result, equals(IncomeCase.tooSmall));
    });

    test('returns IncomeCase.invalid when input is invalid', () {
      final IncomeCase result = dopUtilFunctions.getIncomeCase('invalid');
      expect(result, equals(IncomeCase.invalid));
    });

    test('returns IncomeCase.exceed when input exceeds max income', () {
      final IncomeCase result =
          dopUtilFunctions.getIncomeCase('99999,9999'); // Assuming maxIncomeInBillions is 1.0
      expect(result, equals(IncomeCase.exceed));
    });

    test('returns IncomeCase.valid when input is a valid income', () {
      final IncomeCase result = dopUtilFunctions.getIncomeCase('0.5');
      expect(result, equals(IncomeCase.valid));
    });
  });

  group('AddressCase test', () {
    test('returns AddressCase.empty', () {
      final AddressCase result = dopUtilFunctions.getAddressCase('');
      expect(result, AddressCase.empty);
    });

    test('returns AddressCase.tooLong', () {
      const String fakeAddress =
          'xgboutmldchbgvorhwnqkwliwuoqjvuogmjmltjboatoqabigggbywmepwkpvruswlzsdti';
      final AddressCase result = dopUtilFunctions.getAddressCase(fakeAddress);
      expect(result, AddressCase.tooLong);
    });

    test('returns AddressCase.hasSpecialCharacter ', () {
      const String fakeAddress = '123@ Street';
      final AddressCase result = dopUtilFunctions.getAddressCase(fakeAddress);
      expect(result, AddressCase.hasSpecialCharacter);
    });

    test('returns AddressCase.valid', () {
      final AddressCase result = dopUtilFunctions.getAddressCase('123 Hai Ba Trung');
      expect(result, AddressCase.valid);
    });
  });

  group('convertIncome utils', () {
    test('returns null when input is null', () {
      final int? result = dopUtilFunctions.convertIncome(null);
      expect(result, isNull);
    });

    test('returns null when formatIncomeAmount returns null', () {
      final int? result = dopUtilFunctions.convertIncome('invalid input');
      expect(result, isNull);
    });

    test('returns correct int value when formatIncomeAmount returns a double', () {
      const String testInput = '123,4';
      const int testOutput = 123400000; // 1000.0 * 1e6

      final int? result = dopUtilFunctions.convertIncome(testInput);

      expect(result, equals(testOutput));
    });
  });

  group('Test launchUrl', () {
    final Uri uri = FakeUri();
    final WebViewConfiguration webViewConfiguration = FakeWebViewConfiguration();

    late UrlLauncherWrapper urlLauncherWrapper;

    setUpAll(() {
      getIt.registerLazySingleton<UrlLauncherWrapper>(() => MockUrlLauncherWrapper());
      urlLauncherWrapper = getIt.get<UrlLauncherWrapper>();

      registerFallbackValue(uri);
      registerFallbackValue(LaunchMode.platformDefault);
      registerFallbackValue(webViewConfiguration);

      when(() => urlLauncherWrapper.launchUrl(
            any(),
            mode: any(named: 'mode'),
            webViewConfiguration: any(named: 'webViewConfiguration'),
            webOnlyWindowName: any(named: 'webOnlyWindowName'),
          )).thenAnswer((_) async => true);
    });

    tearDownAll(() {
      getIt.unregister(instance: urlLauncherWrapper);
    });

    test('launchUrl with default params', () {
      dopUtilFunctions.launchUrl(uri);

      expect(
        verify(() => urlLauncherWrapper.launchUrl(
              uri,
              mode: captureAny(named: 'mode'),
              webViewConfiguration: captureAny(named: 'webViewConfiguration'),
              webOnlyWindowName: captureAny(named: 'webOnlyWindowName'),
            )).captured,
        <dynamic>[
          LaunchMode.platformDefault,
          isA<WebViewConfiguration>()
              .having(
                (WebViewConfiguration p0) => p0.enableDomStorage,
                'verify enableDomStorage',
                true,
              )
              .having(
                (WebViewConfiguration p0) => p0.enableJavaScript,
                'verify enableJavaScript',
                true,
              )
              .having(
                (WebViewConfiguration p0) => p0.headers,
                'verify headers',
                equals(<String, String>{}),
              ),
          null,
        ],
      );
    });

    test('launchUrl with custom params', () {
      const LaunchMode launchMode = LaunchMode.inAppWebView;
      final Map<String, String> fakeHeaders = <String, String>{'key': 'value'};
      const String webOnlyWindowName = 'webOnlyWindowName';

      dopUtilFunctions.launchUrl(
        uri,
        mode: launchMode,
        webViewConfiguration: WebViewConfiguration(
          enableDomStorage: false,
          enableJavaScript: false,
          headers: fakeHeaders,
        ),
        webOnlyWindowName: webOnlyWindowName,
      );

      expect(
        verify(() => urlLauncherWrapper.launchUrl(
              uri,
              mode: captureAny(named: 'mode'),
              webViewConfiguration: captureAny(named: 'webViewConfiguration'),
              webOnlyWindowName: captureAny(named: 'webOnlyWindowName'),
            )).captured,
        <dynamic>[
          launchMode,
          isA<WebViewConfiguration>()
              .having(
                (WebViewConfiguration p0) => p0.enableDomStorage,
                'verify enableDomStorage',
                false,
              )
              .having(
                (WebViewConfiguration p0) => p0.enableJavaScript,
                'verify enableJavaScript',
                false,
              )
              .having(
                (WebViewConfiguration p0) => p0.headers,
                'verify headers',
                equals(fakeHeaders),
              ),
          webOnlyWindowName,
        ],
      );
    });
  });

  group('test openDOPPDFView', () {
    final DOPNativePDFViewArg fakeArgument = DOPNativePDFViewArg(url: 'testUrl');
    final String screenName = Screen.dopNativePDFViewScreen.name;

    test('openDOPPDFView navigate right screen with right argument', () {
      dopUtilFunctions.openDOPPDFView(fakeArgument);
      expect(
        verify(() => mockNavigatorContext.pushNamed(
              screenName,
              extra: captureAny(named: 'extra'),
            )).captured,
        <dynamic>[
          isA<DOPNativePDFViewArg>().having(
            (DOPNativePDFViewArg arg) => arg.url,
            'verify url',
            isNotNull,
          ),
        ],
      );
    });
  });

  group('Test openDOPWebView', () {
    test('openDOPWebView should open right screen', () {
      const String url = 'fakeUrl';
      dopUtilFunctions.openDOPWebView(DOPNativeWebViewArg(url: url));
      expect(
        verify(() => commonNavigator.pushNamed(
              any(),
              captureAny(),
              extra: captureAny(named: 'extra'),
            )).captured,
        <dynamic>[
          Screen.dopNativeWebViewScreenName,
          isA<DOPNativeWebViewArg>().having((DOPNativeWebViewArg p0) => p0.url, 'verify url', url),
        ],
      );
    });

    test('openOtherProductWeb should open right screen', () {
      dopUtilFunctions.openOtherProductWeb();
      expect(
        verify(() => commonNavigator.pushNamed(
              any(),
              captureAny(),
              extra: captureAny(named: 'extra'),
            )).captured,
        <dynamic>[
          Screen.dopNativeWebViewScreenName,
          isA<DOPNativeWebViewArg>().having((DOPNativeWebViewArg p0) => p0.url, 'verify url',
              DOPNativeWebsiteUrl.otherProductWebUrl),
        ],
      );
    });
  });

  group('Test clearDOPSessionData', () {
    test('should clear data relate to a DOP application', () {
      dopUtilFunctions.clearDOPSessionData();

      verify(() => mockAppState.dopNativeState).called(1);
      verify(() => mockDOPNativeState.clearDOPSessionData()).called(1);
    });
  });

  group('PhoneCase test', () {
    test('returns PhoneCase.empty', () {
      final PhoneCase result = dopUtilFunctions.getPhoneCase('');
      expect(result, PhoneCase.empty);
    });

    test('returns PhoneCase.invalid ', () {
      const String fakePhone = '1239999';
      final PhoneCase result = dopUtilFunctions.getPhoneCase(fakePhone);
      expect(result, PhoneCase.invalid);
    });

    test('returns PhoneCase.valid', () {
      const String fakePhone = '0912345678';
      final PhoneCase result = dopUtilFunctions.getPhoneCase(fakePhone);
      expect(result, PhoneCase.valid);
    });
  });

  group('CompanyNameCase test', () {
    test('returns CompanyNameCase.empty', () {
      final CompanyNameCase result = dopUtilFunctions.getCompanyNameCase('');
      expect(result, CompanyNameCase.empty);
    });

    test('returns CompanyNameCase.tooLong', () {
      final CompanyNameCase result = dopUtilFunctions.getCompanyNameCase(fakeCompanyNameTooLong);
      expect(result, CompanyNameCase.tooLong);
    });

    test('returns CompanyNameCase.valid', () {
      const String fakeCompany = 'TNHH Trần Thành Công 123';
      final CompanyNameCase result = dopUtilFunctions.getCompanyNameCase(fakeCompany);
      expect(result, CompanyNameCase.valid);
    });

    test('returns CompanyNameCase.valid with hasSpecialCharacter', () {
      const String fakeCompany = 'TNHH !Trần Thành Công 123';
      final CompanyNameCase result = dopUtilFunctions.getCompanyNameCase(fakeCompany);
      expect(result, CompanyNameCase.valid);
    });
  });

  group('verify validatePOSLimit()', () {
    test('verify validatePOSLimit() with valid case', () {
      final PosLimitCase resultCase = dopUtilFunctions.validatePOSLimit(
        posLimitInputted: 1000,
        posLimitAllowed: 1000,
      );

      expect(resultCase, PosLimitCase.valid);
    });

    test('verify validatePOSLimit() with value < POS limit', () {
      final PosLimitCase resultCase = dopUtilFunctions.validatePOSLimit(
        posLimitInputted: 90,
        posLimitAllowed: 1000,
      );

      expect(resultCase, PosLimitCase.valid);
    });

    test('verify validatePOSLimit() with value > POS limit', () {
      final PosLimitCase resultCase = dopUtilFunctions.validatePOSLimit(
        posLimitInputted: 1001,
        posLimitAllowed: 1000,
      );

      expect(resultCase, PosLimitCase.invalid);
    });

    test('verify validatePOSLimit() with value = 0', () {
      final PosLimitCase resultCase = dopUtilFunctions.validatePOSLimit(
        posLimitInputted: 0,
        posLimitAllowed: 1000,
      );

      expect(resultCase, PosLimitCase.invalid);
    });
  });

  group('isPhoneNumberValid test', () {
    final Map<String, bool> testCases = <String, bool>{
      /// correct
      '0311111111': true,
      '0511111111': true,
      '0711111111': true,
      '0811111111': true,
      '0911111111': true,

      /// wrong
      '': false, // empty
      // old phone number
      '01691111111': false,
      '01201111111': false,
      // wrong prefix
      '0011111111': false,
      '0111111111': false,
      '0211111111': false,
      '0411111111': false,
      '0611111111': false,
      '84011111111': false,
      '84111111111': false,
      '84211111111': false,
      '84411111111': false,
      '84611111111': false,
      '8435999999': false,
      '84311111111': false,
      '84511111111': false,
      '84711111111': false,
      '84811111111': false,
      '84911111111': false,
      // too short
      '031111': false,
      '8431111': false,
      // too long
      '03111111111': false,
      '843111111111': false,
      // contain non-number character
      '0371111a11': false,
    };
    test('isPhoneNumberValid return correct result of test cases', () {
      testCases.forEach((String key, bool value) {
        expect(dopUtilFunctions.isPhoneNumberValid(key), value);
      });
    });
  });

  group('validateEmail test', () {
    test('validateEmail return false if email is empty', () {
      final bool result = dopUtilFunctions.validateEmail(email: '');
      expect(result, false);
    });

    test('validateEmail return true if email is valid', () {
      final bool result = dopUtilFunctions.validateEmail(email: '<EMAIL>');
      expect(result, true);
    });
  });

  group('Test show blurred tooltip', () {
    testWidgets('showBlurredTooltip adds and auto removes overlay', (WidgetTester tester) async {
      bool isTooltipAdded = false;

      void showTooltip(BuildContext context) {
        dopUtilFunctions.showBlurredTooltip(
          context: context,
          targetKey: GlobalKey(),
          message: 'Your message',
        );
        isTooltipAdded = true;
      }

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (BuildContext context) {
              return ElevatedButton(
                onPressed: () => showTooltip(context),
                child: const Text('Show Tooltip'),
              );
            },
          ),
        ),
      );

      // Verify if tooltip is not added initially
      expect(isTooltipAdded, isFalse);

      // Tap the button to trigger the tooltip
      await tester.tap(find.byType(ElevatedButton));
      await tester.pumpAndSettle();

      // Verify if the tooltip is added
      expect(isTooltipAdded, isTrue);

      // Simulate the 3-second delay
      await tester.pump(const Duration(seconds: 3));

      await tester.pumpAndSettle();
      expect(find.byKey(Key('remove_blur_button')), findsNothing);
    });

    testWidgets('showBlurredTooltip adds and manual tap to removes overlay',
        (WidgetTester tester) async {
      bool isTooltipAdded = false;

      void showTooltip(BuildContext context) {
        dopUtilFunctions.showBlurredTooltip(
          context: context,
          targetKey: GlobalKey(),
          message: 'Your message',
        );
        isTooltipAdded = true;
      }

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (BuildContext context) {
              return ElevatedButton(
                onPressed: () => showTooltip(context),
                child: const Text('Show Tooltip'),
              );
            },
          ),
        ),
      );

      // Verify if tooltip is not added initially
      expect(isTooltipAdded, isFalse);

      // Tap the button to trigger the tooltip
      await tester.tap(find.byType(ElevatedButton));
      await tester.pumpAndSettle();

      // Verify if the tooltip is added
      expect(isTooltipAdded, isTrue);

      // Tap the dismiss tooltip
      await tester.tap(find.byKey(Key('remove_blur_button')));
      await tester.pumpAndSettle();

      expect(find.byKey(Key('remove_blur_button')), findsNothing);
      // Simulate the 3-second delay
      await tester.pump(const Duration(seconds: 3));
    });
  });

  group('Test openTPBAppLink', () {
    final Uri uri = FakeUri();
    final WebViewConfiguration webViewConfiguration = FakeWebViewConfiguration();

    late UrlLauncherWrapper urlLauncherWrapper;

    setUpAll(() {
      getIt.registerLazySingleton<UrlLauncherWrapper>(() => MockUrlLauncherWrapper());
      urlLauncherWrapper = getIt.get<UrlLauncherWrapper>();

      registerFallbackValue(uri);
      registerFallbackValue(LaunchMode.platformDefault);
      registerFallbackValue(webViewConfiguration);

      when(() => urlLauncherWrapper.launchUrl(
            any(),
            mode: any(named: 'mode'),
            webViewConfiguration: any(named: 'webViewConfiguration'),
            webOnlyWindowName: any(named: 'webOnlyWindowName'),
          )).thenAnswer((_) async => true);
    });

    tearDownAll(() {
      getIt.unregister(instance: urlLauncherWrapper);
    });

    test('openTPBAppLink should launch with right url', () {
      const String url = DOPNativeWebsiteUrl.tpbAppLink;
      dopUtilFunctions.openTPBAppLink();
      expect(
          verify(() => dopUtilFunctions.launchUrl(
                captureAny(),
              )).captured,
          <dynamic>[
            isA<Uri>().having(
              (Uri p0) => p0.toString(),
              'verify uri',
              url,
            ),
          ]);
    });
  });

  group('Test showDialogConfirm', () {
    test('showDialogConfirm with default param', () async {
      const String textPositive = 'textPositive';
      const DOPNativeDialogId dialogId = DOPNativeDialogId.confirmCloseDOPNativeFlowDialog;

      dopUtilFunctions.showDialogConfirm(
        textPositive: textPositive,
        dialogId: dialogId,
      );

      final WidgetBuilder builder = verify(() => evoFlutterWrapper.showDialog<void>(
            barrierDismissible: any(named: 'barrierDismissible'),
            builder: captureAny(named: 'builder'),
          )).captured.single;

      final PopScope dialogWidget = builder.call(mockNavigatorContext) as PopScope;
      expect(dialogWidget.canPop, true);

      final EvoDialogConfirm dialogChild = dialogWidget.child as EvoDialogConfirm;

      expect(dialogChild.textPositive, textPositive);
      expect(dialogChild.dialogId, dialogId.id);

      expect(dialogChild.title, isNull);
      expect(dialogChild.textNegative, isNull);
      expect(dialogChild.footer, isNull);
      expect(dialogChild.onClickPositive, isNull);
      expect(dialogChild.onClickNegative, isNull);
      expect(dialogChild.imageHeader, isNull);
      expect(dialogChild.positiveButtonStyle, defaultPositiveButtonStyle);
      expect(dialogChild.negativeButtonStyle, defaultNegativeButtonStyle);
      expect(dialogChild.titleTextStyle, isNull);
      expect(dialogChild.contentTextStyle, isNull);
      expect(dialogChild.isShowButtonClose, false);
      expect(dialogChild.titleTextAlign, isNull);
      expect(dialogChild.contentTextAlign, isNull);
      expect(dialogChild.buttonListOrientation, isNull);
      expect(dialogChild.positiveDelayOverlay, isNull);
      expect(dialogChild.positiveDelayInSeconds, isNull);
    });

    test('showDialogConfirm with custom param', () async {
      const String textPositive = 'textPositive';
      const DOPNativeDialogId dialogId = DOPNativeDialogId.confirmCloseDOPNativeFlowDialog;
      const String content = 'content';
      const String title = 'title';
      const String textNegative = 'textNegative';
      const SizedBox footer = SizedBox();
      onClickPositive() {}
      onClickNegative() {}
      const SizedBox imageHeader = SizedBox();
      const bool isDismissible = false;
      const ButtonStyle positiveButtonStyle = ButtonStyle();
      const ButtonStyle negativeButtonStyle = ButtonStyle();
      const TextStyle titleTextStyle = TextStyle();
      const TextStyle contentTextStyle = TextStyle();
      final Map<String, dynamic> loggingEventMetaData = <String, dynamic>{
        'fake_metadata_key': 'fake_metadata_value',
      };
      final Map<String, dynamic> loggingEventOnShowMetaData = <String, dynamic>{
        'fake_reason_key': 'fake_reason_value',
      };
      const bool isShowButtonClose = true;
      const TextAlign titleTextAlign = TextAlign.center;
      const TextAlign contentTextAlign = TextAlign.center;
      const ButtonListOrientation buttonListOrientation =
          ButtonListOrientation.horizontalLeftToRight;
      const int positiveDelayInSeconds = 2;

      dopUtilFunctions.showDialogConfirm(
        textPositive: textPositive,
        dialogId: dialogId,
        content: content,
        title: title,
        textNegative: textNegative,
        footer: footer,
        onClickPositive: onClickPositive,
        onClickNegative: onClickNegative,
        imageHeader: imageHeader,
        isDismissible: isDismissible,
        positiveButtonStyle: positiveButtonStyle,
        negativeButtonStyle: negativeButtonStyle,
        titleTextStyle: titleTextStyle,
        contentTextStyle: contentTextStyle,
        loggingEventOnShowMetaData: loggingEventOnShowMetaData,
        loggingEventMetaData: loggingEventMetaData,
        isShowButtonClose: isShowButtonClose,
        titleTextAlign: titleTextAlign,
        contentTextAlign: contentTextAlign,
        buttonListOrientation: buttonListOrientation,
        positiveDelayInSeconds: positiveDelayInSeconds,
      );

      final WidgetBuilder builder = verify(() => evoFlutterWrapper.showDialog<void>(
            barrierDismissible: isDismissible,
            builder: captureAny(named: 'builder'),
          )).captured.single;

      final PopScope dialogWidget = builder.call(mockNavigatorContext) as PopScope;
      expect(dialogWidget.canPop, isDismissible);

      final EvoDialogConfirm dialogChild = dialogWidget.child as EvoDialogConfirm;

      expect(dialogChild.textPositive, textPositive);
      expect(dialogChild.title, title);
      expect(dialogChild.textNegative, textNegative);
      expect(dialogChild.footer, footer);
      expect(dialogChild.onClickPositive, onClickPositive);
      expect(dialogChild.onClickNegative, onClickNegative);
      expect(dialogChild.imageHeader, imageHeader);
      expect(dialogChild.positiveButtonStyle, positiveButtonStyle);
      expect(dialogChild.negativeButtonStyle, negativeButtonStyle);
      expect(dialogChild.titleTextStyle, titleTextStyle);
      expect(dialogChild.contentTextStyle, contentTextStyle);
      expect(dialogChild.dialogId, dialogId.id);
      expect(dialogChild.loggingEventMetaData, loggingEventMetaData);
      expect(dialogChild.loggingEventOnShowMetaData, loggingEventOnShowMetaData);
      expect(dialogChild.isShowButtonClose, isShowButtonClose);
      expect(dialogChild.titleTextAlign, titleTextAlign);
      expect(dialogChild.contentTextAlign, contentTextAlign);
      expect(dialogChild.buttonListOrientation, buttonListOrientation);
      expect(dialogChild.positiveDelayInSeconds, positiveDelayInSeconds);
      expect(dialogChild.positiveDelayOverlay, isNotNull);
      final Widget? overlay = dialogChild.positiveDelayOverlay?.call(positiveDelayInSeconds);
      expect(overlay, isA<Padding>());
    });
  });

  group('test getPaddingBottom', () {
    testWidgets('Test getPaddingBottom return 32 when viewPadding is 0',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (BuildContext context) {
              return Padding(
                padding: EdgeInsets.only(bottom: dopUtilFunctions.getPaddingBottom(context)),
                child: Container(),
              );
            },
          ),
        ),
      );

      await tester.pump();
      final Finder paddingFinder = find.byType(Padding);
      final Padding paddingWidget = tester.widget(paddingFinder);
      expect(paddingWidget.padding, const EdgeInsets.only(bottom: 32));
    });

    testWidgets('Test getPaddingBottom return 0 when viewPadding is not 0',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: MediaQuery(
            data: const MediaQueryData(
              viewPadding: EdgeInsets.only(
                bottom: 10,
              ),
            ),
            child: Builder(
              builder: (BuildContext context) {
                return Padding(
                  padding: EdgeInsets.only(bottom: dopUtilFunctions.getPaddingBottom(context)),
                  child: Container(),
                );
              },
            ),
          ),
        ),
      );

      await tester.pump();
      final Finder paddingFinder = find.byType(Padding);
      final Padding paddingWidget = tester.widget(paddingFinder);
      expect(paddingWidget.padding, const EdgeInsets.only());
    });
  });

  group('verify openPOSLimitDialog()', () {
    setUp(() {
      when(() => mockAppState.dopNativeState).thenReturn(mockDOPNativeState);
    });

    test('cannot open POS limit dialog', () {
      when(() => mockDOPNativeState.posLimitNumber).thenReturn(null);
      bool isFailed = false;
      onFailed(_) {
        isFailed = true;
      }

      dopUtilFunctions.openPOSLimitDialog(onFailed: onFailed);
      expect(isFailed, true);

      verifyNever(() => evoFlutterWrapper.showDialog<void>(
            builder: any(named: 'builder'),
          ));
    });

    test('open POS limit dialog', () {
      when(() => mockDOPNativeState.posLimitNumber).thenReturn(1000);
      bool isFailed = false;
      onFailed(_) {
        isFailed = true;
      }

      dopUtilFunctions.openPOSLimitDialog(onFailed: onFailed);
      expect(isFailed, false);

      verify(() => evoFlutterWrapper.showDialog<void>(
            builder: any(named: 'builder'),
          )).called(1);
    });
  });

  group('verify getAmountFromStr()', () {
    test('getAmountFromStr is called', () {
      const String fakeValue = '1234.56';
      const int fakeResult = 1234;

      when(() => evoUtilFunction.getAmountFromStr(any())).thenReturn(fakeResult);

      final int? result = dopUtilFunctions.getAmountFromStr(fakeValue);

      expect(result, fakeResult);
      verify(() => evoUtilFunction.getAmountFromStr(fakeValue)).called(1);
    });
  });

  group('getDevicePlatform', () {
    late MockDevicePlatform mockDevicePlatform;
    setUp(() {
      mockDevicePlatform = MockDevicePlatform();
      getIt.registerSingleton<DevicePlatform>(mockDevicePlatform);
    });

    tearDown(() {
      getIt.unregister<DevicePlatform>();
    });

    test('returns android when platform is Android', () {
      when(() => mockDevicePlatform.isAndroid()).thenReturn(true);

      final String result = dopUtilFunctions.getDevicePlatform();

      expect(result, DopDevicePlatformConstant.android);
    });

    test('returns ios when platform is iOS', () {
      when(() => mockDevicePlatform.isAndroid()).thenReturn(false);

      final String result = dopUtilFunctions.getDevicePlatform();

      expect(result, DopDevicePlatformConstant.ios);
    });
  });

  group('verify isCorrectDateDOPFormat()', () {
    test('verify with date is null', () {
      final bool result = dopUtilFunctions.isCorrectDateDOPFormat(
        dateInputted: null,
      );

      expect(result, false);
    });

    test('verify with date is empty', () {
      final bool result = dopUtilFunctions.isCorrectDateDOPFormat(
        dateInputted: '',
      );

      expect(result, false);
    });

    test('verify right date format', () {
      const String date = '2022-12-10';
      final bool result = dopUtilFunctions.isCorrectDateDOPFormat(
        dateInputted: date,
      );

      expect(result, true);
    });

    test('verify fail date format with format yy-MM-dd', () {
      const String date = '22-10-12';
      final bool result = dopUtilFunctions.isCorrectDateDOPFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with format yyyy-dd-MM', () {
      const String date = '2022-24-12';
      final bool result = dopUtilFunctions.isCorrectDateDOPFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with format dd/MM/yyyy', () {
      const String date = '2022/12/10';
      final bool result = dopUtilFunctions.isCorrectDateDOPFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with month > 12', () {
      const String date = '2022-13-12';
      final bool result = dopUtilFunctions.isCorrectDateDOPFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with day > 31', () {
      const String date = '2022-11-32';
      final bool result = dopUtilFunctions.isCorrectDateDOPFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with day = 0', () {
      const String date = '2022-11-0';
      final bool result = dopUtilFunctions.isCorrectDateDOPFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with month = 0', () {
      const String date = '2022-0-11';
      final bool result = dopUtilFunctions.isCorrectDateDOPFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with day is invalid', () {
      const String date = '2022-02-30';
      final bool result = dopUtilFunctions.isCorrectDateDOPFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with format yyyy-MM-dd hh:mm:ss', () {
      const String date = '2022-12-11 12:22:11';
      final bool result = dopUtilFunctions.isCorrectDateDOPFormat(
        dateInputted: date,
      );

      expect(result, false);
    });
  });

  group('Test getCifCase', () {
    final DOPNativeCifInfoEntity cifInfoWithUseNewCif =
        DOPNativeCifInfoEntity.fromJson(<String, dynamic>{
      'idCard': 'fake_id_card',
      'useNewCif': true,
    });

    final DOPNativeCifInfoEntity cifInfoWithoutUseNewCif =
        DOPNativeCifInfoEntity.fromJson(<String, dynamic>{
      'idCard': 'fake_id_card',
    });

    DOPNativeState getMockDopNativeState(String uiVersion) {
      return DOPNativeState(
          dopApplicationState: DOPNativeApplicationStateEntity(
        uiVersion: uiVersion,
      ));
    }

    setUp(() {
      when(() => mockAppState.dopNativeState).thenReturn(getMockDopNativeState(
        DOPNativeCifConfirmUIVersion.difNationId.value,
      ));
    });

    group('CifInfo.useNewCif is NOT null', () {
      test(
          'return CifCase.difNationIdWithCifInfo if uiVersion == DOPNativeCifConfirmUIVersion.difNationId',
          () {
        final CifCase cifCase = dopUtilFunctions.getCifCase(
            cifInfo: cifInfoWithUseNewCif,
            dopApplicationState: DOPNativeApplicationStateEntity(
              uiVersion: DOPNativeCifConfirmUIVersion.difNationId.value,
            ));
        expect(cifCase, CifCase.difNationIdWithCifInfo);
      });

      test(
          'return CifCase.otherDifWithCifInfo if uiVersion != DOPNativeCifConfirmUIVersion.difNationId',
          () {
        final CifCase cifCase = dopUtilFunctions.getCifCase(
          cifInfo: cifInfoWithUseNewCif,
          dopApplicationState: DOPNativeApplicationStateEntity(),
        );
        expect(cifCase, CifCase.otherDifWithCifInfo);
      });
    });

    group('CifInfo.useNewCif null', () {
      test(
          'return CifCase.difNationIdWithoutCifInfo if uiVersion == DOPNativeCifConfirmUIVersion.difNationId',
          () {
        final CifCase cifCase = dopUtilFunctions.getCifCase(
          dopApplicationState: DOPNativeApplicationStateEntity(
            uiVersion: DOPNativeCifConfirmUIVersion.difNationId.value,
          ),
          cifInfo: cifInfoWithoutUseNewCif,
        );
        expect(cifCase, CifCase.difNationIdWithoutCifInfo);
      });

      test('return CifCase.difPhone if uiVersion == DOPNativeCifConfirmUIVersion.difPhone', () {
        final CifCase cifCase = dopUtilFunctions.getCifCase(
          dopApplicationState: DOPNativeApplicationStateEntity(
            uiVersion: DOPNativeCifConfirmUIVersion.difPhone.value,
          ),
          cifInfo: cifInfoWithoutUseNewCif,
        );
        expect(cifCase, CifCase.difPhone);
      });

      test('return CifCase.difCif if uiVersion == DOPNativeCifConfirmUIVersion.difCif', () {
        final CifCase cifCase = dopUtilFunctions.getCifCase(
          dopApplicationState: DOPNativeApplicationStateEntity(
            uiVersion: DOPNativeCifConfirmUIVersion.difCif.value,
          ),
          cifInfo: cifInfoWithoutUseNewCif,
        );
        expect(cifCase, CifCase.difCif);
      });

      test('return CifCase.difInfo if uiVersion == DOPNativeCifConfirmUIVersion.difInfo', () {
        final CifCase cifCase = dopUtilFunctions.getCifCase(
          dopApplicationState: DOPNativeApplicationStateEntity(
            uiVersion: DOPNativeCifConfirmUIVersion.difInfo.value,
          ),
          cifInfo: cifInfoWithoutUseNewCif,
        );
        expect(cifCase, CifCase.difInfo);
      });

      test('return CifCase.cifReopen if uiVersion == DOPNativeCifConfirmUIVersion.cifReopen', () {
        final CifCase cifCase = dopUtilFunctions.getCifCase(
          dopApplicationState: DOPNativeApplicationStateEntity(
            uiVersion: DOPNativeCifConfirmUIVersion.cifReopen.value,
          ),
          cifInfo: cifInfoWithoutUseNewCif,
        );
        expect(cifCase, CifCase.cifReopen);
      });

      test('return CifCase.unknown if uiVersion is unknown', () {
        final CifCase cifCase = dopUtilFunctions.getCifCase(
          dopApplicationState: DOPNativeApplicationStateEntity(),
          cifInfo: cifInfoWithoutUseNewCif,
        );
        expect(cifCase, CifCase.unknown);
      });
    });

    group('CifInfo is null', () {
      test(
          'return CifCase.difNationIdWithoutCifInfo if uiVersion == DOPNativeCifConfirmUIVersion.difNationId',
          () {
        final CifCase cifCase = dopUtilFunctions.getCifCase(
            dopApplicationState: DOPNativeApplicationStateEntity(
          uiVersion: DOPNativeCifConfirmUIVersion.difNationId.value,
        ));
        expect(cifCase, CifCase.difNationIdWithoutCifInfo);
      });

      test('return CifCase.difPhone if uiVersion == DOPNativeCifConfirmUIVersion.difPhone', () {
        final CifCase cifCase = dopUtilFunctions.getCifCase(
            dopApplicationState: DOPNativeApplicationStateEntity(
          uiVersion: DOPNativeCifConfirmUIVersion.difPhone.value,
        ));
        expect(cifCase, CifCase.difPhone);
      });

      test('return CifCase.difCif if uiVersion == DOPNativeCifConfirmUIVersion.difCif', () {
        final CifCase cifCase = dopUtilFunctions.getCifCase(
            dopApplicationState: DOPNativeApplicationStateEntity(
          uiVersion: DOPNativeCifConfirmUIVersion.difCif.value,
        ));
        expect(cifCase, CifCase.difCif);
      });

      test('return CifCase.difInfo if uiVersion == DOPNativeCifConfirmUIVersion.difInfo', () {
        final CifCase cifCase = dopUtilFunctions.getCifCase(
            dopApplicationState: DOPNativeApplicationStateEntity(
          uiVersion: DOPNativeCifConfirmUIVersion.difInfo.value,
        ));
        expect(cifCase, CifCase.difInfo);
      });

      test('return CifCase.cifReopen if uiVersion == DOPNativeCifConfirmUIVersion.cifReopen', () {
        final CifCase cifCase = dopUtilFunctions.getCifCase(
            dopApplicationState: DOPNativeApplicationStateEntity(
          uiVersion: DOPNativeCifConfirmUIVersion.cifReopen.value,
        ));
        expect(cifCase, CifCase.cifReopen);
      });

      test('return CifCase.unknown if uiVersion is unknown', () {
        final CifCase cifCase =
            dopUtilFunctions.getCifCase(dopApplicationState: DOPNativeApplicationStateEntity());
        expect(cifCase, CifCase.unknown);
      });
    });
  });

  group('isMWGSalesmanIDValid', () {
    test('returns true for valid codes', () {
      expect(dopUtilFunctions.isMWGSalesmanIDValid('1234'), isTrue);
      expect(dopUtilFunctions.isMWGSalesmanIDValid('12345'), isTrue);
      expect(dopUtilFunctions.isMWGSalesmanIDValid('456789'), isTrue);
    });

    test('returns false for invalid codes', () {
      expect(dopUtilFunctions.isMWGSalesmanIDValid('123'), isFalse);
      expect(dopUtilFunctions.isMWGSalesmanIDValid('1234567'), isFalse);
      expect(dopUtilFunctions.isMWGSalesmanIDValid('12a34'), isFalse);
      expect(dopUtilFunctions.isMWGSalesmanIDValid('abcd'), isFalse);
      expect(dopUtilFunctions.isMWGSalesmanIDValid('123 45'), isFalse);
      expect(dopUtilFunctions.isMWGSalesmanIDValid(''), isFalse);
    });
  });

  group('isViettelStoreSalesmanIDValid', () {
    test('returns true for valid codes', () {
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('VST123'), isTrue);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('VSTabc'), isTrue);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('VST1a2b'), isTrue);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('vst123'), isTrue);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('VSTa1'), isTrue);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('VST1'), isTrue);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('Vst123'), isTrue);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('VSt123'), isTrue);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('vsT123'), isTrue);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('vST123'), isTrue);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('vSt123'), isTrue);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('VST123456'), isTrue);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('VST12345a'), isTrue);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('VST12a456'), isTrue);
    });

    test('returns false for invalid codes', () {
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('VST'), isFalse);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('VST1234567'), isFalse);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('VST####'), isFalse);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('123456'), isFalse);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('vst@123'), isFalse);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('VST123 '), isFalse);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('abc123 '), isFalse);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid('VSTabcdefg '), isFalse);
      expect(dopUtilFunctions.isViettelStoreSalesmanIDValid(''), isFalse);
    });
  });

  group('isFRTSalesmanIDValid', () {
    test('returns true for valid codes', () {
      expect(dopUtilFunctions.isFRTSalesmanIDValid('1'), isTrue);
      expect(dopUtilFunctions.isFRTSalesmanIDValid('12'), isTrue);
      expect(dopUtilFunctions.isFRTSalesmanIDValid('123'), isTrue);
      expect(dopUtilFunctions.isFRTSalesmanIDValid('1234'), isTrue);
      expect(dopUtilFunctions.isFRTSalesmanIDValid('12345'), isTrue);
      expect(dopUtilFunctions.isFRTSalesmanIDValid('456789'), isTrue);
    });

    test('returns false for invalid codes', () {
      expect(dopUtilFunctions.isFRTSalesmanIDValid('a'), isFalse);
      expect(dopUtilFunctions.isFRTSalesmanIDValid('1234567'), isFalse);
      expect(dopUtilFunctions.isFRTSalesmanIDValid('12a34'), isFalse);
      expect(dopUtilFunctions.isFRTSalesmanIDValid('abcd'), isFalse);
      expect(dopUtilFunctions.isFRTSalesmanIDValid('123 45'), isFalse);
      expect(dopUtilFunctions.isFRTSalesmanIDValid(''), isFalse);
    });
  });

  group('test share function', () {
    test('should not call share when data is null', () async {
      await dopUtilFunctions.sharingData(null);

      verifyNever(() => mockSharingFeature.share(any()));
    });

    test('should call share with correct data', () async {
      when(() => mockSharingFeature.share(any())).thenAnswer((_) async => true);

      const String testData = 'Test Data';

      await dopUtilFunctions.sharingData(testData);

      verify(() => mockSharingFeature.share(testData)).called(1);
    });
  });

  group('verify isCorrectDateEKYCFormat()', () {
    test('verify with date is null', () {
      final bool result = dopUtilFunctions.isCorrectDateEKYCFormat(
        dateInputted: null,
      );

      expect(result, false);
    });

    test('verify with date is empty', () {
      final bool result = dopUtilFunctions.isCorrectDateEKYCFormat(
        dateInputted: '',
      );

      expect(result, false);
    });

    test('verify right date format', () {
      const String date = '10/12/2022';
      final bool result = dopUtilFunctions.isCorrectDateEKYCFormat(
        dateInputted: date,
      );

      expect(result, true);
    });

    test('verify fail date format with format dd/MM/yy', () {
      const String date = '10/12/22';
      final bool result = dopUtilFunctions.isCorrectDateEKYCFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with format yyyy/MM/dd', () {
      const String date = '2022/12/24';
      final bool result = dopUtilFunctions.isCorrectDateEKYCFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with format dd-MM-yyyy', () {
      const String date = '10-12-2022';
      final bool result = dopUtilFunctions.isCorrectDateEKYCFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with month > 12', () {
      const String date = '11/13/2022';
      final bool result = dopUtilFunctions.isCorrectDateEKYCFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with day > 31', () {
      const String date = '32/11/2022';
      final bool result = dopUtilFunctions.isCorrectDateEKYCFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with day = 0', () {
      const String date = '0/11/2022';
      final bool result = dopUtilFunctions.isCorrectDateEKYCFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with month = 0', () {
      const String date = '11/0/2022';
      final bool result = dopUtilFunctions.isCorrectDateEKYCFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with day is invalid', () {
      const String date = '30/02/2022';
      final bool result = dopUtilFunctions.isCorrectDateEKYCFormat(
        dateInputted: date,
      );

      expect(result, false);
    });

    test('verify fail date format with format dd/MM/yyyy hh:mm:ss', () {
      const String date = '11/12/2022 12:22:11';
      final bool result = dopUtilFunctions.isCorrectDateEKYCFormat(
        dateInputted: date,
      );

      expect(result, false);
    });
  });

  group('test convertDOPToEKYCDateFormat()', () {
    test('verify convert day yyyy-MM-dd to dd/MM/yyyy', () {
      final String? result = dopUtilFunctions.convertDOPToEKYCDateFormat('2023-12-31');
      expect(result, '31/12/2023');
    });

    test('verify return null if input is null', () {
      final String? result = dopUtilFunctions.convertDOPToEKYCDateFormat(null);
      expect(result, isNull);
    });

    test('verify return null if input is invalid date', () {
      final String? result = dopUtilFunctions.convertDOPToEKYCDateFormat('12-31-2023');
      expect(result, isNull);
    });
  });

  group('test showDOPInputPhoneDialog', () {
    test('should call show on DOPNativeInputPhoneNumberDialog', () async {
      when(() => evoFlutterWrapper.showDialog<void>(
            useSafeArea: any(named: 'useSafeArea'),
            builder: any(named: 'builder'),
          )).thenAnswer((_) => Future<void>.value());
      when(() => mockAppState.currentScreenId).thenReturn(EventTrackingScreenId.undefined);

      dopUtilFunctions.showDOPInputPhoneDialog();
      final WidgetBuilder builder = verify(() => evoFlutterWrapper.showDialog<void>(
            useSafeArea: any(named: 'useSafeArea'),
            builder: captureAny(named: 'builder'),
          )).captured.single;

      final Widget widget = builder(mockNavigatorContext);

      expect(
          widget,
          isA<DOPNativeInputPhoneNumberDialog>()
              .having(
                (DOPNativeInputPhoneNumberDialog p0) => p0.dialogId,
                'verify phone number',
                DOPNativeDialogId.inputPhoneNumberDialog.id,
              )
              .having(
                (DOPNativeInputPhoneNumberDialog p0) => p0.phoneNumber,
                'verify phone number',
                null,
              )
              .having(
                (DOPNativeInputPhoneNumberDialog p0) => p0.autoRequestOTP,
                'verify autoRequestOTP',
                null,
              ));
    });

    test('should call show on DOPNativeInputPhoneNumberDialog with passing params', () async {
      when(() => evoFlutterWrapper.showDialog<void>(
            useSafeArea: any(named: 'useSafeArea'),
            builder: any(named: 'builder'),
          )).thenAnswer((_) => Future<void>.value());
      when(() => mockAppState.currentScreenId).thenReturn(EventTrackingScreenId.undefined);

      dopUtilFunctions.showDOPInputPhoneDialog(
        phoneNumber: 'phone',
        autoRequestOTP: true,
      );

      final WidgetBuilder builder = verify(() => evoFlutterWrapper.showDialog<void>(
            useSafeArea: any(named: 'useSafeArea'),
            builder: captureAny(named: 'builder'),
          )).captured.single;

      final Widget widget = builder(mockNavigatorContext);

      expect(
        widget,
        isA<DOPNativeInputPhoneNumberDialog>()
            .having(
              (DOPNativeInputPhoneNumberDialog p0) => p0.dialogId,
              'verify phone number',
              DOPNativeDialogId.inputPhoneNumberDialog.id,
            )
            .having(
              (DOPNativeInputPhoneNumberDialog p0) => p0.phoneNumber,
              'verify phone number',
              'phone',
            )
            .having(
              (DOPNativeInputPhoneNumberDialog p0) => p0.autoRequestOTP,
              'verify autoRequestOTP',
              true,
            ),
      );
    });
  });

  group('test showOpenAppSettingCameraDialog', () {
    final UserJourneyHandler mockUserJourneyHandler = MockUserJourneyHandler();
    const String fakeTitle = 'fake_title';
    const String fakeContent = 'fake_content';
    const String fakeTextPositive = 'fake_text_positive';
    const String fakeTextNegative = 'fake_text_negative';
    setUp(() {
      UserJourneyHandler.instanceForTesting = mockUserJourneyHandler;
      when(() => mockUserJourneyHandler.saveInfoDOPNativeJourney())
          .thenAnswer((_) => Future<void>.value());
      final BuildContext? navigationContext = getIt.get<GlobalKeyProvider>().navigatorContext;
      when(() => navigationContext?.pop()).thenAnswer((_) {});
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        MethodChannel('com.spencerccf.app_settings/methods'),
        (MethodCall methodCall) async {
          return null;
        },
      );
    });

    tearDown(() {
      UserJourneyHandler.resetToOriginalInstance();
    });

    test('should call show on showOpenAppSettingCameraDialog', () async {
      await dopUtilFunctions.showOpenAppSettingCameraDialog(
        title: fakeTitle,
        content: fakeContent,
        textPositive: fakeTextPositive,
        textNegative: fakeTextNegative,
      );

      verify(() => EvoDialogHelper().showDialogConfirm(
            onClickPositive: any(named: 'onClickPositive'),
            title: fakeTitle,
            content: fakeContent,
            textPositive: fakeTextPositive,
            textNegative: fakeTextNegative,
            dialogId: EvoDialogId.activateCameraPermissionDialog,
          )).called(1);
    });

    test('should showOpenAppSettingCameraDialog onClickPositive callback', () async {
      await dopUtilFunctions.showOpenAppSettingCameraDialog(
        title: fakeTitle,
        content: fakeContent,
        textPositive: fakeTextPositive,
        textNegative: fakeTextNegative,
      );

      final List<dynamic> captured = verify(() => EvoDialogHelper().showDialogConfirm(
            onClickPositive: captureAny(named: 'onClickPositive'),
            title: fakeTitle,
            content: fakeContent,
            textPositive: fakeTextPositive,
            textNegative: fakeTextNegative,
            dialogId: EvoDialogId.activateCameraPermissionDialog,
          )).captured;
      final VoidCallback onClickPositiveCallBack = captured[0];
      onClickPositiveCallBack();
      verify(() => getIt.get<GlobalKeyProvider>().navigatorContext?.pop()).called(1);
    });
  });

  group('showBottomSheetPdf', () {
    setUp(() {
      final CommonImageProvider mockImageProvider = MockEvoImageProvider();
      getIt.registerLazySingleton<CommonImageProvider>(() => mockImageProvider);
      when(() => getIt.get<CommonImageProvider>().asset(
            any(),
            fit: any(named: 'fit'),
            width: any(named: 'width'),
            height: any(named: 'height'),
          )).thenReturn(Container());
      when(() => commonNavigator.pop(any())).thenAnswer((_) {
        Navigator.of(getIt.get<GlobalKeyProvider>().navigatorContext!).pop();
      });
    });

    tearDown(() {
      getIt.unregister<CommonImageProvider>();
    });

    testWidgets('should render bottom sheet correctly', (WidgetTester tester) async {
      getIt.unregister<GlobalKeyProvider>();
      getIt.registerLazySingleton<GlobalKeyProvider>(() => GlobalKeyProvider());
      await tester.pumpWidget(
        MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          home: Scaffold(
            body: Builder(
              builder: (BuildContext context) {
                return ElevatedButton(
                    onPressed: () {
                      dopUtilFunctions.showBottomSheetPdf(
                          url: 'http://example.com', loadingWidget: SizedBox());
                    },
                    child: Text(''));
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      await tester.tap(find.byType(ElevatedButton));
      await tester.pumpAndSettle();
      expect(find.byType(PdfViewWidget), isNotNull);
      await tester.tap(find.byKey(Key('close_pdf_bottom_sheet')));

      await tester.pumpAndSettle();
      //should find nothing after closed
      expect(find.byType(PdfViewWidget), findsNothing);
    });

    testWidgets('handles null context gracefully', (WidgetTester tester) async {
      final GlobalKeyProvider globalKeyProvider = getIt.get<GlobalKeyProvider>();
      when(() => globalKeyProvider.navigatorContext).thenAnswer((_) => null);

      final Future<void> result = dopUtilFunctions.showBottomSheetPdf(url: 'http://example.com');
      expect(result, isA<Future<void>>());
    });
  });

  group('test formatDateTimeToHourMinute', () {
    final String hourStr = DOPNativeStrings.dopNativeHour;
    final String minuteStr = DOPNativeStrings.dopNativeMinute;

    test('formatDateTimeToHourMinute returns correct format', () {
      final DateTime testTime = DateTime(2025, 3, 17, 14, 30); // 14:30
      final String expectedOutput = '14 $hourStr 30 $minuteStr';

      final String result = dopUtilFunctions.formatDateTimeToHourMinute(testTime);

      expect(result, expectedOutput);
    });

    test('formatDateTimeToHourMinute handles midnight correctly', () {
      final DateTime testTime = DateTime(2025, 3, 17, 0); // 00:00
      final String expectedOutput = '00 $hourStr 00 $minuteStr';

      final String result = dopUtilFunctions.formatDateTimeToHourMinute(testTime);

      expect(result, expectedOutput);
    });

    test('formatDateTimeToHourMinute handles single-digit hour and minute correctly', () {
      final DateTime testTime = DateTime(2025, 3, 17, 9, 5); // 09:05
      final String expectedOutput = '09 $hourStr 05 $minuteStr';

      final String result = dopUtilFunctions.formatDateTimeToHourMinute(testTime);

      expect(result, expectedOutput);
    });
  });
}
