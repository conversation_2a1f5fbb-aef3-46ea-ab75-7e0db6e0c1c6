import 'package:evoapp/feature/dop_native/widgets/notes_container/dop_native_notes_container_item.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeNotesContainerItem', () {
    test('should correctly initialize properties', () {
      // Arrange
      const String description = 'Sample description';
      const String imgAsset = 'assets/sample.png';

      // Act
      final DOPNativeNotesContainerItem item = DOPNativeNotesContainerItem(
        description: description,
        imgAsset: imgAsset,
      );

      // Assert
      expect(item.description, equals(description));
      expect(item.imgAsset, equals(imgAsset));
    });
  });
}
