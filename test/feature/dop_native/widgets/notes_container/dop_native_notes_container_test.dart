import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/widgets/notes_container/dop_native_notes_container.dart';
import 'package:evoapp/feature/dop_native/widgets/notes_container/dop_native_notes_container_item.dart';
import 'package:evoapp/feature/dop_native/widgets/notes_container/dop_native_notes_container_item_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';
import '../../../announcement/widget/no_announcement_widget_test.dart';

void main() {
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    registerFallbackValue(MockContext());
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterColor();
    getItRegisterTextStyle();
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());

    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    when(
      () => mockCommonImageProvider.asset(
        any(),
        fit: BoxFit.cover,
        height: 32,
      ),
    ).thenReturn(const SizedBox());
  });

  testWidgets('DOPNativeNotesContainer displays items correctly', (WidgetTester tester) async {
    // Create a list of DOPNativeNotesContainerItem objects
    final List<DOPNativeNotesContainerItem> items = <DOPNativeNotesContainerItem>[
      DOPNativeNotesContainerItem(description: 'Note 1', imgAsset: 'asset 1'),
      DOPNativeNotesContainerItem(description: 'Note 2', imgAsset: 'asset 2'),
    ];

    // Build the DOPNativeNotesContainer widget
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: DOPNativeNotesContainer(items: items),
        ),
      ),
    );

    // Verify that the DOPNativeNotesContainerItemWidget widgets are displayed
    expect(find.byType(DOPNativeNotesContainerItemWidget), findsNWidgets(items.length));

    // Verify that the correct number of dividers are displayed
    expect(
      find.byElementPredicate((Element element) {
        final Widget widget = element.widget;
        if (widget is Container) {
          return widget.constraints?.maxHeight == 1;
        }
        return false;
      }),
      findsNWidgets(items.length - 1),
    );
  });
}
