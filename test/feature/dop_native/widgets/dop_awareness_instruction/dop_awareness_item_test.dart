import 'package:evoapp/feature/dop_native/widgets/dop_awareness_instruction/dop_awareness_item.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeAwarenessItem', () {
    test('should create an instance with provided parameters', () {
      const String desc_1 = 'desc_1';
      const String desc_2 = 'desc_2';
      final List<DescriptionInlineSpan> descriptionSpans = <DescriptionInlineSpan>[
        const DescriptionInlineSpan(desc_1),
        const DescriptionInlineSpan(desc_2, negative: true),
      ];
      const String imgAsset = 'assets/image.png';

      final DOPNativeAwarenessItem item = DOPNativeAwarenessItem(
        imgAsset: imgAsset,
        descriptions: descriptionSpans,
      );

      // Verify the imgAsset is set correctly
      expect(item.imgAsset, equals(imgAsset));

      // Verify the descriptions list is set correctly
      expect(item.descriptions, equals(descriptionSpans));

      // Verify the descriptions have the correct values and flags
      expect(item.descriptions[0].value, equals(desc_1));
      expect(item.descriptions[0].negative, equals(false));
      expect(item.descriptions[1].value, equals(desc_2));
      expect(item.descriptions[1].negative, equals(true));
    });
  });

  group('DescriptionInlineSpan', () {
    test('should create an instance with provided value and default negative', () {
      const String testString = 'test_string';
      const DescriptionInlineSpan span = DescriptionInlineSpan(testString);

      expect(span.value, equals(testString));
      expect(span.negative, isFalse);
    });

    test('should create an instance with provided value and negative', () {
      const String testString = 'test_string';
      const DescriptionInlineSpan span = DescriptionInlineSpan(testString, negative: true);

      expect(span.value, equals(testString));
      expect(span.negative, isTrue);
    });
  });
}
