import 'package:evoapp/data/response/dop_native/dop_native_application_state_entity.dart';
import 'package:evoapp/feature/dop_native/base/cubit/dop_native_application_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeApplicationState', () {
    test('DOPNativeApplicationStateInitial should be initialized correctly', () {
      final DOPNativeApplicationStateInitial state = DOPNativeApplicationStateInitial();
      expect(state, isA<DOPNativeApplicationState>());
    });

    test('DOPNativeApplicationStateLoading should be initialized correctly', () {
      final DOPNativeApplicationStateLoading state = DOPNativeApplicationStateLoading();
      expect(state, isA<DOPNativeApplicationState>());
    });

    test('DOPNativeApplicationStateLoaded should contain the entity', () {
      final DOPNativeApplicationStateEntity entity = DOPNativeApplicationStateEntity();
      final DOPNativeApplicationStateLoaded state = DOPNativeApplicationStateLoaded(entity);
      expect(state, isA<DOPNativeApplicationState>());
      expect(state.entity, equals(entity));
    });

    test('DOPNativeApplicationStateError should contain the error model', () {
      final ErrorUIModel errorModel = ErrorUIModel(userMessage: 'An error occurred');
      final DOPNativeApplicationStateError state = DOPNativeApplicationStateError(errorModel);
      expect(state, isA<DOPNativeApplicationState>());
      expect(state.error, equals(errorModel));
    });
  });
}
