import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_application_state_entity.dart';
import 'package:evoapp/feature/dop_native/base/cubit/dop_native_application_state.dart';
import 'package:evoapp/feature/dop_native/base/cubit/dop_native_application_state_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../constant.dart';

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

class MockAppState extends Mock implements AppState {}

void main() {
  const String fakeUniqueToken = 'fakeUniqueToken';

  final DOPNativeRepo mockDOPNativeRepo = MockDOPNativeRepo();
  final AppState appState = AppState();

  late DOPNativeApplicationStateCubit cubit;

  final DOPNativeApplicationStateEntity cachedEntity =
      DOPNativeApplicationStateEntity.fromBaseResponse(
    BaseResponse(
      statusCode: CommonHttpClient.SUCCESS,
      response: <String, dynamic>{
        'data': <String, dynamic>{
          'current_step': 'step1',
          'ui_version': 'v1.0',
          'flow_selected_at': 1,
          'flow_config': <String, dynamic>{
            'flow_selected_at': 1,
            'ui_version': 'v1.0',
          },
        },
      },
    ),
  );

  final DOPNativeApplicationStateEntity responseEntity =
      DOPNativeApplicationStateEntity.fromBaseResponse(
    BaseResponse(
      statusCode: CommonHttpClient.SUCCESS,
      response: <String, dynamic>{
        'data': <String, dynamic>{
          'current_step': 'step2',
          'ui_version': 'v1.0',
          'flow_selected_at': 2,
          'flow_config': <String, dynamic>{
            'flow_selected_at': 2,
            'ui_version': 'v1.0',
          },
        },
      },
    ),
  );

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  setUp(() {
    appState.dopNativeState.uniqueToken = fakeUniqueToken;
    appState.dopNativeState.dopApplicationState = null;

    cubit = DOPNativeApplicationStateCubit(
      dopNativeRepo: mockDOPNativeRepo,
      appState: appState,
    );

    when(() => mockDOPNativeRepo.getApplicationState(
          token: any(named: 'token'),
          flowSelectedAt: any(named: 'flowSelectedAt'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer(
      (_) async => responseEntity,
    );
  });

  tearDown(() {
    reset(mockDOPNativeRepo);
  });

  test('Initial state', () {
    expect(cubit.state, isA<DOPNativeApplicationStateInitial>());
  });

  group('Test getApplicationState', () {
    blocTest<DOPNativeApplicationStateCubit, DOPNativeApplicationState>(
      'getApplicationState success without cached data',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeApplicationStateCubit cubit) => cubit.getApplicationState(),
      expect: () => <dynamic>[
        isA<DOPNativeApplicationStateLoading>(),
        isA<DOPNativeApplicationStateLoaded>().having(
          (DOPNativeApplicationStateLoaded p0) => p0.entity,
          'verify responseEntity',
          responseEntity,
        ),
      ],
      verify: (_) {
        expect(
          verify(
            () => mockDOPNativeRepo.getApplicationState(
                token: captureAny(named: 'token'),
                flowSelectedAt: captureAny(named: 'flowSelectedAt'),
                mockConfig: any(named: 'mockConfig')),
          ).captured,
          <dynamic>[
            fakeUniqueToken,
            null,
          ],
        );
      },
    );

    blocTest<DOPNativeApplicationStateCubit, DOPNativeApplicationState>(
      'getApplicationState success with the same cached data',
      build: () => cubit,
      setUp: () {
        appState.dopNativeState.dopApplicationState = responseEntity;
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeApplicationStateCubit cubit) => cubit.getApplicationState(),
      expect: () => <dynamic>[
        isA<DOPNativeApplicationStateLoading>(),
        isA<DOPNativeApplicationStateLoaded>().having(
          (DOPNativeApplicationStateLoaded p0) => p0.entity,
          'verify responseEntity',
          isA<DOPNativeApplicationStateEntity>()
              .having(
                (DOPNativeApplicationStateEntity p1) => p1.currentStep,
                'verify currentStep',
                'step2',
              )
              .having(
                (DOPNativeApplicationStateEntity p2) => p2.flowConfig?.flowSelectedAt,
                'verify flowSelectedAt',
                2,
              ),
        ),
      ],
      verify: (_) {
        expect(
          verify(
            () => mockDOPNativeRepo.getApplicationState(
                token: captureAny(named: 'token'),
                flowSelectedAt: captureAny(named: 'flowSelectedAt'),
                mockConfig: any(named: 'mockConfig')),
          ).captured,
          <dynamic>[
            fakeUniqueToken,
            2,
          ],
        );
      },
    );

    blocTest<DOPNativeApplicationStateCubit, DOPNativeApplicationState>(
      'getApplicationState success with different cached data',
      build: () => cubit,
      setUp: () {
        appState.dopNativeState.dopApplicationState = cachedEntity;
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeApplicationStateCubit cubit) => cubit.getApplicationState(),
      expect: () => <dynamic>[
        isA<DOPNativeApplicationStateLoading>(),
        isA<DOPNativeApplicationStateLoaded>().having(
          (DOPNativeApplicationStateLoaded p0) => p0.entity,
          'verify responseEntity',
          isA<DOPNativeApplicationStateEntity>()
              .having(
                (DOPNativeApplicationStateEntity p1) => p1.currentStep,
                'verify currentStep',
                'step2',
              )
              .having(
                (DOPNativeApplicationStateEntity p2) => p2.flowConfig?.flowSelectedAt,
                'verify flowSelectedAt',
                2,
              ),
        ),
      ],
      verify: (_) {
        expect(
          verify(
            () => mockDOPNativeRepo.getApplicationState(
                token: captureAny(named: 'token'),
                flowSelectedAt: captureAny(named: 'flowSelectedAt'),
                mockConfig: any(named: 'mockConfig')),
          ).captured,
          <dynamic>[
            fakeUniqueToken,
            1,
          ],
        );
      },
    );

    blocTest<DOPNativeApplicationStateCubit, DOPNativeApplicationState>(
      'getApplicationState error',
      build: () => cubit,
      setUp: () {
        when(() => mockDOPNativeRepo.getApplicationState(
              token: any(named: 'token'),
              flowSelectedAt: any(named: 'flowSelectedAt'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => DOPNativeApplicationStateEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: null,
            ),
          ),
        );
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeApplicationStateCubit cubit) => cubit.getApplicationState(),
      expect: () => <dynamic>[
        isA<DOPNativeApplicationStateLoading>(),
        isA<DOPNativeApplicationStateError>(),
      ],
      verify: (_) {
        expect(
          verify(
            () => mockDOPNativeRepo.getApplicationState(
                token: captureAny(named: 'token'),
                flowSelectedAt: captureAny(named: 'flowSelectedAt'),
                mockConfig: any(named: 'mockConfig')),
          ).captured,
          <dynamic>[
            fakeUniqueToken,
            null,
          ],
        );
      },
    );

    blocTest<DOPNativeApplicationStateCubit, DOPNativeApplicationState>(
      'getApplicationState success and needToHandleSpecialEvent is true but dopApplicationState is null',
      build: () => cubit,
      setUp: () {
        appState.dopNativeState.dopApplicationState = null;
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeApplicationStateCubit cubit) =>
          cubit.getApplicationState(needToHandleSpecialEvent: true),
      expect: () => <dynamic>[
        isA<DOPNativeApplicationStateLoaded>().having(
          (DOPNativeApplicationStateLoaded p0) => p0.entity,
          'verify responseEntity',
          isA<DOPNativeApplicationStateEntity>()
              .having(
                (DOPNativeApplicationStateEntity p1) => p1.currentStep,
                'verify currentStep',
                'step2',
              )
              .having(
                (DOPNativeApplicationStateEntity p2) => p2.flowConfig?.flowSelectedAt,
                'verify flowSelectedAt',
                2,
              ),
        ),
      ],
      verify: (_) {
        verify(
          () => mockDOPNativeRepo.getApplicationState(
              token: captureAny(named: 'token'),
              flowSelectedAt: captureAny(named: 'flowSelectedAt'),
              mockConfig: any(named: 'mockConfig')),
        ).called(1);
      },
    );

    blocTest<DOPNativeApplicationStateCubit, DOPNativeApplicationState>(
      'getApplicationState success and needToHandleSpecialEvent is true and dopApplicationState is not previous state',
      build: () => cubit,
      setUp: () {
        appState.dopNativeState.dopApplicationState = cachedEntity;
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeApplicationStateCubit cubit) =>
          cubit.getApplicationState(needToHandleSpecialEvent: true),
      expect: () => <dynamic>[
        isA<DOPNativeApplicationStateLoaded>().having(
          (DOPNativeApplicationStateLoaded p0) => p0.entity,
          'verify responseEntity',
          isA<DOPNativeApplicationStateEntity>()
              .having(
                (DOPNativeApplicationStateEntity p1) => p1.currentStep,
                'verify currentStep',
                'step2',
              )
              .having(
                (DOPNativeApplicationStateEntity p2) => p2.flowConfig?.flowSelectedAt,
                'verify flowSelectedAt',
                2,
              ),
        ),
      ],
      verify: (_) {
        verify(
          () => mockDOPNativeRepo.getApplicationState(
              token: captureAny(named: 'token'),
              flowSelectedAt: captureAny(named: 'flowSelectedAt'),
              mockConfig: any(named: 'mockConfig')),
        ).called(1);
      },
    );

    blocTest<DOPNativeApplicationStateCubit, DOPNativeApplicationState>(
      'getApplicationState success and needToHandleSpecialEvent is true and dopApplicationState is previous state',
      build: () => cubit,
      setUp: () {
        appState.dopNativeState.dopApplicationState = responseEntity;
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeApplicationStateCubit cubit) =>
          cubit.getApplicationState(needToHandleSpecialEvent: true),
      expect: () => <dynamic>[
        isA<DOPNativeApplicationStateInitial>(),
      ],
      verify: (_) {
        verify(
          () => mockDOPNativeRepo.getApplicationState(
              token: captureAny(named: 'token'),
              flowSelectedAt: captureAny(named: 'flowSelectedAt'),
              mockConfig: any(named: 'mockConfig')),
        ).called(1);
      },
    );

    blocTest<DOPNativeApplicationStateCubit, DOPNativeApplicationState>(
      'getApplicationState error and needToHandleSpecialEvent is true',
      build: () => cubit,
      setUp: () {
        when(() => mockDOPNativeRepo.getApplicationState(
              token: any(named: 'token'),
              flowSelectedAt: any(named: 'flowSelectedAt'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => DOPNativeApplicationStateEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: null,
            ),
          ),
        );
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeApplicationStateCubit cubit) =>
          cubit.getApplicationState(needToHandleSpecialEvent: true),
      expect: () => <dynamic>[
        isA<DOPNativeApplicationStateInitial>(),
      ],
      verify: (_) {
        expect(
          verify(
            () => mockDOPNativeRepo.getApplicationState(
                token: captureAny(named: 'token'),
                flowSelectedAt: captureAny(named: 'flowSelectedAt'),
                mockConfig: any(named: 'mockConfig')),
          ).captured,
          <dynamic>[
            fakeUniqueToken,
            null,
          ],
        );
      },
    );
  });

  group('Test updateDOPApplicationState', () {
    test('Saved dopApplicationState null, should set mew new entity to app state', () {
      final DOPNativeApplicationStateEntity entity = DOPNativeApplicationStateEntity();
      cubit.updateDOPApplicationState(entity);

      expect(appState.dopNativeState.dopApplicationState, entity);
    });

    test('Saved dopApplicationState not null, should set updated entity to app state', () {
      appState.dopNativeState.dopApplicationState =
          DOPNativeApplicationStateEntity(currentStep: 'step1');
      final DOPNativeApplicationStateEntity newEntity =
          DOPNativeApplicationStateEntity(currentStep: 'step2');
      cubit.updateDOPApplicationState(newEntity);

      expect(appState.dopNativeState.dopApplicationState?.currentStep, newEntity.currentStep);
    });
  });

  group('isApplicationStateLoaded', () {
    test('should return true when currentStep match', () {
      final DOPNativeApplicationStateEntity entity =
          DOPNativeApplicationStateEntity(currentStep: 'step1');
      appState.dopNativeState.dopApplicationState =
          DOPNativeApplicationStateEntity(currentStep: 'step1');

      final bool result = cubit.isApplicationStateLoaded(entity);

      expect(result, isTrue);
    });

    test('should return false when currentStep does not match', () {
      final DOPNativeApplicationStateEntity entity =
          DOPNativeApplicationStateEntity(currentStep: 'step2');
      appState.dopNativeState.dopApplicationState =
          DOPNativeApplicationStateEntity(currentStep: 'step1');

      final bool result = cubit.isApplicationStateLoaded(entity);

      expect(result, isFalse);
    });

    test('should return false when dopApplicationState is null', () {
      final DOPNativeApplicationStateEntity entity =
          DOPNativeApplicationStateEntity(currentStep: 'step1');
      appState.dopNativeState.dopApplicationState = null;

      final bool result = cubit.isApplicationStateLoaded(entity);

      expect(result, isFalse);
    });
  });
}
