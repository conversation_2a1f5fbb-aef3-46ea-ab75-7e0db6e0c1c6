import 'package:evoapp/feature/dop_native/resources/dop_native_website_url.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Check website URLs', () {
    expect(DOPNativeWebsiteUrl.tpbEvoSpecialOfferUrl,
        'https://www.goevo.vn/dop/content/uu-dai-dac-quyen-cua-the-tpbank-evo');
    expect(DOPNativeWebsiteUrl.cashbackProgramUrl,
        'https://www.goevo.vn/dop/content/chuong-trinh-mua-sam-lien-tay-hoan-tien-khong-gioi-han-len-den-30');
    expect(DOPNativeWebsiteUrl.registerAndUseUrl,
        'https://www.goevo.vn/dop/content/dang-ky-va-su-dung-ngay-sau-15-phut');
    expect(DOPNativeWebsiteUrl.emiProgramUrl, 'https://www.goevo.vn/dop/content/tra-gop-0');
    expect(DOPNativeWebsiteUrl.freeAnnualFeeUrl,
        'https://www.goevo.vn/dop/content/mien-phi-thuong-nien-nam-dau-tien');
    expect(DOPNativeWebsiteUrl.freeInterestUrl,
        'https://www.goevo.vn/dop/content/mien-lai-len-den-45-ngay');
    expect(DOPNativeWebsiteUrl.foodUrl, 'https://www.goevo.vn/dop/food');
    expect(DOPNativeWebsiteUrl.travelUrl, 'https://www.goevo.vn/dop/travel');
    expect(DOPNativeWebsiteUrl.shoppingUrl, 'https://www.goevo.vn/dop/shopping');
    expect(DOPNativeWebsiteUrl.introductionUrl, 'https://www.goevo.vn/dop/content/gioi-thieu');
    expect(DOPNativeWebsiteUrl.faqUrl, 'https://www.goevo.vn/dop/faq');
    expect(DOPNativeWebsiteUrl.listingVoucher, 'https://www.goevo.vn/dop/listing-voucher');
    expect(DOPNativeWebsiteUrl.nearestBranchesUrl, 'https://tpb.vn/lien-he/tim-diem-giao-dich');
    expect(DOPNativeWebsiteUrl.otherProductWebUrl, 'https://tpb.vn/khach-hang-ca-nhan');
    expect(DOPNativeWebsiteUrl.tpbWarning,
        'https://tpb.vn/tin-tuc/tin-tpbank/********-tpbank-canh-bao-hanh-vi-lua-dao-chiem-doat-thong-tin-cua-khach-hang');
    expect(
      DOPNativeWebsiteUrl.tpbAppLink,
      'https://tpb-ebankx.onelink.me/As35/website',
    );
    expect(
      DOPNativeWebsiteUrl.dopNativeCardActivateRedirectURL,
      'evo://dop-card-activating',
    );
  });
}
