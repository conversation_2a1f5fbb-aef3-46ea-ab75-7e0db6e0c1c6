import 'package:evoapp/feature/dop_native/resources/dop_native_images.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String assetPath = DOPNativeImages.assetPath;
  const String png = DOPNativeImages.png;
  const String svg = DOPNativeImages.svg;

  test('verify constants', () {
    expect(assetPath, 'assets/dop_native_images/');
    expect(png, '.png');
    expect(svg, '.svg');
  });

  test('verify PNG constant image', () {
    expect(DOPNativeImages.icTpBankLogo, '${assetPath}ic_tp_bank_logo$png');

    // Introduction screen
    expect(DOPNativeImages.imgHeaderTPBank, '${assetPath}img_header_tp_bank$png');
    expect(DOPNativeImages.bgDOPNativeIntroduction, '${assetPath}bg_dop_native_introduction$png');
    expect(
        DOPNativeImages.imgTitleTopOfIntroduction, '${assetPath}img_title_top_of_introduction$png');
    expect(DOPNativeImages.imgCashBack10PercentBanner,
        '${assetPath}img_cash_back_10_percent_banner$png');
    expect(
        DOPNativeImages.imgCashBackNoLimitBanner, '${assetPath}img_cash_back_no_limit_banner$png');
    expect(
        DOPNativeImages.imgNoUseInformationBanner, '${assetPath}img_no_use_information_banner$png');
    expect(DOPNativeImages.imgUseEVOCardBanner, '${assetPath}img_use_evo_card_banner$png');
    expect(DOPNativeImages.imgEMI0PercentBanner, '${assetPath}img_emi_0_percent_banner$png');
    expect(DOPNativeImages.imgFreeAnnualFeeBanner, '${assetPath}img_free_annual_fee_banner$png');
    expect(DOPNativeImages.imgFreeUpToBanner, '${assetPath}img_free_up_to_banner$png');
    expect(DOPNativeImages.imgTitleIntroductionFooter,
        '${assetPath}img_title_introduction_footer$png');
    expect(
        DOPNativeImages.imgCardIntroductionFooter, '${assetPath}img_card_introduction_footer$png');
    expect(DOPNativeImages.imgFooterLine, '${assetPath}img_footer_line$png');
    expect(DOPNativeImages.imgMadeByFooter, '${assetPath}img_made_by_footer$png');
    expect(DOPNativeImages.imgIntroIconFooter, '${assetPath}img_intro_icon_footer$png');
    expect(DOPNativeImages.imgSupportIconFooter, '${assetPath}img_support_icon_footer$png');
    expect(DOPNativeImages.imgQuestionIconFooter, '${assetPath}img_question_icon_footer$png');
    expect(DOPNativeImages.icFaceOTPInvalid, '${assetPath}ic_face_otp_invalid$png');

    /// DOP Selfie Verification Introduction Screen
    expect(DOPNativeImages.imgSelfieCaptureAware1, '${assetPath}img_selfie_capture_aware_1$png');
    expect(DOPNativeImages.imgSelfieCaptureAware2, '${assetPath}img_selfie_capture_aware_2$png');
    expect(DOPNativeImages.imgSelfieCaptureAware3, '${assetPath}img_selfie_capture_aware_3$png');
    expect(DOPNativeImages.imgSelfieVerifySuccess, '${assetPath}img_selfie_verify_success$png');

    // Card activation status
    expect(DOPNativeImages.imgEvoCardSmall, '${assetPath}img_evo_cards_small$png');
    expect(DOPNativeImages.imgEvoSettingSmall, '${assetPath}img_evo_setting_small$png');

    // DOP Native Appraising Verification Screen
    expect(
        DOPNativeImages.imgAppraisingVerification, '${assetPath}img_appraising_verification$png');

    // DOP Native failure screen
    expect(DOPNativeImages.icDOPRejectUserRegister, '${assetPath}ic_dop_reject_user_register$png');
  });

  test('verify SVG constant image', () {
    expect(DOPNativeImages.icOtpCodeError, '${assetPath}ic_otp_code_error$svg');
    expect(DOPNativeImages.icOtpRateLimit, '${assetPath}ic_otp_rate_limit$svg');
    expect(DOPNativeImages.icEvoLogo, '${assetPath}ic_evo_logo$svg');
    expect(DOPNativeImages.icHelpChat, '${assetPath}ic_help_chat$svg');
    expect(DOPNativeImages.icArrowRight, '${assetPath}ic_arrow_right$svg');
    expect(DOPNativeImages.icArrowRightDisabled, '${assetPath}ic_arrow_right_disabled$svg');
    expect(DOPNativeImages.icArrowLeft, '${assetPath}ic_arrow_left$svg');
    expect(DOPNativeImages.icErrorTextField, '${assetPath}ic_error_text_field$svg');
    expect(DOPNativeImages.icSMSPhone, '${assetPath}ic_sms_phone$svg');
    expect(DOPNativeImages.welcomeBackImage, '${assetPath}img_welcome_back$svg');
    expect(
        DOPNativeImages.icScrollToTopIntroduction, '${assetPath}ic_scroll_to_top_introduction$svg');
    expect(DOPNativeImages.icClose, '${assetPath}ic_close$svg');
    expect(DOPNativeImages.icArrowDown, '${assetPath}ic_arrow_down$svg');
    expect(DOPNativeImages.icSuffixTextFieldError, '${assetPath}ic_suffix_text_field_error$svg');
    expect(DOPNativeImages.icSuffixTextFieldCheck, '${assetPath}ic_suffix_text_field_check$svg');
    expect(DOPNativeImages.icInfoOutline, '${assetPath}ic_info_outline$svg');
    expect(DOPNativeImages.icTimeout, '${assetPath}ic_timeout$svg');

    /// ID Verification Introduction Screen
    expect(DOPNativeImages.imgIdVerification, '${assetPath}img_id_verification$svg');
    expect(DOPNativeImages.imgIdVerificationAware1, '${assetPath}img_id_verification_aware_1$svg');
    expect(DOPNativeImages.imgIdVerificationAware2, '${assetPath}img_id_verification_aware_2$svg');
    expect(DOPNativeImages.imgIdVerificationAware3, '${assetPath}img_id_verification_aware_3$svg');
    expect(DOPNativeImages.icDOPCamera, '${assetPath}ic_dop_camera$svg');

    /// DOP Native ID verification confirm
    expect(DOPNativeImages.icDOPNativeEdit, '${assetPath}ic_dop_native_edit$svg');

    /// ekyc_ui_only Processing Screen
    expect(DOPNativeImages.imgEkycProcessing, '${assetPath}img_ekyc_processing$svg');

    /// ekyc_ui_only Success Screen
    expect(DOPNativeImages.imgIDCard, '${assetPath}img_id_card$svg');

    /// DOP Native ekyc Error Screen
    expect(DOPNativeImages.imgInvalidID, '${assetPath}img_invalid_id$svg');
    expect(DOPNativeImages.imgExceedCapturing, '${assetPath}img_exceed_capturing$svg');
    expect(DOPNativeImages.imgInvalidCaptured, '${assetPath}img_invalid_captured$svg');

    /// DOP Selfie Verification Introduction Screen
    expect(DOPNativeImages.imgSelfieCapture, '${assetPath}img_selfie_capture$svg');

    /// DOP Selfie Verification Screen
    expect(DOPNativeImages.imgSelfieVerification, '${assetPath}img_selfie_verification$svg');

    // Metadata PopUp
    expect(DOPNativeImages.icCloseFlat, '${assetPath}ic_close_flat$svg');
    expect(DOPNativeImages.icBack, '${assetPath}ic_back$svg');

    // DOP Native internet error
    expect(DOPNativeImages.icInternetError, '${assetPath}ic_internet_error$svg');

    // DOP Native sand lock icon
    expect(DOPNativeImages.icSandClock, '${assetPath}ic_sand_clock$svg');

    // Inform success
    expect(DOPNativeImages.imgEvoCards, '${assetPath}img_evo_cards$png');

    // DOP Native App Form Additional Info
    expect(DOPNativeImages.icBackStep, '${assetPath}ic_back_step$svg');

    // DOP Native additional info
    expect(DOPNativeImages.icDopNativeInfo, '${assetPath}ic_dop_native_info$svg');

    // DOP Native checkbox
    expect(DOPNativeImages.icCheck, '${assetPath}ic_check$svg');

    // EContract, TPB contact & TPB warning widget
    expect(DOPNativeImages.icRejectWithdrawMoneyInvitation,
        '${assetPath}ic_reject_withdraw_money_invitation$svg');
    expect(DOPNativeImages.icProtectCVV, '${assetPath}ic_protect_cvv$svg');

    /// Card activation status
    expect(DOPNativeImages.icCardVerify, '${assetPath}ic_card_verify$svg');

    /// E-Sign review
    expect(DOPNativeImages.icInfo, '${assetPath}ic_info$svg');
    expect(DOPNativeImages.icZoomIn, '${assetPath}ic_zoom_in$svg');

    /// E-success
    expect(DOPNativeImages.icClock, '${assetPath}ic_clock$svg');

    /// NFC reader instruction
    expect(DOPNativeImages.imgNFCReader, '${assetPath}img_nfc_reader$svg');
    expect(DOPNativeImages.imgNFCAware1, '${assetPath}img_nfc_aware_1$svg');
    expect(DOPNativeImages.imgNFCAware2, '${assetPath}img_nfc_aware_2$svg');
    expect(DOPNativeImages.imgNFCAware3, '${assetPath}img_nfc_aware_3$svg');
    expect(DOPNativeImages.icPhone, '${assetPath}ic_phone$svg');

    expect(DOPNativeImages.imgNFCGuide1, '${assetPath}img_nfc_guide_1$svg');
    expect(DOPNativeImages.imgNFCGuide2, '${assetPath}img_nfc_guide_2$svg');

    /// Acquisition Reward
    expect(DOPNativeImages.imgAcquisitionReward, '${assetPath}img_acquisition_reward$svg');
    expect(DOPNativeImages.imgAcquisitionRewardItem, '${assetPath}img_acquisition_reward_item$svg');
    expect(DOPNativeImages.imgAcquisitionRewardItemDashSeparator,
        '${assetPath}img_acquisition_reward_item_dash_separator$svg');

    /// DOP FaceOTP
    expect(DOPNativeImages.imgFaceOTPSuccess, '${assetPath}img_face_otp_success$png');

    /// Video Player
    expect(DOPNativeImages.icPlay, '${assetPath}ic_play$svg');
    expect(DOPNativeImages.icReload, '${assetPath}ic_reload$svg');

    /// Switch flow
    expect(DOPNativeImages.imgExistingRecord, '${assetPath}img_existing_record$svg');
    expect(DOPNativeImages.imgDuplicateRecord, '${assetPath}img_duplicate_record$svg');

    /// Collect Location
    expect(DOPNativeImages.imgCollectLocation, '${assetPath}img_collect_location$svg');

    /// Sophia call
    expect(DOPNativeImages.imgSophiaCall, '${assetPath}img_sophia_call$svg');
    expect(DOPNativeImages.icClockOutline, '${assetPath}ic_clock_outline$svg');
  });
}
