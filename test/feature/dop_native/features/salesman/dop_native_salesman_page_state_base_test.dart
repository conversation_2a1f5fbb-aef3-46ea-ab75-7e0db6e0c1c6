import 'dart:async';

import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_application_state_entity.dart';
import 'package:evoapp/feature/dop_native/base/cubit/dop_native_application_state.dart';
import 'package:evoapp/feature/dop_native/base/cubit/dop_native_application_state_cubit.dart';
import 'package:evoapp/feature/dop_native/features/salesman/cubit/dop_native_salesman_id_cubit.dart';
import 'package:evoapp/feature/dop_native/features/salesman/cubit/dop_native_salesman_id_state.dart';
import 'package:evoapp/feature/dop_native/features/salesman/dop_native_salesman_page_state_base.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_button_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_colors.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_resources.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_text_styles.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:evoapp/feature/dop_native/widgets/appbar/dop_native_appbar_widget.dart';
import 'package:evoapp/feature/dop_native/widgets/text_field/dop_native_text_field_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test.dart';
import '../../../../base/evo_page_state_base_test_config.dart';
import '../../../../util/flutter_test_config.dart';
import '../../../../util/interceptor/unauthorized_interceptor_test.dart';
import '../../base/cubit/dop_native_application_state_cubit_test.dart';

class MockDOPNativeSalesmanIdCubit extends Mock implements DOPNativeSalesmanIdCubit {}

class MockDOPNativeApplicationStateCubit extends Mock implements DOPNativeApplicationStateCubit {}

class TestDOPNativeSalesmanPage extends PageBase {
  final DOPNativeSalesmanIdCubit salesmanIdCubit;
  final DOPNativeApplicationStateCubit applicationStateCubit;

  const TestDOPNativeSalesmanPage({
    required this.salesmanIdCubit,
    required this.applicationStateCubit,
    super.key,
  });

  @override
  TestDOPNativeSalesmanPageState createState() => TestDOPNativeSalesmanPageState(
        dopNativeApplicationStateCubit: applicationStateCubit,
        salesmanIdCubit: salesmanIdCubit,
      );

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: 'test_route');
}

class TestDOPNativeSalesmanPageState
    extends DOPNativeSalesmanPageStateBase<TestDOPNativeSalesmanPage> {
  bool isShowLoadingCalled = false;
  bool isHideLoadingCalled = false;
  bool isHandleDopEvoApiErrorCalled = false;

  @override
  final DOPNativeApplicationStateCubit dopNativeApplicationStateCubit;

  TestDOPNativeSalesmanPageState({
    required this.dopNativeApplicationStateCubit,
    super.salesmanIdCubit,
  });

  @override
  bool hasListenNetwork() => false;

  @override
  bool hasListenAuthorizationSessionExpired() => false;

  @override
  bool hasListenMaintenanceMode() => false;

  @override
  StaffIdScreenType get staffIdScreenType => StaffIdScreenType.inputId;

  @override
  String get title => 'Test Title';

  @override
  String get description => 'Test Description';

  @override
  Widget buildHeader() {
    return const Text('Header');
  }

  @override
  void showDOPLoading() {
    isShowLoadingCalled = true;
  }

  @override
  void hideDOPLoading() {
    isHideLoadingCalled = true;
  }

  @override
  Future<void> handleDopEvoApiError(
    ErrorUIModel? errorUIModel, {
    bool isReplaceCurrentScreen = false,
  }) async {
    isHandleDopEvoApiErrorCalled = true;
  }
}

void main() {
  late MockDOPNativeSalesmanIdCubit mockSalesmanIdCubit;
  late MockDOPNativeApplicationStateCubit mockApplicationStateCubit;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  setUp(() {
    initConfigEvoPageStateBase();
    setupMockImageProvider();

    getIt.registerSingleton<DOPNativeRepo>(MockDOPNativeRepo());
    getIt.registerSingleton<AuthenticationRepo>(MockAuthenticationRepo());

    getIt.registerLazySingleton<DopNativeButtonStyles>(() => DopNativeButtonStyles());
    getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
    getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());

    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();

    getIt.registerSingleton<EvoSnackBar>(MockEvoSnackBar());

    setUtilsMockInstanceForTesting();

    mockSalesmanIdCubit = MockDOPNativeSalesmanIdCubit();
    mockApplicationStateCubit = MockDOPNativeApplicationStateCubit();
  });

  tearDown(() {
    resetUtilMockToOriginalInstance();
    getIt.reset();
  });

  test('should use provided salesmanIdCubit when not null', () {
    // Create test instance with provided cubit
    final TestDOPNativeSalesmanPageState testState = TestDOPNativeSalesmanPageState(
      salesmanIdCubit: mockSalesmanIdCubit,
      dopNativeApplicationStateCubit: MockDOPNativeApplicationStateCubit(),
    );

    // Verify the instance is using our provided cubit
    expect(testState.salesmanIdCubit, equals(mockSalesmanIdCubit));
  });

  test('should create new DOPNativeSalesmanIdCubit when salesmanIdCubit is null', () {
    // Create a test subclass that doesn't require salesmanIdCubit parameter
    final TestDOPNativeSalesmanPageState testState = TestDOPNativeSalesmanPageState(
      dopNativeApplicationStateCubit: MockDOPNativeApplicationStateCubit(),
    );

    // Verify a new instance was created
    expect(testState.salesmanIdCubit, isNotNull);
    expect(testState.salesmanIdCubit, isNot(equals(mockSalesmanIdCubit)));
    expect(testState.salesmanIdCubit, isA<DOPNativeSalesmanIdCubit>());
  });

  group('handleSalesmanIdState', () {
    test('calls showDOPLoading on DOPNativeSalesmanIdLoading', () {
      final TestDOPNativeSalesmanPageState pageState = TestDOPNativeSalesmanPageState(
        salesmanIdCubit: mockSalesmanIdCubit,
        dopNativeApplicationStateCubit: mockApplicationStateCubit,
      );

      pageState.handleSalesmanIdState(DOPNativeSalesmanIdLoading());

      expect(pageState.isShowLoadingCalled, true);
      expect(pageState.isHideLoadingCalled, false);
    });

    test('handles DOPNativeGetSalesmanIdSuccess', () {
      const String salesmanId = '12345';
      when(() => mockSalesmanIdCubit.validateSalesmanID(salesmanId)).thenAnswer((_) async {});
      final TestDOPNativeSalesmanPageState pageState = TestDOPNativeSalesmanPageState(
        salesmanIdCubit: mockSalesmanIdCubit,
        dopNativeApplicationStateCubit: mockApplicationStateCubit,
      );

      pageState.handleSalesmanIdState(
        DOPNativeGetSalesmanIdSuccess(salesmanIdWithoutPrefix: salesmanId),
      );

      expect(pageState.isShowLoadingCalled, false);
      expect(pageState.isHideLoadingCalled, true);
      pageState.codeTextController.text = salesmanId;
      verify(() => mockSalesmanIdCubit.validateSalesmanID(salesmanId)).called(1);
    });

    test('handles DOPNativeSubmitSalesmanIdSuccess', () {
      when(() => mockApplicationStateCubit.getApplicationState()).thenAnswer((_) async {});
      final TestDOPNativeSalesmanPageState pageState = TestDOPNativeSalesmanPageState(
        salesmanIdCubit: mockSalesmanIdCubit,
        dopNativeApplicationStateCubit: mockApplicationStateCubit,
      );

      pageState.handleSalesmanIdState(DOPNativeSubmitSalesmanIdSuccess());

      expect(pageState.isShowLoadingCalled, false);
      expect(pageState.isHideLoadingCalled, true);
      verify(() => mockApplicationStateCubit.getApplicationState()).called(1);
    });

    test('handles DOPNativeSalesmanIdError', () {
      final TestDOPNativeSalesmanPageState pageState = TestDOPNativeSalesmanPageState(
        salesmanIdCubit: mockSalesmanIdCubit,
        dopNativeApplicationStateCubit: mockApplicationStateCubit,
      );

      pageState.handleSalesmanIdState(DOPNativeSalesmanIdError());

      expect(pageState.isShowLoadingCalled, false);
      expect(pageState.isHideLoadingCalled, true);
      expect(pageState.isHandleDopEvoApiErrorCalled, true);
    });
  });

  test('onSubmit', () {
    when(
      () => mockSalesmanIdCubit.submitSalesmanId(
        inputtedId: '',
        isConfirmId: false,
      ),
    ).thenAnswer((_) async {});
    final TestDOPNativeSalesmanPageState pageState = TestDOPNativeSalesmanPageState(
      salesmanIdCubit: mockSalesmanIdCubit,
      dopNativeApplicationStateCubit: mockApplicationStateCubit,
    );

    pageState.onSubmit();

    verify(
      () => mockSalesmanIdCubit.submitSalesmanId(inputtedId: '', isConfirmId: false),
    ).called(1);
  });

  group('buildCTA', () {
    late TestDOPNativeSalesmanPageState pageState;
    late StreamController<DOPNativeSalesmanIdState> salesmanStateStreamController;
    late StreamController<DOPNativeApplicationState> applicationStateStreamController;

    setUp(() {
      // Make it a broadcast stream to allow multiple listeners
      salesmanStateStreamController = StreamController<DOPNativeSalesmanIdState>.broadcast();
      applicationStateStreamController = StreamController<DOPNativeApplicationState>.broadcast();

      when(() => mockSalesmanIdCubit.stream).thenAnswer(
        (_) => salesmanStateStreamController.stream,
      );

      pageState = TestDOPNativeSalesmanPageState(
        salesmanIdCubit: mockSalesmanIdCubit,
        dopNativeApplicationStateCubit: mockApplicationStateCubit,
      );
    });

    tearDown(() {
      salesmanStateStreamController.close();
      applicationStateStreamController.close();
    });

    Widget createWidgetUnderTest() {
      return MaterialApp(
        home: Scaffold(
          body: BlocProvider<DOPNativeSalesmanIdCubit>.value(
            value: mockSalesmanIdCubit,
            child: pageState.buildCTA(),
          ),
        ),
      );
    }

    testWidgets('should create enabled button when state is DOPNativeValidateSalesmanIdSuccess',
        (WidgetTester tester) async {
      // Setup
      when(() => mockSalesmanIdCubit.state).thenReturn(DOPNativeValidateSalesmanIdSuccess());

      // Create widget
      await tester.pumpWidget(createWidgetUnderTest());

      // Verify
      final CommonButton button = tester.widget<CommonButton>(find.byType(CommonButton));
      expect(button.onPressed, isNotNull);
    });

    testWidgets('should create disabled button when state is DOPNativeValidateSalesmanIdFailed',
        (WidgetTester tester) async {
      // Setup
      when(() => mockSalesmanIdCubit.state).thenReturn(
        DOPNativeValidateSalesmanIdFailed(errorMsg: 'Error message'),
      );

      // Create widget
      await tester.pumpWidget(createWidgetUnderTest());

      // Verify
      final CommonButton button = tester.widget<CommonButton>(find.byType(CommonButton));
      expect(button.onPressed, isNull);
    });

    testWidgets('should call onSubmit when button is pressed and enabled',
        (WidgetTester tester) async {
      // Setup
      when(() => mockSalesmanIdCubit.state).thenReturn(DOPNativeValidateSalesmanIdSuccess());
      when(
        () => mockSalesmanIdCubit.submitSalesmanId(
          inputtedId: any(named: 'inputtedId'),
          isConfirmId: any(named: 'isConfirmId'),
        ),
      ).thenAnswer((_) async {});

      // Create widget
      await tester.pumpWidget(createWidgetUnderTest());

      // Action
      await tester.tap(find.byType(CommonButton));
      await tester.pump();

      // Verify
      verify(
        () => mockSalesmanIdCubit.submitSalesmanId(
          inputtedId: any(named: 'inputtedId'),
          isConfirmId: any(named: 'isConfirmId'),
        ),
      ).called(1);
    });
  });

  group('buildSalesmanIdPrefix', () {
    late TestDOPNativeSalesmanPageState pageState;

    setUp(() {
      mockSalesmanIdCubit = MockDOPNativeSalesmanIdCubit();
      mockApplicationStateCubit = MockDOPNativeApplicationStateCubit();

      pageState = TestDOPNativeSalesmanPageState(
        salesmanIdCubit: mockSalesmanIdCubit,
        dopNativeApplicationStateCubit: mockApplicationStateCubit,
      );
    });

    tearDown(() {
      getIt.unregister<AppState>();
    });

    testWidgets('should return SizedBox when prefix is empty', (WidgetTester tester) async {
      getIt<AppState>().dopNativeState.dopApplicationState = DOPNativeApplicationStateEntity(
        flowConfig: FlowConfig(
          leadSource: '',
        ),
      );

      // Render the widget
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(body: pageState.buildSalesmanIdPrefix()),
      ));

      // Verify a SizedBox with width 16 is returned
      final SizedBox sizedBox = tester.widget<SizedBox>(find.byType(SizedBox));
      expect(sizedBox.width, 16);
    });

    testWidgets('should return Padding with Text when prefix is not empty',
        (WidgetTester tester) async {
      final String testPrefix = 'VST';
      getIt<AppState>().dopNativeState.dopApplicationState = DOPNativeApplicationStateEntity(
        flowConfig: FlowConfig(
          leadSource: 'viettel_store',
        ),
      );

      // Render the widget
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(body: pageState.buildSalesmanIdPrefix()),
      ));

      // Verify we have a Padding widget
      expect(find.byType(Padding), findsOneWidget);

      // Verify the Padding has correct values
      final Padding padding = tester.widget<Padding>(find.byType(Padding));
      expect(padding.padding, const EdgeInsets.fromLTRB(16, 12, 0, 12));

      // Verify the Text widget has correct text
      expect(find.text(testPrefix), findsOneWidget);

      // Verify the text has correct style
      final Text text = tester.widget<Text>(find.text(testPrefix));
      expect(text.style, dopNativeTextStyles.bodyLarge(dopNativeColors.textActive));
    });
  });

  group('buildInputCode', () {
    late TestDOPNativeSalesmanPageState pageState;
    late StreamController<DOPNativeSalesmanIdState> salesmanStateStreamController;

    setUp(() {
      // Make it a broadcast stream to allow multiple listeners
      salesmanStateStreamController = StreamController<DOPNativeSalesmanIdState>.broadcast();

      when(() => mockSalesmanIdCubit.stream).thenAnswer(
        (_) => salesmanStateStreamController.stream,
      );

      pageState = TestDOPNativeSalesmanPageState(
        salesmanIdCubit: mockSalesmanIdCubit,
        dopNativeApplicationStateCubit: mockApplicationStateCubit,
      );
    });

    tearDown(() {
      salesmanStateStreamController.close();
    });

    Widget createWidgetUnderTest() {
      return MaterialApp(
        home: Scaffold(
          body: BlocProvider<DOPNativeSalesmanIdCubit>.value(
            value: mockSalesmanIdCubit,
            child: pageState.buildInputCode(),
          ),
        ),
      );
    }

    testWidgets(
        'should display text field without error when state is not DOPNativeValidateSalesmanIdFailed',
        (WidgetTester tester) async {
      // Setup
      when(() => mockSalesmanIdCubit.state).thenReturn(DOPNativeSalesmanIdLoading());
      when(() => mockSalesmanIdCubit.validateSalesmanID(any())).thenAnswer((_) async {});

      // Create widget
      await tester.pumpWidget(createWidgetUnderTest());

      // Verify
      expect(find.byType(DOPNativeTextField), findsOneWidget);
      final DOPNativeTextField textField =
          tester.widget<DOPNativeTextField>(find.byType(DOPNativeTextField));
      expect(textField.controller, equals(pageState.codeTextController));
      expect(textField.errorText, isNull);
    });

    testWidgets('should display error message when state is DOPNativeValidateSalesmanIdFailed',
        (WidgetTester tester) async {
      // Setup
      const String errorMessage = 'Invalid salesman ID';
      when(() => mockSalesmanIdCubit.state).thenReturn(
        DOPNativeValidateSalesmanIdFailed(errorMsg: errorMessage),
      );
      when(() => mockSalesmanIdCubit.validateSalesmanID(any())).thenAnswer((_) async {});

      // Create widget
      await tester.pumpWidget(createWidgetUnderTest());

      // Verify
      expect(find.byType(DOPNativeTextField), findsOneWidget);
      final DOPNativeTextField textField =
          tester.widget<DOPNativeTextField>(find.byType(DOPNativeTextField));
      expect(textField.errorText, equals(errorMessage));
    });

    testWidgets('should call validateSalesmanID when text changes', (WidgetTester tester) async {
      // Setup
      const String newText = '12345';
      when(() => mockSalesmanIdCubit.state).thenReturn(DOPNativeSalesmanIdLoading());
      when(() => mockSalesmanIdCubit.validateSalesmanID(any())).thenAnswer((_) async {});

      // Create widget
      await tester.pumpWidget(createWidgetUnderTest());

      // Find text field
      final DOPNativeTextField textField =
          tester.widget<DOPNativeTextField>(find.byType(DOPNativeTextField));

      // Call onChanged callback
      textField.onChanged?.call(newText);

      // Verify
      verify(() => mockSalesmanIdCubit.validateSalesmanID(newText)).called(1);
    });

    testWidgets('should use prefixWidget from buildSalesmanIdPrefix', (WidgetTester tester) async {
      // Setup
      when(() => mockSalesmanIdCubit.state).thenReturn(DOPNativeSalesmanIdLoading());
      when(() => mockSalesmanIdCubit.validateSalesmanID(any())).thenAnswer((_) async {});

      // Make sure prefix is empty for this test
      getIt<AppState>().dopNativeState.dopApplicationState = DOPNativeApplicationStateEntity(
        flowConfig: FlowConfig(leadSource: ''),
      );

      // Create widget
      await tester.pumpWidget(createWidgetUnderTest());

      // Verify
      final DOPNativeTextField textField =
          tester.widget<DOPNativeTextField>(find.byType(DOPNativeTextField));
      expect(textField.prefixWidget, isA<SizedBox>());
    });

    testWidgets('should use correct hint text when prefix is empty', (WidgetTester tester) async {
      // Setup
      when(() => mockSalesmanIdCubit.state).thenReturn(DOPNativeSalesmanIdLoading());
      when(() => mockSalesmanIdCubit.validateSalesmanID(any())).thenAnswer((_) async {});

      // Make sure prefix is empty
      getIt<AppState>().dopNativeState.dopApplicationState = DOPNativeApplicationStateEntity(
        flowConfig: FlowConfig(leadSource: ''),
      );

      // Create widget
      await tester.pumpWidget(createWidgetUnderTest());

      // Verify
      final DOPNativeTextField textField =
          tester.widget<DOPNativeTextField>(find.byType(DOPNativeTextField));
      expect(textField.hintText, equals(DOPNativeStrings.pleaseEnterSalesmanID));
    });

    testWidgets('should use empty hint text when prefix is not empty', (WidgetTester tester) async {
      // Setup
      when(() => mockSalesmanIdCubit.state).thenReturn(DOPNativeSalesmanIdLoading());
      when(() => mockSalesmanIdCubit.validateSalesmanID(any())).thenAnswer((_) async {});

      // Make sure prefix is not empty
      getIt<AppState>().dopNativeState.dopApplicationState = DOPNativeApplicationStateEntity(
        flowConfig: FlowConfig(leadSource: 'viettel_store'),
      );

      // Create widget
      await tester.pumpWidget(createWidgetUnderTest());

      // Verify
      final DOPNativeTextField textField =
          tester.widget<DOPNativeTextField>(find.byType(DOPNativeTextField));
      expect(textField.hintText, equals(''));
    });
  });

  group('getContentWidget', () {
    late TestDOPNativeSalesmanPageState pageState;
    late StreamController<DOPNativeSalesmanIdState> salesmanStateStreamController;

    setUp(() {
      // Make it a broadcast stream to allow multiple listeners
      salesmanStateStreamController = StreamController<DOPNativeSalesmanIdState>.broadcast();

      when(() => mockSalesmanIdCubit.stream).thenAnswer(
        (_) => salesmanStateStreamController.stream,
      );

      when(() => mockSalesmanIdCubit.close()).thenAnswer((_) async {});

      pageState = TestDOPNativeSalesmanPageState(
        salesmanIdCubit: mockSalesmanIdCubit,
        dopNativeApplicationStateCubit: mockApplicationStateCubit,
      );
    });

    tearDown(() {
      salesmanStateStreamController.close();
    });

    Widget createWidgetUnderTest() {
      return MaterialApp(
        home: Builder(
          builder: (BuildContext context) => pageState.getContentWidget(context),
        ),
      );
    }

    testWidgets('should render all components correctly', (WidgetTester tester) async {
      // Setup
      when(() => mockSalesmanIdCubit.state).thenReturn(DOPNativeSalesmanIdLoading());
      when(() => mockSalesmanIdCubit.validateSalesmanID(any())).thenAnswer((_) async {});

      // Create widget
      await tester.pumpWidget(createWidgetUnderTest());

      // Verify scaffold is present
      expect(find.byType(Scaffold), findsOneWidget);

      // Verify app bar is present
      expect(find.byType(DOPNativeAppBar), findsOneWidget);

      // Verify header is present
      expect(find.text('Header'), findsOneWidget);

      // Verify title is present
      expect(find.text('Test Title'), findsOneWidget);

      // Verify description is present
      expect(find.text('Test Description'), findsOneWidget);

      // Verify input code widget is present
      expect(find.byType(DOPNativeTextField), findsOneWidget);

      // Verify CTA button is present
      expect(find.byType(CommonButton), findsOneWidget);
    });

    testWidgets('should call handleSalesmanIdState when state changes',
        (WidgetTester tester) async {
      // Setup
      when(() => mockSalesmanIdCubit.state).thenReturn(DOPNativeSalesmanIdLoading());
      when(() => mockSalesmanIdCubit.validateSalesmanID(any())).thenAnswer((_) async {});

      // Create widget
      await tester.pumpWidget(createWidgetUnderTest());

      // Verify initial state
      expect(pageState.isShowLoadingCalled, false);

      // Emit state to trigger the BlocListener
      salesmanStateStreamController.add(DOPNativeSalesmanIdLoading());
      await tester.pump();

      // Verify that handleSalesmanIdState was called
      expect(pageState.isShowLoadingCalled, true);
    });

    testWidgets('should update UI when state changes to error state', (WidgetTester tester) async {
      // Setup
      const String errorMessage = 'Invalid salesman ID';
      when(() => mockSalesmanIdCubit.state).thenReturn(DOPNativeSalesmanIdLoading());
      when(() => mockSalesmanIdCubit.validateSalesmanID(any())).thenAnswer((_) async {});

      // Create widget
      await tester.pumpWidget(createWidgetUnderTest());

      // Initially, there should be no error
      expect(find.text(errorMessage), findsNothing);

      // Change to error state
      salesmanStateStreamController.add(DOPNativeValidateSalesmanIdFailed(errorMsg: errorMessage));
      await tester.pump();

      // Now the text field should show error
      final DOPNativeTextField textField = tester.widget<DOPNativeTextField>(
        find.byType(DOPNativeTextField),
      );
      expect(textField.errorText, equals(errorMessage));
    });

    testWidgets('should render correct background color', (WidgetTester tester) async {
      // Setup
      when(() => mockSalesmanIdCubit.state).thenReturn(DOPNativeSalesmanIdLoading());
      when(() => mockSalesmanIdCubit.validateSalesmanID(any())).thenAnswer((_) async {});

      // Create widget
      await tester.pumpWidget(createWidgetUnderTest());

      // Verify scaffold background color
      final Scaffold scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.backgroundColor, dopNativeColors.screenBackground);
    });
  });
}
