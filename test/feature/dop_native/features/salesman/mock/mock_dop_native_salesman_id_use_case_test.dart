import 'package:evoapp/feature/dop_native/features/salesman/mock/mock_dop_native_salesman_id_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Test MockDopNativeSalesmanIdUseCase', () {
    expect(MockDopNativeSalesmanIdUseCase.submitSalesmanIdSuccess.value,
        'dop_native_submit_salesman_id_success.json');
    expect(MockDopNativeSalesmanIdUseCase.submitConfirmSalesmanIdSuccess.value,
        'dop_native_submit_confirm_salesman_id_success.json');
    expect(MockDopNativeSalesmanIdUseCase.getSalesmanIdSuccess.value,
        'dop_native_get_salesman_id_success.json');
  });

  test('Test getMockDOPNativeSalesmanId', () {
    expect(
      getMockDOPNativeSalesmanId(MockDopNativeSalesmanIdUseCase.submitSalesmanIdSuccess),
      'dop_native_submit_salesman_id_success.json',
    );

    expect(
      getMockDOPNativeSalesmanId(MockDopNativeSalesmanIdUseCase.submitConfirmSalesmanIdSuccess),
      'dop_native_submit_confirm_salesman_id_success.json',
    );

    expect(
      getMockDOPNativeSalesmanId(MockDopNativeSalesmanIdUseCase.getSalesmanIdSuccess),
      'dop_native_get_salesman_id_success.json',
    );
  });
}
