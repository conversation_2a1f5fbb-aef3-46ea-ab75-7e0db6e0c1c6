import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_card_status_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_credit_assignment_entity.dart';
import 'package:evoapp/feature/dop_native/features/e_success/cubit/dop_native_e_success_cubit.dart';
import 'package:evoapp/feature/dop_native/features/e_success/utils/dop_native_e_success_validator.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../constant.dart';

class MockDopNativeRepo extends Mock implements DOPNativeRepo {}

class MockCardStatusEntity extends Mock implements DOPNativeCardStatusEntity {}

class MockCreditAssignmentEntity extends Mock implements DOPNativeCreditAssignmentEntity {}

class MockESuccessValidator extends Mock implements DOPNativeESuccessValidator {}

void main() {
  late final DOPNativeRepo mockRepo;
  late DOPNativeESuccessCubit cubit;
  final MockCardStatusEntity mockCardStatusEntity = MockCardStatusEntity();
  final MockCreditAssignmentEntity mockCreditEntity = MockCreditAssignmentEntity();
  final DOPNativeESuccessValidator mockValidator = MockESuccessValidator();

  setUpAll(() {
    mockRepo = MockDopNativeRepo();
  });

  setUp(() {
    cubit = DOPNativeESuccessCubit(
      dopNativeRepo: mockRepo,
      validator: mockValidator,
    );

    when(() => mockRepo.getAppraisingCreditAssignment(
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async => mockCreditEntity);

    when(() => mockRepo.getCardStatus(mockConfig: any(named: 'mockConfig')))
        .thenAnswer((_) async => mockCardStatusEntity);
  });

  tearDown(() {
    reset(mockRepo);
    reset(mockCardStatusEntity);
    reset(mockCreditEntity);
    reset(mockValidator);
  });

  group('verify initial value', () {
    test('state is DOPNativeESuccessInitial', () {
      expect(cubit.state, isA<DOPNativeESuccessInitial>());
    });
  });

  group('verify invoke repo', () {
    test('getCardStatus call repo.getCardStatus', () async {
      final DOPNativeCardStatusEntity entity = await cubit.getCardStatus();

      expect(entity, mockCardStatusEntity);
      verify(() => mockRepo.getCardStatus(mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('getCreditAssignment call repo.getAppraisingCreditAssignment', () async {
      final DOPNativeCreditAssignmentEntity entity = await cubit.getCreditAssignment();

      expect(
        entity,
        mockCreditEntity,
      );

      verify(
        () => mockRepo.getAppraisingCreditAssignment(
          mockConfig: any(named: 'mockConfig'),
        ),
      ).called(1);
    });
  });

  group('verify getScreenData', () {
    tearDown(() {
      reset(mockRepo);
    });

    verifyRepoCalled() {
      verify(() => mockRepo.getAppraisingCreditAssignment(
            mockConfig: any(named: 'mockConfig'),
          )).called(1);

      verify(
        () => mockRepo.getCardStatus(
          mockConfig: any(named: 'mockConfig'),
        ),
      ).called(1);
    }

    blocTest<DOPNativeESuccessCubit, DOPNativeESuccessState>('getScreenData success',
        build: () => cubit,
        setUp: () {
          when(() => mockCreditEntity.statusCode).thenReturn(CommonHttpClient.SUCCESS);
          when(() => mockCardStatusEntity.statusCode).thenReturn(CommonHttpClient.SUCCESS);
          when(() => mockValidator.validate(cardStatus: any(named: 'cardStatus'))).thenReturn(true);
        },
        act: (DOPNativeESuccessCubit cubit) => cubit.getScreenData(),
        wait: TestConstant.blocEmitStateDelayDuration,
        verify: (_) {
          verifyRepoCalled();
        },
        expect: () => <TypeMatcher<DOPNativeESuccessState>>[
              isA<GetScreenDataLoading>(),
              isA<GetScreenDataSuccess>()
                  .having(
                    (GetScreenDataSuccess p0) => p0.cardStatus,
                    'cardStatus should equal',
                    mockCardStatusEntity,
                  )
                  .having(
                    (GetScreenDataSuccess p0) => p0.creditAssignment,
                    'creditAssignment should equal',
                    mockCreditEntity,
                  )
            ]);

    blocTest<DOPNativeESuccessCubit, DOPNativeESuccessState>('getStatusCard failed',
        wait: TestConstant.blocEmitStateDelayDuration,
        build: () => cubit,
        setUp: () {
          when(() => mockCreditEntity.statusCode).thenReturn(CommonHttpClient.SUCCESS);
          when(() => mockCardStatusEntity.statusCode).thenReturn(CommonHttpClient.BAD_REQUEST);
          when(() => mockValidator.validate(cardStatus: any(named: 'cardStatus'))).thenReturn(true);
        },
        act: (DOPNativeESuccessCubit cubit) => cubit.getScreenData(),
        verify: (_) => verifyRepoCalled(),
        expect: () => <TypeMatcher<DOPNativeESuccessState>>[
              isA<GetScreenDataLoading>(),
              isA<GetScreenDataFailure>().having(
                (GetScreenDataFailure p0) => p0.error.statusCode,
                'matched statusCode',
                CommonHttpClient.BAD_REQUEST,
              )
            ]);

    blocTest<DOPNativeESuccessCubit, DOPNativeESuccessState>('getCreditAssignment failed',
        wait: TestConstant.blocEmitStateDelayDuration,
        build: () => cubit,
        setUp: () {
          when(() => mockCreditEntity.statusCode).thenReturn(CommonHttpClient.BAD_REQUEST);
          when(() => mockCardStatusEntity.statusCode).thenReturn(CommonHttpClient.SUCCESS);
          when(() => mockValidator.validate(cardStatus: any(named: 'cardStatus'))).thenReturn(true);
        },
        act: (DOPNativeESuccessCubit cubit) => cubit.getScreenData(),
        verify: (_) => verifyRepoCalled(),
        expect: () => <TypeMatcher<DOPNativeESuccessState>>[
              isA<GetScreenDataLoading>(),
              isA<GetScreenDataSuccess>()
                  .having(
                    (GetScreenDataSuccess p0) => p0.cardStatus,
                    'cardStatus should equal',
                    mockCardStatusEntity,
                  )
                  .having(
                    (GetScreenDataSuccess p0) => p0.creditAssignment?.creditAmount,
                    'creditAmount should be null',
                    isNull,
                  )
            ]);

    blocTest<DOPNativeESuccessCubit, DOPNativeESuccessState>(
        'getStatusCard failed when validator.validate return false',
        wait: TestConstant.blocEmitStateDelayDuration,
        build: () => cubit,
        setUp: () {
          when(() => mockCreditEntity.statusCode).thenReturn(CommonHttpClient.SUCCESS);
          when(() => mockCardStatusEntity.statusCode).thenReturn(CommonHttpClient.SUCCESS);
          when(() => mockValidator.validate(cardStatus: any(named: 'cardStatus')))
              .thenReturn(false);
        },
        act: (DOPNativeESuccessCubit cubit) => cubit.getScreenData(),
        verify: (_) => verifyRepoCalled(),
        expect: () => <TypeMatcher<DOPNativeESuccessState>>[
              isA<GetScreenDataLoading>(),
              isA<GetScreenDataFailure>().having(
                (GetScreenDataFailure p0) => p0.error.statusCode,
                'matched statusCode',
                CommonHttpClient.INVALID_FORMAT,
              )
            ]);
  });
}
