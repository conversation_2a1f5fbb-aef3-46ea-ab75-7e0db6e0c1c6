import 'package:evoapp/data/response/dop_native/dop_native_card_status_entity.dart';
import 'package:evoapp/feature/dop_native/features/e_success/dop_native_e_success_ui_version.dart';
import 'package:evoapp/feature/dop_native/features/e_success/widgets/dop_native_e_success_ui_model.dart';
import 'package:evoapp/feature/dop_native/features/e_success/widgets/dop_native_e_success_ui_model_creator.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCardStatusEntity extends Mock implements DOPNativeCardStatusEntity {}

void main() {
  group('DOPNativeESuccessUIModelCreator null case', () {
    test('create should return null if isOfflineMerchant or consentLinkCard is null', () {
      final MockCardStatusEntity cardStatus = MockCardStatusEntity();
      final DOPNativeESuccessUIModelCreator creator = DOPNativeESuccessUIModelCreator(
        cardStatus: cardStatus,
        uiVersion: 'any_ui_version',
      );
      when(() => cardStatus.isOfflineMerchant).thenReturn(null);
      when(() => cardStatus.consentLinkCard).thenReturn(true);

      final DOPNativeESuccessUIModel? result = creator.create();

      expect(result, isNull);
    });

    test('create should return null if isOfflineMerchant is false and uiVersion is not autoCic',
        () {
      final MockCardStatusEntity cardStatus = MockCardStatusEntity();
      final DOPNativeESuccessUIModelCreator creator = DOPNativeESuccessUIModelCreator(
        cardStatus: cardStatus,
        uiVersion: 'any_ui_version',
      );

      when(() => cardStatus.isOfflineMerchant).thenReturn(false);
      when(() => cardStatus.consentLinkCard).thenReturn(true);

      final DOPNativeESuccessUIModel? result = creator.create();

      expect(result, isNull);
    });
  });

  group('DOPNativeESuccessUIModelCreator nonnull case', () {
    test(
        'create should return DOPNativeOnlineMerchantCICUIModel if isOfflineMerchant is false and uiVersion is autoCic',
        () {
      final MockCardStatusEntity cardStatus = MockCardStatusEntity();
      final DOPNativeESuccessUIModelCreator creator = DOPNativeESuccessUIModelCreator(
        cardStatus: cardStatus,
        uiVersion: DOPNativeESuccessUIVersion.autoCic.value,
      );

      when(() => cardStatus.isOfflineMerchant).thenReturn(false);
      when(() => cardStatus.consentLinkCard).thenReturn(true);

      final DOPNativeESuccessUIModel? result = creator.create();

      expect(result, isA<DOPNativeOnlineMerchantCICUIModel>());
    });

    test('create should return DOPNativeOfflineMerchantUIModel if isOfflineMerchant is true', () {
      final MockCardStatusEntity cardStatus = MockCardStatusEntity();
      final DOPNativeESuccessUIModelCreator creator = DOPNativeESuccessUIModelCreator(
        cardStatus: cardStatus,
        uiVersion: 'any_ui_version',
      );
      when(() => cardStatus.isOfflineMerchant).thenReturn(true);
      when(() => cardStatus.consentLinkCard).thenReturn(true);

      final DOPNativeESuccessUIModel? result = creator.create();

      expect(result, isA<DOPNativeOfflineMerchantUIModel>());
    });
  });
}
