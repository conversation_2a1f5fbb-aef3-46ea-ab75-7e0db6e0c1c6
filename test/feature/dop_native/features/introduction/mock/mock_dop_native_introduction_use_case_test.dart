import 'package:evoapp/feature/dop_native/features/introduction/mock/mock_dop_native_introduction_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('getMockDOPNativeIntroductionFileNameByCase should return correct file names', () {
    expect(
      getMockDOPNativeIntroductionFileNameByCase(
          MockTestDOPNativeIntroductionUseCase.registrationCampaign),
      'dop_native_registration_campaign.json',
    );

    expect(
      getMockDOPNativeIntroductionFileNameByCase(
          MockTestDOPNativeIntroductionUseCase.registerSuccess),
      'dop_native_register_success.json',
    );

    expect(
      getMockDOPNativeIntroductionFileNameByCase(
          MockTestDOPNativeIntroductionUseCase.registerSuccessVerdictIsEmpty),
      'dop_native_register_success_verdict_is_empty.json',
    );

    expect(
      getMockDOPNativeIntroductionFileNameByCase(
          MockTestDOPNativeIntroductionUseCase.registerWithLimitExceeded),
      'dop_native_register_limit_exceeded.json',
    );

    expect(
      getMockDOPNativeIntroductionFileNameByCase(
          MockTestDOPNativeIntroductionUseCase.registerUnqualified),
      'dop_native_register_unqualified.json',
    );

    expect(
      getMockDOPNativeIntroductionFileNameByCase(
          MockTestDOPNativeIntroductionUseCase.registerDuplicate),
      'dop_native_register_duplicate.json',
    );

    expect(
      getMockDOPNativeIntroductionFileNameByCase(MockTestDOPNativeIntroductionUseCase.registerFail),
      'dop_native_register_fail.json',
    );

    expect(
      getMockDOPNativeIntroductionFileNameByCase(
          MockTestDOPNativeIntroductionUseCase.registerExistingRecord),
      'dop_native_register_existing_record.json',
    );

    expect(
      getMockDOPNativeIntroductionFileNameByCase(
          MockTestDOPNativeIntroductionUseCase.registerInvalidPToken),
      'dop_native_register_invalid_ptoken.json',
    );

    expect(
      getMockDOPNativeIntroductionFileNameByCase(
          MockTestDOPNativeIntroductionUseCase.registerExpiredPToken),
      'dop_native_register_expired_ptoken.json',
    );

    expect(
      getMockDOPNativeIntroductionFileNameByCase(
          MockTestDOPNativeIntroductionUseCase.registerDuplicateReject),
      'dop_native_register_duplicate_reject.json',
    );
  });
}
