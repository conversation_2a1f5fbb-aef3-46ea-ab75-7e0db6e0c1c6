import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_application_state_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_bootstrap_auth_settings_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_bootstrap_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_data_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_registration_campaign_entity.dart';
import 'package:evoapp/feature/deep_link/deep_link_utils.dart';
import 'package:evoapp/feature/deep_link/model/deep_link_model.dart';
import 'package:evoapp/feature/deep_link/model/deep_link_shared_data.dart';
import 'package:evoapp/feature/dop_native/features/introduction/cubit/dop_native_introduction_cubit.dart';
import 'package:evoapp/feature/dop_native/models/dop_native_state.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/extension.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/url_launcher_uri_wrapper.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../constant.dart';
import '../../../../util/test_util.dart';

class MockUrlLauncherWrapper extends Mock implements UrlLauncherWrapper {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

class MockAppState extends Mock implements AppState {}

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

class MockDeepLinkUtils extends Mock implements DeepLinkUtils {}

void main() {
  const String fakeUniqueToken = 'fake_unique_token';

  late DOPNativeIntroductionCubit cubit;
  late LoggingRepo mockLoggingRepo;
  late UrlLauncherWrapper urlLauncherWrapper;
  late DOPNativeRepo mockDOPNativeRepo;
  late AppState appState;
  late DeepLinkUtils mockDeepLinkUtils;
  late DOPUtilFunctions mockDOPUtilFunctions;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());

    getIt.registerLazySingleton<UrlLauncherWrapper>(() => MockUrlLauncherWrapper());
    urlLauncherWrapper = getIt.get<UrlLauncherWrapper>();

    getIt.registerSingleton<AppState>(MockAppState());
    appState = getIt.get<AppState>();

    getIt.registerSingleton<DOPNativeRepo>(MockDOPNativeRepo());

    getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
    mockLoggingRepo = getIt.get<LoggingRepo>();

    registerFallbackValue(LeadSource.evoNative);

    when(() => appState.dopNativeState).thenReturn(DOPNativeState());

    getIt.registerLazySingleton<DeepLinkUtils>(() => MockDeepLinkUtils());
    mockDeepLinkUtils = getIt.get<DeepLinkUtils>();

    getIt.registerLazySingleton<DOPUtilFunctions>(() => MockDOPUtilFunctions());
    mockDOPUtilFunctions = getIt.get<DOPUtilFunctions>();
  });

  setUp(() {
    mockDOPNativeRepo = getIt.get<DOPNativeRepo>();
    cubit = DOPNativeIntroductionCubit(
      dopNativeRepo: mockDOPNativeRepo,
      appState: appState,
    );
  });

  tearDown(() {
    appState.dopNativeState.clear();
    reset(mockDOPNativeRepo);
    reset(urlLauncherWrapper);
    reset(mockDOPUtilFunctions);
  });

  tearDownAll(() {
    getIt.reset();
  });

  test('Default state is DOPNativeIntroductionInitial', () {
    expect(cubit.state, isA<DOPNativeIntroductionInitial>());
  });

  void verifySaveInfoCampaignToAppState({
    required String campaignCode,
    required String source,
  }) {
    expect(appState.dopNativeState.campaignCode, campaignCode);
    expect(appState.dopNativeState.source, source);
  }

  group('verify initToRegistrationCampaign()', () {
    const String fakeCampaignCode = 'fake_campaign_code';
    const String fakeSource = 'fake_source';
    const String fakeChallengeType = 'none';
    const String fakeUIVersion = 'fake_ui_version';
    const String fakeUIFlow = 'fake_ui_flow';
    const String fakeLeadSource = LeadSource.mwg;

    setUp(() {
      when(() => appState.deepLinkSharedData).thenReturn(DeepLinkSharedData(
        deepLink: DeepLinkModel(
          params: <String, String>{'lead_source': fakeLeadSource},
          screenName: '',
        ),
      ));

      when(() => mockDeepLinkUtils.removeEvoDeepLinkKeysFromMap(any()))
          .thenReturn(<String, String>{'lead_source': fakeLeadSource});
    });

    tearDown(() {
      appState.dopNativeState.clear();
      reset(mockDOPNativeRepo);
    });

    blocTest<DOPNativeIntroductionCubit, DOPNativeIntroductionState>(
      'emits [RegistrationCampaignSuccess] when campaignCode is null',
      build: () => cubit,
      act: (DOPNativeIntroductionCubit cubit) => cubit.initToRegistrationCampaign(),
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(
          () => mockDOPNativeRepo.registrationCampaign(
            campaignCode: any(named: 'campaignCode'),
            leadSource: any(named: 'leadSource'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async => DOPNativeRegistrationCampaignEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: await TestUtil.getResponseMock('dop_native_registration_campaign.json'),
            )));
      },
      expect: () => <dynamic>[
        isA<DOPNativeIntroductionLoading>(),
        isA<RegistrationCampaignSuccess>()
            .having((RegistrationCampaignSuccess state) => state.entity.campaignCode,
                'verify campaign_code', fakeCampaignCode)
            .having((RegistrationCampaignSuccess state) => state.entity.challengeType,
                'verify challenge_type', fakeChallengeType)
            .having((RegistrationCampaignSuccess state) => state.entity.source, 'verify source',
                fakeSource)
            .having((RegistrationCampaignSuccess state) => state.entity.uiFlow?.first,
                'verify ui_flow', fakeUIFlow)
            .having((RegistrationCampaignSuccess state) => state.entity.uiVersion,
                'verify ui_version', fakeUIVersion),
      ],
      verify: (_) {
        verify(
          () => mockDOPNativeRepo.registrationCampaign(
            mockConfig: any(named: 'mockConfig'),
            leadSource: LeadSource.mwg,
          ),
        ).called(1);

        verifySaveInfoCampaignToAppState(
          campaignCode: fakeCampaignCode,
          source: fakeSource,
        );
      },
    );

    blocTest<DOPNativeIntroductionCubit, DOPNativeIntroductionState>(
      'emits [RegistrationCampaignSuccess] when campaignCode is not null',
      build: () => cubit,
      act: (DOPNativeIntroductionCubit cubit) => cubit.initToRegistrationCampaign(
        campaignCode: fakeCampaignCode,
      ),
      setUp: () {
        when(
          () => mockDOPNativeRepo.registrationCampaign(
            campaignCode: any(named: 'campaignCode'),
            leadSource: any(named: 'leadSource'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async => DOPNativeRegistrationCampaignEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: await TestUtil.getResponseMock('dop_native_registration_campaign.json'),
            )));
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<DOPNativeIntroductionLoading>(),
        isA<RegistrationCampaignSuccess>()
            .having((RegistrationCampaignSuccess state) => state.entity.campaignCode,
                'verify campaign_code', fakeCampaignCode)
            .having((RegistrationCampaignSuccess state) => state.entity.challengeType,
                'verify challenge_type', fakeChallengeType)
            .having((RegistrationCampaignSuccess state) => state.entity.source, 'verify source',
                fakeSource)
            .having((RegistrationCampaignSuccess state) => state.entity.uiFlow?.first,
                'verify ui_flow', fakeUIFlow)
            .having((RegistrationCampaignSuccess state) => state.entity.uiVersion,
                'verify ui_version', fakeUIVersion),
      ],
      verify: (_) {
        verify(
          () => mockDOPNativeRepo.registrationCampaign(
            campaignCode: fakeCampaignCode,
            leadSource: LeadSource.mwg,
            mockConfig: any(named: 'mockConfig'),
          ),
        ).called(1);

        verifySaveInfoCampaignToAppState(
          campaignCode: fakeCampaignCode,
          source: fakeSource,
        );
      },
    );

    blocTest<DOPNativeIntroductionCubit, DOPNativeIntroductionState>(
      'emits [RegistrationCampaignFail] when call API fail',
      build: () => cubit,
      act: (DOPNativeIntroductionCubit cubit) =>
          cubit.initToRegistrationCampaign(campaignCode: fakeCampaignCode),
      setUp: () {
        when(
          () => mockDOPNativeRepo.registrationCampaign(
            campaignCode: any(named: 'campaignCode'),
            leadSource: any(named: 'leadSource'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async => DOPNativeRegistrationCampaignEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: null,
            )));
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<DOPNativeIntroductionLoading>(),
        isA<RegistrationCampaignFail>().having(
            (RegistrationCampaignFail state) => state.error, 'verify error', isA<ErrorUIModel>()),
      ],
      verify: (_) {
        verify(
          () => mockDOPNativeRepo.registrationCampaign(
            campaignCode: fakeCampaignCode,
            leadSource: LeadSource.mwg,
            mockConfig: any(named: 'mockConfig'),
          ),
        ).called(1);
      },
    );
  });

  group('test registrationCampaign', () {
    const String fakeCampaignCode = 'fake_campaign_code';
    const String fakeSource = 'fake_source';
    const String fakeChallengeType = 'none';
    const String fakeUIVersion = 'fake_ui_version';
    const String fakeUIFlow = 'fake_ui_flow';

    test('verify call registrationCampaign success', () async {
      when(
        () => mockDOPNativeRepo.registrationCampaign(
          campaignCode: any(named: 'campaignCode'),
          leadSource: any(named: 'leadSource'),
          mockConfig: any(named: 'mockConfig'),
        ),
      ).thenAnswer((_) async => DOPNativeRegistrationCampaignEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_registration_campaign.json'),
          )));

      final DOPNativeRegistrationCampaignEntity entity = await cubit.registrationCampaign();

      verify(
        () => mockDOPNativeRepo.registrationCampaign(
          campaignCode: any(named: 'campaignCode'),
          leadSource: any(named: 'leadSource'),
          mockConfig: any(named: 'mockConfig'),
        ),
      ).called(1);

      expect(
        entity,
        isA<DOPNativeRegistrationCampaignEntity>()
            .having((DOPNativeRegistrationCampaignEntity entity) => entity.campaignCode,
                'verify campaign_code', fakeCampaignCode)
            .having((DOPNativeRegistrationCampaignEntity entity) => entity.challengeType,
                'verify challenge_type', fakeChallengeType)
            .having((DOPNativeRegistrationCampaignEntity entity) => entity.source, 'verify source',
                fakeSource)
            .having((DOPNativeRegistrationCampaignEntity entity) => entity.uiFlow?.first,
                'verify ui_flow', fakeUIFlow)
            .having((DOPNativeRegistrationCampaignEntity entity) => entity.uiVersion,
                'verify ui_version', fakeUIVersion),
      );

      verifySaveInfoCampaignToAppState(
        campaignCode: fakeCampaignCode,
        source: fakeSource,
      );
    });

    test('verify call registrationCampaign failed', () async {
      when(
        () => mockDOPNativeRepo.registrationCampaign(
          campaignCode: any(named: 'campaignCode'),
          leadSource: any(named: 'leadSource'),
          mockConfig: any(named: 'mockConfig'),
        ),
      ).thenAnswer((_) async => DOPNativeRegistrationCampaignEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));

      final DOPNativeRegistrationCampaignEntity entity = await cubit.registrationCampaign();

      verify(
        () => mockDOPNativeRepo.registrationCampaign(
          campaignCode: any(named: 'campaignCode'),
          leadSource: any(named: 'leadSource'),
          mockConfig: any(named: 'mockConfig'),
        ),
      ).called(1);

      expect(
          entity,
          isA<DOPNativeRegistrationCampaignEntity>().having(
              (DOPNativeRegistrationCampaignEntity p0) => p0.statusCode,
              'verify status code',
              CommonHttpClient.BAD_REQUEST));
    });
  });

  group('verify continueDOEJourney()', () {
    test('verify continueDOEJourney()', () {
      when(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(any()))
          .thenAnswer((_) => Future<void>.value());

      const String fakeDOPNativeAccessToken = 'fake_dop_native_access_token';
      const String fakePhoneNumber = 'fake_phone_number';
      const String fakeUniqueToken = 'fake_unique_token';
      const String fakeCampaignCode = 'fake_campaign_code';
      const String fakeSource = 'fake_source';

      expect(appState.dopNativeState.dopNativeAccessToken, null);
      expect(appState.dopNativeState.phoneNumber, null);
      expect(appState.dopNativeState.uniqueToken, null);
      expect(appState.dopNativeState.dopApplicationState, null);

      final DOPNativeApplicationStateEntity fakeDOPApplicationStateEntity =
          DOPNativeApplicationStateEntity(
        uiVersion: 'v1.0',
      );

      final DOPNativeDataEntity entity = DOPNativeDataEntity(
        phoneNumber: fakePhoneNumber,
        accessToken: fakeDOPNativeAccessToken,
        uniqueToken: fakeUniqueToken,
        campaignCode: fakeCampaignCode,
        source: fakeSource,
        dopApplicationState: fakeDOPApplicationStateEntity,
      );

      cubit.continueDOEJourney(entity);

      verify(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(fakeDOPNativeAccessToken))
          .called(1);
    });
  });

  test('verify saveInfoToAppState()', () {
    const String fakeDOPNativeAccessToken = 'fake_dop_native_access_token';
    const String fakePhoneNumber = 'fake_phone_number';
    const String fakeUniqueToken = 'fake_unique_token';
    const String fakeCampaignCode = 'fake_campaign_code';
    const String fakeSource = 'fake_source';

    expect(appState.dopNativeState.dopNativeAccessToken, null);
    expect(appState.dopNativeState.phoneNumber, null);
    expect(appState.dopNativeState.uniqueToken, null);
    expect(appState.dopNativeState.dopApplicationState, null);

    final DOPNativeApplicationStateEntity fakeDOPApplicationStateEntity =
        DOPNativeApplicationStateEntity(
      uiVersion: 'v1.0',
    );

    final DOPNativeDataEntity entity = DOPNativeDataEntity(
      phoneNumber: fakePhoneNumber,
      accessToken: fakeDOPNativeAccessToken,
      uniqueToken: fakeUniqueToken,
      campaignCode: fakeCampaignCode,
      source: fakeSource,
      dopApplicationState: fakeDOPApplicationStateEntity,
    );

    cubit.saveInfoToAppState(entity);

    expect(appState.dopNativeState.phoneNumber, entity.phoneNumber);
    expect(appState.dopNativeState.dopNativeAccessToken, entity.accessToken);
    expect(appState.dopNativeState.uniqueToken, entity.uniqueToken);
    expect(appState.dopNativeState.campaignCode, entity.campaignCode);
    expect(appState.dopNativeState.source, entity.source);
    expect(appState.dopNativeState.dopApplicationState, fakeDOPApplicationStateEntity);
  });

  test('verify saveInfoCampaignToAppState()', () {
    const String fakeCampaignCode = 'fake_campaign_code';
    const String fakeSource = 'fake_source';

    expect(appState.dopNativeState.campaignCode, null);
    expect(appState.dopNativeState.source, null);

    final DOPNativeRegistrationCampaignEntity entity = DOPNativeRegistrationCampaignEntity(
      campaignCode: fakeCampaignCode,
      source: fakeSource,
    );

    cubit.saveInfoCampaignToAppState(
      campaignCode: fakeCampaignCode,
      source: fakeSource,
    );

    expect(appState.dopNativeState.campaignCode, entity.campaignCode);
    expect(appState.dopNativeState.source, entity.source);
  });

  group('Test requestToDialPhoneNumber', () {
    const String phoneNumber = '0900000000';
    final Uri expectUri = phoneNumber.uriForDialNumber();

    blocTest<DOPNativeIntroductionCubit, DOPNativeIntroductionState>(
      'Test can dial phone number',
      build: () => cubit,
      act: (DOPNativeIntroductionCubit cubit) => cubit.requestToDialPhoneNumber(phoneNumber),
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(() => urlLauncherWrapper.canLaunchUrl(expectUri)).thenAnswer((_) async {
          return true;
        });

        when(() => urlLauncherWrapper.launchUrl(expectUri)).thenAnswer((_) async {
          return true;
        });
      },
      expect: () => <dynamic>[
        isA<DOPNativeIntroductionLoading>(),
        isA<DialPhoneNumberLaunchSucceed>(),
      ],
      verify: (_) {
        verify(() => urlLauncherWrapper.canLaunchUrl(expectUri)).called(1);
        verify(() => urlLauncherWrapper.launchUrl(expectUri)).called(1);
      },
    );

    blocTest<DOPNativeIntroductionCubit, DOPNativeIntroductionState>(
      'Test can not dial phone number',
      build: () => cubit,
      act: (DOPNativeIntroductionCubit cubit) => cubit.requestToDialPhoneNumber(phoneNumber),
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(() => urlLauncherWrapper.canLaunchUrl(expectUri)).thenAnswer((_) async {
          return false;
        });

        when(() => urlLauncherWrapper.launchUrl(expectUri)).thenAnswer((_) async {
          return true;
        });
      },
      expect: () => <dynamic>[
        isA<DOPNativeIntroductionLoading>(),
        isA<DialPhoneNumberCanNotLaunch>(),
      ],
      verify: (_) {
        verify(() => urlLauncherWrapper.canLaunchUrl(expectUri)).called(1);
        verifyNever(() => urlLauncherWrapper.launchUrl(expectUri));
      },
    );

    blocTest<DOPNativeIntroductionCubit, DOPNativeIntroductionState>(
      'Test launchUrl return false',
      build: () => cubit,
      act: (DOPNativeIntroductionCubit cubit) => cubit.requestToDialPhoneNumber(phoneNumber),
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(() => urlLauncherWrapper.canLaunchUrl(expectUri)).thenAnswer((_) async {
          return true;
        });

        when(() => urlLauncherWrapper.launchUrl(expectUri)).thenAnswer((_) async {
          return false;
        });
      },
      expect: () => <dynamic>[
        isA<DOPNativeIntroductionLoading>(),
        isA<DialPhoneNumberLaunchFailed>(),
      ],
      verify: (_) {
        verify(() => urlLauncherWrapper.canLaunchUrl(expectUri)).called(1);
        verify(() => urlLauncherWrapper.launchUrl(expectUri)).called(1);
      },
    );

    blocTest<DOPNativeIntroductionCubit, DOPNativeIntroductionState>(
      'Test launchUrl throw exception',
      build: () => cubit,
      act: (DOPNativeIntroductionCubit cubit) => cubit.requestToDialPhoneNumber(phoneNumber),
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(() => urlLauncherWrapper.canLaunchUrl(expectUri)).thenAnswer((_) async {
          return true;
        });

        when(() => urlLauncherWrapper.launchUrl(expectUri)).thenAnswer((_) async {
          throw PlatformException(code: 'can_not_launch_url');
        });

        when(() => mockLoggingRepo.logErrorEvent(
              errorType: any(named: 'errorType'),
              args: any(named: 'args'),
            )).thenAnswer(
          (_) {
            return Future<void>.value();
          },
        );
      },
      expect: () => <dynamic>[
        isA<DOPNativeIntroductionLoading>(),
        isA<DialPhoneNumberLaunchError>(),
      ],
      verify: (_) {
        verify(() => urlLauncherWrapper.canLaunchUrl(expectUri)).called(1);
        verify(() => urlLauncherWrapper.launchUrl(expectUri)).called(1);
      },
    );
  });

  group('Test getBootstrap', () {
    bool isLogEventCalled = false;
    setUp(() async {
      isLogEventCalled = false;
      final Map<String, dynamic> responseDataBootstrapInfo = await TestUtil.getResponseMock(
          'dop_native_get_bootstrap_after_inputting_phone_number.json');
      final DOPNativeBootstrapEntity bootstrapInfoEntity =
          DOPNativeBootstrapEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: responseDataBootstrapInfo,
      ));

      final Map<String, dynamic> responseDataBootstrapAuthSettings =
          await TestUtil.getResponseMock('dop_native_bootstrap_auth_settings_success.json');
      final DOPNativeBootstrapAuthSettingsEntity fakeBootstrapAuthSettingsEntity =
          DOPNativeBootstrapAuthSettingsEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: responseDataBootstrapAuthSettings,
      ));

      when(() => mockDOPNativeRepo.getBootstrapInfo(
            token: any(named: 'token'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) => Future<DOPNativeBootstrapEntity>.value(bootstrapInfoEntity));

      when(() => mockDOPNativeRepo.getBootstrapAuthSettings(
              token: any(named: 'token'), mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) =>
              Future<DOPNativeBootstrapAuthSettingsEntity>.value(fakeBootstrapAuthSettingsEntity));
    });

    tearDown(() {
      // clear bootstrapAuthSettings after each test case
      appState.dopNativeState.bootstrapAuthSettings = null;
    });

    blocTest<DOPNativeIntroductionCubit, DOPNativeIntroductionState>(
      'Call getBootstrapInfo success & getBootstrapAuthSettings success',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIntroductionCubit cubit) => cubit.getBootstrap(
        fakeUniqueToken,
        logEvent: () {
          isLogEventCalled = true;
        },
      ),
      expect: () => <dynamic>[
        isA<DOPNativeIntroductionLoading>(),
        isA<DOPNativeBootstrapAuthSettingsLoaded>().having(
          (DOPNativeBootstrapAuthSettingsLoaded p0) => p0.bootstrapAuthSettingsEntity,
          'verify entity',
          isA<DOPNativeBootstrapAuthSettingsEntity>()
              .having(
                (DOPNativeBootstrapAuthSettingsEntity p0) => p0.authType,
                'verify authType',
                'otp',
              )
              .having(
                (DOPNativeBootstrapAuthSettingsEntity p0) => p0.accessToken,
                'verify accessToken',
                'fake_access_token',
              ),
        ),
      ],
      verify: (_) {
        expect(appState.dopNativeState.bootstrapAuthSettings, isNotNull);
        expect(isLogEventCalled, true);
        verify(() => mockDOPNativeRepo.getBootstrapInfo(
              token: fakeUniqueToken,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockDOPNativeRepo.getBootstrapAuthSettings(
              token: fakeUniqueToken,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<DOPNativeIntroductionCubit, DOPNativeIntroductionState>('Call getBootstrapInfo error',
        build: () => cubit,
        setUp: () async {
          when(
            () => mockDOPNativeRepo.getBootstrapInfo(
              token: any(named: 'token'),
              mockConfig: any(named: 'mockConfig'),
            ),
          ).thenAnswer((_) =>
              Future<DOPNativeBootstrapEntity>.value(DOPNativeBootstrapEntity.unserializable()));
        },
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeIntroductionCubit cubit) => cubit.getBootstrap(
              fakeUniqueToken,
              logEvent: () {
                isLogEventCalled = true;
              },
            ),
        expect: () => <dynamic>[
              isA<DOPNativeIntroductionLoading>(),
              isA<DOPNativeGetBootstrapFailed>().having(
                (DOPNativeGetBootstrapFailed p0) => p0.error,
                'verify error',
                isA<ErrorUIModel>(),
              )
            ],
        verify: (_) {
          expect(isLogEventCalled, false);
          expect(appState.dopNativeState.bootstrapAuthSettings, isNull);

          verify(() => mockDOPNativeRepo.getBootstrapInfo(
                token: fakeUniqueToken,
                mockConfig: any(named: 'mockConfig'),
              )).called(1);

          verifyNever(() => mockDOPNativeRepo.getBootstrapAuthSettings(
                token: fakeUniqueToken,
                mockConfig: any(named: 'mockConfig'),
              ));
        });

    blocTest<DOPNativeIntroductionCubit, DOPNativeIntroductionState>(
        'Call getBootstrapInfo success & getBootstrapAuthSettings error',
        build: () => cubit,
        setUp: () async {
          when(
            () => mockDOPNativeRepo.getBootstrapAuthSettings(
              token: any(named: 'token'),
              mockConfig: any(named: 'mockConfig'),
            ),
          ).thenAnswer((_) => Future<DOPNativeBootstrapAuthSettingsEntity>.value(
              DOPNativeBootstrapAuthSettingsEntity.unserializable()));
        },
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (DOPNativeIntroductionCubit cubit) => cubit.getBootstrap(
              fakeUniqueToken,
              logEvent: () {
                isLogEventCalled = true;
              },
            ),
        expect: () => <dynamic>[
              isA<DOPNativeIntroductionLoading>(),
              isA<DOPNativeGetBootstrapFailed>().having(
                (DOPNativeGetBootstrapFailed p0) => p0.error,
                'verify error',
                isA<ErrorUIModel>(),
              )
            ],
        verify: (_) {
          expect(isLogEventCalled, false);
          expect(appState.dopNativeState.bootstrapAuthSettings, isNull);

          verify(() => mockDOPNativeRepo.getBootstrapInfo(
                token: fakeUniqueToken,
                mockConfig: any(named: 'mockConfig'),
              )).called(1);

          verify(() => mockDOPNativeRepo.getBootstrapAuthSettings(
                token: fakeUniqueToken,
                mockConfig: any(named: 'mockConfig'),
              )).called(1);
        });
  });

  group('Test getBootstrap with id_card_auth', () {
    setUp(() async {
      final Map<String, dynamic> responseDataBootstrapInfo = await TestUtil.getResponseMock(
          'dop_native_get_bootstrap_after_inputting_phone_number.json');
      final DOPNativeBootstrapEntity bootstrapInfoEntity =
          DOPNativeBootstrapEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: responseDataBootstrapInfo,
      ));

      final Map<String, dynamic> responseDataBootstrapAuthSettings =
          await TestUtil.getResponseMock('dop_native_get_bootstrap_auth_id_card_auth.json');
      final DOPNativeBootstrapAuthSettingsEntity fakeBootstrapAuthSettingsEntity =
          DOPNativeBootstrapAuthSettingsEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: responseDataBootstrapAuthSettings,
      ));

      when(() => mockDOPNativeRepo.getBootstrapInfo(
            token: any(named: 'token'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) => Future<DOPNativeBootstrapEntity>.value(bootstrapInfoEntity));

      when(() => mockDOPNativeRepo.getBootstrapAuthSettings(
              token: any(named: 'token'), mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) =>
              Future<DOPNativeBootstrapAuthSettingsEntity>.value(fakeBootstrapAuthSettingsEntity));
    });

    tearDown(() {
      // clear after each test case
      appState.dopNativeState.bootstrapAuthSettings = null;
      appState.dopNativeState.dopNativeAccessToken = null;
    });

    blocTest<DOPNativeIntroductionCubit, DOPNativeIntroductionState>(
      'Call getBootstrapInfo success & getBootstrapAuthSettings success',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIntroductionCubit cubit) => cubit.getBootstrap(fakeUniqueToken),
      expect: () => <dynamic>[
        isA<DOPNativeIntroductionLoading>(),
        isA<DOPNativeBootstrapAuthSettingsLoaded>().having(
          (DOPNativeBootstrapAuthSettingsLoaded p0) => p0.bootstrapAuthSettingsEntity,
          'verify entity',
          isA<DOPNativeBootstrapAuthSettingsEntity>()
              .having(
                (DOPNativeBootstrapAuthSettingsEntity p0) => p0.authType,
                'verify authType',
                'id_card_auth',
              )
              .having(
                (DOPNativeBootstrapAuthSettingsEntity p0) => p0.accessToken,
                'verify accessToken',
                'fake_access_token',
              ),
        ),
      ],
      verify: (_) {
        expect(appState.dopNativeState.bootstrapAuthSettings, isNotNull);
        verify(() => mockDOPNativeRepo.getBootstrapInfo(
              token: fakeUniqueToken,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockDOPNativeRepo.getBootstrapAuthSettings(
              token: fakeUniqueToken,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        expect(appState.dopNativeState.dopNativeAccessToken, 'fake_access_token');
        verify(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader('fake_access_token'))
            .called(1);
      },
    );
  });

  group('Test getBootstrapInfo', () {
    setUp(() async {
      final Map<String, dynamic> responseDataBootstrapInfo = await TestUtil.getResponseMock(
          'dop_native_get_bootstrap_after_inputting_phone_number.json');
      final DOPNativeBootstrapEntity bootstrapInfoEntity =
          DOPNativeBootstrapEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: responseDataBootstrapInfo,
      ));

      final Map<String, dynamic> responseDataBootstrapAuthSettings =
          await TestUtil.getResponseMock('dop_native_bootstrap_auth_settings_success.json');
      final DOPNativeBootstrapAuthSettingsEntity fakeBootstrapAuthSettingsEntity =
          DOPNativeBootstrapAuthSettingsEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: responseDataBootstrapAuthSettings,
      ));

      when(() => mockDOPNativeRepo.getBootstrapInfo(
            token: any(named: 'token'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) => Future<DOPNativeBootstrapEntity>.value(bootstrapInfoEntity));

      when(() => mockDOPNativeRepo.getBootstrapAuthSettings(
              token: any(named: 'token'), mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) =>
              Future<DOPNativeBootstrapAuthSettingsEntity>.value(fakeBootstrapAuthSettingsEntity));
    });

    test('Call API success', () async {
      final DOPNativeBootstrapEntity result = await cubit.getBootstrapInfo(fakeUniqueToken);

      expect(result.uiVersion, 'v1.0');
    });

    test('Call API error', () async {
      when(
        () => mockDOPNativeRepo.getBootstrapInfo(
          token: any(named: 'token'),
          mockConfig: any(named: 'mockConfig'),
        ),
      ).thenAnswer(
          (_) => Future<DOPNativeBootstrapEntity>.value(DOPNativeBootstrapEntity.unserializable()));

      final DOPNativeBootstrapEntity result = await cubit.getBootstrapInfo(fakeUniqueToken);

      expect(result.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });
  });

  group('Test getBootstrapAuthSettings', () {
    test('Call API success', () async {
      const String filename = 'dop_native_bootstrap_auth_settings_success.json';
      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);
      final DOPNativeBootstrapAuthSettingsEntity entity =
          DOPNativeBootstrapAuthSettingsEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: responseData,
      ));
      when(() => mockDOPNativeRepo.getBootstrapAuthSettings(
            token: any(named: 'token'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) => Future<DOPNativeBootstrapAuthSettingsEntity>.value(entity));

      final DOPNativeBootstrapAuthSettingsEntity result =
          await cubit.getBootstrapAuthSettings(fakeUniqueToken);

      expect(result.authType, 'otp');
      expect(result.accessToken, 'fake_access_token');
    });

    test('Call API error', () async {
      when(
        () => mockDOPNativeRepo.getBootstrapAuthSettings(
          token: any(named: 'token'),
          mockConfig: any(named: 'mockConfig'),
        ),
      ).thenAnswer((_) => Future<DOPNativeBootstrapAuthSettingsEntity>.value(
          DOPNativeBootstrapAuthSettingsEntity.unserializable()));

      final DOPNativeBootstrapAuthSettingsEntity result =
          await cubit.getBootstrapAuthSettings(fakeUniqueToken);

      expect(result.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });
  });

  group('test initWithUniqueToken function', () {
    const String uniqueToken = 'unique_token';

    setUp(() {
      when(
        () => mockDOPNativeRepo.registrationCampaign(
          campaignCode: any(named: 'campaignCode'),
          leadSource: any(named: 'leadSource'),
          mockConfig: any(named: 'mockConfig'),
        ),
      ).thenAnswer((_) async => DOPNativeRegistrationCampaignEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_registration_campaign.json'),
          )));

      when(() => mockDOPNativeRepo.getBootstrapInfo(
              token: any(named: 'token'), mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) async => DOPNativeBootstrapEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.SUCCESS,
                response: <String, dynamic>{},
              )));

      when(() => mockDOPNativeRepo.getBootstrapAuthSettings(
              token: any(named: 'token'), mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) => Future<DOPNativeBootstrapAuthSettingsEntity>.value(
              DOPNativeBootstrapAuthSettingsEntity()));
    });

    tearDown(() {
      appState.dopNativeState.clear();
      reset(mockDOPNativeRepo);
    });

    blocTest<DOPNativeIntroductionCubit, DOPNativeIntroductionState>(
        'should set uniqueToken and call getBootstrap when uniqueToken is provided',
        setUp: () {
          when(() => appState.deepLinkSharedData).thenReturn(DeepLinkSharedData(
            deepLink: DeepLinkModel(
              params: <String, String>{},
              screenName: '',
            ),
          ));
        },
        build: () => cubit,
        act: (_) async {
          await cubit.initWithUniqueToken(uniqueToken);
        },
        wait: TestConstant.blocEmitStateDelayDuration,
        verify: (_) {
          expect(appState.dopNativeState.uniqueToken, uniqueToken);

          verify(() => mockDOPNativeRepo.registrationCampaign(
              campaignCode: any(named: 'campaignCode'),
              leadSource: any(named: 'leadSource'),
              mockConfig: any(named: 'mockConfig'))).called(1);

          expect(
            verify(() => mockDOPNativeRepo.getBootstrapInfo(
                token: captureAny(named: 'token'),
                mockConfig: captureAny(named: 'mockConfig'))).captured,
            <dynamic>[
              isA<String?>().having((String? p0) => p0, 'verify token', uniqueToken),
              isA<MockConfig>()
                  .having((MockConfig p0) => p0.enable, 'mockConfig with enable = false', false)
            ],
          );

          expect(
            verify(() => mockDOPNativeRepo.getBootstrapAuthSettings(
                token: captureAny(named: 'token'),
                mockConfig: captureAny(named: 'mockConfig'))).captured,
            <dynamic>[
              isA<String?>().having((String? p0) => p0, 'verify token', uniqueToken),
              isA<MockConfig>()
                  .having((MockConfig p0) => p0.enable, 'mockConfig with enable = false', false)
            ],
          );
        });
  });
}
