import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/nfc_reader/result/nfc_detection_result.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/nfc_reader/result/nfc_error.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Test fromMap', () {
    test('Empty json', () {
      final NFCDetectionResult result = NFCDetectionResult.fromMap(<dynamic, dynamic>{});

      expect(result.rawData, null);
      expect(result.personalData, null);
      expect(result.error, null);
      expect(result.errorMessage, null);
    });

    test('Full json', () {
      final NFCDetectionResult result = NFCDetectionResult.fromMap(<dynamic, dynamic>{
        'rawData': <String, String>{
          'key': 'value',
        },
        'personalData': <String, String>{
          'key': 'value',
        },
        'error': deviceNotSupportNFCAndroid,
      });

      final Map<dynamic, dynamic>? rawData = result.rawData;
      expect(rawData, isNotNull);

      final Map<dynamic, dynamic>? personalData = result.personalData;
      expect(personalData, isNotNull);

      expect(result.error, NFCError.deviceNotSupportNFC);
      expect(
        result.errorMessage,
        'SDK returns code=$deviceNotSupportNFCAndroid, please check this errorCode from NFC SDK documentation',
      );
    });
  });

  test('Test sdkException', () {
    final NFCDetectionResult result = NFCDetectionResult.sdkException('error_message');

    expect(result.rawData, null);
    expect(result.personalData, null);
    expect(result.error, NFCError.sdkException);
    expect(result.errorMessage, 'error_message');
  });

  test('Test nullResult', () {
    final NFCDetectionResult result = NFCDetectionResult.nullResult();

    expect(result.rawData, null);
    expect(result.personalData, null);
    expect(result.error, NFCError.nullResult);
    expect(result.errorMessage, 'SDK returns null result after scanning');
  });
}
