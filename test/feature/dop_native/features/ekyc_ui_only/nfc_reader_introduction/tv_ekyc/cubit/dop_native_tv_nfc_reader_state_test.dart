import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/nfc_reader_introduction/tv_ekyc/cubit/dop_native_tv_nfc_reader_cubit.dart';
import 'package:evoapp/feature/ekyc/model/ekyc_result_model.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeTvNFCReaderState', () {
    test('DOPNativeTvNFCReaderInitial should be an instance of DOPNativeTvNFCReaderState', () {
      final DOPNativeTvNFCReaderInitial state = DOPNativeTvNFCReaderInitial();
      expect(state, isA<DOPNativeTvNFCReaderState>());
    });

    test('DOPNativeTvNFCReaderLoading should be an instance of DOPNativeTvNFCReaderState', () {
      final DOPNativeTvNFCReaderLoading state = DOPNativeTvNFCReaderLoading();
      expect(state, isA<DOPNativeTvNFCReaderState>());
    });

    test('DOPNativeTvNFCReaderSuccess should be an instance of DOPNativeTvNFCReaderState', () {
      final DOPNativeTvNFCReaderSuccess state = DOPNativeTvNFCReaderSuccess();

      expect(state, isA<DOPNativeTvNFCReaderState>());
    });

    test('DOPNativeTvNFCReaderFailed holds correct error', () {
      final ErrorUIModel error = ErrorUIModel();

      final DOPNativeTvNFCReaderFailed state = DOPNativeTvNFCReaderFailed(error: error);

      expect(state.error, error);
    });

    test('DOPNativeTvNFCReaderCannotRead should be an instance of DOPNativeTvNFCReaderState', () {
      final ErrorUIModel error = ErrorUIModel();
      final DOPNativeTvNFCReaderUnableToStart state = DOPNativeTvNFCReaderUnableToStart(error);
      expect(state, isA<DOPNativeTvNFCReaderState>());
      expect(state.error, error);
    });

    test('DOPNativeTvNFCUnsupported should be a subclass of DOPNativeTvNFCReaderState', () {
      expect(DOPNativeTvNFCUnsupported(), isA<DOPNativeTvNFCReaderState>());
    });

    test('DOPNativeTvNFCFormDataLoading should be a subclass of DOPNativeTvNFCReaderState', () {
      expect(DOPNativeTvNFCFormDataLoading(), isA<DOPNativeTvNFCReaderState>());
    });

    test('DOPNativeValidateTvNFCDataSuccess should be a subclass of DOPNativeTvNFCReaderState', () {
      expect(DOPNativeValidateTvNFCDataSuccess(), isA<DOPNativeTvNFCReaderState>());
    });

    test('DOPNativeTvNFCFormDataFailed should be a subclass of DOPNativeTvNFCReaderState', () {
      final ErrorUIModel error = ErrorUIModel();

      final DOPNativeTvNFCFormDataFailed state = DOPNativeTvNFCFormDataFailed(error: error);

      expect(state.error, error);
    });

    test('DOPNativeTvNFCReaderError should be a subclass of DOPNativeTvNFCReaderState', () {
      final TVSDKFailReason failReason = TVSDKFailReason.sessionExpired;

      final DOPNativeTvNFCReaderError state = DOPNativeTvNFCReaderError(
        failReason: failReason,
      );

      expect(state, isA<DOPNativeTvNFCReaderState>());
      expect(state.failReason, failReason);
    });
  });
}
