import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/nfc_reader_introduction/unsupported/cubit/dop_native_nfc_device_unsupported_cubit.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeNFCDeviceUnsupportedState', () {
    test(
        'DOPNativeNFCDeviceUnsupportedInitial should be an instance of DOPNativeNFCDeviceUnsupportedState',
        () {
      final DOPNativeNFCDeviceUnsupportedInitial state = DOPNativeNFCDeviceUnsupportedInitial();
      expect(state, isA<DOPNativeNFCDeviceUnsupportedState>());
    });

    test(
        'DOPNativeNFCDeviceUnsupportedLoading should be an instance of DOPNativeNFCDeviceUnsupportedState',
        () {
      final DOPNativeNFCDeviceUnsupportedLoading state = DOPNativeNFCDeviceUnsupportedLoading();
      expect(state, isA<DOPNativeNFCDeviceUnsupportedState>());
    });

    test(
        'DOPNativeNFCDeviceUnsupportedOneLinkGenerated should be an instance of DOPNativeNFCDeviceUnsupportedState',
        () {
      const String link = 'sample_link';

      final DOPNativeNFCDeviceUnsupportedOneLinkGenerated state =
          DOPNativeNFCDeviceUnsupportedOneLinkGenerated(oneLink: link);

      expect(state, isA<DOPNativeNFCDeviceUnsupportedState>());
      expect(state.oneLink, link);
    });
  });
}
