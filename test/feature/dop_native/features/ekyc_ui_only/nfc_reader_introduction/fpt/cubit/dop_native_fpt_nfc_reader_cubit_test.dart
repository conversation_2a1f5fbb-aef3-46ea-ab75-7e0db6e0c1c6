import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/request/dop_native/dop_native_submit_nfc_request.dart';
import 'package:evoapp/data/response/dop_native/dop_native_application_form_data_entity.dart';
import 'package:evoapp/feature/dop_native/features/cif_confirm/mock_file/mock_dop_native_application_form_data_use_case.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/nfc_reader_introduction/constants/dop_native_nfc_entry_point.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/nfc_reader_introduction/fpt/cubit/dop_native_fpt_nfc_reader_cubit.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/nfc_reader_introduction/fpt/models/dop_native_fpt_nfc_reader_introduction_step.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/nfc_reader_introduction/models/dop_native_nfc_shared_model.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/fpt_sdk_bridge.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/nfc_reader/result/nfc_availability_type.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/nfc_reader/result/nfc_detection_result.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/nfc_reader/result/nfc_error.dart';
import 'package:evoapp/feature/dop_native/features/logging/metadata_define/dop_native_event_metadata.dart';
import 'package:evoapp/feature/dop_native/features/logging/screen_action_define/dop_native_special_action_event.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/feature/logging/evo_event_tracking_utils/evo_event_tracking_utils.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../../constant.dart';
import '../../../../../../../util/test_util.dart';

class MockDopNativeEkycUIOnlyRepo extends Mock implements DopNativeEkycUIOnlyRepo {}

class MockDOPUtilFunction extends Mock implements DOPUtilFunctions {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockFptSdkBridge extends Mock implements FptSdkBridge {}

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

class MockEvoEventTrackingUtils extends Mock implements EvoEventTrackingUtils {}

void main() {
  late DOPNativeFptNFCReaderCubit cubit;
  late DopNativeEkycUIOnlyRepo mockDopNativeEkycUIOnlyRepo;
  late AppState mockAppState;
  late DOPUtilFunctions mockDopUtilFunction;
  late FptSdkBridge mockFptSdkBridge;
  late DOPNativeRepo mockDOPRepo;
  late LoggingRepo mockLoggingRepo;
  late EvoEventTrackingUtils evoEventTrackingUtils;

  const String fakeIdCardNumber = '123456789012';

  const String fakeAccessToken = 'valid_token';

  const Map<dynamic, dynamic> fakeRawData = <dynamic, dynamic>{
    'key1': 'value1',
    'key2': 'value2',
  };

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(const DOPNativeSubmitNFCRequest(rawData: fakeRawData));

    getIt.registerSingleton<DopNativeEkycUIOnlyRepo>(MockDopNativeEkycUIOnlyRepo());
    mockDopNativeEkycUIOnlyRepo = getIt.get<DopNativeEkycUIOnlyRepo>();

    getIt.registerSingleton<EvoEventTrackingUtils>(MockEvoEventTrackingUtils());
    evoEventTrackingUtils = getIt.get<EvoEventTrackingUtils>();

    getIt.registerSingleton<AppState>(AppState());
    mockAppState = getIt.get<AppState>();

    getIt.registerSingleton<DOPUtilFunctions>(MockDOPUtilFunction());
    mockDopUtilFunction = getIt.get<DOPUtilFunctions>();

    getIt.registerSingleton<LoggingRepo>(MockLoggingRepo());
    mockLoggingRepo = getIt.get<LoggingRepo>();

    getIt.registerSingleton<DOPNativeRepo>(MockDOPNativeRepo());
    mockDOPRepo = getIt.get<DOPNativeRepo>();
  });

  setUp(() {
    mockFptSdkBridge = MockFptSdkBridge();
    cubit = DOPNativeFptNFCReaderCubit(
      dopNativeEkycUIOnlyRepo: mockDopNativeEkycUIOnlyRepo,
      appState: mockAppState,
      fptSdkBridge: mockFptSdkBridge,
      dopNativeRepo: mockDOPRepo,
    );
    when(() => mockFptSdkBridge.readNfc(idCardNumber: any(named: 'idCardNumber'))).thenAnswer(
      (_) async => NFCDetectionResult(),
    );
    when(() => mockFptSdkBridge.checkNfcSupport()).thenAnswer(
      (_) async => NfcAvailabilityType.available,
    );
    when(() => mockDopUtilFunction.isVietnameseCitizenIdCardAfter2016(any())).thenReturn(true);

    when(
      () => mockDopNativeEkycUIOnlyRepo.submitNFC(
        request: any(named: 'request'),
        mockConfig: any(named: 'mockConfig'),
      ),
    ).thenAnswer(
      (_) async => BaseEntity.fromBaseResponse(
        BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: await TestUtil.getResponseMock('dop_native_submit_nfc_success.json'),
        ),
      ),
    );

    when(
      () => mockLoggingRepo.logErrorEvent(
        errorType: any(named: 'errorType'),
        args: any(named: 'args'),
      ),
    ).thenAnswer((_) => Future<void>.value());
  });

  tearDown(() {
    reset(mockDopNativeEkycUIOnlyRepo);
    reset(mockDopUtilFunction);
    reset(mockFptSdkBridge);
    reset(evoEventTrackingUtils);
    reset(mockDOPRepo);
    reset(mockLoggingRepo);
  });

  tearDownAll(() {
    getIt.reset();
  });

  blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
    'Default state',
    build: () => cubit,
    verify: (DOPNativeFptNFCReaderCubit cubit) {
      expect(cubit.state, isA<DOPNativeFptNFCReaderInitialState>());
    },
  );

  blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
    'initial dopNative',
    build: () => cubit,
    setUp: () async {
      final String filename = MockDOPNativeApplicationFormData.getApplicationFormDataSuccess.value;
      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);
      final BaseResponse response = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: responseData,
      );

      when(
        () => mockDOPRepo.getApplicationFormData(mockConfig: any(named: 'mockConfig')),
      ).thenAnswer(
        (_) async => DOPNativeApplicationFormDataEntity.fromBaseResponse(response),
      );
    },
    act: (DOPNativeFptNFCReaderCubit cubit) => cubit.initial(NFCEntryPoint.dopNative),
    wait: TestConstant.blocEmitStateDelayDuration,
    expect: () => <dynamic>[
      isA<DOPNativeFptNFCReaderLoadingState>(),
      isA<DOPNativeFptNFCReaderInitialSuccess>(),
    ],
    verify: (DOPNativeFptNFCReaderCubit cubit) {
      // call API to get form data
      verify(
        () => mockDOPRepo.getApplicationFormData(mockConfig: any(named: 'mockConfig')),
      ).called(1);
    },
  );

  blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
    'initial dopWeb',
    build: () => cubit,
    setUp: () {
      mockAppState.dopNativeState.nfcSharedModel = DOPNativeNFCSharedModel(
        nationalIdNumber: fakeIdCardNumber,
        accessToken: fakeAccessToken,
      );
    },
    act: (DOPNativeFptNFCReaderCubit cubit) => cubit.initial(NFCEntryPoint.dopWebView),
    wait: TestConstant.blocEmitStateDelayDuration,
    expect: () => <dynamic>[
      isA<DOPNativeFptNFCReaderInitialSuccess>(),
    ],
    verify: (DOPNativeFptNFCReaderCubit cubit) {
      verify(() => mockDopUtilFunction.setDOPNativeAccessTokenHeader(fakeAccessToken)).called(1);
      expect(cubit.idCardNumber, fakeIdCardNumber);
    },
  );

  group('verify submitNFC() method', () {
    blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      'submitNFC with success state',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (_) => cubit.submitNFC(rawData: fakeRawData),
      expect: () => <dynamic>[
        isA<DOPNativeFptNFCReaderSubmitNFCDataSuccessState>(),
      ],
      verify: (_) {
        expect(
          verify(
            () => mockDopNativeEkycUIOnlyRepo.submitNFC(
              request: captureAny(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            ),
          ).captured.single,
          isA<DOPNativeSubmitNFCRequest>()
              .having(
                (DOPNativeSubmitNFCRequest request) => request.rawData,
                'verify rawData',
                fakeRawData,
              )
              .having(
                (DOPNativeSubmitNFCRequest request) => request.vendor,
                'verify vendor',
                DOPNativeSubmitNFCVendor.fpt,
              ),
        );
        verify(
          () => evoEventTrackingUtils.sendEvoSpecialEvent(
            eventActionId: DOPNativeSpecialActionEvent.nfcSubmitResult,
            metaData: <String, dynamic>{
              DOPNativeEventMetadataKey.stepStatus: DOPNativeEventMetadataValue.succeed,
            },
          ),
        );
      },
    );

    blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      'submitNFC with fail state',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(
          () => mockDopNativeEkycUIOnlyRepo.submitNFC(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer(
          (_) async => BaseEntity.fromBaseResponse(
            BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: await TestUtil.getResponseMock('dop_native_submit_nfc_fail.json'),
            ),
          ),
        );
      },
      act: (_) => cubit.submitNFC(rawData: fakeRawData),
      expect: () => <dynamic>[
        isA<DOPNativeFptNFCReaderSubmitNFCDataFailedState>(),
      ],
      verify: (_) {
        expect(
          verify(
            () => mockDopNativeEkycUIOnlyRepo.submitNFC(
              request: captureAny(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            ),
          ).captured.single,
          isA<DOPNativeSubmitNFCRequest>()
              .having(
                (DOPNativeSubmitNFCRequest request) => request.rawData,
                'verify rawData',
                fakeRawData,
              )
              .having(
                (DOPNativeSubmitNFCRequest request) => request.vendor,
                'verify vendor',
                DOPNativeSubmitNFCVendor.fpt,
              ),
        );
        evoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: DOPNativeSpecialActionEvent.nfcSubmitResult,
          metaData: <String, dynamic>{
            DOPNativeEventMetadataKey.stepStatus: DOPNativeEventMetadataValue.failed,
            DOPNativeEventMetadataKey.errorCode: 400,
            DOPNativeEventMetadataKey.verdict: 'failure',
            DOPNativeEventMetadataKey.message: 'verify nfc failed',
          },
        );
      },
    );
  });

  group('verify retrieveNFCSharedModel() method', () {
    blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      'do not emit state when parameters are valid',
      build: () => cubit,
      setUp: () {
        mockAppState.dopNativeState.nfcSharedModel = DOPNativeNFCSharedModel(
          nationalIdNumber: fakeIdCardNumber,
          accessToken: fakeAccessToken,
        );
      },
      act: (_) => cubit.retrieveNFCSharedModel(),
      expect: () => <dynamic>[
        isA<DOPNativeFptNFCReaderInitialSuccess>(),
      ],
      verify: (_) {
        expect(cubit.idCardNumber, fakeIdCardNumber);
      },
    );

    blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      'when invalid nationalIdNumber emits DOPNativeNFCReaderUnableToStart',
      build: () => cubit,
      setUp: () {
        mockAppState.dopNativeState.nfcSharedModel = DOPNativeNFCSharedModel(
          nationalIdNumber: '',
          accessToken: fakeAccessToken,
        );
      },
      act: (_) => cubit.retrieveNFCSharedModel(),
      expect: () => <dynamic>[
        isA<DOPNativeFptNFCReaderInitialDataInvalidState>().having(
          (DOPNativeFptNFCReaderInitialDataInvalidState p0) => p0.errorUIModel.verdict,
          'verify local verdict',
          'NFCReader: invalid nationalIdNumber: ${''}',
        ),
      ],
      verify: (_) {
        expect(cubit.idCardNumber, null);
      },
    );
  });

  group('verify checkNFCSharedModelIsValid() method', () {
    test('returns false when idCardNumber is not valid', () {
      when(() => mockDopUtilFunction.isVietnameseCitizenIdCardAfter2016(any())).thenReturn(false);

      final bool result = cubit.checkNFCSharedModelIsValid(
        idCardNumber: fakeIdCardNumber,
      );

      expect(result, false);
    });

    test('returns true when all fields are valid', () {
      final bool result = cubit.checkNFCSharedModelIsValid(
        idCardNumber: fakeIdCardNumber,
      );
      expect(result, true);
    });

    test('returns true when entryPoint is NFCEntryPoint.dopWebView and access token is null', () {
      final bool result = cubit.checkNFCSharedModelIsValid(
        idCardNumber: fakeIdCardNumber,
      );
      expect(result, true);
    });
  });

  group('checkCCCDIsValid', () {
    test('should return false when idCard is null', () {
      final bool result = cubit.checkCCCDIsValid(null);

      expect(result, false);
      verifyNever(() => mockDopUtilFunction.isVietnameseCitizenIdCardAfter2016(any()));
    });

    test('should return false when idCard is empty', () {
      final bool result = cubit.checkCCCDIsValid('');

      expect(result, false);
      verifyNever(() => mockDopUtilFunction.isVietnameseCitizenIdCardAfter2016(any()));
    });

    test('should return true when idCard is a valid citizen ID card after 2016', () {
      const String idCardNumber = '123456789012';
      final bool result = cubit.checkCCCDIsValid(idCardNumber);

      expect(result, true);
      verify(() => mockDopUtilFunction.isVietnameseCitizenIdCardAfter2016(idCardNumber)).called(1);
    });

    test('should return false when idCard is not a valid citizen ID card after 2016', () {
      const String idCardNumber = '987654321098';
      when(() => mockDopUtilFunction.isVietnameseCitizenIdCardAfter2016(any())).thenReturn(false);
      final bool result = cubit.checkCCCDIsValid(idCardNumber);

      expect(result, false);
      verify(() => mockDopUtilFunction.isVietnameseCitizenIdCardAfter2016(idCardNumber)).called(1);
    });
  });

  group('DOPNativeNFCReaderCubit verify checkNfcSupportAndReadIfAvailable', () {
    blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      'verify state when nfc supported and start read',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeFptNFCReaderCubit cubit) => cubit.checkNfcSupportAndStartReadIfAvailable(),
      setUp: () {
        cubit.idCardNumber = fakeIdCardNumber;
        when(() => mockFptSdkBridge.checkNfcSupport()).thenAnswer(
          (_) async => NfcAvailabilityType.available,
        );
        when(() => mockFptSdkBridge.readNfc(idCardNumber: fakeIdCardNumber)).thenAnswer(
          (_) async => NFCDetectionResult(),
        );
      },
      verify: (_) {
        verify(() => mockFptSdkBridge.checkNfcSupport()).called(1);
        verify(() => mockFptSdkBridge.readNfc(idCardNumber: fakeIdCardNumber)).called(1);
        verify(
          () => mockDopNativeEkycUIOnlyRepo.submitNFC(
            request: captureAny(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).called(1);
      },
      expect: () => <TypeMatcher<DOPNativeFptNFCReaderState>>[
        isA<DOPNativeFptNFCReaderLoadingState>(),
        isA<DOPNativeFptNFCReaderSubmitNFCDataSuccessState>(),
      ],
    );

    blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      'verify state when nfc unsupported',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeFptNFCReaderCubit cubit) => cubit.checkNfcSupportAndStartReadIfAvailable(),
      setUp: () {
        when(() => mockFptSdkBridge.checkNfcSupport()).thenAnswer(
          (_) async => NfcAvailabilityType.unsupported,
        );
      },
      verify: (_) {
        verify(() => mockFptSdkBridge.checkNfcSupport()).called(1);
      },
      expect: () => <TypeMatcher<DOPNativeFptNFCReaderState>>[
        isA<DOPNativeFptNFCReaderLoadingState>(),
        isA<DOPNativeFptNFCReaderUnsupportedState>(),
      ],
    );

    blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      'verify state when nfc disabled',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeFptNFCReaderCubit cubit) => cubit.checkNfcSupportAndStartReadIfAvailable(),
      setUp: () {
        when(() => mockFptSdkBridge.checkNfcSupport()).thenAnswer(
          (_) async => NfcAvailabilityType.disabled,
        );
      },
      verify: (_) {
        verify(() => mockFptSdkBridge.checkNfcSupport()).called(1);
      },
      expect: () => <TypeMatcher<DOPNativeFptNFCReaderState>>[
        isA<DOPNativeFptNFCReaderLoadingState>(),
        isA<DOPNativeFptNFCReaderDisabledState>(),
      ],
    );
  });

  group('test getDOPNativeFormData', () {
    blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      'Get form data NFC success ',
      setUp: () async {
        final String filename =
            MockDOPNativeApplicationFormData.getApplicationFormDataSuccess.value;
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);
        final BaseResponse response = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: responseData,
        );

        when(
          () => mockDOPRepo.getApplicationFormData(mockConfig: any(named: 'mockConfig')),
        ).thenAnswer(
          (_) async => DOPNativeApplicationFormDataEntity.fromBaseResponse(response),
        );
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeFptNFCReaderCubit cubit) async {
        await cubit.getDOPNativeFormData();
      },
      expect: () => <dynamic>[
        isA<DOPNativeFptNFCReaderLoadingState>(),
        isA<DOPNativeFptNFCReaderInitialSuccess>(),
      ],
      verify: (_) {
        verify(
          () => mockDOPRepo.getApplicationFormData(mockConfig: any(named: 'mockConfig')),
        ).called(1);
      },
    );

    blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      'Get form data NFC success failed',
      setUp: () async {
        final String filename =
            MockDOPNativeApplicationFormData.getApplicationFormDataSuccess.value;
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);
        final BaseResponse response = BaseResponse(
          statusCode: CommonHttpClient.INVALID_FORMAT,
          response: responseData,
        );

        when(
          () => mockDOPRepo.getApplicationFormData(mockConfig: any(named: 'mockConfig')),
        ).thenAnswer(
          (_) async => DOPNativeApplicationFormDataEntity.fromBaseResponse(response),
        );
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeFptNFCReaderCubit cubit) async {
        await cubit.getDOPNativeFormData();
      },
      expect: () => <dynamic>[
        isA<DOPNativeFptNFCReaderLoadingState>(),
        isA<DOPNativeFptNFCReaderInitialRetrieveFormDataFailed>(),
      ],
      verify: (_) {
        verify(
          () => mockDOPRepo.getApplicationFormData(mockConfig: any(named: 'mockConfig')),
        ).called(1);
      },
    );

    blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      'Get form data NFC success but data invalid (personalInfo is null)',
      setUp: () async {
        final String filename =
            MockDOPNativeApplicationFormData.getApplicationFormDataNFCInvalidData.value;
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);
        final BaseResponse response = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: responseData,
        );

        when(
          () => mockDOPRepo.getApplicationFormData(mockConfig: any(named: 'mockConfig')),
        ).thenAnswer(
          (_) async => DOPNativeApplicationFormDataEntity.fromBaseResponse(response),
        );
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeFptNFCReaderCubit cubit) async {
        await cubit.getDOPNativeFormData();
      },
      expect: () => <dynamic>[
        isA<DOPNativeFptNFCReaderLoadingState>(),
        isA<DOPNativeFptNFCReaderInitialDataInvalidState>(),
      ],
      verify: (_) {
        verify(
          () => mockDOPRepo.getApplicationFormData(mockConfig: any(named: 'mockConfig')),
        ).called(1);
      },
    );
  });

  group('verify receiveAccessTokenFromDOPWebView', () {
    test('returns false when accessToken is null', () {
      mockAppState.dopNativeState.nfcSharedModel = DOPNativeNFCSharedModel(
        nationalIdNumber: fakeIdCardNumber,
      );

      expect(cubit.receiveAccessTokenFromDOPWebView(), false);
      expect(cubit.state, isA<DOPNativeFptNFCReaderInitialDataInvalidState>());
      verifyNever(() => mockDopUtilFunction.setDOPNativeAccessTokenHeader(fakeAccessToken));
    });

    test('returns false when accessToken is empty', () {
      mockAppState.dopNativeState.nfcSharedModel = DOPNativeNFCSharedModel(
        nationalIdNumber: fakeIdCardNumber,
        accessToken: '',
      );

      expect(cubit.receiveAccessTokenFromDOPWebView(), false);
      expect(cubit.state, isA<DOPNativeFptNFCReaderInitialDataInvalidState>());
      verifyNever(() => mockDopUtilFunction.setDOPNativeAccessTokenHeader(fakeAccessToken));
    });

    test('returns true and saves accessToken when accessToken is valid', () {
      mockAppState.dopNativeState.nfcSharedModel = DOPNativeNFCSharedModel(
        nationalIdNumber: fakeIdCardNumber,
        accessToken: fakeAccessToken,
      );

      expect(cubit.receiveAccessTokenFromDOPWebView(), true);
      verify(() => mockDopUtilFunction.setDOPNativeAccessTokenHeader(fakeAccessToken)).called(1);
    });
  });

  group('test changeStep', () {
    blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      'changeStep should emit DOPNativeFptNFCReaderStepChanged',
      build: () => cubit,
      act: (DOPNativeFptNFCReaderCubit cubit) async {
        cubit.changeStep(DopNativeFptNfcReaderIntroductionStep.video);
      },
      expect: () => <dynamic>[
        isA<DOPNativeFptNFCReaderStepChangedState>().having(
          (DOPNativeFptNFCReaderStepChangedState state) => state.step,
          'state.step should be video',
          DopNativeFptNfcReaderIntroductionStep.video,
        ),
      ],
      verify: (DOPNativeFptNFCReaderCubit cubit) {
        cubit.currentStep == DopNativeFptNfcReaderIntroductionStep.video;
      },
    );

    blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      'changeStep should emit DOPNativeFptNFCReaderStepChanged',
      build: () => cubit,
      act: (DOPNativeFptNFCReaderCubit cubit) async {
        cubit.changeStep(DopNativeFptNfcReaderIntroductionStep.animation);
      },
      expect: () => <dynamic>[
        isA<DOPNativeFptNFCReaderStepChangedState>().having(
          (DOPNativeFptNFCReaderStepChangedState state) => state.step,
          'state.step should be animation',
          DopNativeFptNfcReaderIntroductionStep.animation,
        ),
      ],
      verify: (DOPNativeFptNFCReaderCubit cubit) {
        cubit.currentStep == DopNativeFptNfcReaderIntroductionStep.animation;
      },
    );
  });

  group('test clearNFCSharedModel', () {
    blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      'clearNFCSharedModel should clear nfcSharedModel in app state',
      build: () => cubit,
      setUp: () {
        mockAppState.dopNativeState.nfcSharedModel = DOPNativeNFCSharedModel(
          nationalIdNumber: fakeIdCardNumber,
          accessToken: fakeAccessToken,
        );
      },
      act: (DOPNativeFptNFCReaderCubit cubit) {
        cubit.clearNFCSharedModel();
      },
      verify: (DOPNativeFptNFCReaderCubit cubit) {
        mockAppState.dopNativeState.nfcSharedModel = null;
      },
    );
  });

  group('handleReadNFCResult', () {
    blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      'emits error state when nfcError is not null',
      build: () => cubit,
      act: (DOPNativeFptNFCReaderCubit cubit) {
        final NFCDetectionResult detectionResult = NFCDetectionResult(
          error: NFCError.cannotOpenNFC,
          errorMessage: 'test error',
        );
        cubit.handleReadNFCResult(detectionResult);
      },
      expect: () => <TypeMatcher<DOPNativeFptNFCReaderReadNFCErrorState>>[
        isA<DOPNativeFptNFCReaderReadNFCErrorState>()
            .having(
              (DOPNativeFptNFCReaderReadNFCErrorState s) => s.errorMessage,
              'errorMessage',
              'test error',
            )
            .having(
              (DOPNativeFptNFCReaderReadNFCErrorState s) => s.nfcError,
              'errorMessage',
              NFCError.cannotOpenNFC,
            ),
      ],
    );

    blocTest<DOPNativeFptNFCReaderCubit, DOPNativeFptNFCReaderState>(
      'calls submitNFC when nfcError is null',
      build: () => cubit,
      act: (DOPNativeFptNFCReaderCubit cubit) {
        final Map<String, String> testRawData = <String, String>{'key': 'value'};
        final NFCDetectionResult detectionResult = NFCDetectionResult(
          rawData: testRawData,
        );
        cubit.handleReadNFCResult(detectionResult);
      },
      expect: () => <TypeMatcher<DOPNativeFptNFCReaderSubmitNFCDataSuccessState>>[
        isA<DOPNativeFptNFCReaderSubmitNFCDataSuccessState>(),
      ],
      verify: (_) {},
    );
  });

  group('startReadNFC', () {
    test('calls readNfc and passes result to handleReadNFCResult when idCardNumber is not null',
        () async {
      // Set idCardNumber so that the function proceeds.
      cubit.idCardNumber = fakeIdCardNumber;

      await cubit.startReadNFC();

      // Verify that fptSdkBridge.readNfc was called with the correct id.
      verify(
        () => mockDopNativeEkycUIOnlyRepo.submitNFC(
          request: captureAny(named: 'request'),
          mockConfig: any(named: 'mockConfig'),
        ),
      ).called(1);
    });
  });

  group(r'logEventSubmitNFCSucceed', () {
    test('sends expected event with succeed metaData', () {
      // Act: call the function.
      cubit.logEventSubmitNFCSucceed();

      // Assert: verify the tracking method is called with expected arguments.
      verify(
        () => evoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: DOPNativeSpecialActionEvent.nfcSubmitResult,
          metaData: <String, dynamic>{
            DOPNativeEventMetadataKey.stepStatus: DOPNativeEventMetadataValue.succeed,
          },
        ),
      ).called(1);
    });
  });

  group('logEventSubmitNFCFailed', () {
    test('sends expected event with failed metaData using the BaseEntity', () {
      // Arrange: create a dummy BaseEntity.
      final BaseEntity fakeResponse = BaseEntity(
        statusCode: CommonHttpClient.BAD_REQUEST,
        verdict: 'fake_verdict',
        message: 'fake_message',
      );

      // Act: call the method with dummyEntity.
      cubit.logEventSubmitNFCFailed(fakeResponse);

      // Assert: verify that the tracking method is called with expected metaData.
      verify(
        () => evoEventTrackingUtils.sendEvoSpecialEvent(
          eventActionId: DOPNativeSpecialActionEvent.nfcSubmitResult,
          metaData: <String, dynamic>{
            DOPNativeEventMetadataKey.stepStatus: DOPNativeEventMetadataValue.failed,
            DOPNativeEventMetadataKey.errorCode: fakeResponse.statusCode,
            DOPNativeEventMetadataKey.verdict: fakeResponse.verdict,
            DOPNativeEventMetadataKey.message: fakeResponse.message,
          },
        ),
      ).called(1);
    });
  });

  group('logParamError', () {
    test('calls LoggingRepo.logErrorEvent with correct parameters', () {
      // Arrange: define test paramName and paramValue.
      const String testParamName = 'testParam';
      const String testParamValue = '12345';

      // Act: call the function.
      cubit.logParamError(testParamName, testParamValue);

      // Assert: verify that LoggingRepo.logErrorEvent is called with expected args.
      expect(
        verify(
          () => mockLoggingRepo.logErrorEvent(
            errorType: 'dop_nfc',
            args: captureAny(named: 'args'),
          ),
        ).captured.single,
        isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> map) => map['step'],
              'verify step',
              'start',
            )
            .having(
              (Map<String, dynamic> map) => map['error'],
              'verify error',
              'invalid_params',
            )
            .having(
              (Map<String, dynamic> map) => map['param_name'],
              'verify param_name',
              testParamName,
            )
            .having(
              (Map<String, dynamic> map) => map['param_value'],
              'verify param_value',
              testParamValue,
            ),
      );
    });

    test('calls LoggingRepo.logErrorEvent using "null" when paramValue is null', () {
      // Arrange: define test paramName with null paramValue.
      const String testParamName = 'nullParam';

      // Act: call the function.
      cubit.logParamError(testParamName, null);

      // Assert: verify that LoggingRepo.logErrorEvent gets a string "null" for param_value.
      expect(
        verify(
          () => mockLoggingRepo.logErrorEvent(
            errorType: 'dop_nfc',
            args: captureAny(named: 'args'),
          ),
        ).captured.single,
        isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> map) => map['step'],
              'verify step',
              'start',
            )
            .having(
              (Map<String, dynamic> map) => map['error'],
              'verify error',
              'invalid_params',
            )
            .having(
              (Map<String, dynamic> map) => map['param_name'],
              'verify param_name',
              testParamName,
            )
            .having(
              (Map<String, dynamic> map) => map['param_value'],
              'verify param_value',
              'null',
            ),
      );
    });
  });
}
