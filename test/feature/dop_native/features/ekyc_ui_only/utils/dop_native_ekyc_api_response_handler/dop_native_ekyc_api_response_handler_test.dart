import 'package:evoapp/data/response/dop_native/dop_native_bootstrap_auth_settings_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_check_id_card_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_ekyc_status_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_upload_id_card_entity.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/ui_model/ekyc_error_ui_model.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/utils/dop_native_ekyc_api_response_handler/dop_native_ekyc_api_response_handler.dart';
import 'package:evoapp/feature/dop_native/models/dop_native_state.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

class MockAppState extends Mock implements AppState {}

void main() {
  late AppState mockAppState;
  late DOPUtilFunctions mockDOPUtilFunctions;

  const String mockUserMessage = 'mock-user-message';
  final EkycCommonApiResponsesHandler ekycCommonApiResponsesHandler =
      EkycCommonApiResponsesHandler();

  setUpAll(() {
    getIt.registerSingleton<AppState>(MockAppState());
    mockAppState = getIt<AppState>();

    getIt.registerSingleton<DOPUtilFunctions>(MockDOPUtilFunctions());
    mockDOPUtilFunctions = getIt<DOPUtilFunctions>();
  });

  setUp(() {
    when(() => mockAppState.dopNativeState).thenReturn(DOPNativeState());
  });

  tearDown(() {
    reset(mockAppState);
    reset(mockDOPUtilFunctions);
  });

  tearDownAll(() {
    getIt.reset();
  });

  getBaseResponse({
    String verdict = BaseEntity.verdictSuccess,
    int statusCode = CommonHttpClient.SUCCESS,
  }) {
    return BaseResponse(statusCode: statusCode, response: <String, dynamic>{
      'verdict': verdict,
      'data': <String, dynamic>{
        'user_message': mockUserMessage,
      }
    });
  }

  group('getStatusError', () {
    test('should return null when verdict is success', () {
      final DOPNativeEkycStatusEntity entity =
          DOPNativeEkycStatusEntity.fromBaseResponse(getBaseResponse());

      final EkycErrorUIModel? result = ekycCommonApiResponsesHandler.getStatusError(entity);
      expect(result, null);
    });

    test('should return null when verdict is success and data.status is failure', () {
      final BaseResponse baseResponse =
          BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
        'verdict': BaseEntity.verdictSuccess,
        'data': <String, dynamic>{
          'user_message': mockUserMessage,
          'status': 'failure',
        }
      });

      final DOPNativeEkycStatusEntity entity =
          DOPNativeEkycStatusEntity.fromBaseResponse(baseResponse);

      final EkycErrorUIModel? result = ekycCommonApiResponsesHandler.getStatusError(entity);

      expect(result, isA<EkycErrorUIModel>());
      expect(result?.code, EkycErrorCode.otherEkycError);
      expect(result?.message, mockUserMessage);
    });

    test('should return commonError when statusCode == 400 and verdict != success', () {
      final DOPNativeEkycStatusEntity entity = DOPNativeEkycStatusEntity.fromBaseResponse(
          getBaseResponse(verdict: 'mock-verdict', statusCode: CommonHttpClient.BAD_REQUEST));

      final EkycErrorUIModel? result = ekycCommonApiResponsesHandler.getStatusError(entity);
      expect(result, isNot(null));
      expect(result?.code, EkycErrorCode.commonError);
      expect(result?.message, mockUserMessage);
    });

    test('should return http code when statusCode != 400 and verdict != success', () {
      final DOPNativeEkycStatusEntity entity =
          DOPNativeEkycStatusEntity.fromBaseResponse(getBaseResponse(
        verdict: 'mock-verdict',
        statusCode: CommonHttpClient.UNKNOWN_ERRORS,
      ));

      final EkycErrorUIModel? result = ekycCommonApiResponsesHandler.getStatusError(entity);
      expect(result, isNot(null));
      expect(result?.code, EkycErrorCode.commonError);
      expect(result?.message, mockUserMessage);
    });
  });

  group('getUploadImageError', () {
    test('should return null when verdict is success', () {
      final DOPNativeUploadIdCardEntity entity =
          DOPNativeUploadIdCardEntity.fromBaseResponse(getBaseResponse());

      final EkycErrorUIModel? result = ekycCommonApiResponsesHandler.getUploadImageError(entity);
      expect(result, null);
    });

    test('should return limit_exceed error', () {
      final DOPNativeUploadIdCardEntity entity =
          DOPNativeUploadIdCardEntity.fromBaseResponse(getBaseResponse(
        verdict: DOPNativeUploadIdCardEntity.verdictLimitExceed,
      ));

      final EkycErrorUIModel? result = ekycCommonApiResponsesHandler.getUploadImageError(entity);
      expect(result, isNot(null));
      expect(result?.code, EkycErrorCode.limitExceed);
      expect(result?.message, mockUserMessage);
    });

    test('should return card_type_not_supported ', () {
      final DOPNativeUploadIdCardEntity entity =
          DOPNativeUploadIdCardEntity.fromBaseResponse(getBaseResponse(
        verdict: DOPNativeUploadIdCardEntity.verdictCardTypeNotSupportedCode,
      ));

      final EkycErrorUIModel? result = ekycCommonApiResponsesHandler.getUploadImageError(entity);
      expect(result, isNot(null));
      expect(result?.code, EkycErrorCode.cardTypeNotSupported);
      expect(result?.message, mockUserMessage);
    });

    test('should return qr_code_required ', () {
      final DOPNativeUploadIdCardEntity entity =
          DOPNativeUploadIdCardEntity.fromBaseResponse(getBaseResponse(
        verdict: DOPNativeUploadIdCardEntity.verdictQrCodeRequired,
      ));

      final EkycErrorUIModel? result = ekycCommonApiResponsesHandler.getUploadImageError(entity);
      expect(result, isNot(null));
      expect(result?.code, EkycErrorCode.qrCodeRequired);
      expect(result?.message, mockUserMessage);
    });

    test('should return other_ekyc_error when statusCode == 400 and verdict == failure', () {
      final DOPNativeUploadIdCardEntity entity =
          DOPNativeUploadIdCardEntity.fromBaseResponse(getBaseResponse(
        verdict: DOPNativeUploadIdCardEntity.verdictFailure,
        statusCode: CommonHttpClient.BAD_REQUEST,
      ));

      final EkycErrorUIModel? result = ekycCommonApiResponsesHandler.getUploadImageError(entity);
      expect(result, isNot(null));
      expect(result?.code, EkycErrorCode.otherEkycError);
      expect(result?.message, mockUserMessage);
    });

    test(
        'should return http code when statusCode != 400 and verdict != success and verdict != failure',
        () {
      final DOPNativeUploadIdCardEntity entity =
          DOPNativeUploadIdCardEntity.fromBaseResponse(getBaseResponse(
        verdict: 'mock-verdict',
        statusCode: CommonHttpClient.UNKNOWN_ERRORS,
      ));

      final EkycErrorUIModel? result = ekycCommonApiResponsesHandler.getUploadImageError(entity);
      expect(result, isNot(null));
      expect(result?.code, EkycErrorCode.commonError);
      expect(result?.message, mockUserMessage);
    });
  });

  group('Test getCheckIdCardError', () {
    test('getCheckIdCardError should return null for success verdict', () {
      final DOPNativeCheckIDCardEntity entity = DOPNativeCheckIDCardEntity.fromBaseResponse(
        BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{'job_id': '1234'},
            'verdict': 'success'
          },
        ),
      );
      final EkycErrorUIModel? error = ekycCommonApiResponsesHandler.getCheckIdCardError(entity);
      expect(error, isNull);
    });

    test('getCheckIdCardError should return EkycErrorUIModel for error verdict', () {
      final DOPNativeCheckIDCardEntity entity = DOPNativeCheckIDCardEntity.fromBaseResponse(
        BaseResponse(
          statusCode: CommonHttpClient.UNKNOWN_ERRORS,
          response: <String, dynamic>{
            'data': <String, dynamic>{'user_message': 'Some error'},
            'verdict': 'someOtherVerdict',
          },
        ),
      );
      final EkycErrorUIModel? error = ekycCommonApiResponsesHandler.getCheckIdCardError(entity);
      expect(error, isA<EkycErrorUIModel>());
      expect(error?.code, EkycErrorCode.commonError);
      expect(error?.message, 'Some error');
    });
  });

  group('Test saveAccessTokenIfNeeded', () {
    test('should save access token when auth type is ID Card and access token is not null', () {
      final String oldAccessToken = 'old_access_token';
      final String newAccessToken = 'new_access_token';
      final DOPNativeState state = DOPNativeState(
        bootstrapAuthSettings: DOPNativeBootstrapAuthSettingsEntity(
          authType: 'id_card_auth',
        ),
        dopNativeAccessToken: oldAccessToken,
      );
      // Arrange
      when(() => mockAppState.dopNativeState).thenReturn(state);

      when(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(newAccessToken)).thenAnswer(
        (_) {},
      );

      final DOPNativeEkycStatusEntity entity = DOPNativeEkycStatusEntity.fromBaseResponse(
        BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'status': DOPNativeEkycStatusEntity.statusSuccess,
              'access_token': newAccessToken,
            }
          },
        ),
      );

      // Act
      ekycCommonApiResponsesHandler.saveAccessTokenIfNeeded(entity);

      // Assert
      expect(mockAppState.dopNativeState.dopNativeAccessToken, newAccessToken);
      verify(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(newAccessToken)).called(1);
    });

    test('should not save access token when auth type is not ID Card', () {
      final String oldAccessToken = 'old_access_token';
      final String newAccessToken = 'new_access_token';
      final DOPNativeState state = DOPNativeState(
        bootstrapAuthSettings: DOPNativeBootstrapAuthSettingsEntity(
          authType: 'otp',
        ),
        dopNativeAccessToken: oldAccessToken,
      );
      // Arrange
      when(() => mockAppState.dopNativeState).thenReturn(state);

      when(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(newAccessToken)).thenAnswer(
        (_) {},
      );

      final DOPNativeEkycStatusEntity entity = DOPNativeEkycStatusEntity.fromBaseResponse(
        BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'status': DOPNativeEkycStatusEntity.statusSuccess,
              'access_token': newAccessToken,
            }
          },
        ),
      );

      // Act
      ekycCommonApiResponsesHandler.saveAccessTokenIfNeeded(entity);

      // Assert
      expect(mockAppState.dopNativeState.dopNativeAccessToken, oldAccessToken);
      verifyNever(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(newAccessToken));
    });

    test('should not save access token when access token is null', () {
      final String oldAccessToken = 'old_access_token';
      final String? newAccessToken = null;
      final DOPNativeState state = DOPNativeState(
        bootstrapAuthSettings: DOPNativeBootstrapAuthSettingsEntity(
          authType: 'id_card_auth',
        ),
        dopNativeAccessToken: oldAccessToken,
      );
      // Arrange
      when(() => mockAppState.dopNativeState).thenReturn(state);

      when(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(newAccessToken)).thenAnswer(
        (_) {},
      );

      final DOPNativeEkycStatusEntity entity = DOPNativeEkycStatusEntity.fromBaseResponse(
        BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'status': DOPNativeEkycStatusEntity.statusSuccess,
              'access_token': newAccessToken,
            }
          },
        ),
      );

      // Act
      ekycCommonApiResponsesHandler.saveAccessTokenIfNeeded(entity);

      // Assert
      expect(mockAppState.dopNativeState.dopNativeAccessToken, oldAccessToken);
      verifyNever(() => mockDOPUtilFunctions.setDOPNativeAccessTokenHeader(newAccessToken));
    });
  });
}
