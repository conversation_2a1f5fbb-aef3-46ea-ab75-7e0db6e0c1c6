import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import 'package:evoapp/data/request/dop_native/dop_native_id_card_image_type.dart';
import 'package:evoapp/data/response/dop_native/dop_native_check_id_card_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_upload_id_card_entity.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/id_card_qr_code_verification/cubit/dop_native_id_card_qr_code_verification_cubit.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/ui_model/ekyc_error_ui_model.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/utils/dop_native_ekyc_api_response_handler/dop_native_ekyc_api_response_handler.dart';
import 'package:evoapp/feature/dop_native/util/dop_native_submit_status_polling/dop_native_submit_status_polling.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../constant.dart';
import '../../../../../../util/test_util.dart';
import '../../ekyc_submit_status_mock_config.dart';

class MockDopNativeEkycUIOnlyRepo extends Mock implements DopNativeEkycUIOnlyRepo {}

class MockDOPNativeSubmitStatusPolling extends Mock implements DOPNativeSubmitStatusPolling {}

class MockEkycCommonApiResponsesHandler extends Mock implements EkycCommonApiResponsesHandler {}

class FakeDOPNativeUploadIdCardEntity extends Fake implements DOPNativeUploadIdCardEntity {}

class FakeEkycErrorUIModel extends Fake implements EkycErrorUIModel {}

void main() {
  final DopNativeEkycUIOnlyRepo dopNativeEkycUiOnlyRepo = MockDopNativeEkycUIOnlyRepo();
  final DOPNativeSubmitStatusPolling dopNativeSubmitStatusPolling =
      MockDOPNativeSubmitStatusPolling();
  final EkycCommonApiResponsesHandler commonApisResponseHandler =
      MockEkycCommonApiResponsesHandler();

  const String fakeCardQrCodeBase64Image = 'base64Image';
  const String fakeCardType = 'cardType';
  final EkycErrorUIModel fakeEkycErrorUIModel = FakeEkycErrorUIModel();

  late DOPNativeIdCardQRCodeVerificationCubit cubit;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(FakeDOPNativeUploadIdCardEntity());
  });

  setUp(() {
    cubit = DOPNativeIdCardQRCodeVerificationCubit(
      dopNativeEkycUiOnlyRepo: dopNativeEkycUiOnlyRepo,
      dopNativeSubmitStatusPolling: dopNativeSubmitStatusPolling,
      commonApisResponseHandler: commonApisResponseHandler,
    );
  });

  group('uploadQrCodeIdCard', () {
    blocTest<DOPNativeIdCardQRCodeVerificationCubit, DOPNativeIdCardQRCodeVerificationState>(
      'Upload qr code fail',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        final DOPNativeUploadIdCardEntity entity = DOPNativeUploadIdCardEntity.fromBaseResponse(
          BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          ),
        );

        when(() => dopNativeEkycUiOnlyRepo.uploadQrCodeIdCard(
              cardQrCodeBase64Image: any(named: 'cardQrCodeBase64Image'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => entity,
        );
        when(() => commonApisResponseHandler.getUploadImageError(any())).thenReturn(
          fakeEkycErrorUIModel,
        );
      },
      act: (DOPNativeIdCardQRCodeVerificationCubit cubit) => cubit.uploadQrCodeIdCard(
        cardQrCodeBase64Image: fakeCardQrCodeBase64Image,
      ),
      expect: () => <dynamic>[
        isA<QRCodeVerificationProcessing>(),
        isA<QRCodeUploadFailed>(),
      ],
      verify: (_) {
        verify(() => dopNativeEkycUiOnlyRepo.uploadQrCodeIdCard(
              cardQrCodeBase64Image: fakeCardQrCodeBase64Image,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
        verify(() => commonApisResponseHandler.getUploadImageError(any())).called(1);

        verifyNever(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            ));
        verifyNever(() =>
            dopNativeSubmitStatusPolling.delayToPolling(onDoPolling: any(named: 'onDoPolling')));
      },
    );

    blocTest<DOPNativeIdCardQRCodeVerificationCubit, DOPNativeIdCardQRCodeVerificationState>(
      'Upload qr code success, submit status success',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () async {
        const String filename = 'dop_native_upload_ekyc_card_id_success.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);
        final DOPNativeUploadIdCardEntity uploadEntity =
            DOPNativeUploadIdCardEntity.fromBaseResponse(
          BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: responseData,
          ),
        );

        when(() => dopNativeEkycUiOnlyRepo.uploadQrCodeIdCard(
              cardQrCodeBase64Image: any(named: 'cardQrCodeBase64Image'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => uploadEntity,
        );
        when(() => commonApisResponseHandler.getUploadImageError(any())).thenReturn(null);

        when(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingSuccessEntity;
        });
      },
      act: (DOPNativeIdCardQRCodeVerificationCubit cubit) => cubit.uploadQrCodeIdCard(
        cardQrCodeBase64Image: fakeCardQrCodeBase64Image,
      ),
      expect: () => <dynamic>[
        isA<QRCodeVerificationProcessing>(),
        isA<QRCodeVerificationSucceed>(),
      ],
      verify: (_) {
        verify(() => dopNativeEkycUiOnlyRepo.uploadQrCodeIdCard(
              cardQrCodeBase64Image: fakeCardQrCodeBase64Image,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
        verify(() => commonApisResponseHandler.getUploadImageError(any())).called(1);
        verify(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verifyNever(() =>
            dopNativeSubmitStatusPolling.delayToPolling(onDoPolling: any(named: 'onDoPolling')));
      },
    );

    blocTest<DOPNativeIdCardQRCodeVerificationCubit, DOPNativeIdCardQRCodeVerificationState>(
      'Upload qr code success, submit status success for id card auth',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () async {
        const String filename = 'dop_native_upload_ekyc_card_id_success.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);
        final DOPNativeUploadIdCardEntity uploadEntity =
            DOPNativeUploadIdCardEntity.fromBaseResponse(
          BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: responseData,
          ),
        );

        when(() => dopNativeEkycUiOnlyRepo.uploadQrCodeIdCard(
              cardQrCodeBase64Image: any(named: 'cardQrCodeBase64Image'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => uploadEntity,
        );
        when(() => commonApisResponseHandler.getUploadImageError(any())).thenReturn(null);

        when(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingSuccessIdCardAuthEntity;
        });
      },
      act: (DOPNativeIdCardQRCodeVerificationCubit cubit) => cubit.uploadQrCodeIdCard(
        cardQrCodeBase64Image: fakeCardQrCodeBase64Image,
      ),
      expect: () => <dynamic>[
        isA<QRCodeVerificationProcessing>(),
        isA<QRCodeVerificationSucceed>(),
      ],
      verify: (_) {
        verify(() => dopNativeEkycUiOnlyRepo.uploadQrCodeIdCard(
              cardQrCodeBase64Image: fakeCardQrCodeBase64Image,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
        verify(() => commonApisResponseHandler.getUploadImageError(any())).called(1);

        verify(
          () => commonApisResponseHandler.saveAccessTokenIfNeeded(pollingSuccessIdCardAuthEntity),
        ).called(1);

        verify(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verifyNever(() =>
            dopNativeSubmitStatusPolling.delayToPolling(onDoPolling: any(named: 'onDoPolling')));
      },
    );

    blocTest<DOPNativeIdCardQRCodeVerificationCubit, DOPNativeIdCardQRCodeVerificationState>(
      'Upload qr code success, submit status fail',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () async {
        const String filename = 'dop_native_upload_ekyc_card_id_success.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);
        final DOPNativeUploadIdCardEntity uploadEntity =
            DOPNativeUploadIdCardEntity.fromBaseResponse(
          BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: responseData,
          ),
        );

        when(() => dopNativeEkycUiOnlyRepo.uploadQrCodeIdCard(
              cardQrCodeBase64Image: any(named: 'cardQrCodeBase64Image'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => uploadEntity,
        );
        when(() => commonApisResponseHandler.getUploadImageError(any())).thenReturn(null);

        when(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingFailureEntity;
        });
      },
      act: (DOPNativeIdCardQRCodeVerificationCubit cubit) => cubit.uploadQrCodeIdCard(
        cardQrCodeBase64Image: fakeCardQrCodeBase64Image,
      ),
      expect: () => <dynamic>[
        isA<QRCodeVerificationProcessing>(),
        isA<QRCodeVerificationFailed>(),
      ],
      verify: (_) {
        verify(() => dopNativeEkycUiOnlyRepo.uploadQrCodeIdCard(
              cardQrCodeBase64Image: fakeCardQrCodeBase64Image,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
        verify(() => commonApisResponseHandler.getUploadImageError(any())).called(1);
        verify(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verifyNever(() =>
            dopNativeSubmitStatusPolling.delayToPolling(onDoPolling: any(named: 'onDoPolling')));
      },
    );

    blocTest<DOPNativeIdCardQRCodeVerificationCubit, DOPNativeIdCardQRCodeVerificationState>(
      'Upload qr code success, submit status pending',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () async {
        const String filename = 'dop_native_upload_ekyc_card_id_success.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);
        final DOPNativeUploadIdCardEntity uploadEntity =
            DOPNativeUploadIdCardEntity.fromBaseResponse(
          BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: responseData,
          ),
        );

        when(() => dopNativeEkycUiOnlyRepo.uploadQrCodeIdCard(
              cardQrCodeBase64Image: any(named: 'cardQrCodeBase64Image'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => uploadEntity,
        );
        when(() => commonApisResponseHandler.getUploadImageError(any())).thenReturn(null);

        when(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingPendingEntity;
        });
      },
      act: (DOPNativeIdCardQRCodeVerificationCubit cubit) => cubit.uploadQrCodeIdCard(
        cardQrCodeBase64Image: fakeCardQrCodeBase64Image,
      ),
      expect: () => <dynamic>[
        isA<QRCodeVerificationProcessing>(),
      ],
      verify: (_) {
        verify(() => dopNativeEkycUiOnlyRepo.uploadQrCodeIdCard(
              cardQrCodeBase64Image: fakeCardQrCodeBase64Image,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
        verify(() => commonApisResponseHandler.getUploadImageError(any())).called(1);
        verify(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
        verify(() =>
                dopNativeSubmitStatusPolling.delayToPolling(onDoPolling: any(named: 'onDoPolling')))
            .called(1);
      },
    );

    blocTest<DOPNativeIdCardQRCodeVerificationCubit, DOPNativeIdCardQRCodeVerificationState>(
      'Upload qr code success, submit status in progress',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () async {
        const String filename = 'dop_native_upload_ekyc_card_id_success.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);
        final DOPNativeUploadIdCardEntity uploadEntity =
            DOPNativeUploadIdCardEntity.fromBaseResponse(
          BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: responseData,
          ),
        );

        when(() => dopNativeEkycUiOnlyRepo.uploadQrCodeIdCard(
              cardQrCodeBase64Image: any(named: 'cardQrCodeBase64Image'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => uploadEntity,
        );
        when(() => commonApisResponseHandler.getUploadImageError(any())).thenReturn(null);

        when(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingInProgressEntity;
        });
      },
      act: (DOPNativeIdCardQRCodeVerificationCubit cubit) => cubit.uploadQrCodeIdCard(
        cardQrCodeBase64Image: fakeCardQrCodeBase64Image,
      ),
      expect: () => <dynamic>[
        isA<QRCodeVerificationProcessing>(),
      ],
      verify: (_) {
        verify(() => dopNativeEkycUiOnlyRepo.uploadQrCodeIdCard(
              cardQrCodeBase64Image: fakeCardQrCodeBase64Image,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
        verify(() => commonApisResponseHandler.getUploadImageError(any())).called(1);
        verify(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
        verify(() =>
                dopNativeSubmitStatusPolling.delayToPolling(onDoPolling: any(named: 'onDoPolling')))
            .called(1);
      },
    );
  });

  group('Test cancelPollingSubmitStatus', () {
    test('cancelPollingSubmitStatus', () {
      cubit.cancelPollingSubmitStatus();
      verify(() => dopNativeSubmitStatusPolling.cancel()).called(1);
    });
  });

  group('Test checkFrontIdCard', () {
    setUpAll(() {
      registerFallbackValue(DOPNativeIDCardImageType.front);
      registerFallbackValue(DOPNativeCheckIDCardEntity.unserializable());
    });

    blocTest<DOPNativeIdCardQRCodeVerificationCubit, DOPNativeIdCardQRCodeVerificationState>(
      'Check id card failed',
      build: () => cubit,
      setUp: () async {
        const String filename = 'dop_native_check_id_card_fail.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);

        when(() => dopNativeEkycUiOnlyRepo.checkIdCard(
              imageType: any(named: 'imageType'),
              cardType: any(named: 'cardType'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => DOPNativeCheckIDCardEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: responseData,
            )));

        when(() => commonApisResponseHandler.getCheckIdCardError(any()))
            .thenReturn(const EkycErrorUIModel(code: EkycErrorCode.otherEkycError));
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIdCardQRCodeVerificationCubit cubit) => cubit.checkFrontIdCard(
        cardType: fakeCardType,
      ),
      expect: () => <dynamic>[
        isA<QRCodeVerificationProcessing>(),
        isA<QRCodeVerificationFailed>().having(
          (QRCodeVerificationFailed p0) => p0.error.code,
          'verify error.code',
          EkycErrorCode.otherEkycError,
        ),
      ],
      verify: (_) {
        verify(() => dopNativeEkycUiOnlyRepo.checkIdCard(
              imageType: DOPNativeIDCardImageType.front,
              cardType: fakeCardType,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => commonApisResponseHandler.getCheckIdCardError(any())).called(1);

        verifyNever(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(jobId: any(named: 'jobId')));
      },
    );

    blocTest<DOPNativeIdCardQRCodeVerificationCubit, DOPNativeIdCardQRCodeVerificationState>(
      'Check id card success and pollingSubmitStatus success',
      build: () => cubit,
      setUp: () async {
        const String filename = 'dop_native_check_id_card_success.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);

        when(() => dopNativeEkycUiOnlyRepo.checkIdCard(
              imageType: any(named: 'imageType'),
              cardType: any(named: 'cardType'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => DOPNativeCheckIDCardEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: responseData,
            )));

        when(() => commonApisResponseHandler.getCheckIdCardError(any())).thenReturn(null);

        when(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingSuccessEntity;
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIdCardQRCodeVerificationCubit cubit) => cubit.checkFrontIdCard(
        cardType: fakeCardType,
      ),
      expect: () => <dynamic>[
        isA<QRCodeVerificationProcessing>(),
        isA<QRCodeVerificationSucceed>(),
      ],
      verify: (_) {
        verify(() => dopNativeEkycUiOnlyRepo.checkIdCard(
              imageType: DOPNativeIDCardImageType.front,
              cardType: fakeCardType,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => commonApisResponseHandler.getCheckIdCardError(any())).called(1);

        verify(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<DOPNativeIdCardQRCodeVerificationCubit, DOPNativeIdCardQRCodeVerificationState>(
      'Check id card success and pollingSubmitStatus success',
      build: () => cubit,
      setUp: () async {
        const String filename = 'dop_native_check_id_card_success.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);

        when(() => dopNativeEkycUiOnlyRepo.checkIdCard(
              imageType: any(named: 'imageType'),
              cardType: any(named: 'cardType'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => DOPNativeCheckIDCardEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: responseData,
            )));

        when(() => commonApisResponseHandler.getCheckIdCardError(any())).thenReturn(null);

        when(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingFailureEntity;
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIdCardQRCodeVerificationCubit cubit) => cubit.checkFrontIdCard(
        cardType: fakeCardType,
      ),
      expect: () => <dynamic>[
        isA<QRCodeVerificationProcessing>(),
        isA<QRCodeVerificationFailed>()
            .having(
              (QRCodeVerificationFailed p0) => p0.error.code,
              'error.code == common_error',
              EkycErrorCode.commonError,
            )
            .having(
              (QRCodeVerificationFailed p0) => p0.error.message,
              'error.message == entity.data.user_message',
              mockUserMessage,
            ),
      ],
      verify: (_) {
        verify(() => dopNativeEkycUiOnlyRepo.checkIdCard(
              imageType: DOPNativeIDCardImageType.front,
              cardType: fakeCardType,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => commonApisResponseHandler.getCheckIdCardError(any())).called(1);

        verify(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<DOPNativeIdCardQRCodeVerificationCubit, DOPNativeIdCardQRCodeVerificationState>(
      'Check id card success and pollingSubmitStatus success',
      build: () => cubit,
      setUp: () async {
        const String filename = 'dop_native_check_id_card_success.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);

        when(() => dopNativeEkycUiOnlyRepo.checkIdCard(
              imageType: any(named: 'imageType'),
              cardType: any(named: 'cardType'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => DOPNativeCheckIDCardEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: responseData,
            )));

        when(() => commonApisResponseHandler.getCheckIdCardError(any())).thenReturn(null);

        when(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingPendingEntity;
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIdCardQRCodeVerificationCubit cubit) => cubit.checkFrontIdCard(
        cardType: fakeCardType,
      ),
      expect: () => <dynamic>[
        isA<QRCodeVerificationProcessing>(),
      ],
      verify: (_) {
        verify(() => dopNativeEkycUiOnlyRepo.checkIdCard(
              imageType: DOPNativeIDCardImageType.front,
              cardType: fakeCardType,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => commonApisResponseHandler.getCheckIdCardError(any())).called(1);

        verify(() => dopNativeEkycUiOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );
  });
}
