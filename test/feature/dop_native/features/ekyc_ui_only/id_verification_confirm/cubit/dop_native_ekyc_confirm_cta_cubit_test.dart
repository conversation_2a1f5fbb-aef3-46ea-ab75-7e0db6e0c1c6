import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/id_verification_confirm/cubit/dop_native_ekyc_confirm_cta_cubit.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/id_verification_confirm/cubit/dop_native_ekyc_confirm_cta_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late DOPNativeEkycConfirmCTACubit cubit;

  setUp(() {
    cubit = DOPNativeEkycConfirmCTACubit();
  });

  test('Initial state', () {
    expect(
        cubit.state,
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify default enable',
          false,
        ));
  });

  group('Test updateResidenceAddress', () {
    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give streetFieldState = valid, update residenceAddressFieldState = valid, should emit state DOPNativeEkycConfirmCTAState with enable = true',
      build: () => cubit,
      setUp: () {
        cubit.streetFieldState = FieldState.valid;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateResidenceAddress(
        FieldState.valid,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          true,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give streetFieldState = valid, update residenceAddressFieldState = noneCheck, should emit state DOPNativeEkycConfirmCTAState with enable = true',
      build: () => cubit,
      setUp: () {
        cubit.streetFieldState = FieldState.valid;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateResidenceAddress(
        FieldState.noneCheck,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          true,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give streetFieldState = valid, update residenceAddressFieldState = invalid, should emit state DOPNativeEkycConfirmCTAState with enable = false',
      build: () => cubit,
      setUp: () {
        cubit.streetFieldState = FieldState.valid;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateResidenceAddress(
        FieldState.invalid,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          false,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give streetFieldState = noneCheck, update residenceAddressFieldState = valid, should emit state DOPNativeEkycConfirmCTAState with enable = true',
      build: () => cubit,
      setUp: () {
        cubit.streetFieldState = FieldState.noneCheck;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateResidenceAddress(
        FieldState.valid,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          true,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give streetFieldState = noneCheck, update residenceAddressFieldState = noneCheck, should emit state DOPNativeEkycConfirmCTAState with enable = true',
      build: () => cubit,
      setUp: () {
        cubit.streetFieldState = FieldState.noneCheck;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateResidenceAddress(
        FieldState.noneCheck,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          true,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give streetFieldState = noneCheck, update residenceAddressFieldState = invalid, should emit state DOPNativeEkycConfirmCTAState with enable = false',
      build: () => cubit,
      setUp: () {
        cubit.streetFieldState = FieldState.noneCheck;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateResidenceAddress(
        FieldState.invalid,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          false,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give streetFieldState = invalid, update residenceAddressFieldState = valid, should emit state DOPNativeEkycConfirmCTAState with enable = false',
      build: () => cubit,
      setUp: () {
        cubit.streetFieldState = FieldState.invalid;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateResidenceAddress(
        FieldState.valid,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          false,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give streetFieldState = invalid, update residenceAddressFieldState = noneCheck, should emit state DOPNativeEkycConfirmCTAState with enable = false',
      build: () => cubit,
      setUp: () {
        cubit.streetFieldState = FieldState.invalid;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateResidenceAddress(
        FieldState.noneCheck,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          false,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give streetFieldState = invalid, update residenceAddressFieldState = invalid, should emit state DOPNativeEkycConfirmCTAState with enable = false',
      build: () => cubit,
      setUp: () {
        cubit.streetFieldState = FieldState.invalid;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateResidenceAddress(
        FieldState.invalid,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          false,
        ),
      ],
    );
  });

  group('Test updateStreetAddress', () {
    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give residenceAddressFieldState = valid, update streetFieldState = valid, should emit state DOPNativeEkycConfirmCTAState with enable = true',
      build: () => cubit,
      setUp: () {
        cubit.residenceAddressFieldState = FieldState.valid;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateStreetAddress(
        FieldState.valid,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          true,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give residenceAddressFieldState = valid, update streetFieldState = noneCheck, should emit state DOPNativeEkycConfirmCTAState with enable = true',
      build: () => cubit,
      setUp: () {
        cubit.residenceAddressFieldState = FieldState.valid;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateStreetAddress(
        FieldState.noneCheck,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          true,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give residenceAddressFieldState = valid, update streetFieldState = invalid, should emit state DOPNativeEkycConfirmCTAState with enable = false',
      build: () => cubit,
      setUp: () {
        cubit.residenceAddressFieldState = FieldState.valid;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateStreetAddress(
        FieldState.invalid,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          false,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give residenceAddressFieldState = noneCheck, update streetFieldState = valid, should emit state DOPNativeEkycConfirmCTAState with enable = true',
      build: () => cubit,
      setUp: () {
        cubit.residenceAddressFieldState = FieldState.noneCheck;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateStreetAddress(
        FieldState.valid,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          true,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give residenceAddressFieldState = noneCheck, update streetFieldState = noneCheck, should emit state DOPNativeEkycConfirmCTAState with enable = true',
      build: () => cubit,
      setUp: () {
        cubit.residenceAddressFieldState = FieldState.noneCheck;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateStreetAddress(
        FieldState.noneCheck,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          true,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give residenceAddressFieldState = noneCheck, update streetFieldState = invalid, should emit state DOPNativeEkycConfirmCTAState with enable = false',
      build: () => cubit,
      setUp: () {
        cubit.residenceAddressFieldState = FieldState.noneCheck;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateStreetAddress(
        FieldState.invalid,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          false,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give residenceAddressFieldState = invalid, update streetFieldState = valid, should emit state DOPNativeEkycConfirmCTAState with enable = false',
      build: () => cubit,
      setUp: () {
        cubit.residenceAddressFieldState = FieldState.invalid;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateStreetAddress(
        FieldState.valid,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          false,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give residenceAddressFieldState = invalid, update streetFieldState = noneCheck, should emit state DOPNativeEkycConfirmCTAState with enable = false',
      build: () => cubit,
      setUp: () {
        cubit.residenceAddressFieldState = FieldState.invalid;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateStreetAddress(
        FieldState.noneCheck,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          false,
        ),
      ],
    );

    blocTest<DOPNativeEkycConfirmCTACubit, DOPNativeEkycConfirmCTAState>(
      'Give residenceAddressFieldState = invalid, update streetFieldState = invalid, should emit state DOPNativeEkycConfirmCTAState with enable = false',
      build: () => cubit,
      setUp: () {
        cubit.residenceAddressFieldState = FieldState.invalid;
      },
      act: (DOPNativeEkycConfirmCTACubit cubit) => cubit.updateStreetAddress(
        FieldState.invalid,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeEkycConfirmCTAState>().having(
          (DOPNativeEkycConfirmCTAState p0) => p0.enable,
          'verify enable',
          false,
        ),
      ],
    );
  });

  group('Test isEnable', () {
    test('Give residenceAddressFieldState = valid, streetFieldState = valid, should return true',
        () {
      cubit.residenceAddressFieldState = FieldState.valid;
      cubit.streetFieldState = FieldState.valid;

      expect(cubit.isEnable, isTrue);
    });

    test(
        'Give residenceAddressFieldState = valid, streetFieldState = noneCheck, should return true',
        () {
      cubit.residenceAddressFieldState = FieldState.valid;
      cubit.streetFieldState = FieldState.noneCheck;

      expect(cubit.isEnable, isTrue);
    });

    test('Give residenceAddressFieldState = valid, streetFieldState = invalid, should return false',
        () {
      cubit.residenceAddressFieldState = FieldState.valid;
      cubit.streetFieldState = FieldState.invalid;

      expect(cubit.isEnable, isFalse);
    });

    test(
        'Give residenceAddressFieldState = noneCheck, streetFieldState = valid, should return true',
        () {
      cubit.residenceAddressFieldState = FieldState.noneCheck;
      cubit.streetFieldState = FieldState.valid;

      expect(cubit.isEnable, isTrue);
    });

    test(
        'Give residenceAddressFieldState = noneCheck, streetFieldState = noneCheck, should return true',
        () {
      cubit.residenceAddressFieldState = FieldState.noneCheck;
      cubit.streetFieldState = FieldState.noneCheck;

      expect(cubit.isEnable, isTrue);
    });

    test(
        'Give residenceAddressFieldState = noneCheck, streetFieldState = invalid, should return false',
        () {
      cubit.residenceAddressFieldState = FieldState.noneCheck;
      cubit.streetFieldState = FieldState.invalid;

      expect(cubit.isEnable, isFalse);
    });

    test('Give residenceAddressFieldState = invalid, streetFieldState = valid, should return false',
        () {
      cubit.residenceAddressFieldState = FieldState.invalid;
      cubit.streetFieldState = FieldState.valid;

      expect(cubit.isEnable, isFalse);
    });

    test(
        'Give residenceAddressFieldState = invalid, streetFieldState = noneCheck, should return false',
        () {
      cubit.residenceAddressFieldState = FieldState.invalid;
      cubit.streetFieldState = FieldState.noneCheck;

      expect(cubit.isEnable, isFalse);
    });

    test(
        'Give residenceAddressFieldState = invalid, streetFieldState = invalid, should return false',
        () {
      cubit.residenceAddressFieldState = FieldState.invalid;
      cubit.streetFieldState = FieldState.invalid;

      expect(cubit.isEnable, isFalse);
    });
  });
}
