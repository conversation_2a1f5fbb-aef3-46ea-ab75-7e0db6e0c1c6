import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/id_verification_confirm/mock/mock_dop_native_get_ocr_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Test MockTestDOPNativeGetOcrUseCase', () {
    expect(
      MockTestDOPNativeGetOcrUseCase.getOCRDataSuccess.value,
      'dop_native_get_ocr_data_success.json',
    );
    expect(
      MockTestDOPNativeGetOcrUseCase.getOCRDataSuccessWithoutResidentAddress.value,
      'dop_native_get_ocr_data_success_without_resident_address.json',
    );
    expect(
      MockTestDOPNativeGetOcrUseCase.getOCRDataSuccessWithoutEmptyOldIDCard.value,
      'dop_native_get_ocr_data_success_with_empty_old_id_card.json',
    );
  });

  test('Test getMockDOPNativeGetOcrFileNameByCase', () {
    expect(
      getMockDOPNativeGetOcrFileNameByCase(
        MockTestDOPNativeGetOcrUseCase.getOCRDataSuccess,
      ),
      'dop_native_get_ocr_data_success.json',
    );
    expect(
      getMockDOPNativeGetOcrFileNameByCase(
        MockTestDOPNativeGetOcrUseCase.getOCRDataSuccessWithoutResidentAddress,
      ),
      'dop_native_get_ocr_data_success_without_resident_address.json',
    );
    expect(
      getMockDOPNativeGetOcrFileNameByCase(
        MockTestDOPNativeGetOcrUseCase.getOCRDataSuccessWithoutEmptyOldIDCard,
      ),
      'dop_native_get_ocr_data_success_with_empty_old_id_card.json',
    );
  });
}
