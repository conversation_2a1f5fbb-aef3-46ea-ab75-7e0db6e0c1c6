import 'package:evoapp/data/response/dop_native/dop_native_upload_face_id_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_verify_face_id_entity.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/face_otp/cubit/dop_native_face_otp_cubit.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/tv_ekyc/tv_image_wrapper.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/ui_model/ekyc_error_ui_model.dart';
import 'package:evoapp/feature/ekyc/model/ekyc_result_model.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DopNativeFaceOtpState', () {
    test('DopNativeFaceOtpInitial should be instantiated', () {
      final DopNativeFaceOtpInitial state = DopNativeFaceOtpInitial();
      expect(state, isA<DopNativeFaceOtpInitial>());
    });

    test('DopNativeFaceOtpLoading should be instantiated', () {
      final DopNativeFaceOtpLoading state = DopNativeFaceOtpLoading();
      expect(state, isA<DopNativeFaceOtpLoading>());
    });

    test('DopNativeFaceOtpInitSDKSuccess should be instantiated', () {
      final DopNativeFaceOtpInitSDKSuccess state = DopNativeFaceOtpInitSDKSuccess();
      expect(state, isA<DopNativeFaceOtpInitSDKSuccess>());
    });

    test('DopNativeFaceOtpInitSDKFailure should hold TVSDKResult', () {
      final TVSDKResult result = TVSDKResult.succeed();
      final DopNativeFaceOtpInitSDKFailure state =
          DopNativeFaceOtpInitSDKFailure(tvSDKResult: result);
      expect(state.tvSDKResult, result);
    });

    test('DopNativeFaceOtpCaptureSuccess should hold selfieImage', () {
      const TVImageWrapper image = TVImageWrapper();
      final DopNativeFaceOtpCaptureSuccess state =
          DopNativeFaceOtpCaptureSuccess(selfieImage: image);
      expect(state.selfieImage, image);
    });

    test('DopNativeFaceOtpCaptureFailure should hold tvSDKResult', () {
      final TVSDKResult result = TVSDKResult.succeed();
      final DopNativeFaceOtpCaptureFailure state =
          DopNativeFaceOtpCaptureFailure(tvSDKResult: result);
      expect(state.tvSDKResult, result);
    });

    test('DopNativeFaceOtpUploadSuccess should hold entity', () {
      final DOPNativeUploadFaceOtpIdEntity entity = DOPNativeUploadFaceOtpIdEntity();
      final DopNativeFaceOtpUploadSuccess state = DopNativeFaceOtpUploadSuccess(entity: entity);
      expect(state.entity, entity);
    });

    test('DopNativeFaceOtpUploadFailure should hold error', () {
      const EkycErrorUIModel error = EkycErrorUIModel(code: EkycErrorCode.commonError);
      final DopNativeFaceOtpUploadFailure state = DopNativeFaceOtpUploadFailure(error: error);
      expect(state.error, error);
    });

    test('DopNativeVerifyFaceOtpSuccess should hold entity', () {
      final DOPNativeVerifyFaceOtpIdEntity entity = DOPNativeVerifyFaceOtpIdEntity();
      final DopNativeVerifyFaceOtpSuccess state = DopNativeVerifyFaceOtpSuccess(entity: entity);
      expect(state.entity, entity);
    });

    test('DopNativeVerifyFaceOtpFailure should hold error', () {
      const EkycErrorUIModel error = EkycErrorUIModel(code: EkycErrorCode.commonError);
      final DopNativeVerifyFaceOtpFailure state = DopNativeVerifyFaceOtpFailure(error: error);
      expect(state.error, error);
    });

    test('DopNativeFaceOtpCommonFailure should hold error', () {
      final ErrorUIModel error = ErrorUIModel();
      final DopNativeFaceOtpCommonFailure state = DopNativeFaceOtpCommonFailure(error: error);
      expect(state.error, error);
    });
  });
}
