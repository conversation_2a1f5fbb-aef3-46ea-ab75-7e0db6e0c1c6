import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_ekyc_status_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_selfie_submit_gestures_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_upload_image_entity.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/tv_ekyc/liveness_mode.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/sdk_bridge/tv_ekyc/tv_image_wrapper.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/selfie/selfie_verification/cubit/dop_native_ekyc_selfie_verification_cubit.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/ui_model/ekyc_error_ui_model.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/utils/dop_native_ekyc_api_response_handler/dop_native_ekyc_api_response_handler.dart';
import 'package:evoapp/feature/dop_native/util/dop_native_submit_status_polling/dop_native_submit_status_polling.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../../constant.dart';
import '../../../../../../../util/test_util.dart';

class MockDopNativeEkycUIOnlyRepo extends Mock implements DopNativeEkycUIOnlyRepo {}

class MockDOPNativeSubmitStatusPolling extends Mock implements DOPNativeSubmitStatusPolling {}

class MockEkycCommonApiResponsesHandler extends Mock implements EkycCommonApiResponsesHandler {}

void main() {
  final DOPNativeSubmitStatusPolling mockDOPNativeSubmitStatusPolling =
      MockDOPNativeSubmitStatusPolling();

  late DopNativeEkycUIOnlyRepo mockDopNativeEkycUIOnlyRepo;
  late EkycCommonApiResponsesHandler mockCommonApiResponsesHandler;

  late DopNativeEkycSelfieVerificationCubit cubit;

  const String mockUserMessage = 'mock-user-message';

  DOPNativeEkycStatusEntity createSubmitStatusEntity(String status,
      {String? verdict, String? userMessage}) {
    return DOPNativeEkycStatusEntity.fromBaseResponse(
      BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'verdict': verdict,
          'data': <String, dynamic>{
            'status': status,
            'error_code': '',
            'label': 'label1',
            'qr_readable': true,
            'user_message': userMessage
          }
        },
      ),
    );
  }

  final DOPNativeEkycStatusEntity pollingSuccessEntity = createSubmitStatusEntity(
    DOPNativeEkycStatusEntity.statusSuccess,
    verdict: 'success',
  );
  final DOPNativeEkycStatusEntity pollingFailureEntity = createSubmitStatusEntity(
    DOPNativeEkycStatusEntity.statusFailure,
    verdict: 'fake-verdict',
    userMessage: mockUserMessage,
  );
  final DOPNativeEkycStatusEntity pollingPendingEntity = createSubmitStatusEntity(
    DOPNativeEkycStatusEntity.statusPending,
    verdict: 'success',
  );
  final DOPNativeEkycStatusEntity pollingInProgressEntity =
      createSubmitStatusEntity(DOPNativeEkycStatusEntity.statusInProgress, verdict: 'success');

  const String fakeBatchId = 'fake_batch_id';
  const String fakeRawImageBase64 = 'fakeRawImageBase64';
  const LivenessModeWrapper fakeLivenessMode = LivenessModeWrapper.active;
  const Direction fakeDirection = Direction.frontalMain;

  final List<TVImageWrapper> fakeSelfieImages = <TVImageWrapper>[
    const TVImageWrapper(
      rawImageBase64: fakeRawImageBase64,
      direction: fakeDirection,
    ),
  ];

  final BaseEntity fakeSuccessToBaseEntity = BaseEntity(
    statusCode: CommonHttpClient.SUCCESS,
    verdict: BaseEntity.verdictSuccess,
  );
  final BaseEntity fakeFailureToBaseEntity = BaseEntity(
    statusCode: CommonHttpClient.BAD_REQUEST,
    verdict: DOPNativeSelfieSubmitGesturesEntity.verdictFailure,
  );

  late DOPNativeSelfieSubmitGesturesEntity submitGesturesSuccessEntity;

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();

    registerFallbackValue(DOPNativeEkycStatusEntity.unserializable());

    getIt.registerSingleton<DopNativeEkycUIOnlyRepo>(MockDopNativeEkycUIOnlyRepo());
    mockDopNativeEkycUIOnlyRepo = getIt.get<DopNativeEkycUIOnlyRepo>();

    getIt.registerSingleton<EkycCommonApiResponsesHandler>(MockEkycCommonApiResponsesHandler());
    mockCommonApiResponsesHandler = getIt.get<EkycCommonApiResponsesHandler>();

    submitGesturesSuccessEntity = DOPNativeSelfieSubmitGesturesEntity.fromBaseResponse(BaseResponse(
      statusCode: CommonHttpClient.SUCCESS,
      response: await TestUtil.getResponseMock('dop_native_selfie_submit_gestures_success.json'),
    ));
  });

  // Set up the mock repository before each test
  setUp(() {
    cubit = DopNativeEkycSelfieVerificationCubit(
      dopNativeEkycUiOnlyRepo: mockDopNativeEkycUIOnlyRepo,
      dopNativeSubmitStatusPolling: mockDOPNativeSubmitStatusPolling,
      commonApisResponseHandler: mockCommonApiResponsesHandler,
    );

    when(() => mockDopNativeEkycUIOnlyRepo.submitGestureImages(
          batchId: any(named: 'batchId'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async => submitGesturesSuccessEntity);

    when(() => mockDopNativeEkycUIOnlyRepo.submitSelfieType(
          batchId: any(named: 'batchId'),
          selfieType: any(named: 'selfieType'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async => fakeSuccessToBaseEntity);

    when(() => mockDopNativeEkycUIOnlyRepo.uploadEkycImage(
          label: any(named: 'label'),
          base64Image: any(named: 'base64Image'),
          batchId: any(named: 'batchId'),
          direction: any(named: 'direction'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async => DOPNativeUploadImageEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: await TestUtil.getResponseMock('dop_native_upload_ekyc_image_success.json'),
        )));

    when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
          jobId: any(named: 'jobId'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async {
      return pollingSuccessEntity;
    });
  });

  tearDown(() {
    reset(mockDopNativeEkycUIOnlyRepo);
    reset(mockCommonApiResponsesHandler);
    reset(mockDOPNativeSubmitStatusPolling);
  });

  group('verify submitGesture()', () {
    blocTest<DopNativeEkycSelfieVerificationCubit, DopNativeEkycSelfieVerificationState>(
      'Default state',
      build: () => cubit,
      verify: (DopNativeEkycSelfieVerificationCubit cubit) {
        expect(cubit.state, isA<SelfieVerificationInitial>());
      },
    );

    blocTest<DopNativeEkycSelfieVerificationCubit, DopNativeEkycSelfieVerificationState>(
      'Upload Gesture Images success and get submit status success with LivenessModeWrapper.active',
      build: () => cubit,
      act: (DopNativeEkycSelfieVerificationCubit cubit) async => await cubit.submitGestures(
        batchId: fakeBatchId,
        selfieImages: fakeSelfieImages,
        livenessMode: LivenessModeWrapper.active,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<SelfieVerificationLoading>(),
        isA<SelfieVerificationSuccess>(),
      ],
      verify: (_) {
        verify(() => mockCommonApiResponsesHandler.getStatusError(pollingSuccessEntity)).called(1);

        verify(
          () => mockDopNativeEkycUIOnlyRepo.submitSelfieType(
            batchId: fakeBatchId,
            selfieType: LivenessModeWrapper.active.value,
            mockConfig: any(named: 'mockConfig'),
          ),
        ).called(1);

        verify(() => mockDopNativeEkycUIOnlyRepo.submitGestureImages(
              batchId: fakeBatchId,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockDopNativeEkycUIOnlyRepo.uploadEkycImage(
              label: 'portrait',
              base64Image: fakeRawImageBase64,
              batchId: fakeBatchId,
              direction: fakeDirection.value,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<DopNativeEkycSelfieVerificationCubit, DopNativeEkycSelfieVerificationState>(
      'Upload Gesture Images success and get submit status success with LivenessModeWrapper.flash_16',
      build: () => cubit,
      act: (DopNativeEkycSelfieVerificationCubit cubit) async => await cubit.submitGestures(
        batchId: fakeBatchId,
        selfieImages: fakeSelfieImages,
        livenessMode: LivenessModeWrapper.flash_16,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<SelfieVerificationLoading>(),
        isA<SelfieVerificationSuccess>(),
      ],
      verify: (_) {
        verify(() => mockCommonApiResponsesHandler.getStatusError(pollingSuccessEntity)).called(1);

        verify(
          () => mockDopNativeEkycUIOnlyRepo.submitSelfieType(
            batchId: fakeBatchId,
            selfieType: LivenessModeWrapper.flash_16.value,
            mockConfig: any(named: 'mockConfig'),
          ),
        ).called(1);

        verify(() => mockDopNativeEkycUIOnlyRepo.submitGestureImages(
              batchId: fakeBatchId,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockDopNativeEkycUIOnlyRepo.uploadEkycImage(
              label: 'portrait',
              base64Image: fakeRawImageBase64,
              batchId: fakeBatchId,
              direction: fakeDirection.value,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<DopNativeEkycSelfieVerificationCubit, DopNativeEkycSelfieVerificationState>(
      'Upload Gesture Images Success & Get submit status failed',
      build: () => cubit,
      setUp: () {
        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingFailureEntity;
        });
      },
      act: (DopNativeEkycSelfieVerificationCubit cubit) async => await cubit.submitGestures(
        batchId: fakeBatchId,
        selfieImages: fakeSelfieImages,
        livenessMode: LivenessModeWrapper.active,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<SelfieVerificationLoading>(),
        isA<SelfieVerificationFailed>()
            .having(
              (SelfieVerificationFailed p0) => p0.error.code,
              'error.code == common_error',
              EkycErrorCode.commonError,
            )
            .having(
              (SelfieVerificationFailed p0) => p0.error.message,
              'error.message == entity.data.user_message',
              mockUserMessage,
            )
      ],
      verify: (_) {
        verify(() => mockCommonApiResponsesHandler.getStatusError(pollingFailureEntity)).called(1);

        verify(
          () => mockDopNativeEkycUIOnlyRepo.submitSelfieType(
            batchId: fakeBatchId,
            selfieType: LivenessModeWrapper.active.value,
            mockConfig: any(named: 'mockConfig'),
          ),
        ).called(1);

        verify(() => mockDopNativeEkycUIOnlyRepo.submitGestureImages(
              batchId: fakeBatchId,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockDopNativeEkycUIOnlyRepo.uploadEkycImage(
              label: 'portrait',
              base64Image: fakeRawImageBase64,
              batchId: fakeBatchId,
              direction: fakeDirection.value,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<DopNativeEkycSelfieVerificationCubit, DopNativeEkycSelfieVerificationState>(
      'Upload Selfie Images failed',
      build: () => cubit,
      setUp: () {
        when(() => mockDopNativeEkycUIOnlyRepo.uploadEkycImage(
              label: any(named: 'label'),
              base64Image: any(named: 'base64Image'),
              batchId: any(named: 'batchId'),
              direction: any(named: 'direction'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => DOPNativeUploadImageEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: await TestUtil.getResponseMock('dop_native_upload_ekyc_image_fail.json'),
            )));
      },
      act: (DopNativeEkycSelfieVerificationCubit cubit) async => await cubit.submitGestures(
        batchId: fakeBatchId,
        selfieImages: fakeSelfieImages,
        livenessMode: LivenessModeWrapper.active,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<SelfieVerificationLoading>(),
        isA<SelfieVerificationFailed>().having(
          (SelfieVerificationFailed state) => state.error.code,
          'error.code == otherEkycError',
          EkycErrorCode.otherEkycError,
        ),
      ],
      verify: (_) {
        verifyNever(() => mockCommonApiResponsesHandler.getStatusError(any()));

        verify(
          () => mockDopNativeEkycUIOnlyRepo.submitSelfieType(
            batchId: fakeBatchId,
            selfieType: LivenessModeWrapper.active.value,
            mockConfig: any(named: 'mockConfig'),
          ),
        ).called(1);

        verifyNever(() => mockDopNativeEkycUIOnlyRepo.submitGestureImages(
              batchId: fakeBatchId,
              mockConfig: any(named: 'mockConfig'),
            ));

        verify(() => mockDopNativeEkycUIOnlyRepo.uploadEkycImage(
              label: 'portrait',
              base64Image: fakeRawImageBase64,
              batchId: fakeBatchId,
              direction: fakeDirection.value,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<DopNativeEkycSelfieVerificationCubit, DopNativeEkycSelfieVerificationState>(
      'submitGestureImages failed with status LIMIT_EXCEEDED',
      build: () => cubit,
      setUp: () {
        when(() => mockDopNativeEkycUIOnlyRepo.submitGestureImages(
                  batchId: any(named: 'batchId'),
                  mockConfig: any(named: 'mockConfig'),
                ))
            .thenAnswer((_) async =>
                DOPNativeSelfieSubmitGesturesEntity.fromBaseResponse(BaseResponse(
                  statusCode: CommonHttpClient.LIMIT_EXCEEDED,
                  response:
                      await TestUtil.getResponseMock('dop_native_selfie_submit_gestures_fail.json'),
                )));
      },
      act: (DopNativeEkycSelfieVerificationCubit cubit) async => await cubit.submitGestures(
        batchId: fakeBatchId,
        selfieImages: fakeSelfieImages,
        livenessMode: LivenessModeWrapper.active,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<SelfieVerificationLoading>(),
        isA<SelfieVerificationFailed>().having(
          (SelfieVerificationFailed state) => state.error.code,
          'error.code == limitExceed',
          EkycErrorCode.limitExceed,
        ),
      ],
      verify: (_) {
        verifyNever(() => mockCommonApiResponsesHandler.getStatusError(any()));

        verify(
          () => mockDopNativeEkycUIOnlyRepo.submitSelfieType(
            batchId: fakeBatchId,
            selfieType: LivenessModeWrapper.active.value,
            mockConfig: any(named: 'mockConfig'),
          ),
        ).called(1);

        verify(() => mockDopNativeEkycUIOnlyRepo.submitGestureImages(
              batchId: fakeBatchId,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockDopNativeEkycUIOnlyRepo.uploadEkycImage(
              label: 'portrait',
              base64Image: fakeRawImageBase64,
              batchId: fakeBatchId,
              direction: fakeDirection.value,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );
  });

  group('verify submitGestureImages()', () {
    test('submitGestureImages returns DOPNativeSelfieSubmitGesturesEntity', () async {
      final DOPNativeSelfieSubmitGesturesEntity result = await cubit.submitGestureImages(
        batchId: fakeBatchId,
      );

      expect(result, submitGesturesSuccessEntity);
      verify(() => mockDopNativeEkycUIOnlyRepo.submitGestureImages(
            batchId: fakeBatchId,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify uploadSelfieImagesAndSubmitGestureType()', () {
    test('uploadSelfieImagesAndSubmitGestureType with List<BaseEntity> is null', () async {
      final List<BaseEntity> result = await cubit.uploadSelfieImagesAndSubmitGestureType(
        batchId: fakeBatchId,
        selfieImages: null,
        livenessMode: fakeLivenessMode,
      );

      expect(result.first.statusCode, fakeFailureToBaseEntity.statusCode);
      verifyNever(() => mockDopNativeEkycUIOnlyRepo.submitSelfieType(
            batchId: fakeBatchId,
            selfieType: fakeLivenessMode.value,
            mockConfig: any(named: 'mockConfig'),
          ));
      verifyNever(() => mockDopNativeEkycUIOnlyRepo.uploadEkycImage(
            label: any(named: 'label'),
            base64Image: any(named: 'base64Image'),
            batchId: any(named: 'batchId'),
            direction: any(named: 'direction'),
            mockConfig: any(named: 'mockConfig'),
          ));
    });

    test('uploadSelfieImagesAndSubmitGestureType with List<BaseEntity> is not null', () async {
      final List<BaseEntity> result = await cubit.uploadSelfieImagesAndSubmitGestureType(
        batchId: fakeBatchId,
        selfieImages: fakeSelfieImages,
        livenessMode: fakeLivenessMode,
      );

      expect(result.length, 2);
      verify(() => mockDopNativeEkycUIOnlyRepo.submitSelfieType(
            batchId: fakeBatchId,
            selfieType: fakeLivenessMode.value,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
      verify(() => mockDopNativeEkycUIOnlyRepo.uploadEkycImage(
            label: any(named: 'label'),
            base64Image: any(named: 'base64Image'),
            batchId: any(named: 'batchId'),
            direction: any(named: 'direction'),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify submitSelfieType()', () {
    test('submitSelfieType returns BaseEntity', () async {
      final BaseEntity result = await cubit.submitSelfieType(
        batchId: fakeBatchId,
        livenessMode: fakeLivenessMode,
      );

      expect(result.statusCode, CommonHttpClient.SUCCESS);
      verify(() => mockDopNativeEkycUIOnlyRepo.submitSelfieType(
            batchId: fakeBatchId,
            selfieType: fakeLivenessMode.value,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });

  group('verify uploadSelfieImage()', () {
    const String fakeSelfieImage = 'fakeSelfieImage';
    const Direction fakeDirection = Direction.down;
    const TVImageWrapper fakeTVImageWrapper = TVImageWrapper(
      rawImageBase64: fakeSelfieImage,
      direction: fakeDirection,
    );

    test('uploadSelfieImage is success and returns BaseEntity', () async {
      final BaseEntity result = await cubit.uploadSelfieImage(
        batchId: fakeBatchId,
        selfieImage: fakeTVImageWrapper,
      );

      expect(result.statusCode, CommonHttpClient.SUCCESS);
      verify(() => mockDopNativeEkycUIOnlyRepo.uploadEkycImage(
            label: 'portrait',
            base64Image: fakeSelfieImage,
            batchId: fakeBatchId,
            direction: fakeDirection.value,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('uploadSelfieImage with selfieImage is null or empty', () async {
      final BaseEntity result = await cubit.uploadSelfieImage(
        batchId: fakeBatchId,
        selfieImage: const TVImageWrapper(),
      );

      expect(result.statusCode, CommonHttpClient.BAD_REQUEST);
      expect(result.verdict, 'failure');
      verifyNever(() => mockDopNativeEkycUIOnlyRepo.uploadEkycImage(
            label: any(named: 'label'),
            base64Image: any(named: 'base64Image'),
            batchId: any(named: 'batchId'),
            direction: any(named: 'direction'),
            mockConfig: any(named: 'mockConfig'),
          ));
    });
  });

  group('verify pollingSubmitStatus()', () {
    const String fakeJobID = 'fakeJobID';
    final DOPNativeEkycStatusEntity commonErrorEntity = DOPNativeEkycStatusEntity.fromBaseResponse(
      BaseResponse(
        statusCode: CommonHttpClient.BAD_REQUEST,
        response: null,
      ),
    );

    blocTest<DopNativeEkycSelfieVerificationCubit, DopNativeEkycSelfieVerificationState>(
      'pollingSubmitStatus status success',
      build: () => cubit,
      setUp: () {
        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingSuccessEntity;
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DopNativeEkycSelfieVerificationCubit cubit) {
        return cubit.pollingSubmitStatus(fakeJobID);
      },
      expect: () => <dynamic>[
        isA<SelfieVerificationSuccess>(),
      ],
      verify: (_) {
        verify(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: fakeJobID,
              mockConfig: captureAny(named: 'mockConfig'),
            )).called(1);

        verifyNever(() => mockDOPNativeSubmitStatusPolling.delayToPolling(
            onDoPolling: any(named: 'onDoPolling')));

        verify(() => mockCommonApiResponsesHandler.getStatusError(pollingSuccessEntity)).called(1);
      },
    );

    blocTest<DopNativeEkycSelfieVerificationCubit, DopNativeEkycSelfieVerificationState>(
      'pollingSubmitStatus status failed',
      build: () => cubit,
      setUp: () {
        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingFailureEntity;
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DopNativeEkycSelfieVerificationCubit cubit) {
        return cubit.pollingSubmitStatus(fakeJobID);
      },
      expect: () => <dynamic>[
        isA<SelfieVerificationFailed>()
            .having(
              (SelfieVerificationFailed p0) => p0.error.code,
              'error.code == common_error',
              EkycErrorCode.commonError,
            )
            .having(
              (SelfieVerificationFailed p0) => p0.error.message,
              'error.message == entity.data.user_message',
              mockUserMessage,
            )
      ],
      verify: (_) {
        verify(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: fakeJobID,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verifyNever(() => mockDOPNativeSubmitStatusPolling.delayToPolling(
            onDoPolling: any(named: 'onDoPolling')));

        verify(() => mockCommonApiResponsesHandler.getStatusError(pollingFailureEntity)).called(1);
      },
    );

    blocTest<DopNativeEkycSelfieVerificationCubit, DopNativeEkycSelfieVerificationState>(
      'pollingSubmitStatus status pending',
      build: () => cubit,
      setUp: () {
        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingPendingEntity;
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DopNativeEkycSelfieVerificationCubit cubit) {
        return cubit.pollingSubmitStatus(fakeJobID);
      },
      verify: (_) {
        verify(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: fakeJobID,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockDOPNativeSubmitStatusPolling.delayToPolling(
            onDoPolling: any(named: 'onDoPolling'))).called(1);

        verify(() => mockCommonApiResponsesHandler.getStatusError(pollingPendingEntity)).called(1);
      },
    );

    blocTest<DopNativeEkycSelfieVerificationCubit, DopNativeEkycSelfieVerificationState>(
      'pollingSubmitStatus status in progress',
      build: () => cubit,
      setUp: () {
        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingInProgressEntity;
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DopNativeEkycSelfieVerificationCubit cubit) {
        return cubit.pollingSubmitStatus(fakeJobID);
      },
      verify: (_) {
        verify(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: fakeJobID,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockDOPNativeSubmitStatusPolling.delayToPolling(
            onDoPolling: any(named: 'onDoPolling'))).called(1);

        verify(() => mockCommonApiResponsesHandler.getStatusError(pollingInProgressEntity))
            .called(1);
      },
    );

    blocTest<DopNativeEkycSelfieVerificationCubit, DopNativeEkycSelfieVerificationState>(
      'pollingSubmitStatus fail',
      build: () => cubit,
      setUp: () {
        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => commonErrorEntity);
        when(() => mockCommonApiResponsesHandler.getStatusError(commonErrorEntity))
            .thenReturn(const EkycErrorUIModel(code: EkycErrorCode.commonError));
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DopNativeEkycSelfieVerificationCubit cubit) {
        return cubit.pollingSubmitStatus(fakeJobID);
      },
      expect: () => <dynamic>[
        isA<SelfieVerificationFailed>().having(
          (SelfieVerificationFailed p0) => p0.error,
          'verify error',
          isA<EkycErrorUIModel>(),
        ),
      ],
      verify: (_) {
        verify(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: fakeJobID,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verifyNever(() => mockDOPNativeSubmitStatusPolling.delayToPolling(
            onDoPolling: any(named: 'onDoPolling')));
      },
    );
  });

  group('Test cancelPollingSubmitStatus', () {
    test('cancelPollingSubmitStatus should cancel polling', () {
      cubit.cancelPollingSubmitStatus();
      verify(() => mockDOPNativeSubmitStatusPolling.cancel()).called(1);
    });
  });

  group('verify getSelfieImageError', () {
    getBaseResponse(
        {String verdict = BaseEntity.verdictSuccess, int statusCode = CommonHttpClient.SUCCESS}) {
      return BaseResponse(statusCode: statusCode, response: <String, dynamic>{
        'verdict': verdict,
        'data': <String, dynamic>{
          'user_message': mockUserMessage,
        }
      });
    }

    test('should return null when verdict is success and data.status is NOT failure', () {
      final DOPNativeSelfieSubmitGesturesEntity entity =
          DOPNativeSelfieSubmitGesturesEntity.fromBaseResponse(getBaseResponse());

      final EkycErrorUIModel? result = cubit.getSelfieImageError(entity);
      expect(result, null);
    });

    test('should return limit_exceed error', () {
      final DOPNativeSelfieSubmitGesturesEntity entity =
          DOPNativeSelfieSubmitGesturesEntity.fromBaseResponse(getBaseResponse(
        verdict: DOPNativeSelfieSubmitGesturesEntity.verdictLimitExceed,
      ));

      final EkycErrorUIModel? result = cubit.getSelfieImageError(entity);
      expect(result, isNot(null));
      expect(result?.code, EkycErrorCode.limitExceed);
      expect(result?.message, mockUserMessage);
    });

    test('should return other_ekyc_error when statusCode == 400 and verdict == failure', () {
      final DOPNativeSelfieSubmitGesturesEntity entity =
          DOPNativeSelfieSubmitGesturesEntity.fromBaseResponse(getBaseResponse(
        verdict: 'failure',
        statusCode: CommonHttpClient.BAD_REQUEST,
      ));

      final EkycErrorUIModel? result = cubit.getSelfieImageError(entity);
      expect(result, isNot(null));
      expect(result?.code, EkycErrorCode.otherEkycError);
      expect(result?.message, mockUserMessage);
    });

    test('should return other_ekyc_error when statusCode == 400 and verdict != success', () {
      final DOPNativeSelfieSubmitGesturesEntity entity =
          DOPNativeSelfieSubmitGesturesEntity.fromBaseResponse(getBaseResponse(
        verdict: 'mock-verdict',
        statusCode: CommonHttpClient.BAD_REQUEST,
      ));

      final EkycErrorUIModel? result = cubit.getSelfieImageError(entity);
      expect(result, isNot(null));
      expect(result?.code, EkycErrorCode.commonError);
      expect(result?.message, mockUserMessage);
    });

    test('should return http code when statusCode != 400 and verdict != success', () {
      final DOPNativeSelfieSubmitGesturesEntity entity =
          DOPNativeSelfieSubmitGesturesEntity.fromBaseResponse(getBaseResponse(
        verdict: 'mock-verdict',
        statusCode: CommonHttpClient.UNKNOWN_ERRORS,
      ));

      final EkycErrorUIModel? result = cubit.getSelfieImageError(entity);
      expect(result, isNot(null));
      expect(
        result?.code,
        EkycErrorCode.commonError,
      );
      expect(result?.message, mockUserMessage);
    });
  });

  test('verify convertFailureToBaseEntity', () {
    final BaseEntity result = cubit.convertFailureToBaseEntity();
    expect(result.statusCode, CommonHttpClient.BAD_REQUEST);
    expect(result.verdict, DOPNativeSelfieSubmitGesturesEntity.verdictFailure);
  });
}
