import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import 'package:evoapp/data/request/dop_native/dop_native_id_card_image_type.dart';
import 'package:evoapp/data/response/dop_native/dop_native_check_id_card_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_ekyc_status_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_upload_id_card_entity.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/id_card_front_side_verification/cubit/dop_native_id_card_front_side_verification_cubit.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/ui_model/ekyc_error_ui_model.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/utils/dop_native_ekyc_api_response_handler/dop_native_ekyc_api_response_handler.dart';
import 'package:evoapp/feature/dop_native/util/dop_native_submit_status_polling/dop_native_submit_status_polling.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../constant.dart';
import '../../../../../../util/test_util.dart';
import '../../ekyc_submit_status_mock_config.dart';

class MockDopNativeEkycUIOnlyRepo extends Mock implements DopNativeEkycUIOnlyRepo {}

class MockDOPNativeSubmitStatusPolling extends Mock implements DOPNativeSubmitStatusPolling {}

class MockEkycCommonApiResponsesHandler extends Mock implements EkycCommonApiResponsesHandler {}

class TestDOPNativeIdCardFrontSideVerificationCubit
    extends DOPNativeIdCardFrontSideVerificationCubit {
  TestDOPNativeIdCardFrontSideVerificationCubit({
    required super.dopNativeEkycUiOnlyRepo,
    required super.dopNativeSubmitStatusPolling,
    required super.commonApisResponseHandler,
  });

  bool isCalledMethod = false;

  @override
  Future<void> pollingSubmitStatus(String? jobId, {bool isPollingForQrCode = false}) async {
    isCalledMethod = true;
  }
}

void main() {
  late DOPNativeIdCardFrontSideVerificationCubit cubit;
  late DopNativeEkycUIOnlyRepo mockDopNativeEkycUIOnlyRepo;
  final DOPNativeSubmitStatusPolling mockDOPNativeSubmitStatusPolling =
      MockDOPNativeSubmitStatusPolling();
  late EkycCommonApiResponsesHandler mockCommonApiResponsesHandler;

  const String fakeCardType = 'fake_card_type';

  setUpAll(() {
    registerFallbackValue(DOPNativeUploadIdCardEntity.unserializable());
    registerFallbackValue(DOPNativeEkycStatusEntity.unserializable());
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());

    getIt.registerSingleton<DopNativeEkycUIOnlyRepo>(MockDopNativeEkycUIOnlyRepo());
    mockDopNativeEkycUIOnlyRepo = getIt.get<DopNativeEkycUIOnlyRepo>();

    getIt.registerSingleton<EkycCommonApiResponsesHandler>(MockEkycCommonApiResponsesHandler());
    mockCommonApiResponsesHandler = getIt.get<EkycCommonApiResponsesHandler>();
  });

  setUp(() {
    cubit = DOPNativeIdCardFrontSideVerificationCubit(
      dopNativeEkycUiOnlyRepo: mockDopNativeEkycUIOnlyRepo,
      dopNativeSubmitStatusPolling: mockDOPNativeSubmitStatusPolling,
      commonApisResponseHandler: mockCommonApiResponsesHandler,
    );
  });

  tearDown(() {
    reset(mockDopNativeEkycUIOnlyRepo);
    reset(mockCommonApiResponsesHandler);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('verify uploadFrontIdCard()', () {
    const String fakeFrontIdCard = 'fake_front_id_card';

    blocTest<DOPNativeIdCardFrontSideVerificationCubit, DOPNativeIdCardFrontSideVerificationState>(
        'Default state',
        build: () => cubit,
        verify: (DOPNativeIdCardFrontSideVerificationCubit cubit) {
          expect(cubit.state, isA<IdCardVerificationInitial>());
        });

    blocTest<DOPNativeIdCardFrontSideVerificationCubit, DOPNativeIdCardFrontSideVerificationState>(
      'Upload front id card success and get submit status success',
      build: () => cubit,
      setUp: () {
        when(
          () => mockDopNativeEkycUIOnlyRepo.uploadFrontIdCard(
            cardFrontSideBase64Image: any(named: 'cardFrontSideBase64Image'),
            cardType: any(named: 'cardType'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async => DOPNativeUploadIdCardEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response:
                  await TestUtil.getResponseMock('dop_native_upload_ekyc_card_id_success.json'),
            )));

        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingSuccessEntity;
        });
      },
      act: (DOPNativeIdCardFrontSideVerificationCubit cubit) async => await cubit.uploadFrontIdCard(
        cardFrontSideBase64Image: fakeFrontIdCard,
        cardType: fakeCardType,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<IdCardVerificationProcessing>(),
        isA<IdCardVerificationSuccess>(),
      ],
      verify: (_) {
        verify(
          () => mockDopNativeEkycUIOnlyRepo.uploadFrontIdCard(
            cardFrontSideBase64Image: fakeFrontIdCard,
            cardType: fakeCardType,
            mockConfig: any(named: 'mockConfig'),
          ),
        ).called(1);

        verify(() => mockCommonApiResponsesHandler.getStatusError(any())).called(1);
        verify(() => mockCommonApiResponsesHandler.getUploadImageError(any())).called(1);
      },
    );

    blocTest<DOPNativeIdCardFrontSideVerificationCubit, DOPNativeIdCardFrontSideVerificationState>(
      'Upload front id card success and get submit status failed',
      build: () => cubit,
      setUp: () {
        when(
          () => mockDopNativeEkycUIOnlyRepo.uploadFrontIdCard(
            cardFrontSideBase64Image: any(named: 'cardFrontSideBase64Image'),
            cardType: any(named: 'cardType'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async => DOPNativeUploadIdCardEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response:
                  await TestUtil.getResponseMock('dop_native_upload_ekyc_card_id_success.json'),
            )));

        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingFailureEntity;
        });
      },
      act: (DOPNativeIdCardFrontSideVerificationCubit cubit) async => await cubit.uploadFrontIdCard(
        cardFrontSideBase64Image: fakeFrontIdCard,
        cardType: fakeCardType,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<IdCardVerificationProcessing>(),
        isA<IdCardVerificationFailed>()
            .having(
              (IdCardVerificationFailed p0) => p0.error.code,
              'error.code == common_error',
              EkycErrorCode.commonError,
            )
            .having(
              (IdCardVerificationFailed p0) => p0.error.message,
              'error.message == entity.data.user_message',
              mockUserMessage,
            )
      ],
      verify: (_) {
        verify(
          () => mockDopNativeEkycUIOnlyRepo.uploadFrontIdCard(
            cardFrontSideBase64Image: fakeFrontIdCard,
            cardType: fakeCardType,
            mockConfig: any(named: 'mockConfig'),
          ),
        ).called(1);

        verify(() => mockCommonApiResponsesHandler.getStatusError(any())).called(1);
        verify(() => mockCommonApiResponsesHandler.getUploadImageError(any())).called(1);
      },
    );

    blocTest<DOPNativeIdCardFrontSideVerificationCubit, DOPNativeIdCardFrontSideVerificationState>(
      'Upload front id card failed',
      build: () => cubit,
      setUp: () {
        when(
          () => mockDopNativeEkycUIOnlyRepo.uploadFrontIdCard(
            cardFrontSideBase64Image: any(named: 'cardFrontSideBase64Image'),
            cardType: any(named: 'cardType'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async => DOPNativeUploadIdCardEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.LIMIT_EXCEEDED,
              response: null,
            )));
        when(() => mockCommonApiResponsesHandler.getUploadImageError(any()))
            .thenReturn(const EkycErrorUIModel(code: EkycErrorCode.limitExceed));
      },
      act: (DOPNativeIdCardFrontSideVerificationCubit cubit) async => await cubit.uploadFrontIdCard(
        cardFrontSideBase64Image: fakeFrontIdCard,
        cardType: fakeCardType,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<IdCardVerificationProcessing>(),
        isA<IdCardVerificationFailed>().having(
          (IdCardVerificationFailed state) => state.error.code,
          'error.code == limited_exceed',
          EkycErrorCode.limitExceed,
        ),
      ],
      verify: (_) {
        verify(
          () => mockDopNativeEkycUIOnlyRepo.uploadFrontIdCard(
            cardFrontSideBase64Image: fakeFrontIdCard,
            cardType: fakeCardType,
            mockConfig: any(named: 'mockConfig'),
          ),
        ).called(1);

        verifyNever(() => mockCommonApiResponsesHandler.getStatusError(any()));
        verify(() => mockCommonApiResponsesHandler.getUploadImageError(any())).called(1);
      },
    );

    blocTest<DOPNativeIdCardFrontSideVerificationCubit, DOPNativeIdCardFrontSideVerificationState>(
      'Upload front id card, required qr-code',
      build: () => cubit,
      setUp: () {
        when(
          () => mockDopNativeEkycUIOnlyRepo.uploadFrontIdCard(
            cardFrontSideBase64Image: any(named: 'cardFrontSideBase64Image'),
            cardType: any(named: 'cardType'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async => DOPNativeUploadIdCardEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: await TestUtil.getResponseMock(
                  'dop_native_upload_ekyc_front_card_id_required_qr_code.json'),
            )));

        when(() => mockCommonApiResponsesHandler.getUploadImageError(any()))
            .thenReturn(const EkycErrorUIModel(code: EkycErrorCode.qrCodeRequired));
      },
      act: (DOPNativeIdCardFrontSideVerificationCubit cubit) async => await cubit.uploadFrontIdCard(
        cardFrontSideBase64Image: fakeFrontIdCard,
        cardType: fakeCardType,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<IdCardVerificationProcessing>(),
        isA<IdCardVerificationFailed>().having(
          (IdCardVerificationFailed p0) => p0.error.code,
          'verify error code',
          EkycErrorCode.qrCodeRequired,
        ),
      ],
      verify: (_) {
        verify(
          () => mockDopNativeEkycUIOnlyRepo.uploadFrontIdCard(
            cardFrontSideBase64Image: fakeFrontIdCard,
            cardType: fakeCardType,
            mockConfig: any(named: 'mockConfig'),
          ),
        ).called(1);
      },
    );
  });

  group('verify handleAfterUploadImage()', () {
    late TestDOPNativeIdCardFrontSideVerificationCubit testCubit;

    setUp(() {
      testCubit = TestDOPNativeIdCardFrontSideVerificationCubit(
        dopNativeEkycUiOnlyRepo: mockDopNativeEkycUIOnlyRepo,
        dopNativeSubmitStatusPolling: mockDOPNativeSubmitStatusPolling,
        commonApisResponseHandler: mockCommonApiResponsesHandler,
      );

      when(() => mockCommonApiResponsesHandler.getUploadImageError(any())).thenAnswer((_) => null);
    });

    test('success', () async {
      final DOPNativeUploadIdCardEntity entity =
          DOPNativeUploadIdCardEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: await TestUtil.getResponseMock('dop_native_upload_ekyc_card_id_success.json'),
      ));

      testCubit.handleAfterUploadImage(entity);

      expect(testCubit.isCalledMethod, true);
    });

    test('failed', () async {
      when(() => mockCommonApiResponsesHandler.getUploadImageError(any()))
          .thenAnswer((_) => const EkycErrorUIModel(code: EkycErrorCode.commonError));

      final DOPNativeUploadIdCardEntity entity = DOPNativeUploadIdCardEntity.fromBaseResponse(
        BaseResponse(
          statusCode: CommonHttpClient.LIMIT_EXCEEDED,
          response: null,
        ),
      );

      testCubit.handleAfterUploadImage(entity);

      expect(testCubit.isCalledMethod, false);
    });
  });

  group('Test pollingSubmitStatus', () {
    const String fakeJobID = 'fakeJobID';

    blocTest<DOPNativeIdCardFrontSideVerificationCubit, DOPNativeIdCardFrontSideVerificationState>(
      'pollingSubmitStatus status success',
      build: () => cubit,
      setUp: () {
        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingSuccessEntity;
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIdCardFrontSideVerificationCubit cubit) {
        return cubit.pollingSubmitStatus(fakeJobID);
      },
      expect: () => <dynamic>[
        isA<IdCardVerificationSuccess>(),
      ],
      verify: (_) {
        verify(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: fakeJobID,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verifyNever(() => mockDOPNativeSubmitStatusPolling.delayToPolling(
            onDoPolling: any(named: 'onDoPolling')));

        verify(() => mockCommonApiResponsesHandler.getStatusError(any())).called(1);
      },
    );

    blocTest<DOPNativeIdCardFrontSideVerificationCubit, DOPNativeIdCardFrontSideVerificationState>(
      'pollingSubmitStatus success of id card auth',
      build: () => cubit,
      setUp: () {
        when(
          () => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
            jobId: any(named: 'jobId'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return pollingSuccessIdCardAuthEntity;
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIdCardFrontSideVerificationCubit cubit) {
        return cubit.pollingSubmitStatus(fakeJobID);
      },
      expect: () => <dynamic>[
        isA<IdCardVerificationSuccess>(),
      ],
      verify: (_) {
        verify(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: fakeJobID,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verifyNever(() => mockDOPNativeSubmitStatusPolling.delayToPolling(
            onDoPolling: any(named: 'onDoPolling')));

        verify(() => mockCommonApiResponsesHandler.getStatusError(any())).called(1);
        verify(
          () => mockCommonApiResponsesHandler.saveAccessTokenIfNeeded(
            pollingSuccessIdCardAuthEntity,
          ),
        ).called(1);
      },
    );

    blocTest<DOPNativeIdCardFrontSideVerificationCubit, DOPNativeIdCardFrontSideVerificationState>(
      'pollingSubmitStatus status failed',
      build: () => cubit,
      setUp: () {
        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingFailureEntity;
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIdCardFrontSideVerificationCubit cubit) {
        return cubit.pollingSubmitStatus(fakeJobID);
      },
      expect: () => <dynamic>[
        isA<IdCardVerificationFailed>()
            .having(
              (IdCardVerificationFailed p0) => p0.error.code,
              'error.code == common_error',
              EkycErrorCode.commonError,
            )
            .having(
              (IdCardVerificationFailed p0) => p0.error.message,
              'error.message == entity.data.user_message',
              mockUserMessage,
            )
      ],
      verify: (_) {
        verify(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: fakeJobID,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verifyNever(() => mockDOPNativeSubmitStatusPolling.delayToPolling(
            onDoPolling: any(named: 'onDoPolling')));

        verify(() => mockCommonApiResponsesHandler.getStatusError(any())).called(1);
      },
    );

    blocTest<DOPNativeIdCardFrontSideVerificationCubit, DOPNativeIdCardFrontSideVerificationState>(
      'pollingSubmitStatus status pending',
      build: () => cubit,
      setUp: () {
        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingPendingEntity;
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIdCardFrontSideVerificationCubit cubit) {
        return cubit.pollingSubmitStatus(fakeJobID);
      },
      verify: (_) {
        verify(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: fakeJobID,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockDOPNativeSubmitStatusPolling.delayToPolling(
            onDoPolling: any(named: 'onDoPolling'))).called(1);

        verify(() => mockCommonApiResponsesHandler.getStatusError(any())).called(1);
      },
    );

    blocTest<DOPNativeIdCardFrontSideVerificationCubit, DOPNativeIdCardFrontSideVerificationState>(
      'pollingSubmitStatus status in progress',
      build: () => cubit,
      setUp: () {
        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingInProgressEntity;
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIdCardFrontSideVerificationCubit cubit) {
        return cubit.pollingSubmitStatus(fakeJobID);
      },
      verify: (_) {
        verify(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: fakeJobID,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockDOPNativeSubmitStatusPolling.delayToPolling(
            onDoPolling: any(named: 'onDoPolling'))).called(1);

        verify(() => mockCommonApiResponsesHandler.getStatusError(any())).called(1);
      },
    );

    blocTest<DOPNativeIdCardFrontSideVerificationCubit, DOPNativeIdCardFrontSideVerificationState>(
      'pollingSubmitStatus fail',
      build: () => cubit,
      setUp: () {
        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => DOPNativeEkycStatusEntity.fromBaseResponse(
              BaseResponse(
                statusCode: CommonHttpClient.BAD_REQUEST,
                response: null,
              ),
            ));
        when(() => mockCommonApiResponsesHandler.getStatusError(any()))
            .thenReturn(const EkycErrorUIModel(code: EkycErrorCode.commonError));
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIdCardFrontSideVerificationCubit cubit) {
        return cubit.pollingSubmitStatus(fakeJobID);
      },
      expect: () => <dynamic>[
        isA<IdCardVerificationFailed>().having(
          (IdCardVerificationFailed p0) => p0.error,
          'verify error',
          isA<EkycErrorUIModel>(),
        ),
      ],
      verify: (_) {
        verify(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: fakeJobID,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verifyNever(() => mockDOPNativeSubmitStatusPolling.delayToPolling(
            onDoPolling: any(named: 'onDoPolling')));
      },
    );
  });

  group('Test cancelPollingSubmitStatus', () {
    test('cancelPollingSubmitStatus should cancel polling', () {
      cubit.cancelPollingSubmitStatus();
      verify(() => mockDOPNativeSubmitStatusPolling.cancel()).called(1);
    });
  });

  group('Test checkFrontIdCard', () {
    setUpAll(() {
      registerFallbackValue(DOPNativeIDCardImageType.front);
      registerFallbackValue(DOPNativeCheckIDCardEntity.unserializable());
    });

    blocTest<DOPNativeIdCardFrontSideVerificationCubit, DOPNativeIdCardFrontSideVerificationState>(
      'Check id card failed',
      build: () => cubit,
      setUp: () async {
        const String filename = 'dop_native_check_id_card_fail.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);

        when(() => mockDopNativeEkycUIOnlyRepo.checkIdCard(
              imageType: any(named: 'imageType'),
              cardType: any(named: 'cardType'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => DOPNativeCheckIDCardEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: responseData,
            )));

        when(() => mockCommonApiResponsesHandler.getCheckIdCardError(any()))
            .thenReturn(const EkycErrorUIModel(code: EkycErrorCode.otherEkycError));
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIdCardFrontSideVerificationCubit cubit) => cubit.checkFrontIdCard(
        cardType: fakeCardType,
      ),
      expect: () => <dynamic>[
        isA<IdCardVerificationProcessing>(),
        isA<IdCardVerificationFailed>().having(
          (IdCardVerificationFailed p0) => p0.error.code,
          'verify error.code',
          EkycErrorCode.otherEkycError,
        ),
      ],
      verify: (_) {
        verify(() => mockDopNativeEkycUIOnlyRepo.checkIdCard(
              imageType: DOPNativeIDCardImageType.front,
              cardType: fakeCardType,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockCommonApiResponsesHandler.getCheckIdCardError(any())).called(1);

        verifyNever(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(jobId: any(named: 'jobId')));
      },
    );

    blocTest<DOPNativeIdCardFrontSideVerificationCubit, DOPNativeIdCardFrontSideVerificationState>(
      'Check id card success and pollingSubmitStatus success',
      build: () => cubit,
      setUp: () async {
        const String filename = 'dop_native_check_id_card_success.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);

        when(() => mockDopNativeEkycUIOnlyRepo.checkIdCard(
              imageType: any(named: 'imageType'),
              cardType: any(named: 'cardType'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => DOPNativeCheckIDCardEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: responseData,
            )));

        when(() => mockCommonApiResponsesHandler.getCheckIdCardError(any())).thenReturn(null);

        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingSuccessEntity;
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIdCardFrontSideVerificationCubit cubit) => cubit.checkFrontIdCard(
        cardType: fakeCardType,
      ),
      expect: () => <dynamic>[
        isA<IdCardVerificationProcessing>(),
        isA<IdCardVerificationSuccess>(),
      ],
      verify: (_) {
        verify(() => mockDopNativeEkycUIOnlyRepo.checkIdCard(
              imageType: DOPNativeIDCardImageType.front,
              cardType: fakeCardType,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockCommonApiResponsesHandler.getCheckIdCardError(any())).called(1);

        verify(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<DOPNativeIdCardFrontSideVerificationCubit, DOPNativeIdCardFrontSideVerificationState>(
      'Check id card success and pollingSubmitStatus success',
      build: () => cubit,
      setUp: () async {
        const String filename = 'dop_native_check_id_card_success.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);

        when(() => mockDopNativeEkycUIOnlyRepo.checkIdCard(
              imageType: any(named: 'imageType'),
              cardType: any(named: 'cardType'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => DOPNativeCheckIDCardEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: responseData,
            )));

        when(() => mockCommonApiResponsesHandler.getCheckIdCardError(any())).thenReturn(null);

        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingFailureEntity;
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIdCardFrontSideVerificationCubit cubit) => cubit.checkFrontIdCard(
        cardType: fakeCardType,
      ),
      expect: () => <dynamic>[
        isA<IdCardVerificationProcessing>(),
        isA<IdCardVerificationFailed>()
            .having(
              (IdCardVerificationFailed p0) => p0.error.code,
              'error.code == common_error',
              EkycErrorCode.commonError,
            )
            .having(
              (IdCardVerificationFailed p0) => p0.error.message,
              'error.message == entity.data.user_message',
              mockUserMessage,
            ),
      ],
      verify: (_) {
        verify(() => mockDopNativeEkycUIOnlyRepo.checkIdCard(
              imageType: DOPNativeIDCardImageType.front,
              cardType: fakeCardType,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockCommonApiResponsesHandler.getCheckIdCardError(any())).called(1);

        verify(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<DOPNativeIdCardFrontSideVerificationCubit, DOPNativeIdCardFrontSideVerificationState>(
      'Check id card success and pollingSubmitStatus success',
      build: () => cubit,
      setUp: () async {
        const String filename = 'dop_native_check_id_card_success.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);

        when(() => mockDopNativeEkycUIOnlyRepo.checkIdCard(
              imageType: any(named: 'imageType'),
              cardType: any(named: 'cardType'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => DOPNativeCheckIDCardEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.SUCCESS,
              response: responseData,
            )));

        when(() => mockCommonApiResponsesHandler.getCheckIdCardError(any())).thenReturn(null);

        when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return pollingPendingEntity;
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeIdCardFrontSideVerificationCubit cubit) => cubit.checkFrontIdCard(
        cardType: fakeCardType,
      ),
      expect: () => <dynamic>[
        isA<IdCardVerificationProcessing>(),
      ],
      verify: (_) {
        verify(() => mockDopNativeEkycUIOnlyRepo.checkIdCard(
              imageType: DOPNativeIDCardImageType.front,
              cardType: fakeCardType,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockCommonApiResponsesHandler.getCheckIdCardError(any())).called(1);

        verify(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
              jobId: any(named: 'jobId'),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );
  });

  group('Test startProcess', () {
    test('Give cardFrontSideBase64Image NOT NULL, should upload front id card', () {
      when(
        () => mockDopNativeEkycUIOnlyRepo.uploadFrontIdCard(
          cardFrontSideBase64Image: any(named: 'cardFrontSideBase64Image'),
          cardType: any(named: 'cardType'),
          mockConfig: any(named: 'mockConfig'),
        ),
      ).thenAnswer(
        (_) async => DOPNativeUploadIdCardEntity.fromBaseResponse(
          BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_upload_ekyc_card_id_success.json'),
          ),
        ),
      );

      when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
            jobId: any(named: 'jobId'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async {
        return pollingSuccessEntity;
      });

      cubit.startProcess(cardFrontSideBase64Image: 'fake_front_id_card', cardType: fakeCardType);
      verify(() => mockDopNativeEkycUIOnlyRepo.uploadFrontIdCard(
            cardFrontSideBase64Image: 'fake_front_id_card',
            cardType: fakeCardType,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('Give cardFrontSideBase64Image NULL, should check front id card', () {
      registerFallbackValue(DOPNativeIDCardImageType.front);
      registerFallbackValue(DOPNativeCheckIDCardEntity.unserializable());

      when(() => mockDopNativeEkycUIOnlyRepo.checkIdCard(
            imageType: any(named: 'imageType'),
            cardType: any(named: 'cardType'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => DOPNativeCheckIDCardEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('dop_native_check_id_card_success.json'),
          )));

      when(() => mockCommonApiResponsesHandler.getCheckIdCardError(any())).thenReturn(null);

      when(() => mockDopNativeEkycUIOnlyRepo.getSubmitStatus(
            jobId: any(named: 'jobId'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async {
        return pollingSuccessEntity;
      });

      cubit.startProcess(cardType: fakeCardType);

      verify(() => mockDopNativeEkycUIOnlyRepo.checkIdCard(
            imageType: DOPNativeIDCardImageType.front,
            cardType: fakeCardType,
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });
}
