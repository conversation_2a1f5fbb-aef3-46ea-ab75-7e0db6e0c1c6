import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/id_card_front_side_verification/cubit/dop_native_id_card_front_side_verification_cubit.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/ui_model/ekyc_error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeIdCardFrontSideVerificationState', () {
    test('IdCardVerificationFailed holds correct error', () {
      const EkycErrorUIModel error = EkycErrorUIModel(
        code: EkycErrorCode.commonError,
      );
      final IdCardVerificationFailed state = IdCardVerificationFailed(error: error);

      expect(state.error, error);
    });

    test(
        'IdCardVerificationInitial should be an instance of DOPNativeIdCardFrontSideVerificationState',
        () {
      final IdCardVerificationInitial state = IdCardVerificationInitial();
      expect(state, isA<DOPNativeIdCardFrontSideVerificationState>());
    });

    test(
        'IdCardVerificationProcessing should be an instance of DOPNativeIdCardFrontSideVerificationState',
        () {
      final IdCardVerificationProcessing state = IdCardVerificationProcessing();
      expect(state, isA<DOPNativeIdCardFrontSideVerificationState>());
    });

    test(
        'IdCardVerificationSuccess should be an instance of DOPNativeIdCardFrontSideVerificationState',
        () {
      final IdCardVerificationSuccess state = IdCardVerificationSuccess();

      expect(state, isA<DOPNativeIdCardFrontSideVerificationState>());
    });
  });
}
