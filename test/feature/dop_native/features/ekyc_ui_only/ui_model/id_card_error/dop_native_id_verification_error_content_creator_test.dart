import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/ui_model/ekyc_error_ui_model.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/ui_model/id_card_error/dop_native_id_error_content_ui_model.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/ui_model/id_card_error/dop_native_id_verification_error_content_creator.dart';
import 'package:evoapp/feature/dop_native/features/ekyc_ui_only/utils/dop_native_error_content_ui_model.dart';
import 'package:evoapp/feature/dop_native/resources/dop_native_ui_strings.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeEKYCErrorStatusCreator Tests', () {
    test(
        'returns [InvalidImageCapturedUIModel] when ekycError is provided with entity\' user_message',
        () {
      const String mockMessage = 'mock-message';
      const EkycErrorUIModel error =
          EkycErrorUIModel(code: EkycErrorCode.otherEkycError, message: mockMessage);
      final EKYCErrorContentUIModel uiModel = DOPNativeEKYCErrorStatusCreator.create(
        errorUIModel: error,
        onRetry: () {},
      );

      expect(uiModel, isA<InvalidImageCapturedUIModel>());
      expect(uiModel.description, mockMessage);
    });

    test('returns [InvalidImageCapturedUIModel] when ekycError is provided with default message',
        () {
      const EkycErrorUIModel error = EkycErrorUIModel(
        code: EkycErrorCode.otherEkycError,
      );
      final EKYCErrorContentUIModel uiModel = DOPNativeEKYCErrorStatusCreator.create(
        errorUIModel: error,
        onRetry: () {},
      );

      expect(uiModel, isA<InvalidImageCapturedUIModel>());
      expect(uiModel.description, DOPNativeStrings.dopNativeDefaultEkycErrorMessage);
    });

    test('returns [InvalidIdCardUIModel] when cardTypeNotSupported is provided', () {
      const EkycErrorUIModel error = EkycErrorUIModel(
        code: EkycErrorCode.cardTypeNotSupported,
      );
      final EKYCErrorContentUIModel uiModel = DOPNativeEKYCErrorStatusCreator.create(
        errorUIModel: error,
        onRetry: () {},
      );
      expect(uiModel, isA<InvalidIdCardUIModel>());
    });
  });
}
