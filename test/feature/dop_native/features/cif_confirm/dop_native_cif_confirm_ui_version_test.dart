import 'package:evoapp/feature/dop_native/features/cif_confirm/dop_native_cif_confirm_ui_version.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Test enum values', () {
    expect(DOPNativeCifConfirmUIVersion.difPhone.value, 'v9.4.4.0');
    expect(DOPNativeCifConfirmUIVersion.difCif.value, 'v9.4.5.0');
    expect(DOPNativeCifConfirmUIVersion.difNationId.value, 'v9.4.6.0');
    expect(DOPNativeCifConfirmUIVersion.difInfo.value, 'v9.4.7.0');
    expect(DOPNativeCifConfirmUIVersion.cifReopen.value, 'v9.4.8.0');
    expect(DOPNativeCifConfirmUIVersion.cifNoBranch.value, 'v9.4.9.0');
  });
}
