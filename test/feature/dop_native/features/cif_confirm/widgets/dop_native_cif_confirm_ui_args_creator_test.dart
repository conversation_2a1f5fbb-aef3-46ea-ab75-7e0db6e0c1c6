import 'package:evoapp/feature/dop_native/features/cif_confirm/dop_native_cif_confirm_case.dart';
import 'package:evoapp/feature/dop_native/features/cif_confirm/widgets/dop_native_cif_confirm_ui_args.dart';
import 'package:evoapp/feature/dop_native/features/cif_confirm/widgets/dop_native_cif_confirm_ui_args_creator.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  verifyCreatedObj(
    DOPNativeCifConfirmUIArgs? createdObj,
    DOPNativeCifConfirmUIArgs? compareObj,
  ) {
    expect(createdObj?.title, compareObj?.title);
    expect(createdObj?.subtitle, compareObj?.subtitle);
    expect(createdObj?.notes, compareObj?.notes);
    expect(createdObj?.showOpenCardCTA, compareObj?.showOpenCardCTA);
    expect(createdObj?.showConfirmInfoUpdated, compareObj?.showConfirmInfoUpdated);
    expect(createdObj?.showViewNearestBranches, compareObj?.showViewNearestBranches);
  }

  test(
      'create [DOPNativeCifConfirmUIArgs.difNationIdWithCifInfo] when CifCase is difNationIdWithCifInfo',
      () {
    final DOPNativeCifConfirmUIArgsCreator creator = DOPNativeCifConfirmUIArgsCreator();

    final DOPNativeCifConfirmUIArgs? createdObj = creator.create(CifCase.difNationIdWithCifInfo);
    final DOPNativeCifConfirmUIArgs compareObj = DOPNativeCifConfirmUIArgs.difNationIdWithCifInfo();

    verifyCreatedObj(createdObj, compareObj);
  });

  test('create [DOPNativeCifConfirmUIArgs.otherDifWithCifInfo] when CifCase is otherDifWithCifInfo',
      () {
    final DOPNativeCifConfirmUIArgsCreator creator = DOPNativeCifConfirmUIArgsCreator();

    final DOPNativeCifConfirmUIArgs? createdObj = creator.create(CifCase.otherDifWithCifInfo);
    final DOPNativeCifConfirmUIArgs compareObj = DOPNativeCifConfirmUIArgs.otherDifWithCifInfo();

    verifyCreatedObj(createdObj, compareObj);
  });

  test(
      'create [DOPNativeCifConfirmUIArgs.difNationIdWithoutCifInfo] when CifCase is difNationIdWithoutCifInfo',
      () {
    final DOPNativeCifConfirmUIArgsCreator creator = DOPNativeCifConfirmUIArgsCreator();

    final DOPNativeCifConfirmUIArgs? createdObj = creator.create(CifCase.difNationIdWithoutCifInfo);
    final DOPNativeCifConfirmUIArgs compareObj =
        DOPNativeCifConfirmUIArgs.difNationIdWithoutCifInfo();

    verifyCreatedObj(createdObj, compareObj);
  });

  test('create [DOPNativeCifConfirmUIArgs.difPhone] when CifCase is difPhone', () {
    final DOPNativeCifConfirmUIArgsCreator creator = DOPNativeCifConfirmUIArgsCreator();

    final DOPNativeCifConfirmUIArgs? createdObj = creator.create(CifCase.difPhone);
    final DOPNativeCifConfirmUIArgs compareObj = DOPNativeCifConfirmUIArgs.difPhone();

    verifyCreatedObj(createdObj, compareObj);
  });

  test('create [DOPNativeCifConfirmUIArgs.difCif] when CifCase is difCif', () {
    final DOPNativeCifConfirmUIArgsCreator creator = DOPNativeCifConfirmUIArgsCreator();

    final DOPNativeCifConfirmUIArgs? createdObj = creator.create(CifCase.difCif);
    final DOPNativeCifConfirmUIArgs compareObj = DOPNativeCifConfirmUIArgs.difCif();

    verifyCreatedObj(createdObj, compareObj);
  });

  test('create [DOPNativeCifConfirmUIArgs.difInfo] when CifCase is difInfo', () {
    final DOPNativeCifConfirmUIArgsCreator creator = DOPNativeCifConfirmUIArgsCreator();

    final DOPNativeCifConfirmUIArgs? createdObj = creator.create(CifCase.difInfo);
    final DOPNativeCifConfirmUIArgs compareObj = DOPNativeCifConfirmUIArgs.difInfo();

    verifyCreatedObj(createdObj, compareObj);
  });

  test('create [DOPNativeCifConfirmUIArgs.cifReopen] when CifCase is cifReopen', () {
    final DOPNativeCifConfirmUIArgsCreator creator = DOPNativeCifConfirmUIArgsCreator();

    final DOPNativeCifConfirmUIArgs? createdObj = creator.create(CifCase.cifReopen);
    final DOPNativeCifConfirmUIArgs compareObj = DOPNativeCifConfirmUIArgs.cifReopen();

    verifyCreatedObj(createdObj, compareObj);
  });

  test('create null when CifCase is unknown', () {
    final DOPNativeCifConfirmUIArgsCreator creator = DOPNativeCifConfirmUIArgsCreator();

    expect(creator.create(CifCase.unknown), isNull);
  });
}
