import 'package:evoapp/data/response/dop_native/dop_native_application_form_data_entity.dart';
import 'package:evoapp/feature/dop_native/features/additional_form/models/additional_form_data_model.dart';
import 'package:evoapp/feature/dop_native/features/additional_form/models/additional_form_step_model.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../../util/test_util.dart';

void main() {
  late DOPNativeApplicationFormDataEntity fakeFormDataEntity;

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();

    fakeFormDataEntity = DOPNativeApplicationFormDataEntity.fromBaseResponse(BaseResponse(
      statusCode: CommonHttpClient.SUCCESS,
      response: await TestUtil.getResponseMock('dop_native_get_application_form_data_success.json'),
    ));
  });

  group('DOPNativeAdditionalFormDataModel', () {
    final DOPNativeAdditionalFormStepModel formStepModel =
        DOPNativeAdditionalFormStepModel.secretQuestionStep();

    test('should create a model with provided form data entity and step model', () {
      final DOPNativeAdditionalFormDataModel model = DOPNativeAdditionalFormDataModel(
        formDataEntity: fakeFormDataEntity,
        formStepModel: formStepModel,
      );

      expect(model.formDataEntity, fakeFormDataEntity);
      expect(model.formStepModel, formStepModel);
    });

    test('copyWith should create a new model with updated fields', () {
      final DOPNativeAdditionalFormDataModel originalModel = DOPNativeAdditionalFormDataModel(
        formDataEntity: fakeFormDataEntity,
        formStepModel: formStepModel,
      );

      final DOPNativeAdditionalFormStepModel updatedFormStepModel =
          DOPNativeAdditionalFormStepModel.emergencyContactStep();

      final DOPNativeAdditionalFormDataModel updatedModel = originalModel.copyWith(
        formDataEntityValue: fakeFormDataEntity,
        formStepValue: updatedFormStepModel,
      );

      expect(updatedModel.formDataEntity, fakeFormDataEntity);
      expect(updatedModel.formStepModel, updatedFormStepModel);

      expect(originalModel.formDataEntity, fakeFormDataEntity);
      expect(originalModel.formStepModel, formStepModel);
    });
  });
}
