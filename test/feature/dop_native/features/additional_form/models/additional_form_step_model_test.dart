import 'package:evoapp/feature/dop_native/features/additional_form/models/additional_form_step_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AdditionalFormStepModel', () {
    test('secretQuestionStep factory should create the correct instance', () {
      final DOPNativeAdditionalFormStepModel model =
          DOPNativeAdditionalFormStepModel.secretQuestionStep();

      expect(model.currentStep, DOPNativeFormStep.secretQuestion);
      expect(model.nextStep, DOPNativeFormStep.address);
      expect(model.previousStep, DOPNativeFormStep.unknown);
      expect(model.stepIndex, 1);
    });

    test('addressStep factory should create the correct instance', () {
      final DOPNativeAdditionalFormStepModel model = DOPNativeAdditionalFormStepModel.addressStep();

      expect(model.currentStep, DOPNativeFormStep.address);
      expect(model.nextStep, DOPNativeFormStep.emergencyContact);
      expect(model.previousStep, DOPNativeFormStep.secretQuestion);
      expect(model.stepIndex, 2);
    });

    test('emergencyContactStep factory should create the correct instance', () {
      final DOPNativeAdditionalFormStepModel model =
          DOPNativeAdditionalFormStepModel.emergencyContactStep();

      expect(model.currentStep, DOPNativeFormStep.emergencyContact);
      expect(model.nextStep, DOPNativeFormStep.subscribeChannel);
      expect(model.previousStep, DOPNativeFormStep.address);
      expect(model.stepIndex, 3);
    });

    test('subscribeChannelStep factory should create the correct instance', () {
      final DOPNativeAdditionalFormStepModel model =
          DOPNativeAdditionalFormStepModel.subscribeChannelStep();

      expect(model.currentStep, DOPNativeFormStep.subscribeChannel);
      expect(model.nextStep, DOPNativeFormStep.unknown);
      expect(model.previousStep, DOPNativeFormStep.emergencyContact);
      expect(model.stepIndex, 4);
    });

    test('Equatable props should contain all fields that determine equality', () {
      final DOPNativeAdditionalFormStepModel model =
          DOPNativeAdditionalFormStepModel.secretQuestionStep();

      expect(model.props, <Object?>[model.currentStep, model.nextStep, model.previousStep]);
    });
  });
}
