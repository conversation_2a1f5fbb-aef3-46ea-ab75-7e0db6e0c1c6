import 'package:evoapp/feature/dop_native/features/additional_form/models/additional_form_data_model.dart';
import 'package:evoapp/feature/dop_native/features/additional_form/models/additional_form_step_model.dart';
import 'package:evoapp/feature/dop_native/features/additional_form/widgets/secret_question/cubit/dop_native_secret_question_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeSecretQuestionState subclasses', () {
    test('is an instance of DOPNativeSecretQuestionState', () {
      expect(DOPNativeSecretQuestionInitial(), isA<DOPNativeSecretQuestionState>());
      expect(DOPNativeSecretQuestionLoading(), isA<DOPNativeSecretQuestionState>());
      expect(
          OnSubmitSucceed(
            DOPNativeAdditionalFormDataModel(
              formDataEntity: null,
              formStepModel: const DOPNativeAdditionalFormStepModel(
                currentStep: DOPNativeFormStep.unknown,
                nextStep: null,
                previousStep: null,
                stepIndex: 0,
              ),
            ),
          ),
          isA<DOPNativeSecretQuestionState>());
      expect(
        OnSubmitFailed(ErrorUIModel()),
        isA<DOPNativeSecretQuestionState>(),
      );
      expect(
        HasValidInput(),
        isA<DOPNativeSecretQuestionState>(),
      );
      expect(
        HasInvalidInput(''),
        isA<DOPNativeSecretQuestionState>(),
      );
    });
  });

  group('DOPNativeSecretQuestionState should hold correct data', () {
    test('HasInvalidInput should contain an error message', () {
      const String errorMsg = 'invalid-message';
      final HasInvalidInput state = HasInvalidInput(errorMsg);
      expect(state.errorMsg, errorMsg);
    });

    test('FormSubmitSucceed state should contain DOPNativeReferenceInfoEntity', () {
      final DOPNativeAdditionalFormDataModel model = DOPNativeAdditionalFormDataModel(
        formDataEntity: null,
        formStepModel: const DOPNativeAdditionalFormStepModel(
          currentStep: DOPNativeFormStep.unknown,
          nextStep: null,
          previousStep: null,
          stepIndex: 0,
        ),
      );
      final OnSubmitSucceed state = OnSubmitSucceed(
        model,
      );

      expect(state.model, model);
    });

    test('OnSubmitFailed should contain ErrorUIModel error', () {
      final ErrorUIModel error = ErrorUIModel();
      final OnSubmitFailed state = OnSubmitFailed(error);
      expect(state.error, error);
    });
  });
}
