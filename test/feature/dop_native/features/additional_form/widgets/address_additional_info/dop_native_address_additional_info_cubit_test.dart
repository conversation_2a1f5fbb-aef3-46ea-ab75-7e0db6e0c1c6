import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/request/dop_native/dop_native_application_submit_form_request.dart';
import 'package:evoapp/data/response/dop_native/dop_native_application_form_data_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_application_state_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_contact_info_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_metadata_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_metadata_item_entity.dart';
import 'package:evoapp/feature/dop_native/features/additional_form/models/additional_form_data_model.dart';
import 'package:evoapp/feature/dop_native/features/additional_form/models/additional_form_step_model.dart';
import 'package:evoapp/feature/dop_native/features/additional_form/widgets/address_additional_info/cubit/dop_native_address_additional_info_cubit.dart';
import 'package:evoapp/feature/dop_native/util/metadata/dop_native_metadata_utils.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../constant.dart';
import '../../../../../../util/test_util.dart';

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

class MockDOPNativeMetadataUtils extends Mock implements DOPNativeMetadataUtils {}

void main() {
  late DOPNativeAddressAdditionalInfoCubit cubit;
  late DOPNativeRepo mockDopNativeRepo;
  late DOPNativeMetadataUtils mockMetadataUtils;
  late AppState mockAppState;
  late DOPNativeMetadataEntity fakeCardDeliveryType;

  const String fakeDopApplicationState = 'app_form.additional_info';
  const String fakeCurrentStep = 'af.addinfo.address';
  const String fakeCompanyName = 'CÔNG TY TNHH TRUSTING SOCIAL';
  const String fakeCurAddress = '347 Lê Văn Thọ132 Hàm Nghi';
  const String fakeCompanyAddressProvinceId = ' 79';
  const String fakeCompanyAddressDistId = ' 760';
  const String fakeCompanyAddressWardId = ' 26743';
  const String fakeCompanyAddress = ' 132 Hàm Nghi';
  const String fakePickupCardAddress = ' 001';

  const DOPNativeMetadataItemEntity fakeProvinceOfCompany = DOPNativeMetadataItemEntity(
    code: fakeCompanyAddressProvinceId,
    name: 'fakeProvinceOfCompany',
  );
  const DOPNativeMetadataItemEntity fakeDistrictOfCompany = DOPNativeMetadataItemEntity(
    code: fakeCompanyAddressDistId,
    name: 'fakeDistrictOfCompany',
  );
  const DOPNativeMetadataItemEntity fakeWardOfCompany = DOPNativeMetadataItemEntity(
    code: fakeCompanyAddressWardId,
    name: 'fakeWardOfCompany',
  );

  const DOPNativeContactInfoEntity fakeContactInfo = DOPNativeContactInfoEntity(
    companyName: fakeCompanyName,
    curAddress: fakeCurAddress,
    companyAddressProvinceId: fakeCompanyAddressProvinceId,
    companyAddressDistId: fakeCompanyAddressDistId,
    companyAddressWardId: fakeCompanyAddressWardId,
    companyAddress: fakeCompanyAddress,
    pickupCardAddress: fakePickupCardAddress,
  );

  final DOPNativeApplicationSubmitFormRequest formDataRequest =
      DOPNativeApplicationSubmitFormRequest(
    formAppState: fakeDopApplicationState,
    changeState: false,
    formStep: fakeCurrentStep,
    formData: ApplicationFormData(
      contactInfo: fakeContactInfo,
    ),
  );

  final DOPNativeAdditionalFormStepModel formStepModel =
      DOPNativeAdditionalFormStepModel.addressStep();
  late DOPNativeApplicationFormDataEntity formDataEntity;
  late DOPNativeAdditionalFormDataModel fakeModel;

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(DOPNativeApplicationSubmitFormRequest());

    mockMetadataUtils = MockDOPNativeMetadataUtils();

    getIt.registerSingleton<DOPNativeRepo>(MockDOPNativeRepo());
    mockDopNativeRepo = getIt.get<DOPNativeRepo>();

    fakeCardDeliveryType = DOPNativeMetadataEntity.fromJson(BaseResponse(
      statusCode: CommonHttpClient.SUCCESS,
      response: await TestUtil.getResponseMock('dop_native_get_card_delivery_type_success.json'),
    ));

    formDataEntity = DOPNativeApplicationFormDataEntity.fromBaseResponse(BaseResponse(
      statusCode: CommonHttpClient.SUCCESS,
      response: await TestUtil.getResponseMock('dop_native_get_application_form_data_success.json'),
    ));

    fakeModel = DOPNativeAdditionalFormDataModel(
      formDataEntity: formDataEntity,
      formStepModel: formStepModel,
    );
  });

  setUp(() {
    mockAppState = AppState();
    mockAppState.dopNativeState.dopApplicationState = DOPNativeApplicationStateEntity(
      currentStep: fakeDopApplicationState,
    );
    cubit = DOPNativeAddressAdditionalInfoCubit(
      dopNativeRepo: mockDopNativeRepo,
      metadataUtils: mockMetadataUtils,
      appState: mockAppState,
    );
  });

  tearDown(() {
    reset(mockDopNativeRepo);
    reset(mockMetadataUtils);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('verify submitAddressAdditionalInfo()', () {
    blocTest<DOPNativeAddressAdditionalInfoCubit, DOPNativeAddressAdditionalInfoState>(
      'emits DOPNativeAddressAdditionalInfoSucceed when submitAddressAdditionalInfo is successful',
      setUp: () {
        cubit.contactInfo = fakeContactInfo;

        when(
          () => mockDopNativeRepo.submitApplicationForm(
            form: any(named: 'form'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer(
          (_) async => BaseEntity(statusCode: CommonHttpClient.SUCCESS),
        );
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (_) => cubit.submitAddressAdditionalInfo(model: fakeModel),
      expect: () => <dynamic>[
        isA<DOPNativeAddressAdditionalInfoLoading>(),
        isA<DOPNativeAddressAdditionalInfoSucceed>(),
      ],
      verify: (_) {
        expect(
          verify(
            () => mockDopNativeRepo.submitApplicationForm(
              form: captureAny(named: 'form'),
              mockConfig: any(named: 'mockConfig'),
            ),
          ).captured.single,
          isA<DOPNativeApplicationSubmitFormRequest>()
              .having(
                (DOPNativeApplicationSubmitFormRequest request) => request.formAppState,
                'verify formAppState',
                formDataRequest.formAppState,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) => request.changeState,
                'verify changeState',
                false,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) => request.formStep,
                'verify formStep',
                fakeModel.formStepModel.currentStep.value,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) =>
                    request.formData?.contactInfo?.companyName,
                'verify companyName',
                formDataRequest.formData?.contactInfo?.companyName,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) =>
                    request.formData?.contactInfo?.curAddress,
                'verify curAddress',
                formDataRequest.formData?.contactInfo?.curAddress,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) =>
                    request.formData?.contactInfo?.companyAddressProvinceId,
                'verify companyAddressProvinceId',
                formDataRequest.formData?.contactInfo?.companyAddressProvinceId,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) =>
                    request.formData?.contactInfo?.companyAddressDistId,
                'verify companyAddressDistId',
                formDataRequest.formData?.contactInfo?.companyAddressDistId,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) =>
                    request.formData?.contactInfo?.companyAddressWardId,
                'verify companyAddressWardId',
                formDataRequest.formData?.contactInfo?.companyAddressWardId,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) =>
                    request.formData?.contactInfo?.companyAddress,
                'verify companyAddress',
                formDataRequest.formData?.contactInfo?.companyAddress,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) =>
                    request.formData?.contactInfo?.pickupCardAddress,
                'verify pickupCardAddress',
                formDataRequest.formData?.contactInfo?.pickupCardAddress,
              ),
        );
      },
    );

    blocTest<DOPNativeAddressAdditionalInfoCubit, DOPNativeAddressAdditionalInfoState>(
      'emits DOPNativeAddressAdditionalInfoSucceed when submitAddressAdditionalInfo is successful',
      setUp: () {
        cubit.contactInfo = fakeContactInfo;

        when(
          () => mockDopNativeRepo.submitApplicationForm(
            form: any(named: 'form'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer(
          (_) async => BaseEntity(statusCode: CommonHttpClient.BAD_REQUEST),
        );
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (_) => cubit.submitAddressAdditionalInfo(model: fakeModel),
      expect: () => <dynamic>[
        isA<DOPNativeAddressAdditionalInfoLoading>(),
        isA<DOPNativeAddressAdditionalInfoFailed>().having(
          (DOPNativeAddressAdditionalInfoFailed state) => state.error.statusCode,
          'verify statusCode',
          CommonHttpClient.BAD_REQUEST,
        ),
      ],
      verify: (_) {
        expect(
          verify(
            () => mockDopNativeRepo.submitApplicationForm(
              form: captureAny(named: 'form'),
              mockConfig: any(named: 'mockConfig'),
            ),
          ).captured.single,
          isA<DOPNativeApplicationSubmitFormRequest>()
              .having(
                (DOPNativeApplicationSubmitFormRequest request) => request.formAppState,
                'verify formAppState',
                formDataRequest.formAppState,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) => request.changeState,
                'verify changeState',
                false,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) => request.formStep,
                'verify formStep',
                fakeModel.formStepModel.currentStep.value,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) =>
                    request.formData?.contactInfo?.companyName,
                'verify companyName',
                formDataRequest.formData?.contactInfo?.companyName,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) =>
                    request.formData?.contactInfo?.curAddress,
                'verify curAddress',
                formDataRequest.formData?.contactInfo?.curAddress,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) =>
                    request.formData?.contactInfo?.companyAddressProvinceId,
                'verify companyAddressProvinceId',
                formDataRequest.formData?.contactInfo?.companyAddressProvinceId,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) =>
                    request.formData?.contactInfo?.companyAddressDistId,
                'verify companyAddressDistId',
                formDataRequest.formData?.contactInfo?.companyAddressDistId,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) =>
                    request.formData?.contactInfo?.companyAddressWardId,
                'verify companyAddressWardId',
                formDataRequest.formData?.contactInfo?.companyAddressWardId,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) =>
                    request.formData?.contactInfo?.companyAddress,
                'verify companyAddress',
                formDataRequest.formData?.contactInfo?.companyAddress,
              )
              .having(
                (DOPNativeApplicationSubmitFormRequest request) =>
                    request.formData?.contactInfo?.pickupCardAddress,
                'verify pickupCardAddress',
                formDataRequest.formData?.contactInfo?.pickupCardAddress,
              ),
        );
      },
    );
  });

  group('verify getCardDeliveryType()', () {
    blocTest<DOPNativeAddressAdditionalInfoCubit, DOPNativeAddressAdditionalInfoState>(
      'emits DOPNativeGetCardDeliveryTypeSucceed when getCardDeliveryType is successful',
      setUp: () {
        when(
          () => mockMetadataUtils.getCardDeliveryType(),
        ).thenAnswer(
          (_) async => fakeCardDeliveryType,
        );
      },
      build: () => cubit,
      act: (_) => cubit.getCardDeliveryType(),
      expect: () => <dynamic>[
        isA<DOPNativeGetCardDeliveryTypeSucceed>()
            .having(
              (DOPNativeGetCardDeliveryTypeSucceed state) => state.cardDeliveryTypes.statusCode,
              'verify statusCode',
              CommonHttpClient.SUCCESS,
            )
            .having(
              (DOPNativeGetCardDeliveryTypeSucceed state) => state.cardDeliveryTypes,
              'verify cardDeliveryTypes',
              isNotNull,
            ),
      ],
      verify: (_) {
        verify(() => mockMetadataUtils.getCardDeliveryType()).called(1);
      },
    );

    blocTest<DOPNativeAddressAdditionalInfoCubit, DOPNativeAddressAdditionalInfoState>(
      'emits DOPNativeGetCardDeliveryTypeFailed when getCardDeliveryType is failed',
      setUp: () {
        when(
          () => mockMetadataUtils.getCardDeliveryType(),
        ).thenAnswer(
          (_) async => DOPNativeMetadataEntity.fromJson(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )),
        );
      },
      build: () => cubit,
      act: (_) => cubit.getCardDeliveryType(),
      expect: () => <dynamic>[
        isA<DOPNativeGetCardDeliveryTypeFailed>().having(
          (DOPNativeGetCardDeliveryTypeFailed state) => state.error.statusCode,
          'verify statusCode',
          CommonHttpClient.BAD_REQUEST,
        ),
      ],
      verify: (_) {
        verify(() => mockMetadataUtils.getCardDeliveryType()).called(1);
      },
    );
  });

  group('verify getCurrentResidenceAddressName()', () {
    const String fakeResidenceAddressName = 'fakeResidenceAddressName';

    blocTest<DOPNativeAddressAdditionalInfoCubit, DOPNativeAddressAdditionalInfoState>(
      'emits DOPNativeGetResidenceAddressNameSucceed when getResidenceAddressName is successful',
      setUp: () {
        when(
          () => mockMetadataUtils.getResidenceAddressName(
            provinceCode: any(named: 'provinceCode'),
            districtCode: any(named: 'districtCode'),
            wardCode: any(named: 'wardCode'),
          ),
        ).thenAnswer(
          (_) async => fakeResidenceAddressName,
        );
      },
      build: () => cubit,
      act: (_) => cubit.getCurrentResidenceAddressName(
        provinceCode: fakeCompanyAddressProvinceId,
        districtCode: fakeCompanyAddressDistId,
        wardCode: fakeCompanyAddressWardId,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeGetResidenceAddressNameSucceed>().having(
          (DOPNativeGetResidenceAddressNameSucceed state) => state.residenceAddressName,
          'verify residenceAddressName',
          fakeResidenceAddressName,
        ),
      ],
      verify: (_) {
        verify(() => mockMetadataUtils.getResidenceAddressName(
              provinceCode: fakeCompanyAddressProvinceId,
              districtCode: fakeCompanyAddressDistId,
              wardCode: fakeCompanyAddressWardId,
            )).called(1);
      },
    );

    const String fakeEmptyResidenceAddressName = '';

    blocTest<DOPNativeAddressAdditionalInfoCubit, DOPNativeAddressAdditionalInfoState>(
      'emits DOPNativeGetResidenceAddressNameSucceed when getResidenceAddressName is failed',
      setUp: () {
        when(
          () => mockMetadataUtils.getResidenceAddressName(
            provinceCode: any(named: 'provinceCode'),
            districtCode: any(named: 'districtCode'),
            wardCode: any(named: 'wardCode'),
          ),
        ).thenAnswer(
          (_) async => fakeEmptyResidenceAddressName,
        );
      },
      build: () => cubit,
      act: (_) => cubit.getCurrentResidenceAddressName(
        provinceCode: fakeCompanyAddressProvinceId,
        districtCode: fakeCompanyAddressDistId,
        wardCode: fakeCompanyAddressWardId,
      ),
      expect: () => <dynamic>[
        isA<DOPNativeGetResidenceAddressNameFailed>().having(
          (DOPNativeGetResidenceAddressNameFailed state) => state.error,
          'verify error',
          isA<ErrorUIModel>(),
        ),
      ],
      verify: (_) {
        verify(() => mockMetadataUtils.getResidenceAddressName(
              provinceCode: fakeCompanyAddressProvinceId,
              districtCode: fakeCompanyAddressDistId,
              wardCode: fakeCompanyAddressWardId,
            )).called(1);
      },
    );
  });

  group('verify getResidenceAddressName', () {
    test('verify getResidenceAddressName() return value', () async {
      when(
        () => mockMetadataUtils.getResidenceAddressName(
          provinceCode: any(named: 'provinceCode'),
          districtCode: any(named: 'districtCode'),
          wardCode: any(named: 'wardCode'),
        ),
      ).thenAnswer(
        (_) async => fakeCurAddress,
      );

      final String result = await cubit.getResidenceAddressName(
        provinceCode: fakeCompanyAddressProvinceId,
        districtCode: fakeCompanyAddressDistId,
        wardCode: fakeCompanyAddressWardId,
      );

      expect(result, fakeCurAddress);

      verify(() => mockMetadataUtils.getResidenceAddressName(
            provinceCode: fakeCompanyAddressProvinceId,
            districtCode: fakeCompanyAddressDistId,
            wardCode: fakeCompanyAddressWardId,
          )).called(1);
    });
  });

  test('verify setContactInfoItem', () {
    const DOPNativeContactInfoEntity contactInfoEntity = DOPNativeContactInfoEntity(
      companyName: fakeCompanyName,
      curAddress: fakeCurAddress,
      companyAddressProvinceId: fakeCompanyAddressProvinceId,
      companyAddressDistId: fakeCompanyAddressDistId,
      companyAddressWardId: fakeCompanyAddressWardId,
      companyAddress: fakeCompanyAddress,
      pickupCardAddress: fakePickupCardAddress,
    );

    cubit.contactInfo = contactInfoEntity;

    const String fakeStreetOfCurrentAddress = 'fakeStreetOfCurrentAddress';
    const String fakeStreetOfCompanyAddress = 'fakeStreetOfCompanyAddress';
    const String fakePickupCardAddress2 = 'pickupCardAddress2';

    cubit.setContactInfoItem(
      streetOfCurrentAddress: fakeStreetOfCurrentAddress,
      companyName: fakeCompanyName,
      streetOfCompanyAddress: fakeStreetOfCompanyAddress,
      pickupCardAddress: fakePickupCardAddress2,
      provinceOfCompanyCode: fakeCompanyAddressProvinceId,
      districtOfCompanyCode: fakeCompanyAddressDistId,
      wardOfCompanyCode: fakeCompanyAddressWardId,
    );

    final DOPNativeContactInfoEntity contactInfoResult = cubit.contactInfo;
    expect(contactInfoResult.curAddress, fakeStreetOfCurrentAddress);
    expect(contactInfoResult.companyName, fakeCompanyName);
    expect(contactInfoResult.companyAddress, fakeStreetOfCompanyAddress);
    expect(contactInfoResult.pickupCardAddress, fakePickupCardAddress2);
    expect(contactInfoResult.companyAddressProvinceId, fakeCompanyAddressProvinceId);
    expect(contactInfoResult.companyAddressDistId, fakeCompanyAddressDistId);
    expect(contactInfoResult.companyAddressWardId, fakeCompanyAddressWardId);
  });

  test('verify saveCompanyAddress()', () {
    const DOPNativeContactInfoEntity contactInfoEntity = DOPNativeContactInfoEntity();
    cubit.contactInfo = contactInfoEntity;

    cubit.saveCompanyAddress(
      province: fakeProvinceOfCompany,
      district: fakeDistrictOfCompany,
      ward: fakeWardOfCompany,
    );

    final DOPNativeContactInfoEntity contactInfoResult = cubit.contactInfo;
    expect(contactInfoResult.companyAddressProvinceId, fakeCompanyAddressProvinceId);
    expect(contactInfoResult.companyAddressDistId, fakeCompanyAddressDistId);
    expect(contactInfoResult.companyAddressWardId, fakeCompanyAddressWardId);
  });

  group('verify validateForm()', () {
    blocTest<DOPNativeAddressAdditionalInfoCubit, DOPNativeAddressAdditionalInfoState>(
      'emits HasValidForm',
      build: () => cubit,
      act: (_) => cubit.validateForm(
        streetOfCurrentAddress: fakeCurAddress,
        companyName: fakeCompanyName,
        companyAddress: fakeCompanyAddress,
        streetOfCompanyAddress: fakeCompanyAddress,
        pickupCardAddress: fakePickupCardAddress,
      ),
      expect: () => <dynamic>[
        isA<HasValidForm>(),
      ],
    );

    blocTest<DOPNativeAddressAdditionalInfoCubit, DOPNativeAddressAdditionalInfoState>(
      'emits HasInvalidForm',
      build: () => cubit,
      act: (_) => cubit.validateForm(
        streetOfCurrentAddress: '',
        companyName: '',
        companyAddress: '',
        streetOfCompanyAddress: '',
        pickupCardAddress: '',
      ),
      expect: () => <dynamic>[
        isA<HasInvalidForm>(),
      ],
    );
  });

  group('verify emitStateForm()', () {
    blocTest<DOPNativeAddressAdditionalInfoCubit, DOPNativeAddressAdditionalInfoState>(
      'emits HasInvalidForm',
      build: () => cubit,
      act: (_) => cubit.emitStateForm(false),
      expect: () => <dynamic>[
        isA<HasInvalidForm>(),
      ],
    );

    blocTest<DOPNativeAddressAdditionalInfoCubit, DOPNativeAddressAdditionalInfoState>(
      'emits HasValidForm',
      build: () => cubit,
      act: (_) => cubit.emitStateForm(true),
      expect: () => <dynamic>[
        isA<HasValidForm>(),
      ],
    );
  });

  group('updateFormDataModel', () {
    late DOPNativeAdditionalFormDataModel model = DOPNativeAdditionalFormDataModel(
      formDataEntity: null,
      formStepModel: const DOPNativeAdditionalFormStepModel(
        currentStep: DOPNativeFormStep.address,
        nextStep: null,
        previousStep: null,
        stepIndex: 0,
      ),
    );

    setUp(() {
      model = DOPNativeAdditionalFormDataModel(
        formDataEntity: null,
        formStepModel: const DOPNativeAdditionalFormStepModel(
          currentStep: DOPNativeFormStep.address,
          nextStep: null,
          previousStep: null,
          stepIndex: 0,
        ),
      );
    });

    test('formDataEntity.formData be created if null', () {
      final DOPNativeAdditionalFormDataModel nextModel = cubit.updateFormDataModel(
        model: model,
        contactInfo: const DOPNativeContactInfoEntity(),
      );

      expect(
        nextModel.formDataEntity?.formData,
        isNotNull,
      );
    });

    test('updated with correct data', () {
      const DOPNativeContactInfoEntity contactInfoEntity = DOPNativeContactInfoEntity(
        companyName: fakeCompanyName,
        curAddress: fakeCurAddress,
        companyAddressProvinceId: fakeCompanyAddressProvinceId,
        companyAddressDistId: fakeCompanyAddressDistId,
        companyAddressWardId: fakeCompanyAddressWardId,
        companyAddress: fakeCompanyAddress,
        pickupCardAddress: fakePickupCardAddress,
      );

      final DOPNativeAdditionalFormDataModel nextModel = cubit.updateFormDataModel(
        model: model,
        contactInfo: contactInfoEntity,
      );

      expect(nextModel.formDataEntity?.formData?.contactInfo, contactInfoEntity);
    });
  });
}
