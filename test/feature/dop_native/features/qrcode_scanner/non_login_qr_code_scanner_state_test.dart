import 'package:evoapp/feature/dop_native/features/qrcode_scanner/non_login_qr_code_scanner_cubit.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('NonLoginQrCodeScannerState should be instantiated', () {
    final NonLoginQrCodeScannerState initialState = NonLoginQrCodeScannerInitialState();
    expect(initialState, isA<NonLoginQrCodeScannerState>());
  });

  test('NonLoginValidQrCodeState should be instantiated with correct properties', () {
    final NonLoginValidQrCodeState validState = NonLoginValidQrCodeState();
    expect(validState, isA<NonLoginQrCodeScannerState>());
  });

  test('NonLoginInvalidQrCodeState should be instantiated', () {
    final NonLoginInvalidQrCodeState invalidState = NonLoginInvalidQrCodeState();
    expect(invalidState, isA<NonLoginQrCodeScannerState>());
  });

  test('NonLoginDetectGoEvoUrlQrCodeState should be instantiated', () {
    final NonLoginDetectGoEvoUrlQrCodeState goEvoUrlState =
        NonLoginDetectGoEvoUrlQrCodeState('fake URL');
    expect(goEvoUrlState, isA<NonLoginQrCodeScannerState>());
  });
}
