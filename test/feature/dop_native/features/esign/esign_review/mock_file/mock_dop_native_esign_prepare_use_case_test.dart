import 'package:evoapp/feature/dop_native/features/esign/esign_review/mock_file/mock_dop_native_esign_prepare_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('enum value MockDOPNativeESignPrepareUseCase', () {
    expect(
      MockDOPNativeESignPrepareUseCase.success.value,
      'dop_native_prepare_esign_success.json',
    );
    expect(
      MockDOPNativeESignPrepareUseCase.invalidState.value,
      'dop_native_prepare_esign_invalid_state.json',
    );
  });

  test('getMockDOPNativeESignPrepare returns correct values', () {
    expect(
      getMockDOPNativeESignPrepare(MockDOPNativeESignPrepareUseCase.success),
      'dop_native_prepare_esign_success.json',
    );
    expect(
      getMockDOPNativeESignPrepare(MockDOPNativeESignPrepareUseCase.invalidState),
      'dop_native_prepare_esign_invalid_state.json',
    );
  });
}
