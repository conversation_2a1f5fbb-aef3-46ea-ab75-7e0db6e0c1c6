import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/feature/dop_native/features/esign/intro/cubit/dop_native_e_sign_intro_cubit.dart';
import 'package:evoapp/feature/dop_native/features/esign/intro/cubit/dop_native_e_sign_intro_state.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../constant.dart';
import '../../../../../../util/test_util.dart';

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

void main() {
  late DOPNativeESignIntroCubit cubit;
  final DOPNativeRepo mockDOPNativeRepo = MockDOPNativeRepo();

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  setUp(() {
    cubit = DOPNativeESignIntroCubit(dopNativeRepo: mockDOPNativeRepo);
  });

  test('Default state', () {
    expect(cubit.state, isA<DOPNativeESignIntroInitial>());
  });

  group('onNext', () {
    blocTest<DOPNativeESignIntroCubit, DOPNativeESignIntroState>(
      'When success',
      build: () => cubit,
      setUp: () async {
        const String filename = 'dop_native_get_application_state_success_esign_intro_semi.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);
        final BaseResponse response = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: responseData,
        );

        when(() => mockDOPNativeRepo.submitESignIntroNext(mockConfig: any(named: 'mockConfig')))
            .thenAnswer(
          (_) async => BaseEntity.fromBaseResponse(response),
        );
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeESignIntroCubit cubit) => cubit.submitESignIntroNext(),
      expect: () => <dynamic>[
        isA<DOPNativeESignIntroLoading>(),
        isA<DOPNativeESignIntroNextSuccess>(),
      ],
    );

    blocTest<DOPNativeESignIntroCubit, DOPNativeESignIntroState>(
      'When fail',
      build: () => cubit,
      setUp: () {
        when(() => mockDOPNativeRepo.submitESignIntroNext(mockConfig: any(named: 'mockConfig')))
            .thenAnswer(
          (_) async => BaseEntity(statusCode: CommonHttpClient.BAD_REQUEST),
        );
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeESignIntroCubit cubit) => cubit.submitESignIntroNext(),
      expect: () => <dynamic>[
        isA<DOPNativeESignIntroLoading>(),
        isA<DOPNativeESignIntroNextFail>().having(
          (DOPNativeESignIntroNextFail p0) => p0.error.statusCode,
          'verify status code',
          CommonHttpClient.BAD_REQUEST,
        ),
      ],
    );
  });
}
