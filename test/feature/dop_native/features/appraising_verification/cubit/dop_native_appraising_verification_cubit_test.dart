import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/dop_native_repo/dop_native_repo.dart';
import 'package:evoapp/data/response/dop_native/dop_native_appraising_status_entity.dart';
import 'package:evoapp/feature/dop_native/features/appraising_verification/cubit/dop_native_appraising_verification_cubit.dart';
import 'package:evoapp/feature/dop_native/features/appraising_verification/cubit/dop_native_appraising_verification_state.dart';
import 'package:evoapp/feature/dop_native/util/dop_native_submit_status_polling/dop_native_submit_status_polling.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../constant.dart';
import '../../../../../util/test_util.dart';

class MockDOPNativeRepo extends Mock implements DOPNativeRepo {}

class MockDOPNativeSubmitStatusPolling extends Mock implements DOPNativeSubmitStatusPolling {}

void main() {
  final DOPNativeRepo dopNativeRepo = MockDOPNativeRepo();
  final DOPNativeSubmitStatusPolling dopNativeSubmitStatusPolling =
      MockDOPNativeSubmitStatusPolling();

  late DOPNativeAppraisingVerificationCubit cubit;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  setUp(() {
    cubit = DOPNativeAppraisingVerificationCubit(
      dopNativeRepo: dopNativeRepo,
      dopNativeSubmitStatusPolling: dopNativeSubmitStatusPolling,
    );
  });

  group('Test getAppraisingStatus', () {
    blocTest<DOPNativeAppraisingVerificationCubit, DOPNativeAppraisingVerificationState>(
      'Get appraising status success',
      setUp: () async {
        const String filename = 'dop_native_appraising_status_success.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);
        final BaseResponse response = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: responseData,
        );

        when(() => dopNativeRepo.getAppraisingStatus(mockConfig: any(named: 'mockConfig')))
            .thenAnswer(
          (_) async => DOPNativeAppraisingStatusEntity.fromBaseResponse(response),
        );
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeAppraisingVerificationCubit cubit) async {
        await cubit.getAppraisingStatus();
      },
      expect: () => <dynamic>[
        isA<AppraisingVerificationSuccessState>(),
      ],
      verify: (_) {
        verify(() => dopNativeRepo.getAppraisingStatus(mockConfig: any(named: 'mockConfig')))
            .called(1);

        verifyNever(() =>
            dopNativeSubmitStatusPolling.delayToPolling(onDoPolling: any(named: 'onDoPolling')));
      },
    );

    blocTest<DOPNativeAppraisingVerificationCubit, DOPNativeAppraisingVerificationState>(
      'Get appraising status pending',
      setUp: () async {
        const String filename = 'dop_native_appraising_status_pending.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);
        final BaseResponse response = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: responseData,
        );

        when(() => dopNativeRepo.getAppraisingStatus(mockConfig: any(named: 'mockConfig')))
            .thenAnswer(
          (_) async => DOPNativeAppraisingStatusEntity.fromBaseResponse(response),
        );
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeAppraisingVerificationCubit cubit) async {
        await cubit.getAppraisingStatus();
      },
      expect: () => <dynamic>[],
      verify: (_) {
        verify(() => dopNativeRepo.getAppraisingStatus(mockConfig: any(named: 'mockConfig')))
            .called(1);

        verify(() =>
                dopNativeSubmitStatusPolling.delayToPolling(onDoPolling: any(named: 'onDoPolling')))
            .called(1);
      },
    );

    blocTest<DOPNativeAppraisingVerificationCubit, DOPNativeAppraisingVerificationState>(
      'Get appraising status in progress',
      setUp: () async {
        const String filename = 'dop_native_appraising_status_in_progress.json';
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(filename);
        final BaseResponse response = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: responseData,
        );

        when(() => dopNativeRepo.getAppraisingStatus(mockConfig: any(named: 'mockConfig')))
            .thenAnswer(
          (_) async => DOPNativeAppraisingStatusEntity.fromBaseResponse(response),
        );
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeAppraisingVerificationCubit cubit) async {
        await cubit.getAppraisingStatus();
      },
      expect: () => <dynamic>[],
      verify: (_) {
        verify(() => dopNativeRepo.getAppraisingStatus(mockConfig: any(named: 'mockConfig')))
            .called(1);

        verify(() =>
                dopNativeSubmitStatusPolling.delayToPolling(onDoPolling: any(named: 'onDoPolling')))
            .called(1);
      },
    );

    blocTest<DOPNativeAppraisingVerificationCubit, DOPNativeAppraisingVerificationState>(
      'Get appraising status failed',
      setUp: () async {
        final BaseResponse response = BaseResponse(
          statusCode: CommonHttpClient.BAD_REQUEST,
          response: null,
        );

        when(() => dopNativeRepo.getAppraisingStatus(mockConfig: any(named: 'mockConfig')))
            .thenAnswer(
          (_) async => DOPNativeAppraisingStatusEntity.fromBaseResponse(response),
        );
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeAppraisingVerificationCubit cubit) async {
        await cubit.getAppraisingStatus();
      },
      expect: () => <dynamic>[
        isA<AppraisingVerificationFailedState>().having(
          (AppraisingVerificationFailedState p0) => p0.error.statusCode,
          'error has statusCode == 400',
          CommonHttpClient.BAD_REQUEST,
        ),
      ],
      verify: (_) {
        verify(() => dopNativeRepo.getAppraisingStatus(mockConfig: any(named: 'mockConfig')))
            .called(1);

        verifyNever(() =>
            dopNativeSubmitStatusPolling.delayToPolling(onDoPolling: any(named: 'onDoPolling')));
      },
    );

    blocTest<DOPNativeAppraisingVerificationCubit, DOPNativeAppraisingVerificationState>(
      'Test cancelPollingAppraisingStatus',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (DOPNativeAppraisingVerificationCubit cubit) async {
        cubit.cancelPollingAppraisingStatus();
      },
      verify: (_) {
        verify(() => dopNativeSubmitStatusPolling.cancel()).called(1);
      },
    );
  });
}
