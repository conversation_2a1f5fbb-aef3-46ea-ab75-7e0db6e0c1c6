import 'package:evoapp/feature/dop_native/dialogs/metadata_popup/cubit/debouncer.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Debouncer', () {
    test('should execute the action after the specified duration', () async {
      // Arrange
      final Debouncer debouncer = Debouncer(
        duration: const Duration(milliseconds: 100),
      );
      bool actionExecuted = false;
      void action() {
        actionExecuted = true;
      }

      // Act
      debouncer.run(action);
      await Future<void>.delayed(
        const Duration(milliseconds: 100),
      );

      // Assert
      expect(actionExecuted, true);
    });

    test('should cancel the previous action if a new one is scheduled', () async {
      // Arrange
      final Debouncer debouncer = Debouncer(duration: const Duration(milliseconds: 100));
      bool firstActionExecuted = false;
      void firstAction() {
        firstActionExecuted = true;
      }

      bool secondActionExecuted = false;
      void secondAction() {
        secondActionExecuted = true;
      }

      // Act
      debouncer.run(firstAction);
      await Future<void>.delayed(
        const Duration(milliseconds: 50),
      ); // Wait for less than the debounce duration
      debouncer.run(secondAction);
      await Future<void>.delayed(
        const Duration(milliseconds: 100),
      ); // Wait for the debounce duration again

      // Assert
      expect(firstActionExecuted, false);
      expect(secondActionExecuted, true);
    });
  });
}
