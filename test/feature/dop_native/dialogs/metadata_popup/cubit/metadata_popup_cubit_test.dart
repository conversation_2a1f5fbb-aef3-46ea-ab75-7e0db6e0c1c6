import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/dop_native/dialogs/metadata_popup/cubit/metadata_popup_cubit.dart';
import 'package:evoapp/feature/dop_native/dialogs/metadata_popup/cubit/metadata_popup_state.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

void main() {
  filterBy(String data) => data; // Replace with your actual filterBy implementation
  late DOPUtilFunctions mockDOPUtilFunctions;

  setUpAll(() {
    getIt.registerSingleton<DOPUtilFunctions>(MockDOPUtilFunctions());
    mockDOPUtilFunctions = getIt.get<DOPUtilFunctions>();
  });

  tearDown(() {
    reset(mockDOPUtilFunctions);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('MetadataPopupCubit', () {
    final List<String> initialData = <String>['1', '2', '3'];
    blocTest<MetadataPopupCubit<String>, MetadataPopUpState>(
      'emits new state with initial data when init is called',
      build: () => MetadataPopupCubit<String>(
        filterBy: filterBy,
        initialData: <String>[],
      ),
      act: (MetadataPopupCubit<String> cubit) => cubit.updateData(initialData),
      expect: () => <TypeMatcher<MetadataPopUpStateLoaded<String>>>[
        isA<MetadataPopUpStateLoaded<String>>().having(
          (MetadataPopUpStateLoaded<String> state) => state.data,
          'matched init data',
          initialData,
        ),
      ],
    );

    blocTest<MetadataPopupCubit<String>, MetadataPopUpState>(
      'debounces the onQueryChanged calls and filters the data accordingly',
      build: () {
        final MetadataPopupCubit<String> cubit = MetadataPopupCubit<String>(
          filterBy: filterBy,
          initialData: initialData,
        );
        when(
          () => mockDOPUtilFunctions.queryDiacriticsFilter<String>(
            query: any(named: 'query'),
            data: any(named: 'data'),
            filterBy: any(named: 'filterBy'),
          ),
        ).thenReturn(<String>[]); // Replace the return value with expected filtered data
        return cubit;
      },
      act: (MetadataPopupCubit<String> cubit) async {
        cubit.onQueryChanged('query1');
        await Future<void>.delayed(
            const Duration(milliseconds: 50)); // Less than debouncing duration
        cubit.onQueryChanged('query2'); // This call should cancel the previous one
        await Future<void>.delayed(MetadataPopupCubit.debouncingDuration);
      },
      verify: (_) {
        verifyNever(
          () => mockDOPUtilFunctions.queryDiacriticsFilter<String>(
            query: 'query1',
            data: any(named: 'data'),
            filterBy: any(named: 'filterBy'),
          ),
        );
        verify(
          () => mockDOPUtilFunctions.queryDiacriticsFilter<String>(
            query: 'query2',
            data: any(named: 'data'),
            filterBy: any(named: 'filterBy'),
          ),
        ).called(1);
      },
    );
  });
}
