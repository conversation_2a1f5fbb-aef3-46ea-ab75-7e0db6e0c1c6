import 'dart:ui';

import 'package:evoapp/feature/dop_native/dialogs/input_phone_number/dop_native_input_phone_dialog_controller.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeInputPhoneDialogController Tests', () {
    test('Singleton instance is the same', () {
      final DOPNativeInputPhoneDialogController instance1 = DOPNativeInputPhoneDialogController();
      final DOPNativeInputPhoneDialogController instance2 = DOPNativeInputPhoneDialogController();

      expect(instance1, same(instance2));
    });

    test('Initialize controller with corrected properties', () {
      final DOPNativeInputPhoneDialogController controller = DOPNativeInputPhoneDialogController();

      const String testPhoneNumber = '0901234567';

      bool isBootstrapCalled = false;

      void onSuccessCallback({
        String? uniqueToken,
        VoidCallback? onLogEvent,
        VoidCallback? hideInputPhoneDialogLoading,
      }) {
        isBootstrapCalled = true;
      }

      controller.initialize(
        phoneNumber: testPhoneNumber,
        onRegisterSuccess: onSuccessCallback,
      );

      expect(controller.phoneNumber, equals(testPhoneNumber));
      expect(controller.onRegisterSuccess, isNotNull);

      controller.onRegisterSuccess?.call();
      expect(isBootstrapCalled, isTrue);

      expect(controller.isShowingPopup, isFalse);
    });

    test('Update phone number', () {
      final DOPNativeInputPhoneDialogController controller = DOPNativeInputPhoneDialogController();

      const String initialPhoneNumber = '0901234567';
      controller.phoneNumber = initialPhoneNumber;
      expect(controller.phoneNumber, equals(initialPhoneNumber));

      const String newPhoneNumber = '0987654321';
      controller.phoneNumber = newPhoneNumber;
      expect(controller.phoneNumber, equals(newPhoneNumber));
    });

    test('onRegisterSuccess callback works', () {
      final DOPNativeInputPhoneDialogController controller = DOPNativeInputPhoneDialogController();

      bool callbackInvoked = false;

      void onSuccessCallback({
        String? uniqueToken,
        VoidCallback? onLogEvent,
        VoidCallback? hideInputPhoneDialogLoading,
      }) {
        callbackInvoked = true;
      }

      controller.initialize(
        phoneNumber: '0987654321',
        onRegisterSuccess: onSuccessCallback,
      );

      controller.onRegisterSuccess?.call(uniqueToken: 'token123');
      expect(callbackInvoked, isTrue);
    });

    test('test setPopupShowed', () {
      final DOPNativeInputPhoneDialogController controller = DOPNativeInputPhoneDialogController();

      expect(controller.isShowingPopup, isFalse);

      controller.isShowingPopup = true;

      expect(controller.isShowingPopup, isTrue);
    });
  });
}
