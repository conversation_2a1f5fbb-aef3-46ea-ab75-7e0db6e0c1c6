import 'package:evoapp/feature/dop_native/dialogs/input_phone_number/mock/mock_dop_native_bootstrap_auth_setting_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('enum value MockDOPNativeBootstrapAuthSetting', () {
    expect(
      MockDOPNativeBootstrapAuthSetting.getBootstrapAuthTypeOTP.value,
      'dop_native_get_bootstrap_auth_otp.json',
    );
    expect(
      MockDOPNativeBootstrapAuthSetting.getBootstrapAfterInputtingPhoneNumber.value,
      'dop_native_get_bootstrap_after_inputting_phone_number.json',
    );
    expect(
      MockDOPNativeBootstrapAuthSetting.getBootstrapAuthTypeESignOTP.value,
      'dop_native_get_bootstrap_auth_esign_otp.json',
    );
    expect(
      MockDOPNativeBootstrapAuthSetting.getBootstrapAuthTypeIDCardAuth.value,
      'dop_native_get_bootstrap_auth_id_card_auth.json',
    );
  });

  test('getMockDOPNativeBootstrapAuthSetting', () {
    expect(
      getMockDOPNativeBootstrapAuthSetting(
          MockDOPNativeBootstrapAuthSetting.getBootstrapAuthTypeOTP),
      'dop_native_get_bootstrap_auth_otp.json',
    );
    expect(
      getMockDOPNativeBootstrapAuthSetting(
          MockDOPNativeBootstrapAuthSetting.getBootstrapAfterInputtingPhoneNumber),
      'dop_native_get_bootstrap_after_inputting_phone_number.json',
    );
    expect(
      getMockDOPNativeBootstrapAuthSetting(
          MockDOPNativeBootstrapAuthSetting.getBootstrapAuthTypeESignOTP),
      'dop_native_get_bootstrap_auth_esign_otp.json',
    );
    expect(
      getMockDOPNativeBootstrapAuthSetting(
          MockDOPNativeBootstrapAuthSetting.getBootstrapAuthTypeIDCardAuth),
      'dop_native_get_bootstrap_auth_id_card_auth.json',
    );
  });
}
