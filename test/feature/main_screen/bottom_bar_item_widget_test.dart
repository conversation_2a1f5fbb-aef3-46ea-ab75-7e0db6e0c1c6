import 'package:evoapp/feature/main_screen/bottom_bar_item_widget.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class MockCallable extends Mock {
  void call();
}

void main() {
  late MockCallable mockOnTap;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    // Register necessary dependencies
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    // Setup mock for image provider
    when(() => evoImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
        )).thenReturn(const SizedBox());
  });

  setUp(() {
    mockOnTap = MockCallable();
  });

  tearDown(() {
    reset(mockOnTap);
  });

  tearDownAll(() {
    getItUnregisterColor();
    getItUnRegisterMockCommonUtilFunctionAndImageProvider();
  });

  group('BottomBarItemWidget', () {
    const String testIcon = 'test_icon';
    const String testLabel = 'Test Label';

    testWidgets('should render with icon when icon is provided', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BottomBarItemWidget(
              icon: testIcon,
              label: testLabel,
              isSelected: false,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(testLabel), findsOneWidget);
      verify(() => evoImageProvider.asset(
            testIcon,
            color: any(named: 'color'),
            width: 24,
            height: 24,
            fit: BoxFit.scaleDown,
          )).called(1);
    });

    testWidgets('should render with SizedBox when icon is null', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BottomBarItemWidget(
              icon: null,
              label: testLabel,
              isSelected: false,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(testLabel), findsOneWidget);
      // Find SizedBox that's not the one returned by our mock
      final Finder sizedBoxFinder = find.byWidgetPredicate(
        (Widget widget) => widget is SizedBox && widget.width == 24 && widget.height == 24,
      );
      expect(sizedBoxFinder, findsOneWidget);
    });

    testWidgets('should use selected color when isSelected is true', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BottomBarItemWidget(
              icon: testIcon,
              label: testLabel,
              isSelected: true,
            ),
          ),
        ),
      );

      // Assert
      verify(() => evoImageProvider.asset(
            testIcon,
            color: evoColors.bottomSheetSelectedItem,
            width: 24,
            height: 24,
            fit: BoxFit.scaleDown,
          )).called(1);

      final Finder textFinder = find.text(testLabel);
      final Text textWidget = tester.widget<Text>(textFinder);
      expect(textWidget.style?.color, evoColors.bottomSheetSelectedItem);
    });

    testWidgets('should use unselected color when isSelected is false',
        (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BottomBarItemWidget(
              icon: testIcon,
              label: testLabel,
              isSelected: false,
            ),
          ),
        ),
      );

      // Assert
      verify(() => evoImageProvider.asset(
            testIcon,
            color: evoColors.bottomSheetUnselectedItem,
            width: 24,
            height: 24,
            fit: BoxFit.scaleDown,
          )).called(1);

      final Finder textFinder = find.text(testLabel);
      final Text textWidget = tester.widget<Text>(textFinder);
      expect(textWidget.style?.color, evoColors.bottomSheetUnselectedItem);
    });

    testWidgets('should call onTap callback when tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: BottomBarItemWidget(
              icon: testIcon,
              label: testLabel,
              isSelected: false,
              onTap: mockOnTap.call,
            ),
          ),
        ),
      );

      // Act
      await tester.tap(find.byType(InkWell));
      await tester.pump();

      // Assert
      verify(() => mockOnTap()).called(1);
    });

    testWidgets('should not call onTap callback when onTap is null', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BottomBarItemWidget(
              icon: testIcon,
              label: testLabel,
              isSelected: false,
            ),
          ),
        ),
      );

      // Act
      await tester.tap(find.byType(InkWell));
      await tester.pump();

      // Assert - no crash should occur
      expect(find.byType(BottomBarItemWidget), findsOneWidget);
    });

    testWidgets('should have correct text style and alignment', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BottomBarItemWidget(
              icon: testIcon,
              label: testLabel,
              isSelected: false,
            ),
          ),
        ),
      );

      // Assert
      final Finder textFinder = find.text(testLabel);
      final Text textWidget = tester.widget<Text>(textFinder);
      expect(textWidget.textAlign, TextAlign.center);
      expect(textWidget.style?.fontSize, 10);
    });

    testWidgets('should have circular border radius for InkWell', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BottomBarItemWidget(
              icon: testIcon,
              label: testLabel,
              isSelected: false,
            ),
          ),
        ),
      );

      // Assert
      final Finder inkWellFinder = find.byType(InkWell);
      final InkWell inkWell = tester.widget<InkWell>(inkWellFinder);
      expect(inkWell.borderRadius, const BorderRadius.all(Radius.circular(50)));
    });

    testWidgets('should have Column with MainAxisAlignment.center', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: BottomBarItemWidget(
              icon: testIcon,
              label: testLabel,
              isSelected: false,
            ),
          ),
        ),
      );

      // Assert
      final Finder columnFinder = find.byType(Column);
      final Column column = tester.widget<Column>(columnFinder);
      expect(column.mainAxisAlignment, MainAxisAlignment.center);
    });
  });
}
