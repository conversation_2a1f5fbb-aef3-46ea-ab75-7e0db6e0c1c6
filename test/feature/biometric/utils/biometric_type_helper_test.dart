import 'package:evoapp/feature/biometric/utils/biometric_type_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/remote_config/remote_config_helper.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:local_auth/local_auth.dart';
import 'package:mocktail/mocktail.dart';

class MockFeatureToggle extends Mock implements FeatureToggle {}

class MockRemoteConfigHelper extends Mock implements RemoteConfigHelper {}

class MockBiometricsAuthenticate extends Mock implements BiometricsAuthenticate {}

class MockDeviceInfoPlugin extends Mock implements DeviceInfoPlugin {}

class MockEvoFlutterWrapper extends Mock implements EvoFlutterWrapper {}

class MockIosDeviceInfo extends Mock implements IosDeviceInfo {}

class MockIosUtsname extends Mock implements IosUtsname {}

void main() {
  late BiometricTypeHelper biometricTypeHelper;

  late MockDeviceInfoPlugin mockDeviceInfoPlugin;
  late MockBiometricsAuthenticate mockBiometricsAuthenticate;
  late MockRemoteConfigHelper mockRemoteConfigHelper;
  late MockFeatureToggle mockFeatureToggle;
  late MockEvoFlutterWrapper mockEvoFlutterWrapper;

  setUpAll(() {
    mockDeviceInfoPlugin = MockDeviceInfoPlugin();
    mockBiometricsAuthenticate = MockBiometricsAuthenticate();
    mockRemoteConfigHelper = MockRemoteConfigHelper();
    mockFeatureToggle = MockFeatureToggle();
    mockEvoFlutterWrapper = MockEvoFlutterWrapper();

    getIt.registerSingleton<FeatureToggle>(mockFeatureToggle);
    getIt.registerSingleton<EvoFlutterWrapper>(mockEvoFlutterWrapper);

    biometricTypeHelper = BiometricTypeHelper(
      mockBiometricsAuthenticate,
      mockDeviceInfoPlugin,
      mockRemoteConfigHelper,
    );
  });

  tearDownAll(() {
    getIt.reset();
  });

  test('value of defaultIOSUtsNameMachineSupportFingerprint', () {
    expect(biometricTypeHelper.defaultIOSUtsNameMachineSupportFingerprint, <String>[
      'iPhone6,1',
      'iPhone6,2',
      'iPhone8,4',
      'iPhone7,1',
      'iPhone7,2',
      'iPhone8,1',
      'iPhone8,2',
      'iPhone9,1',
      'iPhone9,2',
      'iPhone9,3',
      'iPhone9,4',
      'iPhone10,1',
      'iPhone10,2',
      'iPhone10,4',
      'iPhone10,5',
      'iPhone12,8',
      'iPhone14,6',
    ]);
  });

  group('test getTsBiometricType', () {
    setUpAll(() {
      when(() => mockBiometricsAuthenticate.getAvailableBiometricType()).thenAnswer(
        (_) => Future<List<BiometricType>>.value(<BiometricType>[BiometricType.face]),
      );
    });

    test('returns unknown for unknown platform', () async {
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(false);
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(false);

      final TsBiometricType result = await biometricTypeHelper.getTsBiometricType();
      expect(result, TsBiometricType.unknown);
    });

    test('returns androidBio for android platform', () async {
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(false);
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);

      final TsBiometricType result = await biometricTypeHelper.getTsBiometricType();
      expect(result, TsBiometricType.androidBio);
    });

    test('returns face for ios platform - mock face getAvailableBiometricType', () async {
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(true);
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(false);

      final TsBiometricType result = await biometricTypeHelper.getTsBiometricType();
      expect(result, TsBiometricType.face);
    });
  });

  group('test handleIOSPlatform', () {
    test('contains face -> return face', () async {
      final TsBiometricType result = await biometricTypeHelper.handleIOSPlatform(
        <BiometricType>[
          BiometricType.face,
          BiometricType.iris,
        ],
      );
      expect(result, TsBiometricType.face);
    });

    test('contains fingerprint -> return finger', () async {
      final TsBiometricType result = await biometricTypeHelper.handleIOSPlatform(
        <BiometricType>[
          BiometricType.fingerprint,
          BiometricType.iris,
        ],
      );
      expect(result, TsBiometricType.finger);
    });

    test(
        'another case -> check from getIOSDevicesSupportFinger: return finger if IOSDevicesSupportFinger contains',
        () async {
      final IosDeviceInfo iosInfo = MockIosDeviceInfo();
      final MockIosUtsname utsname = MockIosUtsname();

      when(() => iosInfo.utsname).thenAnswer((_) => utsname);
      when(() => utsname.machine).thenAnswer((_) => 'specialIosName');
      when(() => mockDeviceInfoPlugin.iosInfo).thenAnswer(
        (_) => Future<IosDeviceInfo>.value(iosInfo),
      );

      when(() => mockFeatureToggle.enableRemoteConfigFeature).thenReturn(true);
      when(() => mockRemoteConfigHelper.getIOSDevicesSupportFinger()).thenAnswer(
        (_) => Future<List<String>>.value(<String>['specialIosName']),
      );

      final TsBiometricType result = await biometricTypeHelper.handleIOSPlatform(
        <BiometricType>[
          BiometricType.strong,
          BiometricType.weak,
        ],
      );
      expect(result, TsBiometricType.finger);
    });

    test(
        'another case -> check from getIOSDevicesSupportFinger: return face if IOSDevicesSupportFinger does not contains',
        () async {
      final IosDeviceInfo iosInfo = MockIosDeviceInfo();
      final MockIosUtsname utsname = MockIosUtsname();

      when(() => iosInfo.utsname).thenAnswer((_) => utsname);
      when(() => utsname.machine).thenAnswer((_) => 'specialIosName');
      when(() => mockDeviceInfoPlugin.iosInfo).thenAnswer(
        (_) => Future<IosDeviceInfo>.value(iosInfo),
      );

      when(() => mockFeatureToggle.enableRemoteConfigFeature).thenReturn(true);
      when(() => mockRemoteConfigHelper.getIOSDevicesSupportFinger()).thenAnswer(
        (_) => Future<List<String>>.value(<String>['anotherSpecialIosName']),
      );

      final TsBiometricType result = await biometricTypeHelper.handleIOSPlatform(
        <BiometricType>[
          BiometricType.strong,
          BiometricType.weak,
        ],
      );
      expect(result, TsBiometricType.face);
    });
  });

  group('test getIOSDevicesSupportFinger', () {
    test('case disable feature toggle -> return default', () async {
      when(() => mockFeatureToggle.enableRemoteConfigFeature).thenReturn(false);

      final List<String> result = await biometricTypeHelper.getIOSDevicesSupportFinger();
      expect(result, biometricTypeHelper.defaultIOSUtsNameMachineSupportFingerprint);
    });

    test('case enable feature toggle -> return value from remote-config', () async {
      when(() => mockFeatureToggle.enableRemoteConfigFeature).thenReturn(true);

      when(() => mockRemoteConfigHelper.getIOSDevicesSupportFinger()).thenAnswer(
        (_) => Future<List<String>>.value(<String>['fakeValue']),
      );

      final List<String> result = await biometricTypeHelper.getIOSDevicesSupportFinger();
      expect(result, <String>['fakeValue']);
    });

    test('case enable feature toggle -> return default value when remote-config empty', () async {
      when(() => mockFeatureToggle.enableRemoteConfigFeature).thenReturn(true);
      when(() => mockRemoteConfigHelper.getIOSDevicesSupportFinger()).thenAnswer(
        (_) => Future<List<String>>.value(<String>[]),
      );

      final List<String> result = await biometricTypeHelper.getIOSDevicesSupportFinger();
      expect(result, biometricTypeHelper.defaultIOSUtsNameMachineSupportFingerprint);
    });
  });
}
