import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/repository/user_repo_impl.dart';
import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/model/biometric_ui_model.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_handler_impl.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_popup.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/feature/biometric/utils/biometric_type_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate_impl.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:evoapp/util/token_utils/evo_jwt_helper_impl.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_common_package/util/shared_preferences_impl.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../data/repository/mock_common_http_client.dart';
import '../../../util/flutter_test_config.dart';
import '../../../util/functions/evo_action_handler_test.dart';

class MockAppState extends Mock implements AppState {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockEvoSecureStorageHelperImpl extends Mock implements EvoSecureStorageHelperImpl {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockBiometricsTokenModule extends Mock implements BiometricsTokenModule {}

class MockBiometricTypeHelper extends Mock implements BiometricTypeHelper {}

class MockBiometricAuthenticateImpl extends Mock implements BiometricAuthenticateImpl {}

class MockEvoImageProvider extends Mock implements CommonImageProvider {}

void main() {
  late AppState mockAppState;
  late EvoUtilFunction mockEvoUtilFunction;
  late EvoSecureStorageHelperImpl mockEvoSecureStorage;
  late BiometricsAuthenticate mockBiometricsAuthenticate;
  late BiometricsTokenModule mockBiometricsTokenModule;
  late RequestUserActiveBiometricPopup activeBiometricPopup;
  late EvoSnackBar mockEvoSnackBar;
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    setUtilsMockInstanceForTesting();

    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<AppState>(() => MockAppState());
    mockAppState = getIt.get<AppState>();

    registerFallbackValue(EvoDialogId.askGoToDeviceSecuritySettingBottomSheet);
    registerFallbackValue(SnackBarType.error);
    registerFallbackValue(SnackBarDuration.short.value);

    getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());
    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();

    getIt.registerLazySingleton<CommonSharedPreferencesHelper>(
        () => CommonSharedPreferencesHelperImpl());

    getIt.registerLazySingleton<EvoSecureStorageHelperImpl>(() => MockEvoSecureStorageHelperImpl());
    mockEvoSecureStorage = getIt.get<EvoSecureStorageHelperImpl>();

    getIt.registerLazySingleton<EvoLocalStorageHelper>(() => MockEvoLocalStorageHelper());

    getIt.registerLazySingleton<UserRepo>(
        () => UserRepoImpl(MockCommonHttpClient(), mockEvoSecureStorage));

    getIt.registerLazySingleton<JwtHelper>(() => EvoJwtHelperImpl());

    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());

    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<EvoColors>(() => EvoColors());

    // register Biometrics
    getIt.registerLazySingleton<BiometricsAuthenticate>(() => MockBiometricAuthenticateImpl());
    mockBiometricsAuthenticate = getIt.get<BiometricsAuthenticate>();

    getIt.registerLazySingleton<BiometricsTokenModule>(() => MockBiometricsTokenModule());
    mockBiometricsTokenModule = getIt.get<BiometricsTokenModule>();

    getIt.registerLazySingleton<BiometricTypeHelper>(() => MockBiometricTypeHelper());

    getIt.registerLazySingleton<EvoSnackBar>(() => MockEvoSnackBar());
    mockEvoSnackBar = getIt.get<EvoSnackBar>();

    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    when(() => EvoDialogHelper().showDialogBottomSheet(
          textPositive: any(named: 'textPositive'),
          dialogId: any(named: 'dialogId'),
          content: any(named: 'content'),
          title: any(named: 'title'),
          textNegative: any(named: 'textNegative'),
          footer: any(named: 'footer'),
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
          header: any(named: 'header'),
          isDismissible: any(named: 'isDismissible'),
          positiveButtonStyle: any(named: 'positiveButtonStyle'),
          negativeButtonStyle: any(named: 'negativeButtonStyle'),
          titleTextStyle: any(named: 'titleTextStyle'),
          contentTextStyle: any(named: 'contentTextStyle'),
          onClickClose: any(named: 'onClickClose'),
          isShowButtonClose: any(named: 'isShowButtonClose'),
          buttonClose: any(named: 'buttonClose'),
          loggingEventMetaData: any(named: 'loggingEventMetaData'),
        )).thenAnswer((_) => Future<void>.value());

    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          fit: any(named: 'fit'),
        )).thenReturn(const SizedBox());
  });

  setUp(() {
    activeBiometricPopup = RequestUserActiveBiometricPopup();
  });

  tearDown(() {
    reset(mockAppState);
  });

  tearDownAll(() async {
    await getIt.reset();
    resetUtilMockToOriginalInstance();
  });

  group('verify method showDialogActiveBiometric()', () {
    late BiometricTypeUIModel model;

    setUp(() {
      model = BiometricTypeUIModel.faceAndFinger();
      when(() => mockAppState.bioTypeInfo).thenReturn(model);
    });

    testWidgets('verify showDialogActiveBiometric() with return value',
        (WidgetTester widgetTester) async {
      final RequestUserActivateBiometricStatus result =
          await activeBiometricPopup.showDialogActiveBiometric();

      expect(result, RequestUserActivateBiometricStatus.fail);
      expect(activeBiometricPopup.isTappedActivateBiometric, false);

      verify(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: EvoDialogId.requestEnableActiveBiometricBottomSheet,
            textPositive: EvoStrings.active,
            textNegative: EvoStrings.tutorialSkipButtonText,
            title: EvoStrings.titleActiveBiometric
                .replaceVariableByValue(<String>[model.biometricTypeName]).toString(),
            content: EvoStrings.descriptionActiveBiometric
                .replaceVariableByValue(<String>[model.biometricTypeName]),
            isShowButtonClose: true,
            header: any(named: 'header'),
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });
  });

  group('verify method show()', () {
    late BiometricTypeUIModel model;

    setUp(() {
      model = BiometricTypeUIModel.faceAndFinger();
      when(() => mockAppState.bioTypeInfo).thenReturn(model);
    });

    testWidgets('verify method show() that the user taps active button',
        (WidgetTester widgetTester) async {
      activeBiometricPopup.isTappedActivateBiometric = true;

      await activeBiometricPopup.show();

      verify(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: EvoDialogId.requestEnableActiveBiometricBottomSheet,
            textPositive: EvoStrings.active,
            textNegative: EvoStrings.tutorialSkipButtonText,
            title: EvoStrings.titleActiveBiometric
                .replaceVariableByValue(<String>[model.biometricTypeName]).toString(),
            content: EvoStrings.descriptionActiveBiometric
                .replaceVariableByValue(<String>[model.biometricTypeName]),
            isShowButtonClose: true,
            header: any(named: 'header'),
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });

    testWidgets('verify method show() that the user does not tap active button',
        (WidgetTester widgetTester) async {
      activeBiometricPopup.isTappedActivateBiometric = false;

      await activeBiometricPopup.show();

      verify(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: EvoDialogId.requestEnableActiveBiometricBottomSheet,
            textPositive: EvoStrings.active,
            textNegative: EvoStrings.tutorialSkipButtonText,
            title: EvoStrings.titleActiveBiometric
                .replaceVariableByValue(<String>[model.biometricTypeName]).toString(),
            content: EvoStrings.descriptionActiveBiometric
                .replaceVariableByValue(<String>[model.biometricTypeName]),
            isShowButtonClose: true,
            header: any(named: 'header'),
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });
  });

  group('verify method onTapActive()', () {
    test('verify method onTapActive(), hasEnrolledBiometric = true', () async {
      when(() => mockBiometricsAuthenticate.hasEnrolledBiometric())
          .thenAnswer((_) async => Future<bool>.value(true));

      when(() => mockBiometricsTokenModule.enable(
            callback: any(named: 'callback'),
          )).thenAnswer((_) async => Future<void>.value());

      await activeBiometricPopup.onTapActive();

      verify(() => mockBiometricsAuthenticate.hasEnrolledBiometric()).called(1);
      verify(() => mockBiometricsTokenModule.enable(
            callback: any(named: 'callback'),
          )).called(1);

      expect(activeBiometricPopup.isTappedActivateBiometric, true);
    });

    test('verify method onTapActive(), hasEnrolledBiometric = false', () async {
      when(() => mockBiometricsAuthenticate.hasEnrolledBiometric())
          .thenAnswer((_) async => Future<bool>.value(false));

      when(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: any(named: 'dialogId'),
            title: any(named: 'title'),
            content: any(named: 'content'),
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).thenAnswer((_) async => Future<void>.value());

      when(() => mockAppState.bioTypeInfo).thenReturn(BiometricTypeUIModel.faceAndFinger());

      await activeBiometricPopup.onTapActive();

      verify(() => mockBiometricsAuthenticate.hasEnrolledBiometric()).called(1);
      verify(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: EvoDialogId.askGoToDeviceSecuritySettingBottomSheet,
            title: any(named: 'title'),
            content: any(named: 'content'),
            textPositive: EvoStrings.settingTitle,
            textNegative: EvoStrings.ignoreTitle,
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
          )).called(1);

      expect(activeBiometricPopup.isTappedActivateBiometric, true);
    });
  });

  group('verify onSuccess() method', () {
    test('onSuccess shows success message', () {
      final BiometricTypeUIModel mockBioTypeInfo = BiometricTypeUIModel.faceAndFinger();
      when(() => mockAppState.bioTypeInfo).thenReturn(mockBioTypeInfo);

      when(() => mockEvoSnackBar.show(
            any(),
            durationInSec: any(named: 'durationInSec'),
          )).thenAnswer((_) {
        return Future<bool?>.value(true);
      });

      activeBiometricPopup.onSuccess();

      final String expectedMessage =
          '${EvoStrings.enableText} ${EvoStrings.authenticateText.toLowerCase()} ${mockBioTypeInfo.biometricTypeName} ${EvoStrings.statusSuccess.toLowerCase()}';

      verify(() => mockEvoSnackBar.show(expectedMessage,
          durationInSec: SnackBarDuration.short.value)).called(1);
    });
  });

  group('verify onError() method', () {
    setUp(() {
      when(() => mockEvoUtilFunction.handleBioError(any())).thenAnswer((_) async {
        return Future<void>.value();
      });
    });

    test('onError handles bio error with BiometricTokenModuleErrorType.biometrics', () async {
      const BioAuthError bioError = BioAuthError.userDismiss;

      activeBiometricPopup.onError(
        type: BiometricTokenModuleErrorType.biometrics,
        bioError: bioError,
      );

      verify(() => mockEvoUtilFunction.handleBioError(bioError)).called(1);
    });

    test('onError handles bio error', () async {
      when(() => mockEvoSnackBar.show(
            any(),
            typeSnackBar: any(named: 'typeSnackBar'),
            durationInSec: any(named: 'durationInSec'),
          )).thenAnswer((_) {
        return Future<bool?>.value(true);
      });

      activeBiometricPopup.onError(type: BiometricTokenModuleErrorType.noSupportExtraChallenge);

      verify(() => mockEvoSnackBar.show(CommonStrings.otherGenericErrorMessage,
          typeSnackBar: SnackBarType.error,
          durationInSec: SnackBarDuration.short.value)).called(1);
    });
  });

  group('verify showDialogActivateBiometric() method', () {
    const String title = 'title1';
    const String content = 'content1';
    onActive() {}
    setUpAll(() {
      getIt.registerLazySingleton(() => GlobalKeyProvider());
    });

    tearDownAll(() {
      getIt.unregister<GlobalKeyProvider>();
    });

    testWidgets('verify call showDialogBottomSheet() method in Util Function',
        (WidgetTester widgetTester) async {
      await activeBiometricPopup.showBottomSheetActivateBiometric(
          title: title, content: content, onActive: onActive);

      verify(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: EvoDialogId.requestEnableActiveBiometricBottomSheet,
            content: content,
            textPositive: EvoStrings.active,
            textNegative: EvoStrings.tutorialSkipButtonText,
            title: title,
            isShowButtonClose: true,
            header: any(named: 'header'),
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });

    testWidgets('verify call showDialogBottomSheet() callbacks', (WidgetTester widgetTester) async {
      bool onActiveCalled = false;
      bool onSkipCalled = false;

      await activeBiometricPopup.showBottomSheetActivateBiometric(
          title: title,
          content: content,
          onActive: () {
            onActiveCalled = true;
          },
          onSkip: () {
            onSkipCalled = true;
          });

      final List<dynamic> captured = verify(() => EvoDialogHelper().showDialogBottomSheet(
            dialogId: EvoDialogId.requestEnableActiveBiometricBottomSheet,
            content: content,
            textPositive: EvoStrings.active,
            textNegative: EvoStrings.tutorialSkipButtonText,
            title: title,
            isShowButtonClose: true,
            header: any(named: 'header'),
            onClickNegative: captureAny(named: 'onClickNegative'),
            onClickPositive: captureAny(named: 'onClickPositive'),
          )).captured;

      final VoidCallback onClickPositiveCallBack = captured[0];
      final VoidCallback onClickNegativeCallBack = captured[1];
      onClickPositiveCallBack();
      onClickNegativeCallBack();
      expect(onActiveCalled, true);
      expect(onSkipCalled, true);
    });
  });
}
