import 'package:evoapp/feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'package:evoapp/feature/biometric/model/biometric_ui_model.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_handler.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_handler_impl.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_popup.dart';
import 'package:evoapp/feature/biometric/request_user_active_biometric/request_user_active_biometric_util.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockRequestUserActivateBiometricHandler extends Mock
    implements RequestUserActivateBiometricHandler {}

class MockBiometricsAuthenticate extends Mock implements BiometricsAuthenticate {}

class MockAppState extends Mock implements AppState {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockEvoSecureStorageHelperImpl extends Mock implements EvoSecureStorageHelperImpl {}

class MockBiometricsTokenModule extends Mock implements BiometricsTokenModule {}

class MockRequestUserActiveBiometricUtil extends Mock implements RequestUserActiveBiometricUtil {}

class MockRequestUserActiveBiometricPopup extends Mock implements RequestUserActiveBiometricPopup {}

void main() {
  late MockRequestUserActivateBiometricHandler mockRequestUserActivateBiometricHandler;
  late MockBiometricsAuthenticate mockBiometricsAuthenticate;
  late RequestUserActiveBiometricHandlerImp activeBiometricHandler;
  late AppState mockAppState;
  late RequestUserActiveBiometricUtil mockRequestUserActiveBiometricUtil;
  late RequestUserActiveBiometricPopup mockRequestUserActiveBiometricPopup;

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<EvoSecureStorageHelperImpl>(() => MockEvoSecureStorageHelperImpl());

    mockRequestUserActivateBiometricHandler = MockRequestUserActivateBiometricHandler();

    getIt.registerLazySingleton<AppState>(() => MockAppState());
    mockAppState = getIt.get<AppState>();

    getIt.registerLazySingleton<RequestUserActiveBiometricUtil>(
        () => MockRequestUserActiveBiometricUtil());
    mockRequestUserActiveBiometricUtil = getIt.get<RequestUserActiveBiometricUtil>();

    getIt.registerLazySingleton<BiometricsTokenModule>(() => MockBiometricsTokenModule());

    getIt.registerLazySingleton<BiometricsAuthenticate>(() => mockBiometricsAuthenticate);
    getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());
    getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());

    mockRequestUserActiveBiometricPopup = MockRequestUserActiveBiometricPopup();
  });

  setUp(() {
    mockBiometricsAuthenticate = MockBiometricsAuthenticate();

    activeBiometricHandler = RequestUserActiveBiometricHandlerImp(
      requestUserActiveBiometricUtil: mockRequestUserActiveBiometricUtil,
      biometricsAuthenticate: mockBiometricsAuthenticate,
    );
  });

  tearDown(() {
    reset(mockRequestUserActivateBiometricHandler);
    reset(mockRequestUserActiveBiometricUtil);
  });

  tearDownAll(() async {
    await getIt.reset();
  });

  group('verify start() method checking active biometric without ActivateBiometricUseCase', () {
    test('test checking if the device has not support biometric', () async {
      when(() => mockBiometricsAuthenticate.isDeviceSupportBiometrics()).thenAnswer((_) async {
        return Future<bool>.value(false);
      });

      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.start(useCase: ActivateBiometricUseCase.newDevice);

      verify(() => mockBiometricsAuthenticate.isDeviceSupportBiometrics()).called(1);

      expect(result, RequestUserActivateBiometricStatus.unQualified);
    });

    test('test checking if the device has support biometric & biometric is enabled', () async {
      when(() => mockBiometricsAuthenticate.isDeviceSupportBiometrics()).thenAnswer((_) async {
        return Future<bool>.value(true);
      });

      when(() => mockRequestUserActiveBiometricUtil.getEnableBiometricAuthenticator())
          .thenAnswer((_) async => true);

      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.start(useCase: ActivateBiometricUseCase.newDevice);

      verify(() => mockBiometricsAuthenticate.isDeviceSupportBiometrics()).called(1);

      verify(() => mockRequestUserActiveBiometricUtil.getEnableBiometricAuthenticator()).called(1);

      expect(result, RequestUserActivateBiometricStatus.success);
    });
  });

  group('verify start() method checking active biometric with ActivateBiometricUseCase', () {
    setUp(() {
      when(() => mockRequestUserActiveBiometricUtil.saveTimeShowBiometric()).thenAnswer((_) async {
        return Future<void>.value();
      });

      when(() => mockRequestUserActiveBiometricUtil.getTimeShowBiometric()).thenAnswer((_) async {
        return Future<String?>.value();
      });

      when(() => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup())
          .thenReturn(mockRequestUserActiveBiometricPopup);

      final BiometricTypeUIModel model = BiometricTypeUIModel.faceAndFinger();
      expect(model.toString(), isNotEmpty);
      when(() => mockAppState.bioTypeInfo).thenReturn(model);

      when(() => mockBiometricsAuthenticate.isDeviceSupportBiometrics()).thenAnswer((_) async {
        return Future<bool>.value(true);
      });

      when(() => mockRequestUserActiveBiometricUtil.getEnableBiometricAuthenticator())
          .thenAnswer((_) async => false);
    });

    test('verify with ActivateBiometricUseCase is null case', () async {
      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.start(useCase: null);

      verify(() => mockBiometricsAuthenticate.isDeviceSupportBiometrics()).called(1);

      expect(result, RequestUserActivateBiometricStatus.unQualified);
    });

    test('verify with ActivateBiometricUseCase.first_time_login case', () async {
      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.start(useCase: ActivateBiometricUseCase.firstTimeLogin);

      verify(() => mockBiometricsAuthenticate.isDeviceSupportBiometrics()).called(1);

      expect(result, RequestUserActivateBiometricStatus.unQualified);
    });

    test('verify with ActivateBiometricUseCase.account_setting case', () async {
      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.start(useCase: ActivateBiometricUseCase.accountSetting);

      verify(() => mockBiometricsAuthenticate.isDeviceSupportBiometrics()).called(1);

      expect(result, RequestUserActivateBiometricStatus.unQualified);
    });

    test('verify with ActivateBiometricUseCase.payment_verification case', () async {
      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.start(useCase: ActivateBiometricUseCase.paymentVerification);

      verify(() => mockBiometricsAuthenticate.isDeviceSupportBiometrics()).called(1);

      expect(result, RequestUserActivateBiometricStatus.unQualified);
    });

    test('verify with ActivateBiometricUseCase.new_device and isNewDevice = false', () async {
      when(() => mockRequestUserActiveBiometricUtil.isNewDevice()).thenAnswer((_) async => false);

      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.start(useCase: ActivateBiometricUseCase.newDevice);

      verify(() => mockBiometricsAuthenticate.isDeviceSupportBiometrics()).called(1);

      expect(result, RequestUserActivateBiometricStatus.unQualified);

      verify(() => mockRequestUserActiveBiometricUtil.isNewDevice()).called(1);
    });

    test('verify with ActivateBiometricUseCase.new_device and isNewDevice = true', () async {
      when(() => mockRequestUserActiveBiometricUtil.isNewDevice()).thenAnswer((_) async => true);

      when(() => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup())
          .thenReturn(mockRequestUserActiveBiometricPopup);

      when(() => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric()).thenAnswer((_) =>
          Future<RequestUserActivateBiometricStatus>.value(
              RequestUserActivateBiometricStatus.success));

      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.start(useCase: ActivateBiometricUseCase.newDevice);

      expect(result, RequestUserActivateBiometricStatus.success);

      verify(() => mockRequestUserActiveBiometricUtil.isNewDevice()).called(1);
      verify(() => mockRequestUserActiveBiometricUtil.saveTimeShowBiometric()).called(1);
      verify(() => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup())
          .called(1);
      verify(() => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric()).called(1);
    });

    test(
        'verify with ActivateBiometricUseCase.payment_flow_completed and call showDialogActiveBiometric()',
        () async {
      const String timeNow = '2023-08-17 10:00:00.000';
      when(() => mockRequestUserActiveBiometricUtil.getTimeShowBiometric())
          .thenAnswer((_) async => timeNow);

      when(() => mockRequestUserActiveBiometricUtil.checkTimeShowBiometric(any())).thenReturn(true);

      when(() => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup())
          .thenReturn(mockRequestUserActiveBiometricPopup);

      when(() => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric()).thenAnswer((_) =>
          Future<RequestUserActivateBiometricStatus>.value(
              RequestUserActivateBiometricStatus.fail));

      final RequestUserActivateBiometricStatus result = await activeBiometricHandler.start(
          useCase: ActivateBiometricUseCase.paymentFlowCompleted);

      expect(result, RequestUserActivateBiometricStatus.fail);

      verify(() => mockRequestUserActiveBiometricUtil.getTimeShowBiometric()).called(1);
      verify(() => mockRequestUserActiveBiometricUtil.checkTimeShowBiometric(timeNow)).called(1);
      verify(() => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup())
          .called(1);
      verify(() => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric()).called(1);
    });
  });

  group('verify showDialogActiveBiometric() correctly', () {
    setUp(() {
      when(() => mockRequestUserActiveBiometricUtil.saveTimeShowBiometric()).thenAnswer((_) async {
        return Future<void>.value();
      });

      when(() => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup())
          .thenReturn(mockRequestUserActiveBiometricPopup);
    });

    test('verify showDialogActiveBiometric() when isShowingPopupActiveBiometric = true', () async {
      activeBiometricHandler.isShowingPopupActiveBiometric = true;

      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.showDialogActiveBiometric();

      expect(result, RequestUserActivateBiometricStatus.inProgress);

      verify(() => mockRequestUserActiveBiometricUtil.saveTimeShowBiometric()).called(1);
      verifyNever(() => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup());
      verifyNever(() => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric());
    });

    test(
        'test can show active biometric dialog and return RequestUserActivateBiometricStatus.success',
        () async {
      activeBiometricHandler.isShowingPopupActiveBiometric = false;

      when(() => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric()).thenAnswer((_) =>
          Future<RequestUserActivateBiometricStatus>.value(
              RequestUserActivateBiometricStatus.success));

      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.showDialogActiveBiometric();

      expect(result, RequestUserActivateBiometricStatus.success);

      verify(() => mockRequestUserActiveBiometricUtil.saveTimeShowBiometric()).called(1);
      verify(() => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup())
          .called(1);
      verify(() => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric()).called(1);
    });

    test('test can show active biometric dialog and return RequestUserActivateBiometricStatus.fail',
        () async {
      activeBiometricHandler.isShowingPopupActiveBiometric = false;

      when(() => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric()).thenAnswer((_) =>
          Future<RequestUserActivateBiometricStatus>.value(
              RequestUserActivateBiometricStatus.fail));

      final RequestUserActivateBiometricStatus result =
          await activeBiometricHandler.showDialogActiveBiometric();

      expect(result, RequestUserActivateBiometricStatus.fail);

      verify(() => mockRequestUserActiveBiometricUtil.saveTimeShowBiometric()).called(1);
      verify(() => mockRequestUserActiveBiometricUtil.getRequestUserActiveBiometricPopup())
          .called(1);
      verify(() => mockRequestUserActiveBiometricPopup.showDialogActiveBiometric()).called(1);
    });
  });
}
