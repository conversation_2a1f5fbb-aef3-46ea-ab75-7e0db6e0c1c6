import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/biometric/activate_biometric/enter_pin_popup/enter_pin_popup_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late EnterPinPopupCubit enterPinPopupCubit;

  setUp(() {
    enterPinPopupCubit = EnterPinPopupCubit();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('verify state and data after init', () {
    test('should enterPinPopupCubit.state is EnterPinPopupState ', () async {
      expect(enterPinPopupCubit.state, isA<EnterPinPopupState>());
    });

    test('verify enterPinPopupCubit.state is true after init state', () {
      expect(enterPinPopupCubit.obscurePinText, isTrue);
    });
  });

  group('test toggleObscurePinText() function', () {
    blocTest<EnterPinPopupCubit, EnterPinPopupState>(
      'should emit ObscureEnterPinState when toggleObscurePinText() is called and obscure is false',
      build: () => enterPinPopupCubit,
      act: (EnterPinPopupCubit cubit) => cubit.toggleObscurePinText(),
      expect: () => <dynamic>[
        isA<ObscureEnterPinState>().having(
          (ObscureEnterPinState state) => state.obscure,
          'verify obscure is false',
          isFalse,
        ),
      ],
    );

    blocTest<EnterPinPopupCubit, EnterPinPopupState>(
      'should emit ObscureEnterPinState when toggleObscurePinText() is called and obscure is true',
      build: () => enterPinPopupCubit,
      act: (EnterPinPopupCubit cubit) {
        ///update obscurePinText to false
        cubit.setObscurePinText = false;
        cubit.toggleObscurePinText();
      },
      expect: () => <dynamic>[
        isA<ObscureEnterPinState>().having(
          (ObscureEnterPinState state) => state.obscure,
          'verify obscure is true',
          isTrue,
        ),
      ],
    );
  });

  group('test showLoading() function', () {
    blocTest<EnterPinPopupCubit, EnterPinPopupState>(
      'should emit LoadingEnterPinState when showLoading() is called and obscure is false',
      build: () => enterPinPopupCubit,
      act: (EnterPinPopupCubit cubit) => cubit.showLoading(false),
      expect: () => <dynamic>[
        isA<LoadingEnterPinState>().having(
          (LoadingEnterPinState state) => state.isShowLoading,
          'verify isShowLoading is false',
          isFalse,
        ),
      ],
    );

    blocTest<EnterPinPopupCubit, EnterPinPopupState>(
      'should emit LoadingEnterPinState when showLoading() is called and obscure is true',
      build: () => enterPinPopupCubit,
      act: (EnterPinPopupCubit cubit) => cubit.showLoading(true),
      expect: () => <dynamic>[
        isA<LoadingEnterPinState>().having(
          (LoadingEnterPinState state) => state.isShowLoading,
          'verify isShowLoading is true',
          isTrue,
        ),
      ],
    );
  });

  group('test handleError() function', () {
    blocTest<EnterPinPopupCubit, EnterPinPopupState>(
      'should emit ErrorEnterPinState when handleError() is called and error.statusCode == CommonHttpClient.BAD_REQUEST',
      build: () => enterPinPopupCubit,
      act: (EnterPinPopupCubit cubit) => cubit.handleError(ErrorUIModel(
        statusCode: CommonHttpClient.BAD_REQUEST,
      )),
      expect: () => <dynamic>[
        isA<ErrorEnterPinState>()
            .having(
              (ErrorEnterPinState error) => error.errorUIModel.statusCode,
              'verify errorUIModel.statusCode is CommonHttpClient.BAD_REQUEST',
              CommonHttpClient.BAD_REQUEST,
            )
            .having(
              (ErrorEnterPinState error) => error.isDismissPopup,
              'verify isDismissPopup is false',
              isFalse,
            ),
      ],
    );

    blocTest<EnterPinPopupCubit, EnterPinPopupState>(
      'should emit ErrorEnterPinState when handleError() is called and error.statusCode != CommonHttpClient.BAD_REQUEST',
      build: () => enterPinPopupCubit,
      act: (EnterPinPopupCubit cubit) => cubit.handleError(ErrorUIModel(
        statusCode: CommonHttpClient.LIMIT_EXCEEDED,
      )),
      expect: () => <dynamic>[
        isA<ErrorEnterPinState>()
            .having(
              (ErrorEnterPinState error) => error.errorUIModel.statusCode,
              'verify errorUIModel.statusCode is CommonHttpClient.LIMIT_EXCEEDED',
              CommonHttpClient.LIMIT_EXCEEDED,
            )
            .having(
              (ErrorEnterPinState error) => error.isDismissPopup,
              'verify isDismissPopup is true',
              isTrue,
            ),
      ],
    );
  });
}
