import 'package:evoapp/feature/payment/widget/amount_input/ui_config/amount_input_border.dart';
import 'package:evoapp/feature/payment/widget/amount_input/ui_config/amount_input_ui_config.dart';
import 'package:evoapp/feature/payment/widget/amount_input/ui_config/currency_display_type.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

void main() {
  group('AmountInputUIConfig', () {
    setUpAll(() {
      registerFallbackValue(Colors.black);
      registerFallbackValue(CurrencyDisplayType.icon);
      registerFallbackValue(AmountDefaultInputBorder());
      registerFallbackValue(AmountNonInputBorder());
      getItRegisterColor();
      getItRegisterTextStyle();
    });

    tearDownAll(() {
      getIt.reset();
    });

    test('defaultInputBorder creates correct configuration', () {
      final AmountInputUIConfig config = AmountInputUIConfig.defaultInputBorder();

      expect(config.textStyle, isA<TextStyle>());
      expect(config.hintStyle, isA<TextStyle>());
      expect(config.errorStyle, isA<TextStyle>());
      expect(config.inputBorder, isA<AmountDefaultInputBorder>());
      expect(config.contentPadding, isNull);
      expect(config.isDense, isNull);
      expect(config.currencyDisplayType, CurrencyDisplayType.icon);
    });

    test('nonInputBorder creates correct configuration', () {
      final AmountInputUIConfig config = AmountInputUIConfig.nonInputBorder();

      expect(config.textStyle, isA<TextStyle>());
      expect(config.hintStyle, isA<TextStyle>());
      expect(config.errorStyle, isA<TextStyle>());
      expect(config.inputBorder, isA<AmountNonInputBorder>());
      expect(config.contentPadding, EdgeInsets.zero);
      expect(config.isDense, true);
      expect(config.currencyDisplayType, CurrencyDisplayType.text);
    });
  });
}
