import 'package:evoapp/feature/payment/models/emi_tenor_ui_model.dart';
import 'package:evoapp/feature/payment/widget/emi_tenor_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockVoidCallBack extends Mock {
  void call();
}

void main() {
  const Duration expectedDuration = Duration(milliseconds: 300);
  const int expectedTenorValue = 1;
  const double expectedHeightPercentageSpecialIcon = 28 / 75;

  late CommonImageProvider mockCommonImageProvider;
  late MockVoidCallBack mockVoidCallBack;
  late BoxDecoration expectedBoxDecorationHasSelected;
  late BoxDecoration expectedBoxDecorationNoSelect;

  void initBoxDecoration() {
    expectedBoxDecorationHasSelected = BoxDecoration(
      color: evoColors.emiTenorBackground,
      border: Border.all(color: evoColors.emiTenorBackground),
      borderRadius: BorderRadius.circular(100),
    );

    expectedBoxDecorationNoSelect = BoxDecoration(
      color: evoColors.background,
      border: Border.all(color: evoColors.emiTenorBackground),
      borderRadius: BorderRadius.circular(100),
    );
  }

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    mockCommonImageProvider = getIt.get<CommonImageProvider>();
    mockVoidCallBack = MockVoidCallBack();

    initBoxDecoration();

    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
        )).thenAnswer((_) => Container());
  });

  Widget pumpItemTenorWidget(EmiTenorUIModel? tenorModel) {
    return MaterialApp(
      home: Scaffold(
        body: EmiTenorWidget(
          tenorModel: tenorModel,
          onTap: mockVoidCallBack.call,
        ),
      ),
    );
  }

  Future<void> verifyOnTap(WidgetTester widgetTester) async {
    final Finder finder = find.byType(EmiTenorWidget);
    await widgetTester.tap(finder);
    await widgetTester.pumpAndSettle();
    verify(() => mockVoidCallBack.call()).called(1);
  }

  void verifyBoxDecoration(BoxDecoration expectedBoxDecoration) {
    final Finder finderAnimatedContainer = find.byWidgetPredicate((Widget widget) {
      if (widget is AnimatedContainer) {
        final AnimatedContainer animatedContainer = widget;
        return animatedContainer.decoration == expectedBoxDecoration &&
            animatedContainer.duration == expectedDuration;
      }
      return false;
    });
    expect(finderAnimatedContainer, findsOneWidget);
  }

  void verifyItemTenorContent({
    required WidgetTester widgetTester,
    required Color expectedColor,
    EmiTenorUIModel? tenorModel,
  }) {
    final Finder finderTextTenor = find.text('${tenorModel?.tenor}');
    expect(finderTextTenor, findsOneWidget);

    final Text textTenor = widgetTester.widget(finderTextTenor);
    expect(textTenor.style, evoTextStyles.h400(color: expectedColor));

    final Finder finderTextMonth = find.text(EvoStrings.suffixTitleEmi);
    expect(finderTextMonth, findsOneWidget);

    final Text textMonth = widgetTester.widget(finderTextMonth);
    expect(textMonth.style, evoTextStyles.bodySmall(color: expectedColor));
  }

  void verifyHeightPercentageSpecialIcon(WidgetTester widgetTester) {
    final Finder finder = find.byType(EmiTenorWidget);
    expect(finder, findsOneWidget);

    final EmiTenorWidget emiTenorWidget = widgetTester.widget(finder);
    expect(emiTenorWidget.heightPercentageSpecialIcon, expectedHeightPercentageSpecialIcon);
  }

  group('test EmiTenorWidget', () {
    testWidgets('verify UI EmiTenorWidget with tenorModel is null',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(pumpItemTenorWidget(null));

      //verify [BoxDecoration] AnimatedContainer
      verifyBoxDecoration(expectedBoxDecorationNoSelect);

      //verify itemTenorContent
      verifyItemTenorContent(
        widgetTester: widgetTester,
        expectedColor: evoColors.emiTenorBackground,
      );

      //verify has not special icon
      verifyNever(() => mockCommonImageProvider.asset(any()));

      //verify onTap is null
      final Finder finder = find.byType(EmiTenorWidget);
      expect(finder, findsOneWidget);

      //verify heightPercentageSpecialIcon
      verifyHeightPercentageSpecialIcon(widgetTester);
    });

    testWidgets('verify UI EmiTenorWidget with isRecommended = true and isSelected = true',
        (WidgetTester widgetTester) async {
      final EmiTenorUIModel tenorModel = EmiTenorUIModel(
        isRecommended: true,
        isSelected: true,
        tenor: expectedTenorValue,
      );
      await widgetTester.pumpWidget(pumpItemTenorWidget(tenorModel));

      //verify [BoxDecoration] AnimatedContainer
      verifyBoxDecoration(expectedBoxDecorationHasSelected);

      //verify itemTenorContent
      verifyItemTenorContent(
        widgetTester: widgetTester,
        tenorModel: tenorModel,
        expectedColor: evoColors.background,
      );

      //verify has special icon
      verify(() => mockCommonImageProvider.asset(
            EvoImages.icEmiStar,
            width: any(named: 'width'),
            height: any(named: 'height'),
          )).called(1);

      await verifyOnTap(widgetTester);

      //verify heightPercentageSpecialIcon
      verifyHeightPercentageSpecialIcon(widgetTester);
    });

    testWidgets('verify UI EmiTenorWidget with isRecommended = true and isSelected = false',
        (WidgetTester widgetTester) async {
      final EmiTenorUIModel tenorModel = EmiTenorUIModel(
        isRecommended: true,
        tenor: expectedTenorValue,
      );

      await widgetTester.pumpWidget(pumpItemTenorWidget(tenorModel));

      //verify [BoxDecoration] AnimatedContainer
      verifyBoxDecoration(expectedBoxDecorationNoSelect);

      //verify itemTenorContent
      verifyItemTenorContent(
        widgetTester: widgetTester,
        tenorModel: tenorModel,
        expectedColor: evoColors.emiTenorBackground,
      );

      //verify has special icon
      verify(() => mockCommonImageProvider.asset(
            EvoImages.icEmiStar,
            width: any(named: 'width'),
            height: any(named: 'height'),
          )).called(1);

      await verifyOnTap(widgetTester);

      //verify heightPercentageSpecialIcon
      verifyHeightPercentageSpecialIcon(widgetTester);
    });

    testWidgets('verify UI EmiTenorWidget with isRecommended = false and isSelected = true',
        (WidgetTester widgetTester) async {
      final EmiTenorUIModel tenorModel = EmiTenorUIModel(
        isSelected: true,
        tenor: expectedTenorValue,
      );

      await widgetTester.pumpWidget(
        pumpItemTenorWidget(tenorModel),
      );

      //verify [BoxDecoration] AnimatedContainer
      verifyBoxDecoration(expectedBoxDecorationHasSelected);

      //verify itemTenorContent
      verifyItemTenorContent(
        widgetTester: widgetTester,
        tenorModel: tenorModel,
        expectedColor: evoColors.background,
      );

      //verify has not special icon
      verifyNever(() => mockCommonImageProvider.asset(any()));

      await verifyOnTap(widgetTester);

      //verify heightPercentageSpecialIcon
      verifyHeightPercentageSpecialIcon(widgetTester);
    });

    testWidgets('verify UI EmiTenorWidget with isRecommended = false and isSelected = false',
        (WidgetTester widgetTester) async {
      final EmiTenorUIModel tenorModel = EmiTenorUIModel(
        tenor: expectedTenorValue,
      );

      await widgetTester.pumpWidget(pumpItemTenorWidget(tenorModel));

      //verify [BoxDecoration] AnimatedContainer
      verifyBoxDecoration(expectedBoxDecorationNoSelect);

      //verify itemTenorContent
      verifyItemTenorContent(
        widgetTester: widgetTester,
        tenorModel: tenorModel,
        expectedColor: evoColors.emiTenorBackground,
      );

      //verify has not special icon
      verifyNever(() => mockCommonImageProvider.asset(any()));

      await verifyOnTap(widgetTester);

      //verify heightPercentageSpecialIcon
      verifyHeightPercentageSpecialIcon(widgetTester);
    });
  });
}
