import 'package:evoapp/feature/payment/widget/amount_input/clear_input_amount_icon_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    getItRegisterColor();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
        )).thenReturn(Container());
  });

  testWidgets('displays clear icon', (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: ClearInputAmountIconWidget(
            onPressed: () {},
          ),
        ),
      ),
    );

    expect(find.byType(GestureDetector), findsOneWidget);
  });

  testWidgets('calls onPressed when icon is tapped', (WidgetTester tester) async {
    bool onPressedCalled = false;

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: ClearInputAmountIconWidget(
            onPressed: () {
              onPressedCalled = true;
            },
          ),
        ),
      ),
    );

    final Finder iconFinder = find.byType(ClearInputAmountIconWidget);
    expect(iconFinder, findsOneWidget);
    final ClearInputAmountIconWidget iconWidget =
        tester.widget(iconFinder) as ClearInputAmountIconWidget;
    iconWidget.onPressed();

    expect(onPressedCalled, true);
  });
}
