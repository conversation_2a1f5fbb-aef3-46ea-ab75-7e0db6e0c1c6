import 'package:evoapp/data/response/promotion_info_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/payment/widget/item_text_order_summary_widget.dart';
import 'package:evoapp/feature/payment/widget/payment_detail_info/emi_summary_info_widget.dart';
import 'package:evoapp/feature/payment/widget/payment_tooltips_widget.dart';
import 'package:evoapp/feature/payment/widget/title_and_description_widget.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockFeatureToggle extends Mock implements FeatureToggle {}

void main() {
  const String expectPaymentSource = 'Show Payment Source Name';
  const String fakeOrderNumber = 'fake_order_number';
  const String fakePartnerOrderId = 'fake_partner_order_id';
  late FeatureToggle mockFeatureToggle;

  bool hasTapManualLinkCard = false;
  void onManualLinkCardCallback() {
    hasTapManualLinkCard = true;
  }

  bool hasTapEmiConversionFee = false;
  void onTapEmiConversionFeeCallback() {
    hasTapEmiConversionFee = true;
  }

  setUpAll(() {
    getIt.registerLazySingleton(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
    getIt.registerLazySingleton<CommonImageProvider>(() => CommonImageProviderImpl());
    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    getIt.registerSingleton<FeatureToggle>(MockFeatureToggle());
    mockFeatureToggle = getIt.get<FeatureToggle>();
  });

  setUp(() {
    when(() => mockFeatureToggle.enableInstantCashbackFeature).thenReturn(true);
  });

  tearDown(() {
    reset(mockFeatureToggle);
    hasTapManualLinkCard = false;
  });

  tearDownAll(() {
    getIt.reset();
  });

  // Helper function to verify text in a TitleAndDescriptionWidget
  void verifyOrderSummaryItem({
    required WidgetTester tester,
    required String titleText,
    required String expectedValue,
  }) {
    final Element titleFinder = tester.element(find.text(titleText));
    final TitleAndDescriptionWidget? widget =
        titleFinder.findAncestorWidgetOfExactType<TitleAndDescriptionWidget>();

    expect(widget, isNotNull);
    final Widget? descriptionWidget = widget?.descriptionWidget;
    expect(descriptionWidget, isA<ItemTextOrderSummaryWidget>());

    final ItemTextOrderSummaryWidget description = descriptionWidget as ItemTextOrderSummaryWidget;
    expect(description.text, expectedValue);
  }

  // Helper function to check if an element exists
  void verifyElementExists(WidgetTester tester, String text, {bool shouldExist = true}) {
    final Finder finder = find.text(text);
    if (shouldExist) {
      expect(finder, findsOneWidget);
    } else {
      expect(finder, findsNothing);
    }
  }

  // Helper function to verify outright purchase summary values
  void verifyOutrightPurchaseSummaryValues({
    required WidgetTester tester,
    String? orderNumber,
    String? partnerOrderId,
    String? totalAmount,
    String? fee,
    String? promotionAmount,
    String? cashbackAmount,
  }) {
    if (orderNumber != null) {
      verifyElementExists(tester, orderNumber);
      verifyOrderSummaryItem(
        tester: tester,
        titleText: EvoStrings.paymentSummaryTransactionCode,
        expectedValue: orderNumber,
      );
    } else {
      verifyElementExists(tester, EvoStrings.paymentSummaryTransactionCode, shouldExist: false);
    }

    if (partnerOrderId != null) {
      verifyElementExists(tester, partnerOrderId);
      verifyOrderSummaryItem(
        tester: tester,
        titleText: EvoStrings.paymentPartnerOrderId,
        expectedValue: partnerOrderId,
      );
    } else {
      verifyElementExists(tester, EvoStrings.paymentPartnerOrderId, shouldExist: false);
    }

    if (totalAmount != null) {
      verifyOrderSummaryItem(
        tester: tester,
        titleText: EvoStrings.paymentSummaryAmount,
        expectedValue: totalAmount,
      );
    }

    if (fee != null) {
      verifyOrderSummaryItem(
        tester: tester,
        titleText: EvoStrings.paymentSummaryFee,
        expectedValue: fee,
      );
    }

    verifyElementExists(
      tester,
      EvoStrings.paymentSummaryPromotionAmount,
      shouldExist: promotionAmount != null,
    );

    verifyElementExists(
      tester,
      EvoStrings.paymentSummaryCashbackAmount,
      shouldExist: cashbackAmount != null,
    );
  }

  // Helper function to verify payment source functionality
  Future<void> verifyPaymentSource({
    required WidgetTester tester,
    required String sourceName,
    required bool expectTapToWork,
  }) async {
    final Finder paymentSourceFinder = find.text(sourceName);
    expect(paymentSourceFinder, findsOneWidget);
    await tester.tap(paymentSourceFinder);
    await tester.pump();
    expect(hasTapManualLinkCard, expectTapToWork);
  }

  // Helper function to verify manual link card behavior
  Future<void> verifyManualLinkCard(WidgetTester tester) async {
    final Finder manualLinkFinder = find.text(EvoStrings.cardStatusNotLinked);
    expect(manualLinkFinder, findsOneWidget);
    await tester.tap(manualLinkFinder);
    await tester.pump();
    expect(hasTapManualLinkCard, true);
  }

  // Helper function to build widget for testing
  Future<EmiSummaryInfoWidget> pumpEmiSummaryWidget({
    required WidgetTester tester,
    String? orderNumber,
    String? partnerOrderId,
    int? totalAmount,
    int? fee,
    int? promotionAmount,
    int? cashbackAmount,
    String? sourceName,
    VoidCallback? onManualLinkCard,
    VoidCallback? onTapEmiConversionFee,
  }) async {
    final EmiSummaryInfoWidget widget = EmiSummaryInfoWidget(
      orderNumber: orderNumber,
      partnerOrderId: partnerOrderId,
      promotionAmount: promotionAmount,
      promotionInfo:
          cashbackAmount != null ? PromotionInfoEntity(cashbackAmount: cashbackAmount) : null,
      sourceName: sourceName,
      onManualLinkCard: onManualLinkCard,
      monthlyInstallmentAmount: null,
      conversionFee: null,
      interestRate: null,
      outrightPurchaseDiff: null,
      onTapEmiConversionFee: onTapEmiConversionFee,
    );

    await tester.pumpWidget(MaterialApp(
      home: Scaffold(
        body: widget,
      ),
    ));

    return widget;
  }

  group('EmiSummaryInfoWidget', () {
    testWidgets('should render the correct information', (WidgetTester tester) async {
      await pumpEmiSummaryWidget(
        tester: tester,
        orderNumber: fakeOrderNumber,
        partnerOrderId: fakePartnerOrderId,
        totalAmount: 25000,
        fee: 1000,
        promotionAmount: 500,
        cashbackAmount: 100,
        sourceName: expectPaymentSource,
        onManualLinkCard: onManualLinkCardCallback,
      );

      verifyElementExists(tester, fakeOrderNumber);
      verifyElementExists(tester, fakePartnerOrderId);
      verifyElementExists(tester, '-500đ');
      verifyElementExists(tester, '+100đ');

      await verifyPaymentSource(
        tester: tester,
        sourceName: expectPaymentSource,
        expectTapToWork: false,
      );
    });

    testWidgets('verify has not payment source and click onManualLinkCard',
        (WidgetTester tester) async {
      await pumpEmiSummaryWidget(
        tester: tester,
        totalAmount: 25000,
        fee: 1000,
        promotionAmount: 500,
        onManualLinkCard: onManualLinkCardCallback,
      );

      await verifyManualLinkCard(tester);
    });

    testWidgets('verify payment_type is Emi & click onTapEmiConversionFee',
        (WidgetTester tester) async {
      final EmiSummaryInfoWidget widget = await pumpEmiSummaryWidget(
        tester: tester,
        totalAmount: 25000,
        fee: 1000,
        promotionAmount: 500,
        onTapEmiConversionFee: onTapEmiConversionFeeCallback,
      );

      widget.onTapEmiConversionFee?.call();

      expect(hasTapEmiConversionFee, true);
    });

    testWidgets('verify payment_type is Emi & show Tooltips', (WidgetTester tester) async {
      await pumpEmiSummaryWidget(
        tester: tester,
        totalAmount: 25000,
        fee: 1000,
        promotionAmount: 500,
      );

      final Finder tooltipsFinder = find.byType(PaymentTooltipsWidget);
      expect(tooltipsFinder, findsOneWidget);

      final Finder emiConversionFeeFinder = find.text(EvoStrings.emiConversionFee);
      expect(emiConversionFeeFinder, findsOneWidget);
    });

    testWidgets('verify cashback not shown when feature is off', (WidgetTester tester) async {
      when(() => mockFeatureToggle.enableInstantCashbackFeature).thenReturn(false);

      await pumpEmiSummaryWidget(
        tester: tester,
        orderNumber: fakeOrderNumber,
        partnerOrderId: fakePartnerOrderId,
        totalAmount: 25000,
        fee: 1000,
        promotionAmount: 500,
        cashbackAmount: 100,
        sourceName: expectPaymentSource,
      );

      verifyElementExists(tester, fakeOrderNumber);
      verifyElementExists(tester, fakePartnerOrderId);
      verifyElementExists(tester, '-500đ');
      // Cashback amount should not be shown when feature is off
      verifyElementExists(tester, '+100đ', shouldExist: false);

      verifyOutrightPurchaseSummaryValues(
        tester: tester,
        orderNumber: fakeOrderNumber,
        partnerOrderId: fakePartnerOrderId,
        promotionAmount: '-500đ',
      );
    });
  });
}
