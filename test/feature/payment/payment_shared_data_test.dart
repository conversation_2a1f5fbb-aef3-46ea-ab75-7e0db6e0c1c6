import 'package:evoapp/data/repository/checkout_repo.dart';
import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/order_extra_info_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/payment/emi_shared_data.dart';
import 'package:evoapp/feature/payment/models/payment_entry_point.dart';
import 'package:evoapp/feature/payment/payment_config.dart';
import 'package:evoapp/feature/payment/payment_shared_data.dart';
import 'package:evoapp/feature/payment/qrcode_scanner/model/qr_code_type.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

void main() {
  const QrProductCode fakeProductCode = QrProductCode.pa02;
  late MockEvoUtilFunction mockEvoUtilFunction;

  setUpAll(() {
    mockEvoUtilFunction = MockEvoUtilFunction();
    getIt.registerLazySingleton<EvoUtilFunction>(() => mockEvoUtilFunction);

    // Mock the evoFormatCurrency method
    when(() => mockEvoUtilFunction.evoFormatCurrency(any(),
        currencySymbol: any(named: 'currencySymbol'))).thenReturn('75.000.000');
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('PaymentSharedData', () {
    test(
      'clearAll() should reset all data to NULL value and reset emiSharedData to default values',
      () {
        const Screen expectOrderCreationScreen = Screen.paymentInputAmount;

        // Arrange
        final PaymentSharedData paymentSharedData = PaymentSharedData();
        paymentSharedData.orderSession = OrderSessionEntity();
        paymentSharedData.selectedEmiPackage = EmiPackageEntity();
        paymentSharedData.qrProductCode = fakeProductCode;
        paymentSharedData.selectedVoucher = VoucherEntity();
        paymentSharedData.emiSharedData.emiConditionInfo = EmiConditionInfo(
          allowProductCodes: <String>['customCode'],
          minUserChargeAmount: 5000000,
        );
        paymentSharedData.selectedPaymentService = PaymentService.emi;
        paymentSharedData.paymentEntryPoint = PaymentEntryPoint.paymentWithEMI;
        paymentSharedData.orderCreationScreen = expectOrderCreationScreen;
        paymentSharedData.orderExtraInfo = OrderExtraInfoEntity(
          enableRatingPrompt: true,
          autoApplyVoucher: true,
        );

        // Custom payment config
        paymentSharedData.paymentConfig = PaymentConfigModel(
          minOrderAmount: 5000,
          maxOrderAmount: 50000000,
        );

        // Act
        paymentSharedData.clearAll();

        // Assert
        expect(paymentSharedData.orderSession, isNull);
        expect(paymentSharedData.selectedEmiPackage, isNull);
        expect(paymentSharedData.qrProductCode, isNull);
        expect(paymentSharedData.selectedVoucher, isNull);
        expect(paymentSharedData.selectedPaymentService, isNull);
        expect(paymentSharedData.paymentEntryPoint, isNull);
        expect(paymentSharedData.orderExtraInfo, isNull);

        expect(paymentSharedData.emiSharedData.emiConditionInfo.allowProductCodes,
            <String>[QrProductCode.pa02String, QrProductCode.vn01String, QrProductCode.pa03String]);
        expect(paymentSharedData.emiSharedData.emiConditionInfo.minUserChargeAmount, 3000000);
        expect(
            paymentSharedData.emiSharedData.emiConditionInfo.productCodeToBlockedMerchants?['VN01'],
            contains('2mHhvqiLPlLYyexOLxmS4R6cxaU'));
        expect(paymentSharedData.orderCreationScreen, null);

        // Verify payment config is reset to default
        expect(
            paymentSharedData.paymentConfig.minOrderAmount, PaymentConfig.defaultMinPaymentAmount);
        expect(
            paymentSharedData.paymentConfig.maxOrderAmount, PaymentConfig.defaultMaxPaymentAmount);
      },
    );

    test(
      'clearOrderInfo() should reset orderSession & selectedVoucher & payment service to NULL value',
      () {
        // Arrange
        final PaymentSharedData paymentSharedData = PaymentSharedData();
        paymentSharedData.orderSession = OrderSessionEntity();
        paymentSharedData.selectedVoucher = VoucherEntity();
        paymentSharedData.selectedEmiPackage = EmiPackageEntity();
        paymentSharedData.qrProductCode = fakeProductCode;
        paymentSharedData.selectedPaymentService = PaymentService.emi;
        paymentSharedData.orderExtraInfo = OrderExtraInfoEntity(
          enableRatingPrompt: true,
          autoApplyVoucher: true,
        );

        paymentSharedData.paymentEntryPoint = PaymentEntryPoint.paymentWithEMI;
        paymentSharedData.emiSharedData.emiConditionInfo = EmiConditionInfo(
          allowProductCodes: <String>['customCode1'],
          minUserChargeAmount: 4000000,
        );

        // Act
        paymentSharedData.clearOrderInfo();

        // Assert
        expect(paymentSharedData.orderSession, isNull);
        expect(paymentSharedData.selectedVoucher, isNull);
        expect(paymentSharedData.selectedPaymentService, isNull);
        expect(paymentSharedData.selectedEmiPackage, isNull);
        expect(paymentSharedData.qrProductCode, isNotNull);
        expect(paymentSharedData.orderExtraInfo, isNotNull);

        expect(paymentSharedData.paymentEntryPoint, PaymentEntryPoint.paymentWithEMI);
        expect(paymentSharedData.emiSharedData.emiConditionInfo.allowProductCodes,
            <String>['customCode1']);
        expect(paymentSharedData.emiSharedData.emiConditionInfo.minUserChargeAmount, 4000000);
      },
    );

    test('orderExtraInfo should be properly set and retrieved', () {
      // Arrange
      final PaymentSharedData paymentSharedData = PaymentSharedData();
      final OrderExtraInfoEntity orderExtraInfo = OrderExtraInfoEntity(
        enableRatingPrompt: true,
        autoApplyVoucher: true,
      );

      // Act
      paymentSharedData.orderExtraInfo = orderExtraInfo;

      // Assert
      expect(paymentSharedData.orderExtraInfo, isNotNull);
      expect(paymentSharedData.orderExtraInfo?.enableRatingPrompt, true);
      expect(paymentSharedData.orderExtraInfo?.autoApplyVoucher, true);
    });

    test('orderExtraInfo should be cleared when clearAll() is called', () {
      // Arrange
      final PaymentSharedData paymentSharedData = PaymentSharedData();
      paymentSharedData.orderExtraInfo = OrderExtraInfoEntity(
        enableRatingPrompt: true,
        autoApplyVoucher: true,
      );

      // Verify it's set
      expect(paymentSharedData.orderExtraInfo, isNotNull);

      // Act
      paymentSharedData.clearAll();

      // Assert
      expect(paymentSharedData.orderExtraInfo, isNull);
    });
  });

  group('PaymentConfigModel', () {
    test('should initialize with default values', () {
      // Act
      final PaymentConfigModel paymentConfig = PaymentConfigModel();

      // Assert
      expect(paymentConfig.minOrderAmount, PaymentConfig.defaultMinPaymentAmount);
      expect(paymentConfig.maxOrderAmount, PaymentConfig.defaultMaxPaymentAmount);
    });

    test('should initialize with custom values', () {
      // Arrange
      const int customMinAmount = 5000;
      const int customMaxAmount = 50000000;

      // Act
      final PaymentConfigModel paymentConfig = PaymentConfigModel(
        minOrderAmount: customMinAmount,
        maxOrderAmount: customMaxAmount,
      );

      // Assert
      expect(paymentConfig.minOrderAmount, customMinAmount);
      expect(paymentConfig.maxOrderAmount, customMaxAmount);
    });
  });
}
