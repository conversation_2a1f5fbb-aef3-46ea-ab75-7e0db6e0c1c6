import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/payment/amount_validator.dart';
import 'package:evoapp/feature/payment/input_amount/amount_input_cubit.dart';
import 'package:evoapp/feature/payment/input_amount/amount_input_state.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';

class MockAmountValidator extends Mock implements AmountValidator {}

void main() {
  late AmountInputCubit amountInputCubit;
  final AmountValidator mockAmountValidator = MockAmountValidator();
  const String expectedError = 'expectedError';
  const String amount = '1000';
  const String expectedCurrency = 'đ';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
  });

  setUp(() {
    amountInputCubit = AmountInputCubit(
      amountValidator: mockAmountValidator,
    );
  });

  tearDownAll(() {
    getIt.reset();
  });

  test('Initial state', () {
    expect(amountInputCubit.state, isA<AmountInputIdleState>());
  });

  group('Test validateChangedAmount', () {
    blocTest<AmountInputCubit, AmountInputState>(
      'Test null amount',
      build: () => amountInputCubit,
      act: (AmountInputCubit cubit) => cubit.validateChangedAmount(null),
      expect: () => <dynamic>[
        isA<AmountInputIdleState>(),
      ],
      verify: (AmountInputCubit cubit) {
        verifyNever(
          () => mockAmountValidator.getAmountError(
            amount: any(named: 'amount'),
            currencySuffix: any(named: 'currencySuffix'),
            productCode: any(named: 'productCode'),
          ),
        );
      },
    );

    blocTest<AmountInputCubit, AmountInputState>(
      'Test empty amount',
      build: () => amountInputCubit,
      act: (AmountInputCubit cubit) => cubit.validateChangedAmount(''),
      expect: () => <dynamic>[
        isA<AmountInputIdleState>(),
      ],
      verify: (AmountInputCubit cubit) {
        verifyNever(
          () => mockAmountValidator.getAmountError(
            amount: any(named: 'amount'),
            currencySuffix: any(named: 'currencySuffix'),
            productCode: any(named: 'productCode'),
          ),
        );
      },
    );

    blocTest<AmountInputCubit, AmountInputState>(
      'Test invalid amount',
      build: () => amountInputCubit,
      setUp: () {
        when(
          () => mockAmountValidator.getAmountError(
            amount: any(named: 'amount'),
            currencySuffix: any(named: 'currencySuffix'),
            productCode: any(named: 'productCode'),
          ),
        ).thenReturn(expectedError);
      },
      act: (AmountInputCubit cubit) => cubit.validateChangedAmount('abc'),
      expect: () => <dynamic>[
        isA<InvalidAmountState>().having(
          (InvalidAmountState p0) => p0.errorMessage,
          'test error message',
          expectedError,
        ),
      ],
      verify: (AmountInputCubit cubit) {
        expect(
          verify(
            () => mockAmountValidator.getAmountError(
              amount: captureAny(named: 'amount'),
              currencySuffix: captureAny(named: 'currencySuffix'),
              productCode: any(named: 'productCode'),
            ),
          ).captured,
          <dynamic>[
            'abc',
            expectedCurrency,
          ],
        );
      },
    );

    blocTest<AmountInputCubit, AmountInputState>(
      'Test valid amount',
      build: () => amountInputCubit,
      setUp: () {
        when(
          () => mockAmountValidator.getAmountError(
            amount: any(named: 'amount'),
            currencySuffix: any(named: 'currencySuffix'),
            productCode: any(named: 'productCode'),
          ),
        ).thenReturn(null);
      },
      act: (AmountInputCubit cubit) => cubit.validateChangedAmount(amount),
      expect: () => <dynamic>[
        isA<ValidAmountState>(),
      ],
      verify: (AmountInputCubit cubit) {
        expect(
          verify(
            () => mockAmountValidator.getAmountError(
              amount: captureAny(named: 'amount'),
              currencySuffix: captureAny(named: 'currencySuffix'),
              productCode: any(named: 'productCode'),
            ),
          ).captured,
          <dynamic>[
            amount,
            expectedCurrency,
          ],
        );
      },
    );
  });

  group('Test setButtonValidState', () {
    blocTest<AmountInputCubit, AmountInputState>(
      'Should emit ValidAmountState when setButtonValidState is called',
      build: () => amountInputCubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (AmountInputCubit cubit) => cubit.setValidState(),
      expect: () => <dynamic>[
        isA<ValidAmountState>(),
      ],
    );
  });
}
