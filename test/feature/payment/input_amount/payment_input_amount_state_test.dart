import 'package:evoapp/data/repository/checkout_repo.dart';
import 'package:evoapp/data/response/action_entity.dart';
import 'package:evoapp/data/response/create_order_entity.dart';
import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/feature/payment/input_amount/bloc/payment_input_amount_state.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

void main() {
  final OrderSessionEntity orderSession = OrderSessionEntity();
  final List<EmiPackageEntity> emiPackages = <EmiPackageEntity>[EmiPackageEntity()];
  final PaymentService paymentService = PaymentService.emi;
  final ActionEntity prerequisitesAction = ActionEntity();
  final ErrorUIModel error = ErrorUIModel();

  test('PaymentInputAmountInitial should be an instance of PaymentInputAmountState', () {
    final PaymentInputAmountInitial state = PaymentInputAmountInitial();
    expect(state, isA<PaymentInputAmountState>());
  });

  test('PaymentInputAmountNoteUpdated should contain noteText', () {
    final PaymentInputAmountNoteUpdated state =
        PaymentInputAmountNoteUpdated(noteText: 'Test Note');
    expect(state.noteText, 'Test Note');
    expect(state, isA<PaymentInputAmountState>());
  });

  test('CreatePaymentLoadingState should be an instance of PaymentInputAmountState', () {
    final CreatePaymentLoadingState state = CreatePaymentLoadingState();
    expect(state, isA<PaymentInputAmountState>());
  });

  test('CreatePaymentSuccessState should contain the necessary properties', () {
    final CreatePaymentSuccessState state = CreatePaymentSuccessState(
      paymentService: paymentService,
      orderSession: orderSession,
      emiPackages: emiPackages,
      prerequisitesAction: prerequisitesAction,
    );

    expect(state.paymentService, paymentService);
    expect(state.orderSession, orderSession);
    expect(state.emiPackages, emiPackages);
    expect(state.prerequisitesAction, prerequisitesAction);
    expect(state, isA<PaymentInputAmountState>());
  });

  test('CreatePaymentErrorState should contain error', () {
    final CreatePaymentErrorState state = CreatePaymentErrorState(error);

    expect(state.error, error);
    expect(state, isA<PaymentInputAmountState>());
  });

  test('CreatePaymentWithEMIUnqualifiedState should contain amount and error', () {
    final CreatePaymentWithEMIUnqualifiedState state =
        CreatePaymentWithEMIUnqualifiedState(amount: 100, error: error);

    expect(state.amount, 100);
    expect(state.error, error);
    expect(state, isA<PaymentInputAmountState>());
  });

  test('CreatePaymentWithActivePosLimitErrorState', () {
    final CreateOrderEntity createOrderEntity = CreateOrderEntity();
    final CreatePaymentWithActivePosLimitErrorState state =
        CreatePaymentWithActivePosLimitErrorState(
            entity: createOrderEntity, paymentService: paymentService);

    expect(state, isA<PaymentInputAmountState>());
  });

  test('ValidateSubmittedAmountError should contain errorMessage', () {
    final ValidateSubmittedAmountError state =
        ValidateSubmittedAmountError(errorMessage: 'Invalid amount');

    expect(state.errorMessage, 'Invalid amount');
    expect(state, isA<PaymentInputAmountState>());
  });

  test('ValidateSubmittedAmountSuccess should contain amount and paymentService', () {
    final ValidateSubmittedAmountSuccess state =
        ValidateSubmittedAmountSuccess(amount: 100, paymentService: paymentService);

    expect(state.amount, 100);
    expect(state.paymentService, paymentService);
    expect(state, isA<PaymentInputAmountState>());
  });
}
