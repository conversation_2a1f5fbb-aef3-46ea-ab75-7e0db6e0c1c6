import 'package:evoapp/feature/payment/utils/emi_format_content_utils.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';

class FakeEmiFormatContentUtils with EmiUIFormatContentUtils {}

void main() {
  final FakeEmiFormatContentUtils utils = FakeEmiFormatContentUtils();

  setUpAll(() {
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
  });

  test('formatCurrency should return formatted currency', () {
    /// Number("#,###").format() in [EvoUtilFunction.evoFormatCurrency] method is using the default
    /// locale of the device which run UT , which might have a comma separator instead of a dot separator for grouping.
    expect(utils.formatAmountWithVietnameseCurrency(1000), '1,000đ');
    expect(utils.formatAmountWithVietnameseCurrency(1000000), '1,000,000đ');
    expect(utils.formatAmountWithVietnameseCurrency(null), '-');
  });

  test('formatInterestRate should return formatted interest rate', () {
    expect(utils.formatInterestRate(5.5), '5.5%');
    expect(utils.formatInterestRate(9.99), '9.99%');
    expect(utils.formatInterestRate(0.0), '0%');
    expect(utils.formatInterestRate(null), '-');
  });
}
