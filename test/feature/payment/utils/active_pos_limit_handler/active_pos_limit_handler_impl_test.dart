import 'package:evoapp/data/repository/checkout_repo.dart';
import 'package:evoapp/data/response/action_entity.dart';
import 'package:evoapp/data/response/create_order_entity.dart';
import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/feature/activated_pos_limit/activate_card_guidance/activate_card_guidance_screen.dart';
import 'package:evoapp/feature/activated_pos_limit/models/activated_pos_limit_flow_failed_reason.dart';
import 'package:evoapp/feature/activated_pos_limit/models/card_confirm_activation_succeed_payload.dart';
import 'package:evoapp/feature/activated_pos_limit/setup_pos_limit_guidance/setup_pos_limit_guidance_screen.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/main_screen/main_screen.dart';
import 'package:evoapp/feature/payment/mock_file/mock_checkout_file_name.dart';
import 'package:evoapp/feature/payment/utils/active_pos_limit_handler/active_pos_limit_handler.dart';
import 'package:evoapp/model/evo_action_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_action_handler.dart';
import 'package:evoapp/util/web_link_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';
import '../../../../util/flutter_test_config.dart';
import '../../../../util/test_util.dart';

class MockFeatureToggle extends Mock implements FeatureToggle {}

class TestActivePosLimitHandlerImpl extends ActivePosLimitHandlerImpl {
  bool isCalledCreateActivatePOSLimitFlow = false;
  bool setActivatePosLimitScreen = false;

  @override
  void createActivatePOSLimitFlow(
    OrderActivatePosLimitEntity orderActivatePosLimitEntity, {
    required Screen entryPoint,
    int? orderAmount,
    void Function(OrderActivatePosLimitEntity)? onActivatedPOSLimitFlowSucceed,
  }) {
    isCalledCreateActivatePOSLimitFlow = true;
  }

  @override
  bool isActivatePosLimitScreen(String? screenName) {
    return setActivatePosLimitScreen;
  }
}

void main() {
  late ActivePosLimitHandlerImpl handler;
  late BuildContext mockNavigatorContext;
  late CommonNavigator commonNavigator;
  late FeatureToggle mockFeatureToggle;

  final OrderSessionEntity orderSession = OrderSessionEntity();
  final List<EmiPackageEntity> emiPackages = <EmiPackageEntity>[EmiPackageEntity()];
  final PaymentService paymentService = PaymentService.outrightPurchase;
  final OrderActivatePosLimitEntity mockOrderActivatePosLimitEntity = OrderActivatePosLimitEntity(
    paymentService: paymentService,
    orderSession: orderSession,
    emiPackages: emiPackages,
  );

  final CardConfirmActivationSucceedPayload mockCardConfirmActivationSucceedPayload =
      CardConfirmActivationSucceedPayload();

  final AcceptedActivationCardAndPOSLimitSucceedPayload
      mockAcceptedActivationCardAndPOSLimitSucceedPayload =
      AcceptedActivationCardAndPOSLimitSucceedPayload();

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(MockBuildContext());

    mockNavigatorContext = MockBuildContext();
    commonNavigator = MockCommonNavigator();
    getIt.registerSingleton<CommonNavigator>(commonNavigator);
    setUpMockGlobalKeyProvider(mockNavigatorContext);

    getIt.registerSingleton<AppState>(AppState());

    getIt.registerSingleton<FeatureToggle>(MockFeatureToggle());
    mockFeatureToggle = getIt.get<FeatureToggle>();

    handler = ActivePosLimitHandlerImpl();
  });

  tearDown(() {
    reset(mockNavigatorContext);
    reset(commonNavigator);
  });

  group('verify ActivePosLimitHandlerImpl', () {
    group('handleActivateCardOrPosLimitIfNeed', () {
      late TestActivePosLimitHandlerImpl testHandler;

      setUpAll(() {
        registerFallbackValue(EvoActionModel());
        getItRegisterColor();
        getIt.registerSingleton<WebLinkUtils>(WebLinkUtils());
        EvoActionHandler.setInstanceForTesting(MockEvoActionHandler());
        when(() => EvoActionHandler().handle(any(), arg: any(named: 'arg')))
            .thenAnswer((_) async => true);

        setUpOneLinkDeepLinkRegExForTest();
      });

      setUp(() {
        testHandler = TestActivePosLimitHandlerImpl();
      });

      test('should return false if feature is disabled', () async {
        when(() => mockFeatureToggle.enableActivatePOSLimitFeature).thenReturn(false);

        final bool result = await testHandler.handleActivateCardOrPosLimitIfNeed(
          orderActivatePosLimitEntity:
              OrderActivatePosLimitEntity(paymentService: PaymentService.outrightPurchase),
          entryPoint: Screen.activateCardScreen,
        );

        expect(result, false);
      });

      test('should return false if prerequisitesAction is null', () async {
        when(() => mockFeatureToggle.enableActivatePOSLimitFeature).thenReturn(true);

        final bool result = await testHandler.handleActivateCardOrPosLimitIfNeed(
          orderActivatePosLimitEntity:
              OrderActivatePosLimitEntity(paymentService: PaymentService.outrightPurchase),
          entryPoint: Screen.activateCardScreen,
        );

        expect(result, false);
      });

      test('should return false if screenName is not activate POS limit screen', () async {
        when(() => mockFeatureToggle.enableActivatePOSLimitFeature).thenReturn(true);
        testHandler.setActivatePosLimitScreen = false;

        final ActionEntity mockActionEntity = ActionEntity(
          args: ArgsEntity(screenName: Screen.homeScreen.name),
        );

        final bool result = await handler.handleActivateCardOrPosLimitIfNeed(
          orderActivatePosLimitEntity: OrderActivatePosLimitEntity(
            paymentService: PaymentService.outrightPurchase,
            prerequisitesAction: mockActionEntity,
          ),
          entryPoint: Screen.activateCardScreen,
        );

        expect(result, false);
      });

      test('should call createActivatePOSLimitFlow and handle action', () async {
        final ActionEntity mockActionEntity = ActionEntity(
          args: ArgsEntity(screenName: Screen.activateCardScreen.name),
        );
        testHandler.setActivatePosLimitScreen = true;

        when(() => mockFeatureToggle.enableActivatePOSLimitFeature).thenReturn(true);
        when(() => mockFeatureToggle.enableEventTrackingFeature).thenReturn(true);

        final bool result = await testHandler.handleActivateCardOrPosLimitIfNeed(
          orderActivatePosLimitEntity: OrderActivatePosLimitEntity(
            paymentService: PaymentService.outrightPurchase,
            prerequisitesAction: mockActionEntity,
          ),
          entryPoint: Screen.activateCardScreen,
        );

        verify(() => EvoActionHandler().handle(any(), arg: any(named: 'arg'))).called(1);
        expect(result, true);
      });
    });

    group('handleActivatedPOSLimitErrorState', () {
      late TestActivePosLimitHandlerImpl testHandler;
      late CreateOrderEntity mockCreateOrderEntity;

      setUp(() {
        testHandler = TestActivePosLimitHandlerImpl();
      });

      test(
          'should call createActivatePOSLimitFlow and push ActivateCardGuidanceScreen when verdict is verdictUnqualifiedToActive',
          () async {
        final String mockFileName =
            createOrderMockFileName(verdict: CreateOrderMockVerdict.openActiveCardGuidanceScreen);
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

        mockCreateOrderEntity = CreateOrderEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.BAD_REQUEST,
          response: responseData,
        ));

        testHandler.handleActivatedPOSLimitErrorState(
          orderActivatePosLimitEntity: mockOrderActivatePosLimitEntity,
          entryPoint: Screen.activateCardScreen,
          entity: mockCreateOrderEntity,
        );

        expect(testHandler.isCalledCreateActivatePOSLimitFlow, true);
        verify(() => ActivateCardGuidanceScreen.pushNamed()).called(1);
      });

      test(
          'should call createActivatePOSLimitFlow and push SetupPosLimitGuidanceScreen when verdict is verdictPosLimitInsufficient',
          () async {
        final String mockFileName =
            createOrderMockFileName(verdict: CreateOrderMockVerdict.setPosLimitGuidanceScreen);
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

        mockCreateOrderEntity = CreateOrderEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.BAD_REQUEST,
          response: responseData,
        ));

        testHandler.handleActivatedPOSLimitErrorState(
          orderActivatePosLimitEntity: mockOrderActivatePosLimitEntity,
          entryPoint: Screen.setupPosLimitScreen,
          entity: mockCreateOrderEntity,
        );

        expect(testHandler.isCalledCreateActivatePOSLimitFlow, true);
        verify(() => SetupPosLimitGuidanceScreen.pushNamed()).called(1);
      });

      test(
          'should call createActivatePOSLimitFlow and push SetupPosLimitGuidanceScreen when verdict is verdictCardUnqualifiedToSetPosLimit',
          () async {
        final String mockFileName =
            createOrderMockFileName(verdict: CreateOrderMockVerdict.cardUnqualifiedToSetPosLimit);
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

        mockCreateOrderEntity = CreateOrderEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.BAD_REQUEST,
          response: responseData,
        ));

        testHandler.handleActivatedPOSLimitErrorState(
          orderActivatePosLimitEntity: mockOrderActivatePosLimitEntity,
          entryPoint: Screen.setupPosLimitScreen,
          entity: mockCreateOrderEntity,
        );

        expect(testHandler.isCalledCreateActivatePOSLimitFlow, true);
        verify(() => SetupPosLimitGuidanceScreen.pushNamed()).called(1);
      });

      test('should throw exception for unknown verdict', () async {
        final String mockFileName =
            createOrderMockFileName(verdict: CreateOrderMockVerdict.emiUnqualified);
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

        mockCreateOrderEntity = CreateOrderEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.BAD_REQUEST,
          response: responseData,
        ));

        expect(
            () => testHandler.handleActivatedPOSLimitErrorState(
                  orderActivatePosLimitEntity: mockOrderActivatePosLimitEntity,
                  entryPoint: Screen.setupPosLimitScreen,
                  entity: mockCreateOrderEntity,
                ),
            throwsException);
      });
    });

    group('isActivatePosLimitScreen', () {
      test('should return true for activateCardScreen', () {
        expect(handler.isActivatePosLimitScreen(Screen.activateCardScreen.name), isTrue);
      });

      test('should return true for setupPosLimitScreen', () {
        expect(handler.isActivatePosLimitScreen(Screen.setupPosLimitScreen.name), isTrue);
      });

      test('should return false for other screens', () {
        expect(handler.isActivatePosLimitScreen('otherScreen'), isFalse);
      });

      test('should return false for null screenName', () {
        expect(handler.isActivatePosLimitScreen(null), isFalse);
      });
    });

    group('handleActivatedPOSLimitFlowSucceed', () {
      test('should pop to Scan QR Code screen when payload is CardConfirmActivationSucceedPayload',
          () {
        handler.handleActivatedPOSLimitFlowSucceed(
          orderActivatePosLimitEntity: mockOrderActivatePosLimitEntity,
          payload: mockCardConfirmActivationSucceedPayload,
          entryPoint: Screen.qrCodeScannerScreen,
        );

        verify(() => mockNavigatorContext.popUntilNamed(Screen.qrCodeScannerScreen.name)).called(1);
      });

      test(
          'should pop to Scan QR Code screen when payload is AcceptedActivationCardAndPOSLimitSucceedPayload',
          () {
        handler.handleActivatedPOSLimitFlowSucceed(
          orderActivatePosLimitEntity: mockOrderActivatePosLimitEntity,
          payload: mockAcceptedActivationCardAndPOSLimitSucceedPayload,
          entryPoint: Screen.paymentInputAmount,
        );

        verify(() => mockNavigatorContext.popUntilNamed(Screen.paymentInputAmount.name)).called(1);
      });

      test('should do nothing if orderSession is null', () {
        final OrderActivatePosLimitEntity mockOrderActivatePosLimitEntity =
            OrderActivatePosLimitEntity(
          paymentService: PaymentService.outrightPurchase,
        );

        handler.handleActivatedPOSLimitFlowSucceed(
          orderActivatePosLimitEntity: mockOrderActivatePosLimitEntity,
          entryPoint: Screen.qrCodeScannerScreen,
        );

        verifyNever(() => mockNavigatorContext.popUntilNamed(any()));
      });

      test('should call onActivatedPOSLimitFlowSucceed with new entity', () {
        bool callbackCalled = false;
        handler.handleActivatedPOSLimitFlowSucceed(
          orderActivatePosLimitEntity: mockOrderActivatePosLimitEntity,
          onActivatedPOSLimitFlowSucceed: (OrderActivatePosLimitEntity newEntity) {
            callbackCalled = true;
            expect(newEntity.orderSession, orderSession);
            expect(newEntity.emiPackages, emiPackages);
            expect(newEntity.paymentService, paymentService);
          },
          entryPoint: Screen.qrCodeScannerScreen,
        );

        expect(callbackCalled, isTrue);
      });
    });

    group('handleActivatedPOSLimitFlowFailed', () {
      test('should pop to Scan QR Code screen when reason is userCancelled', () {
        handler.handleActivatedPOSLimitFlowFailed(
          reason: ActivatedPOSLimitFlowFailedReason.userCancelled,
        );

        verify(() => mockNavigatorContext.popUntilNamed(Screen.qrCodeScannerScreen.name)).called(1);
      });

      test('should pop to Scan QR Code screen when reason is userPayAgain', () {
        handler.handleActivatedPOSLimitFlowFailed(
          reason: ActivatedPOSLimitFlowFailedReason.userPayAgain,
        );

        verify(() => mockNavigatorContext.popUntilNamed(Screen.qrCodeScannerScreen.name)).called(1);
      });

      test('should remove until and push replacement named when reason is userNotActivatedCard',
          () {
        handler.handleActivatedPOSLimitFlowFailed(
          reason: ActivatedPOSLimitFlowFailedReason.userNotActivatedCard,
        );

        expect(
            verify(
              () => mockNavigatorContext.removeUntilAndPushReplacementNamed(
                any(),
                any(),
                extra: captureAny(named: 'extra'),
              ),
            ).captured.single,
            isA<MainScreenArg>().having(
              (MainScreenArg p0) => p0.isLoggedIn,
              'verify isLoggedIn',
              true,
            ));
      });

      test('should remove until and push replacement named when reason is userNotSetPosLimit', () {
        handler.handleActivatedPOSLimitFlowFailed(
          reason: ActivatedPOSLimitFlowFailedReason.userNotSetPosLimit,
        );

        expect(
            verify(
              () => mockNavigatorContext.removeUntilAndPushReplacementNamed(
                any(),
                any(),
                extra: captureAny(named: 'extra'),
              ),
            ).captured.single,
            isA<MainScreenArg>().having(
              (MainScreenArg p0) => p0.isLoggedIn,
              'verify isLoggedIn',
              true,
            ));
      });

      test('should navigate to activateCardGuidanceScreen when reason is redirectToTPBApp', () {
        handler.handleActivatedPOSLimitFlowFailed(
          reason: ActivatedPOSLimitFlowFailedReason.redirectToTPBApp,
        );

        verify(() => mockNavigatorContext.popUntilNamed(Screen.qrCodeScannerScreen.name)).called(1);
        verify(() => mockNavigatorContext.pushNamed(Screen.activateCardGuidanceScreen.name))
            .called(1);
      });

      test('should log error message for other reasons', () {
        handler.handleActivatedPOSLimitFlowFailed(
          reason: ActivatedPOSLimitFlowFailedReason.unknown,
          userMessage: 'Test error message',
        );

        verifyNever(() => mockNavigatorContext.popUntilNamed(any()));
        verifyNever(
          () => mockNavigatorContext.removeUntilAndPushReplacementNamed(
            any(),
            any(),
            extra: captureAny(named: 'extra'),
          ),
        );
      });
    });
  });

  group('OrderActivatePosLimitEntity', () {
    test('should create an instance with all properties', () {
      final OrderSessionEntity orderSession = OrderSessionEntity();
      final List<EmiPackageEntity> emiPackages = <EmiPackageEntity>[EmiPackageEntity()];
      final PaymentService paymentService = PaymentService.outrightPurchase;
      final ActionEntity prerequisitesAction = ActionEntity();

      final OrderActivatePosLimitEntity entity = OrderActivatePosLimitEntity(
        paymentService: paymentService,
        orderSession: orderSession,
        emiPackages: emiPackages,
        prerequisitesAction: prerequisitesAction,
      );

      expect(entity.paymentService, paymentService);
      expect(entity.orderSession, orderSession);
      expect(entity.emiPackages, emiPackages);
      expect(entity.prerequisitesAction, prerequisitesAction);
    });

    test('should create an instance with only required properties', () {
      final PaymentService paymentService = PaymentService.outrightPurchase;

      final OrderActivatePosLimitEntity entity = OrderActivatePosLimitEntity(
        paymentService: paymentService,
      );

      expect(entity.paymentService, paymentService);
      expect(entity.orderSession, isNull);
      expect(entity.emiPackages, isNull);
      expect(entity.prerequisitesAction, isNull);
    });
  });
}
