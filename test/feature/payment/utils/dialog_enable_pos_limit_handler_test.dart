import 'package:evoapp/feature/payment/utils/dialog_enable_pos_limit_handler.dart';
import 'package:evoapp/feature/payment/widget/enable_pos_limit_guide/enable_pos_limit_guide_widget.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockDialogEnablePosLimitHandler extends DialogEnablePosLimitHandler {
  bool showDialogEnablePosLimitCalled = false;

  @override
  Future<void> showDialogEnablePosLimit({
    required VoidCallback onClickNegative,
    required VoidCallback onClickPositive,
  }) {
    showDialogEnablePosLimitCalled = true;
    return Future<void>.value();
  }
}

void main() {
  bool onClickNegativeCalled = false;
  bool onClickPositiveCalled = false;

  fakeOnClickNegative() {
    onClickNegativeCalled = true;
  }

  fakeOnClickPositive() {
    onClickPositiveCalled = true;
  }

  setUp(() {
    onClickNegativeCalled = false;
    onClickPositiveCalled = false;
  });

  setUpAll(() {
    setUtilsMockInstanceForTesting();
  });

  tearDownAll(() {
    resetUtilMockToOriginalInstance();
  });

  test('Test DialogEnablePosLimitHandler constants', () {
    expect(
        DialogEnablePosLimitHandler.delayDurationToHandleDialogEnablePosLimitShowing,
        isA<Duration>().having(
          (Duration p0) => p0.inMilliseconds,
          'verify delay time',
          2000,
        ));
  });

  group('Test showDialogEnablePosLimit', () {
    final DialogEnablePosLimitHandler paymentDialogHandler = DialogEnablePosLimitHandler();

    setUpAll(() {
      registerFallbackValue(EvoDialogId.posLimitBottomSheet);

      when(
        () => EvoDialogHelper().showDialogBottomSheet(
          title: any(named: 'title'),
          content: any(named: 'content'),
          dialogId: any(named: 'dialogId'),
          isShowButtonClose: any(named: 'isShowButtonClose'),
          header: any(named: 'header'),
          headerPadding: any(named: 'headerPadding'),
          buttonListOrientation: any(named: 'buttonListOrientation'),

          /// Negative button
          textNegative: any(named: 'textNegative'),
          onClickNegative: any(named: 'onClickNegative'),

          /// Positive button
          textPositive: any(named: 'textPositive'),
          onClickPositive: any(named: 'onClickPositive'),
        ),
      ).thenAnswer((_) => Future<void>.value());
    });

    tearDownAll(() {
      getIt.reset();
    });

    test('Test showDialogEnablePosLimit', () async {
      paymentDialogHandler.showDialogEnablePosLimit(
        onClickNegative: fakeOnClickNegative,
        onClickPositive: fakeOnClickPositive,
      );
      final List<dynamic> capturedResult = verify(
        () => EvoDialogHelper().showDialogBottomSheet(
          title: EvoStrings.posLimitTitle,
          content: EvoStrings.posLimitDescription,
          dialogId: EvoDialogId.posLimitBottomSheet,
          isShowButtonClose: true,
          header: captureAny(named: 'header'),
          headerPadding: EdgeInsets.zero,
          buttonListOrientation: ButtonListOrientation.verticalDown,

          /// Negative button
          textNegative: EvoStrings.moveToHome,
          onClickNegative: captureAny(named: 'onClickNegative'),

          /// Positive button
          textPositive: EvoStrings.enablePosLimitButton,
          onClickPositive: captureAny(named: 'onClickPositive'),
        ),
      ).captured;

      expect(
        capturedResult[0],
        isA<EnablePosLimitGuideWidget>().having(
          (EnablePosLimitGuideWidget widget) => widget.images,
          'images',
          <String>[
            EvoImages.imgPosEnableStep1,
            EvoImages.imgPosEnableStep2,
            EvoImages.imgPosEnableStep3,
            EvoImages.imgPosEnableStep4,
          ],
        ),
      );

      /// Verify negative button
      final VoidCallback onClickNegative = capturedResult[1] as VoidCallback;
      onClickNegative.call();
      expect(onClickNegativeCalled, true);

      /// Verify positive button
      final VoidCallback onClickPositive = capturedResult[2] as VoidCallback;
      onClickPositive.call();
      expect(onClickPositiveCalled, true);
    });
  });

  group('Test delayToHandleDialogEnablePosLimitShowing', () {
    late MockDialogEnablePosLimitHandler paymentDialogHandler;

    setUp(() {
      paymentDialogHandler = MockDialogEnablePosLimitHandler();
    });

    test(
        'Give isDialogEnablePosLimitShown == false, delayToHandleDialogEnablePosLimitShowing will call showDialogEnablePosLimit after delay time',
        () async {
      expect(paymentDialogHandler.isDialogEnablePosLimitShown, false);
      paymentDialogHandler.delayToHandleDialogEnablePosLimitShowing(
        onClickNegative: fakeOnClickNegative,
        onClickPositive: fakeOnClickPositive,
      );

      /// The function showDialogEnablePosLimit is not called yet before delay time
      expect(paymentDialogHandler.isDialogEnablePosLimitShown, false);

      await Future<void>.delayed(
          DialogEnablePosLimitHandler.delayDurationToHandleDialogEnablePosLimitShowing);
      expect(paymentDialogHandler.isDialogEnablePosLimitShown, true);

      expect(paymentDialogHandler.showDialogEnablePosLimitCalled, true);
    });

    test(
        'Give isDialogEnablePosLimitShown == true, delayToHandleDialogEnablePosLimitShowing will NOT call showDialogEnablePosLimit after delay time',
        () async {
      paymentDialogHandler.isDialogEnablePosLimitShown = true;
      paymentDialogHandler.delayToHandleDialogEnablePosLimitShowing(
        onClickNegative: fakeOnClickNegative,
        onClickPositive: fakeOnClickPositive,
      );

      await Future<void>.delayed(
          DialogEnablePosLimitHandler.delayDurationToHandleDialogEnablePosLimitShowing);

      expect(paymentDialogHandler.showDialogEnablePosLimitCalled, false);
    });
  });

  group('Test handleDialogEnablePosLimitShowing', () {
    late MockDialogEnablePosLimitHandler paymentDialogHandler;

    setUp(() {
      paymentDialogHandler = MockDialogEnablePosLimitHandler();
    });

    test(
        'Give isDialogEnablePosLimitShown == false, handleDialogEnablePosLimitShowing will call showDialogEnablePosLimit',
        () {
      expect(paymentDialogHandler.isDialogEnablePosLimitShown, false);
      paymentDialogHandler.handleDialogEnablePosLimitShowing(
        onClickNegative: fakeOnClickNegative,
        onClickPositive: fakeOnClickPositive,
      );

      expect(paymentDialogHandler.isDialogEnablePosLimitShown, true);
      expect(paymentDialogHandler.showDialogEnablePosLimitCalled, true);
    });

    test(
        'Give isDialogEnablePosLimitShown == true, handleDialogEnablePosLimitShowing will NOT call showDialogEnablePosLimit',
        () {
      paymentDialogHandler.isDialogEnablePosLimitShown = true;
      paymentDialogHandler.handleDialogEnablePosLimitShowing(
        onClickNegative: fakeOnClickNegative,
        onClickPositive: fakeOnClickPositive,
      );

      expect(paymentDialogHandler.showDialogEnablePosLimitCalled, false);
    });
  });
}
