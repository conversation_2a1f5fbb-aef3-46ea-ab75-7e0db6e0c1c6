import 'package:evoapp/feature/payment/utils/mock_file/mock_update_order_file_name.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('should return correct value for each enum case', () {
    expect(UpdateOrderMockCase.success.value, 'success');
    expect(UpdateOrderMockCase.successWithInvalidVoucher.value, 'success_with_invalid_voucher');
    expect(UpdateOrderMockCase.successEmptyData.value, 'success_empty_data');
    expect(UpdateOrderMockCase.failRecordNotFound.value, 'fail_record_not_found');
    expect(UpdateOrderMockCase.failSessionNotOpened.value, 'fail_session_not_opened');
    expect(UpdateOrderMockCase.failPaymentInvalid.value, 'fail_payment_invalid');
    expect(UpdateOrderMockCase.failSessionExpired.value, 'fail_session_expired');
    expect(UpdateOrderMockCase.failEmiUnqualified.value, 'fail_emi_unqualified');
    expect(UpdateOrderMockCase.successEmiUpdateOrder.value, 'success_emi_with_packages');
  });

  group('Test function getUpdateOrderMockFileName()', () {
    test('Give UpdateOrderMockCase should return right mock file name', () {
      for (final UpdateOrderMockCase mockCase in UpdateOrderMockCase.values) {
        final String resultMockFileName = getUpdateOrderMockFileName(mockCase);
        final String expectedMockFileName = 'update_order_${mockCase.value}.json';
        expect(resultMockFileName, expectedMockFileName);
      }
    });
  });
}
