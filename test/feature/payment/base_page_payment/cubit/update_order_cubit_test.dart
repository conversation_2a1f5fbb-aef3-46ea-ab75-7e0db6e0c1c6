import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/checkout_repo.dart';
import 'package:evoapp/data/request/order_flow_type.dart';
import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/emi_tenor_offer_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/data/response/update_order_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:evoapp/feature/payment/base_page_payment/cubit/update_order_cubit.dart';
import 'package:evoapp/feature/payment/emi_constants.dart';
import 'package:evoapp/feature/payment/qrcode_scanner/model/qr_code_type.dart';
import 'package:evoapp/feature/payment/utils/payment_with_emi_utils.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../constant.dart';
import '../../../../util/test_util.dart';

class MockCheckOutRepo extends Mock implements CheckOutRepo {}

class MockPaymentWithEMIUtils extends Mock implements PaymentWithEMIUtils {}

void main() {
  final CheckOutRepo mockCheckOutRepo = MockCheckOutRepo();
  final PaymentWithEMIUtils mockPaymentWithEMIUtils = MockPaymentWithEMIUtils();
  late UpdateOrderCubit updateOrderCubit;

  const String fakeSessionId1 = 'fake_session_id_1';
  final OrderSessionEntity orderSessionEntity = OrderSessionEntity(id: fakeSessionId1);

  final EmiPackageEntity fakeEmiPackage = EmiPackageEntity(
    offer: EmiTenorOfferEntity(id: '1'),
    monthlyInstallmentAmount: 100,
  );

  const int voucherId = 123;
  final VoucherEntity voucherEntity = VoucherEntity(id: voucherId);

  const String expectedSessionId = 'fakeSessionId';
  const double expectedEmiMonthlyInstallmentAmount = 1111111;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
  });

  setUp(() {
    updateOrderCubit = UpdateOrderCubit(
      checkOutRepo: mockCheckOutRepo,
      paymentWithEMIUtils: mockPaymentWithEMIUtils,
    );

    when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(
      <int>[voucherId],
    );

    when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(false);
  });

  tearDown(() {
    reset(mockPaymentWithEMIUtils);
  });

  test('Test default state', () {
    expect(updateOrderCubit.state, isA<UpdateOrderInitial>());
  });

  group('Test updateOrderPackage', () {
    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'emits [UpdateOrderExpired] when expired order session is provided',
      setUp: () {
        when(
          () => mockCheckOutRepo.updateOrder(
            sessionId: any(named: 'sessionId'),
            paymentMethodId: any(named: 'paymentMethodId'),
            voucherIds: any(named: 'voucherIds'),
            flowType: any(named: 'flowType'),
            emiOfferId: any(named: 'emiOfferId'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.NOT_FOUND,
            response: await TestUtil.getResponseMock('update_order_fail_session_expired.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(
          <int>[voucherId],
        );
        when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(true);
      },
      build: () => updateOrderCubit,
      act: (UpdateOrderCubit cubit) => cubit.updateOrderPackage(
        orderSession: orderSessionEntity,
        selectedEmiPackage: fakeEmiPackage,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderExpired>(),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              emiOfferId: fakeEmiPackage.offer?.id,
              flowType: OrderFlowType.newOrder,
              voucherIds: <int>[voucherId],
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
        verify(() => mockPaymentWithEMIUtils
            .checkIfOrderExpiredByVerdict(UpdateOrderEntity.verdictSessionExpired)).called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'emits [UpdateOrderUnqualifiedAfterApplyVoucher] when order is not qualified after applying voucher',
      setUp: () {
        when(
          () => mockCheckOutRepo.updateOrder(
            sessionId: any(named: 'sessionId'),
            paymentMethodId: any(named: 'paymentMethodId'),
            voucherIds: any(named: 'voucherIds'),
            flowType: any(named: 'flowType'),
            emiOfferId: any(named: 'emiOfferId'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: await TestUtil.getResponseMock('update_order_fail_emi_unqualified.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(
          <int>[voucherId],
        );
        when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(false);
      },
      build: () => updateOrderCubit,
      act: (UpdateOrderCubit cubit) => cubit.updateOrderPackage(
        orderSession: orderSessionEntity,
        selectedEmiPackage: fakeEmiPackage,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderUnqualifiedAfterApplyVoucher>()
            .having(
              (UpdateOrderUnqualifiedAfterApplyVoucher p0) => p0.error.verdict,
              'verify error verdict',
              UpdateOrderEntity.verdictEmiUnqualified,
            )
            .having(
              (UpdateOrderUnqualifiedAfterApplyVoucher p0) => p0.orderSession,
              'verify orderSession',
              orderSessionEntity,
            )
            .having(
              (UpdateOrderUnqualifiedAfterApplyVoucher p0) => p0.emiPackage,
              'verify emiPackage',
              fakeEmiPackage,
            ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              emiOfferId: fakeEmiPackage.offer?.id,
              flowType: OrderFlowType.newOrder,
              voucherIds: <int>[voucherId],
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
        verify(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict('emi_unqualified'))
            .called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'emits [UpdateOrderError] when updateOrderPackage fails',
      setUp: () {
        when(
          () => mockCheckOutRepo.updateOrder(
            sessionId: any(named: 'sessionId'),
            paymentMethodId: any(named: 'paymentMethodId'),
            voucherIds: any(named: 'voucherIds'),
            flowType: any(named: 'flowType'),
            emiOfferId: any(named: 'emiOfferId'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.NOT_FOUND,
            response: await TestUtil.getResponseMock('update_order_fail_record_not_found.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(
          <int>[voucherId],
        );
        when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(false);
      },
      build: () => updateOrderCubit,
      act: (UpdateOrderCubit cubit) => cubit.updateOrderPackage(
        orderSession: orderSessionEntity,
        selectedEmiPackage: fakeEmiPackage,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderError>().having(
          (UpdateOrderError p0) => p0.error.verdict,
          'verify error verdict',
          'record_not_found',
        ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              emiOfferId: fakeEmiPackage.offer?.id,
              flowType: OrderFlowType.newOrder,
              voucherIds: <int>[voucherId],
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
        verify(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict('record_not_found'))
            .called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'emits [EmiUpdateSelectedVoucherInvalid] when an invalid voucher is provided',
      setUp: () {
        when(
          () => mockCheckOutRepo.updateOrder(
            sessionId: any(named: 'sessionId'),
            paymentMethodId: any(named: 'paymentMethodId'),
            voucherIds: any(named: 'voucherIds'),
            flowType: any(named: 'flowType'),
            emiOfferId: any(named: 'emiOfferId'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response:
                await TestUtil.getResponseMock('update_order_success_with_invalid_voucher.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(
          <int>[voucherId],
        );
        when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(false);
      },
      build: () => updateOrderCubit,
      act: (UpdateOrderCubit cubit) => cubit.updateOrderPackage(
        orderSession: orderSessionEntity,
        selectedEmiPackage: fakeEmiPackage,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderInvalidVoucher>()
            .having(
              (UpdateOrderInvalidVoucher state) =>
                  state.orderSession?.promotionInfo?.invalidVouchers,
              'verify updated order data with invalid voucher',
              isNotEmpty,
            )
            .having(
              (UpdateOrderInvalidVoucher state) => state.error.userMessage,
              'test error user message',
              'update_order_success_with_invalid_voucher',
            )
            .having(
              (UpdateOrderInvalidVoucher state) => state.error.verdict,
              'test error verdict',
              'promotion_invalid',
            )
            .having(
              (UpdateOrderInvalidVoucher state) => state.emiPackage,
              'test emiPackage',
              fakeEmiPackage,
            )
            .having(
              (UpdateOrderInvalidVoucher state) => state.selectedVoucher,
              'test selectedVoucher',
              voucherEntity,
            ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              emiOfferId: fakeEmiPackage.offer?.id,
              flowType: OrderFlowType.newOrder,
              voucherIds: <int>[voucherId],
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
        verifyNever(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any()));
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'emits [EmiUpdateOrderSuccess] when updateOrderPackage is successful',
      setUp: () {
        when(
          () => mockCheckOutRepo.updateOrder(
            sessionId: any(named: 'sessionId'),
            paymentMethodId: any(named: 'paymentMethodId'),
            voucherIds: any(named: 'voucherIds'),
            flowType: any(named: 'flowType'),
            emiOfferId: any(named: 'emiOfferId'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('update_order_success.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(
          <int>[voucherId],
        );
        when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(false);
      },
      build: () => updateOrderCubit,
      act: (UpdateOrderCubit cubit) => cubit.updateOrderPackage(
        orderSession: orderSessionEntity,
        selectedEmiPackage: fakeEmiPackage,
        selectedVoucher: voucherEntity,
        flowType: OrderFlowType.linkCard,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderSuccess>()
            .having(
              (UpdateOrderSuccess p0) => p0.orderSession,
              'verify orderSession',
              isA<OrderSessionEntity>().having(
                (OrderSessionEntity p0) => p0.id,
                'orderSession id',
                expectedSessionId,
              ),
            )
            .having(
              (UpdateOrderSuccess p0) => p0.emiPackage,
              'verify emiPackage',
              isA<EmiPackageEntity>().having(
                (EmiPackageEntity p0) => p0.monthlyInstallmentAmount,
                'emiPackage monthlyInstallmentAmount',
                expectedEmiMonthlyInstallmentAmount,
              ),
            ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              emiOfferId: fakeEmiPackage.offer?.id,
              flowType: OrderFlowType.linkCard,
              voucherIds: <int>[voucherId],
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
        verifyNever(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any()));
      },
    );
  });

  group('Test updateSelectedVoucher', () {
    const int expectedMonthlyInstallmentAmount = 1111111;

    setUpAll(() {
      getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    });

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'should emit UpdateOrderLoading and UpdateOrderSuccess',
      build: () => updateOrderCubit,
      setUp: () {
        when(() => mockCheckOutRepo.updateOrder(
              sessionId: any(named: 'sessionId'),
              emiOfferId: any(named: 'emiOfferId'),
              paymentMethodId: any(named: 'paymentMethodId'),
              flowType: any(named: 'flowType'),
              voucherIds: any(named: 'voucherIds'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('update_order.json'),
          ));
        });
      },
      act: (UpdateOrderCubit cubit) => cubit.updateSelectedVoucher(
        orderSession: orderSessionEntity,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderSuccess>()
            .having(
              (UpdateOrderSuccess state) => state.orderSession?.id,
              'verify order session id',
              fakeSessionId1,
            )
            .having(
              (UpdateOrderSuccess state) => state.emiPackage?.monthlyInstallmentAmount,
              'test emiPackage',
              expectedMonthlyInstallmentAmount,
            )
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              voucherIds: <int>[voucherId],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'Give timeOutInSec = 3 seconds and calling API in time, should call API success',
      build: () => updateOrderCubit,
      setUp: () {
        when(() => mockCheckOutRepo.updateOrder(
              sessionId: any(named: 'sessionId'),
              emiOfferId: any(named: 'emiOfferId'),
              paymentMethodId: any(named: 'paymentMethodId'),
              flowType: any(named: 'flowType'),
              voucherIds: any(named: 'voucherIds'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          await Future<void>.delayed(const Duration(seconds: 2));
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('update_order.json'),
          ));
        });
      },
      act: (UpdateOrderCubit cubit) => cubit.updateSelectedVoucher(
        orderSession: orderSessionEntity,
        selectedVoucher: voucherEntity,
        timeOutInSec: 3,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderSuccess>()
            .having(
              (UpdateOrderSuccess state) => state.orderSession?.id,
              'verify order session id',
              fakeSessionId1,
            )
            .having(
              (UpdateOrderSuccess state) => state.emiPackage?.monthlyInstallmentAmount,
              'test emiPackage',
              expectedMonthlyInstallmentAmount,
            )
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              voucherIds: <int>[voucherId],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'Give timeOutInSec = 3 seconds, null voucher and calling API timeout, should notice remove voucher fail',
      build: () => updateOrderCubit,
      setUp: () {
        when(() => mockCheckOutRepo.updateOrder(
              sessionId: any(named: 'sessionId'),
              emiOfferId: any(named: 'emiOfferId'),
              flowType: any(named: 'flowType'),
              paymentMethodId: any(named: 'paymentMethodId'),
              voucherIds: any(named: 'voucherIds'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          await Future<void>.delayed(const Duration(seconds: 4));
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('update_order.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(
          <int>[],
        );
      },
      act: (UpdateOrderCubit cubit) => cubit.updateSelectedVoucher(
        orderSession: orderSessionEntity,
        timeOutInSec: 3,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderTimeout>().having(
          (UpdateOrderTimeout state) => state.errorMessage,
          'timeout error message',
          EvoStrings.paymentPromotionRemoveTimeout,
        ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              voucherIds: <int>[],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(null)).called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'Give timeOutInSec = 3 seconds, selectedVoucher and calling API timeout, should notice apply voucher fail',
      build: () => updateOrderCubit,
      setUp: () {
        when(() => mockCheckOutRepo.updateOrder(
              sessionId: any(named: 'sessionId'),
              emiOfferId: any(named: 'emiOfferId'),
              flowType: any(named: 'flowType'),
              paymentMethodId: any(named: 'paymentMethodId'),
              voucherIds: any(named: 'voucherIds'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          await Future<void>.delayed(const Duration(seconds: 4));
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('update_order.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(
          <int>[],
        );
      },
      act: (UpdateOrderCubit cubit) => cubit.updateSelectedVoucher(
        orderSession: orderSessionEntity,
        selectedVoucher: VoucherEntity(),
        timeOutInSec: 3,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderTimeout>().having(
          (UpdateOrderTimeout state) => state.errorMessage,
          'timeout error message',
          EvoStrings.paymentPromotionApplyTimeout,
        ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              voucherIds: <int>[],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'Test update voucher case session_expired',
      build: () => updateOrderCubit,
      setUp: () {
        when(() => mockCheckOutRepo.updateOrder(
              sessionId: any(named: 'sessionId'),
              emiOfferId: any(named: 'emiOfferId'),
              flowType: any(named: 'flowType'),
              paymentMethodId: any(named: 'paymentMethodId'),
              voucherIds: any(named: 'voucherIds'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: await TestUtil.getResponseMock('update_order_fail_session_expired.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(true);
      },
      act: (UpdateOrderCubit cubit) => cubit.updateSelectedVoucher(
        orderSession: orderSessionEntity,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderExpired>().having(
          (UpdateOrderExpired state) => state.error.verdict,
          'error verdict',
          UpdateOrderEntity.verdictSessionExpired,
        ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              voucherIds: <int>[voucherId],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
        verify(() => mockPaymentWithEMIUtils
            .checkIfOrderExpiredByVerdict(UpdateOrderEntity.verdictSessionExpired)).called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'Test update voucher case session_not_opened',
      build: () => updateOrderCubit,
      setUp: () {
        when(() => mockCheckOutRepo.updateOrder(
              sessionId: any(named: 'sessionId'),
              emiOfferId: any(named: 'emiOfferId'),
              flowType: any(named: 'flowType'),
              paymentMethodId: any(named: 'paymentMethodId'),
              voucherIds: any(named: 'voucherIds'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: await TestUtil.getResponseMock('update_order_fail_session_not_opened.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(true);
      },
      act: (UpdateOrderCubit cubit) => cubit.updateSelectedVoucher(
        orderSession: orderSessionEntity,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderExpired>().having(
          (UpdateOrderExpired state) => state.error.verdict,
          'error verdict',
          UpdateOrderEntity.verdictSessionNotOpened,
        ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              voucherIds: <int>[voucherId],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
        verify(() => mockPaymentWithEMIUtils
            .checkIfOrderExpiredByVerdict(UpdateOrderEntity.verdictSessionNotOpened)).called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'Test update voucher case fail',
      build: () => updateOrderCubit,
      setUp: () {
        when(() => mockCheckOutRepo.updateOrder(
              sessionId: any(named: 'sessionId'),
              emiOfferId: any(named: 'emiOfferId'),
              flowType: any(named: 'flowType'),
              paymentMethodId: any(named: 'paymentMethodId'),
              voucherIds: any(named: 'voucherIds'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          ));
        });
      },
      act: (UpdateOrderCubit cubit) => cubit.updateSelectedVoucher(
        orderSession: orderSessionEntity,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderError>().having(
          (UpdateOrderError state) => state.error.userMessage,
          'error message',
          null,
        ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              voucherIds: <int>[voucherId],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'Test update order case invalid voucher',
      build: () => updateOrderCubit,
      setUp: () {
        when(() => mockCheckOutRepo.updateOrder(
              sessionId: any(named: 'sessionId'),
              emiOfferId: any(named: 'emiOfferId'),
              flowType: any(named: 'flowType'),
              paymentMethodId: any(named: 'paymentMethodId'),
              voucherIds: any(named: 'voucherIds'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response:
                await TestUtil.getResponseMock('update_order_success_with_invalid_voucher.json'),
          ));
        });
      },
      act: (UpdateOrderCubit cubit) => cubit.updateSelectedVoucher(
        orderSession: orderSessionEntity,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderInvalidVoucher>().having(
          (UpdateOrderInvalidVoucher state) => state.error.userMessage,
          'error message',
          'update_order_success_with_invalid_voucher',
        ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              voucherIds: <int>[voucherId],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'Test update order case valid voucher',
      build: () => updateOrderCubit,
      setUp: () {
        when(() => mockCheckOutRepo.updateOrder(
              sessionId: any(named: 'sessionId'),
              emiOfferId: any(named: 'emiOfferId'),
              flowType: any(named: 'flowType'),
              paymentMethodId: any(named: 'paymentMethodId'),
              voucherIds: any(named: 'voucherIds'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('update_order_success.json'),
          ));
        });
      },
      act: (UpdateOrderCubit cubit) => cubit.updateSelectedVoucher(
        orderSession: orderSessionEntity,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderSuccess>()
            .having(
              (UpdateOrderSuccess state) => state.orderSession?.id,
              'order session id',
              'fakeSessionId',
            )
            .having(
              (UpdateOrderSuccess state) => state.emiPackage?.monthlyInstallmentAmount,
              'test emiPackage',
              expectedMonthlyInstallmentAmount,
            ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              voucherIds: <int>[voucherId],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'Test update order case emi_unqualified',
      build: () => updateOrderCubit,
      setUp: () {
        when(() => mockCheckOutRepo.updateOrder(
              sessionId: any(named: 'sessionId'),
              emiOfferId: any(named: 'emiOfferId'),
              flowType: any(named: 'flowType'),
              paymentMethodId: any(named: 'paymentMethodId'),
              voucherIds: any(named: 'voucherIds'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: await TestUtil.getResponseMock('update_order_fail_emi_unqualified.json'),
          ));
        });
      },
      act: (UpdateOrderCubit cubit) => cubit.updateSelectedVoucher(
        orderSession: orderSessionEntity,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderUnqualifiedAfterApplyVoucher>()
            .having(
              (UpdateOrderUnqualifiedAfterApplyVoucher state) => state.orderSession?.id,
              'order session id',
              orderSessionEntity.id,
            )
            .having(
              (UpdateOrderUnqualifiedAfterApplyVoucher state) => state.selectedVoucher?.id,
              'test selected voucher',
              voucherId,
            ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              voucherIds: <int>[voucherId],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
      },
    );
  });

  group('Test changeToOutrightPayment', () {
    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'emits [UpdateOrderExpired] when expired order session is provided',
      setUp: () {
        when(
          () => mockCheckOutRepo.updateOrder(
            sessionId: any(named: 'sessionId'),
            paymentMethodId: any(named: 'paymentMethodId'),
            voucherIds: any(named: 'voucherIds'),
            flowType: any(named: 'flowType'),
            emiOfferId: any(named: 'emiOfferId'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.NOT_FOUND,
            response: await TestUtil.getResponseMock('update_order_fail_session_expired.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(
          <int>[voucherId],
        );
        when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(true);
      },
      build: () => updateOrderCubit,
      act: (UpdateOrderCubit cubit) => cubit.changeToOutrightPayment(
        orderSession: orderSessionEntity,
        selectedEmiPackage: fakeEmiPackage,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderExpired>(),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              emiOfferId: EmiConstants.emiOfferIdForChangeSessionToOutright,
              voucherIds: <int>[voucherId],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
        verify(() => mockPaymentWithEMIUtils
            .checkIfOrderExpiredByVerdict(UpdateOrderEntity.verdictSessionExpired)).called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'emits [UpdateOrderUnqualifiedAfterApplyVoucher] when order is not qualified after applying voucher',
      setUp: () {
        when(
          () => mockCheckOutRepo.updateOrder(
            sessionId: any(named: 'sessionId'),
            paymentMethodId: any(named: 'paymentMethodId'),
            voucherIds: any(named: 'voucherIds'),
            flowType: any(named: 'flowType'),
            emiOfferId: any(named: 'emiOfferId'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: await TestUtil.getResponseMock('update_order_fail_emi_unqualified.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(
          <int>[voucherId],
        );
        when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(false);
      },
      build: () => updateOrderCubit,
      act: (UpdateOrderCubit cubit) => cubit.changeToOutrightPayment(
        orderSession: orderSessionEntity,
        selectedEmiPackage: fakeEmiPackage,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderUnqualifiedAfterApplyVoucher>()
            .having(
              (UpdateOrderUnqualifiedAfterApplyVoucher p0) => p0.error.verdict,
              'verify error verdict',
              UpdateOrderEntity.verdictEmiUnqualified,
            )
            .having(
              (UpdateOrderUnqualifiedAfterApplyVoucher p0) => p0.orderSession,
              'verify orderSession',
              orderSessionEntity,
            )
            .having(
              (UpdateOrderUnqualifiedAfterApplyVoucher p0) => p0.emiPackage,
              'verify emiPackage',
              fakeEmiPackage,
            ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              emiOfferId: EmiConstants.emiOfferIdForChangeSessionToOutright,
              voucherIds: <int>[voucherId],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
        verify(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict('emi_unqualified'))
            .called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'emits [UpdateOrderError] when changeToOutrightPayment fails',
      setUp: () {
        when(
          () => mockCheckOutRepo.updateOrder(
            sessionId: any(named: 'sessionId'),
            paymentMethodId: any(named: 'paymentMethodId'),
            voucherIds: any(named: 'voucherIds'),
            flowType: any(named: 'flowType'),
            emiOfferId: any(named: 'emiOfferId'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.NOT_FOUND,
            response: await TestUtil.getResponseMock('update_order_fail_record_not_found.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(
          <int>[voucherId],
        );
        when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(false);
      },
      build: () => updateOrderCubit,
      act: (UpdateOrderCubit cubit) => cubit.changeToOutrightPayment(
        orderSession: orderSessionEntity,
        selectedEmiPackage: fakeEmiPackage,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderError>().having(
          (UpdateOrderError p0) => p0.error.verdict,
          'verify error verdict',
          'record_not_found',
        ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              emiOfferId: EmiConstants.emiOfferIdForChangeSessionToOutright,
              voucherIds: <int>[voucherId],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
        verify(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict('record_not_found'))
            .called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'emits [UpdateOrderInvalidVoucher] when an invalid voucher is provided',
      setUp: () {
        when(
          () => mockCheckOutRepo.updateOrder(
            sessionId: any(named: 'sessionId'),
            paymentMethodId: any(named: 'paymentMethodId'),
            voucherIds: any(named: 'voucherIds'),
            flowType: any(named: 'flowType'),
            emiOfferId: any(named: 'emiOfferId'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response:
                await TestUtil.getResponseMock('update_order_success_with_invalid_voucher.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(
          <int>[voucherId],
        );
        when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(false);
      },
      build: () => updateOrderCubit,
      act: (UpdateOrderCubit cubit) => cubit.changeToOutrightPayment(
        orderSession: orderSessionEntity,
        selectedEmiPackage: fakeEmiPackage,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderInvalidVoucher>()
            .having(
              (UpdateOrderInvalidVoucher state) =>
                  state.orderSession?.promotionInfo?.invalidVouchers,
              'verify updated order data with invalid voucher',
              isNotEmpty,
            )
            .having(
              (UpdateOrderInvalidVoucher p0) => p0.error.userMessage,
              'test error user message',
              'update_order_success_with_invalid_voucher',
            )
            .having(
              (UpdateOrderInvalidVoucher p0) => p0.error.verdict,
              'test error verdict',
              'promotion_invalid',
            )
            .having(
              (UpdateOrderInvalidVoucher state) => state.emiPackage,
              'test emiPackage',
              fakeEmiPackage,
            )
            .having(
              (UpdateOrderInvalidVoucher state) => state.selectedVoucher,
              'test selectedVoucher',
              voucherEntity,
            ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              emiOfferId: EmiConstants.emiOfferIdForChangeSessionToOutright,
              voucherIds: <int>[voucherId],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
        verifyNever(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any()));
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'emits [ChangeOrderToOutrightPaymentSuccess] when changeToOutright is successful',
      build: () {
        when(
          () => mockCheckOutRepo.updateOrder(
            sessionId: any(named: 'sessionId'),
            paymentMethodId: any(named: 'paymentMethodId'),
            voucherIds: any(named: 'voucherIds'),
            flowType: any(named: 'flowType'),
            emiOfferId: any(named: 'emiOfferId'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('update_order_success.json'),
          ));
        });
        return updateOrderCubit;
      },
      act: (UpdateOrderCubit cubit) => cubit.changeToOutrightPayment(
        orderSession: orderSessionEntity,
        selectedEmiPackage: fakeEmiPackage,
        selectedVoucher: voucherEntity,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<ChangeOrderToOutrightPaymentSuccess>()
            .having(
              (ChangeOrderToOutrightPaymentSuccess p0) => p0.orderSession,
              'verify orderSession',
              isA<OrderSessionEntity>().having(
                (OrderSessionEntity p0) => p0.id,
                'orderSession id',
                expectedSessionId,
              ),
            )
            .having(
              (ChangeOrderToOutrightPaymentSuccess p0) => p0.selectedVoucher,
              'selectedVoucher',
              voucherEntity,
            ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              emiOfferId: EmiConstants.emiOfferIdForChangeSessionToOutright,
              voucherIds: <int>[voucherId],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(voucherEntity)).called(1);
        verifyNever(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any()));
      },
    );
  });

  group('Test removeVoucher', () {
    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'emits [UpdateOrderExpired] when expired order session is provided',
      setUp: () {
        when(
          () => mockCheckOutRepo.updateOrder(
            sessionId: any(named: 'sessionId'),
            paymentMethodId: any(named: 'paymentMethodId'),
            voucherIds: any(named: 'voucherIds'),
            flowType: any(named: 'flowType'),
            emiOfferId: any(named: 'emiOfferId'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.NOT_FOUND,
            response: await TestUtil.getResponseMock('update_order_fail_session_expired.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(<int>[]);
        when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(true);
      },
      build: () => updateOrderCubit,
      act: (UpdateOrderCubit cubit) => cubit.removeVoucher(
        orderSession: orderSessionEntity,
        emiPackageEntity: fakeEmiPackage,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderExpired>(),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              emiOfferId: fakeEmiPackage.offer?.id,
              voucherIds: <int>[],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(null)).called(1);
        verify(() => mockPaymentWithEMIUtils
            .checkIfOrderExpiredByVerdict(UpdateOrderEntity.verdictSessionExpired)).called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'emits [UpdateOrderUnqualifiedAfterApplyVoucher] when order is not qualified after applying voucher',
      setUp: () {
        when(
          () => mockCheckOutRepo.updateOrder(
            sessionId: any(named: 'sessionId'),
            paymentMethodId: any(named: 'paymentMethodId'),
            voucherIds: any(named: 'voucherIds'),
            flowType: any(named: 'flowType'),
            emiOfferId: any(named: 'emiOfferId'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: await TestUtil.getResponseMock('update_order_fail_emi_unqualified.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(<int>[]);
        when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(false);
      },
      build: () => updateOrderCubit,
      act: (UpdateOrderCubit cubit) => cubit.removeVoucher(
        orderSession: orderSessionEntity,
        emiPackageEntity: fakeEmiPackage,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderUnqualifiedAfterApplyVoucher>()
            .having(
              (UpdateOrderUnqualifiedAfterApplyVoucher p0) => p0.error.verdict,
              'error',
              UpdateOrderEntity.verdictEmiUnqualified,
            )
            .having(
              (UpdateOrderUnqualifiedAfterApplyVoucher p0) => p0.orderSession,
              'verify orderSession',
              orderSessionEntity,
            )
            .having(
              (UpdateOrderUnqualifiedAfterApplyVoucher p0) => p0.emiPackage,
              'verify emiPackage',
              fakeEmiPackage,
            ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              emiOfferId: fakeEmiPackage.offer?.id,
              flowType: OrderFlowType.newOrder,
              voucherIds: <int>[],
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(null)).called(1);
        verify(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict('emi_unqualified'))
            .called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'emits [UpdateOrderError] when removeVoucher fails',
      setUp: () {
        when(
          () => mockCheckOutRepo.updateOrder(
            sessionId: any(named: 'sessionId'),
            paymentMethodId: any(named: 'paymentMethodId'),
            voucherIds: any(named: 'voucherIds'),
            flowType: any(named: 'flowType'),
            emiOfferId: any(named: 'emiOfferId'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.NOT_FOUND,
            response: await TestUtil.getResponseMock('update_order_fail_record_not_found.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(<int>[]);
        when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(false);
      },
      build: () => updateOrderCubit,
      act: (UpdateOrderCubit cubit) => cubit.removeVoucher(
        orderSession: orderSessionEntity,
        emiPackageEntity: fakeEmiPackage,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<UpdateOrderError>().having(
          (UpdateOrderError p0) => p0.error.verdict,
          'verdict',
          'record_not_found',
        ),
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              emiOfferId: fakeEmiPackage.offer?.id,
              voucherIds: <int>[],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(null)).called(1);
        verify(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict('record_not_found'))
            .called(1);
      },
    );

    blocTest<UpdateOrderCubit, UpdateOrderState>(
      'emits [RemoveVoucherInOrderSuccess] when removeVoucher is successful',
      setUp: () {
        when(
          () => mockCheckOutRepo.updateOrder(
            sessionId: any(named: 'sessionId'),
            paymentMethodId: any(named: 'paymentMethodId'),
            voucherIds: any(named: 'voucherIds'),
            flowType: any(named: 'flowType'),
            emiOfferId: any(named: 'emiOfferId'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return UpdateOrderEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock('update_order_success.json'),
          ));
        });

        when(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(any())).thenReturn(<int>[]);
        when(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any())).thenReturn(false);
      },
      build: () => updateOrderCubit,
      act: (UpdateOrderCubit cubit) => cubit.removeVoucher(
        orderSession: orderSessionEntity,
        emiPackageEntity: fakeEmiPackage,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<UpdateOrderLoading>(),
        isA<RemoveVoucherInOrderSuccess>()
            .having(
              (RemoveVoucherInOrderSuccess p0) => p0.orderSession,
              'verify orderSession',
              isA<OrderSessionEntity>().having(
                (OrderSessionEntity p0) => p0.id,
                'orderSession id',
                expectedSessionId,
              ),
            )
            .having(
              (RemoveVoucherInOrderSuccess p0) => p0.emiPackage,
              'verify emiPackage',
              isA<EmiPackageEntity>().having(
                (EmiPackageEntity p0) => p0.monthlyInstallmentAmount,
                'emiPackage monthlyInstallmentAmount',
                expectedEmiMonthlyInstallmentAmount,
              ),
            )
      ],
      verify: (_) {
        verify(() => mockCheckOutRepo.updateOrder(
              sessionId: orderSessionEntity.id,
              emiOfferId: fakeEmiPackage.offer?.id,
              voucherIds: <int>[],
              flowType: OrderFlowType.newOrder,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);

        verify(() => mockPaymentWithEMIUtils.prepareVoucherIdsParam(null)).called(1);
        verifyNever(() => mockPaymentWithEMIUtils.checkIfOrderExpiredByVerdict(any()));
      },
    );
  });

  group('verify checkUpdateOrderIfNeeded', () {
    test('should return false when flowType is newOrder, no voucher, and not DynamicQR', () {
      final OrderSessionEntity orderSession = OrderSessionEntity(productCode: 'other_code');

      final bool result =
          updateOrderCubit.checkUpdateOrderIfNeeded(OrderFlowType.newOrder, null, orderSession);

      expect(result, false);
    });

    test('should return true when flowType is newOrder, no voucher, but is DynamicQR', () {
      final OrderSessionEntity orderSession =
          OrderSessionEntity(productCode: QrProductCode.pa03String);

      final bool result =
          updateOrderCubit.checkUpdateOrderIfNeeded(OrderFlowType.newOrder, null, orderSession);

      expect(result, true);
    });

    test('should return true when flowType is not newOrder', () {
      final OrderSessionEntity orderSession = OrderSessionEntity(productCode: 'other_code');

      final bool result =
          updateOrderCubit.checkUpdateOrderIfNeeded(OrderFlowType.linkCard, null, orderSession);

      expect(result, true);
    });

    test('should return true when voucher is provided', () {
      final OrderSessionEntity orderSession = OrderSessionEntity(productCode: 'other_code');
      final VoucherEntity voucher = VoucherEntity();

      final bool result =
          updateOrderCubit.checkUpdateOrderIfNeeded(OrderFlowType.newOrder, voucher, orderSession);

      expect(result, true);
    });

    test('should handle null orderSession', () {
      final bool result = updateOrderCubit.checkUpdateOrderIfNeeded(OrderFlowType.newOrder, null, null);

      expect(result, false);
    });
  });
}
