import 'package:evoapp/data/response/create_order_entity.dart';
import 'package:evoapp/data/response/order_session_entity.dart';
import 'package:evoapp/feature/payment/switch_order_to_outright_purchase/switch_order_to_outright_purchase_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify SwitchToOutrightPaymentInitState', () {
    final SwitchToOutrightPaymentInitState state = SwitchToOutrightPaymentInitState();
    expect(state, isA<SwitchToOutrightPaymentState>());
  });

  test('verify SwitchToOutrightPaymentApiLoadingState', () {
    final SwitchToOutrightPaymentApiLoadingState state = SwitchToOutrightPaymentApiLoadingState();
    expect(state, isA<SwitchToOutrightPaymentState>());
  });

  test('verify CreateDynamicOrderSuccessState', () {
    const String orderId = 'fake_order_id';
    final OrderSessionEntity orderSessionEntity = OrderSessionEntity(id: orderId);
    final CreateOrderEntity createOrderEntity = CreateOrderEntity(session: orderSessionEntity);

    final CreateDynamicOrderSuccessState state =
        CreateDynamicOrderSuccessState(entity: createOrderEntity);

    expect(state, isA<SwitchToOutrightPaymentState>());
    expect(state.entity?.session, orderSessionEntity);
  });

  test('verify CreateOrderActivatePosLimitErrorState', () {
    final CreateOrderEntity createOrderEntity = CreateOrderEntity();

    final CreateOrderActivatePosLimitErrorState state =
        CreateOrderActivatePosLimitErrorState(entity: createOrderEntity);

    expect(state, isA<SwitchToOutrightPaymentState>());
    expect(state.entity, createOrderEntity);
  });

  test('verify SwitchToOutrightPaymentCommonErrorState', () {
    const int statusCode = 400;
    const String userMessage = 'fake_user_message';
    const String verdict = 'fake_verdict';

    final ErrorUIModel errorUIModel = ErrorUIModel(
      statusCode: statusCode,
      userMessage: userMessage,
      verdict: verdict,
    );

    final SwitchToOutrightPaymentCommonErrorState state =
        SwitchToOutrightPaymentCommonErrorState(errorUIModel: errorUIModel);

    expect(state, isA<SwitchToOutrightPaymentState>());
    expect(state.errorUIModel?.statusCode, statusCode);
    expect(state.errorUIModel?.userMessage, userMessage);
    expect(state.errorUIModel?.verdict, verdict);
  });

  test('verify SwitchToOutrightPaymentOrderExpiredState', () {
    final SwitchToOutrightPaymentOrderExpiredState state =
        SwitchToOutrightPaymentOrderExpiredState();
    expect(state, isA<SwitchToOutrightPaymentState>());
  });
}
