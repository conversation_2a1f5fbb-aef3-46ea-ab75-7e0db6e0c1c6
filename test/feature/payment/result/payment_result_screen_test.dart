import 'dart:async';

import 'package:evoapp/data/repository/checkout_repo.dart';
import 'package:evoapp/data/response/emi_info_entity.dart';
import 'package:evoapp/data/response/payment_result_entity.dart';
import 'package:evoapp/data/response/payment_result_transaction_entity.dart';
import 'package:evoapp/data/response/promotion_info_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/payment/result/bloc/payment_result_cubit.dart';
import 'package:evoapp/feature/payment/result/bloc/payment_result_state.dart';
import 'package:evoapp/feature/payment/result/payment_result_screen.dart';
import 'package:evoapp/feature/payment/utils/dialog_enable_pos_limit_handler.dart';
import 'package:evoapp/feature/payment/widget/order_summary_widget/order_summary_widget.dart';
import 'package:evoapp/feature/payment/widget/payment_result_and_amount_title_widget.dart';
import 'package:evoapp/feature/payment/widget/payment_result_container_widget.dart';
import 'package:evoapp/feature/payment/widget/payment_result_note_widget.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/model/transaction_status_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/task_polling_handler/polling_task.dart';
import 'package:evoapp/util/task_polling_handler/task_polling_handler.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:evoapp/widget/cta_with_powered_by_widget.dart';
import 'package:evoapp/widget/evo_loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/in_app_review/in_app_review_wrapper.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';
import '../../../util/test_util.dart';

class MockFeatureToggle extends Mock implements FeatureToggle {}

class MockPaymentResultCubit extends Mock implements PaymentResultCubit {}

class MockTaskPollingHandler extends Mock implements TaskPollingHandler {}

class MockCheckOutRepo extends Mock implements CheckOutRepo {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

class MockDialogEnablePosLimitHandler extends Mock implements DialogEnablePosLimitHandler {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {
  @override
  Future<bool?> getValueToCheckReviewPopupShown() async => false;
}

class MockInAppReviewWrapper extends Mock implements InAppReviewWrapper {}

class MockTimer extends Mock implements Timer {}

class MockEvoDialogHelper extends Mock implements EvoDialogHelper {}

class MockPaymentResultTransactionEntity extends Mock implements PaymentResultTransactionEntity {
  @override
  String? id;

  @override
  EmiInfoEntity? emiInfo;

  @override
  PromotionInfoEntity? promotionInfo;

  @override
  final String? status = TransactionStatusModel.success.name;

  @override
  final String? paymentService = PaymentService.emi.value;
}

class FakePollingTask extends Fake implements PollingTask {}

class TestPollingTask extends Mock implements PollingTask {
  @override
  final String id;
  @override
  final PollingTaskType type;
  @override
  final DateTime createdTime;

  TestPollingTask({
    required this.id,
    required this.type,
    required this.createdTime,
  });
}

class TestPaymentResultScreen extends PaymentResultScreen {
  const TestPaymentResultScreen({
    required this.cubit,
    required this.dialogEnablePosLimitHandler,
    super.arg,
    super.key,
  });

  final PaymentResultCubit cubit;
  final DialogEnablePosLimitHandler dialogEnablePosLimitHandler;

  @override
  // ignore: no_logic_in_create_state
  TestPaymentResultScreenState createState() => TestPaymentResultScreenState(
        paymentResultCubit: cubit,
        dialogEnablePosLimitHandler: dialogEnablePosLimitHandler,
        key: key,
      );
}

class TestPaymentResultScreenState extends PaymentResultScreenState {
  TestPaymentResultScreenState({
    required this.paymentResultCubit,
    required this.dialogEnablePosLimitHandler,
    this.key,
  });

  @override
  // ignore: overridden_fields
  final PaymentResultCubit paymentResultCubit;
  final Key? key;
  @override
  // ignore: overridden_fields
  final DialogEnablePosLimitHandler dialogEnablePosLimitHandler;

  bool isHandlePaymentFlowCompleteCalled = false;

  @override
  void handlePaymentFlowComplete() {
    isHandlePaymentFlowCompleteCalled = true;
  }
}

void main() {
  late FeatureToggle mockFeatureToggle;
  late PaymentResultCubit mockPaymentResultCubit;
  late CheckOutRepo mockCheckOutRepo;
  late EvoSnackBar mockEvoSnackBar;
  late CommonImageProvider mockCommonImageProvider;
  late EvoLocalStorageHelper mockLocalStorageHelper;
  late TaskPollingHandler mockTaskPollingHandler;
  late MockDialogEnablePosLimitHandler mockDialogEnablePosLimitHandler;
  late MockPaymentResultTransactionEntity mockPaymentResultTransactionEntity;
  final StreamController<PaymentResultState> paymentResultStreamController =
      StreamController<PaymentResultState>.broadcast();

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    registerFallbackValue(ErrorUIModel());
    registerFallbackValue(TransactionStatusModel.success);
    registerFallbackValue(SnackBarType.error);
    registerFallbackValue(PollingTaskType.cashBackResult);
    registerFallbackValue(FakePollingTask());
    registerFallbackValue(EvoDialogId.posLimitBottomSheet);
    registerFallbackValue(EvoDialogId.requestRatingAndReviewDialog);

    mockPaymentResultTransactionEntity = MockPaymentResultTransactionEntity();
    registerFallbackValue(PaymentResultLoadedState(mockPaymentResultTransactionEntity));

    initConfigEvoPageStateBase();

    setUtilsMockInstanceForTesting();

    getIt.registerSingleton<CheckOutRepo>(MockCheckOutRepo());
    mockCheckOutRepo = getIt.get<CheckOutRepo>();

    getIt.registerSingleton<EvoSnackBar>(MockEvoSnackBar());
    mockEvoSnackBar = getIt.get<EvoSnackBar>();

    getIt.registerLazySingleton<FeatureToggle>(() => MockFeatureToggle());
    mockFeatureToggle = getIt<FeatureToggle>();

    getIt.registerSingleton<EvoLocalStorageHelper>(MockEvoLocalStorageHelper());
    mockLocalStorageHelper = getIt.get<EvoLocalStorageHelper>();

    getIt.registerSingleton<InAppReviewWrapper>(MockInAppReviewWrapper());

    getIt.registerSingleton<EvoDialogHelper>(MockEvoDialogHelper());

    getIt.registerSingleton<TaskPollingHandler>(MockTaskPollingHandler());
    mockTaskPollingHandler = getIt.get<TaskPollingHandler>();

    mockDialogEnablePosLimitHandler = MockDialogEnablePosLimitHandler();

    when(() => mockEvoSnackBar.show(
          any(),
          typeSnackBar: any(named: 'typeSnackBar'),
          durationInSec: any(named: 'durationInSec'),
          description: any(named: 'description'),
          marginBottomRatio: any(named: 'marginBottomRatio'),
        )).thenAnswer((Invocation invocation) async {
      return true;
    });

    mockCommonImageProvider = getIt.get<CommonImageProvider>();
    when(
      () => mockCommonImageProvider.asset(
        captureAny(),
        width: captureAny<double?>(named: 'width'),
        height: captureAny<double?>(named: 'height'),
        color: any(named: 'color'),
        fit: any(named: 'fit'),
        cornerRadius: any(named: 'cornerRadius'),
        cacheWidth: any(named: 'cacheWidth'),
        cacheHeight: any(named: 'cacheHeight'),
        package: any(named: 'package'),
      ),
    ).thenAnswer((_) => Container());

    when(() => mockCheckOutRepo.getTransactionDetail(
          transactionId: any(named: 'transactionId'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async => PaymentResultEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: await TestUtil.getResponseMock('payment_result_response_success.json'),
        )));

    when(() => mockLocalStorageHelper.setEnablePosLimitWarning(any()))
        .thenAnswer((_) async => Future<void>.value());

    when(() => mockTaskPollingHandler.saveNewTask(
          any(),
          any(),
          any(),
        )).thenAnswer((_) async => Future<void>.value());

    when(
      () => EvoDialogHelper().showDialogBottomSheet(
        title: any(named: 'title'),
        content: any(named: 'content'),
        dialogId: any(named: 'dialogId'),
        isShowButtonClose: any(named: 'isShowButtonClose'),
        header: any(named: 'header'),
        headerPadding: any(named: 'headerPadding'),
        buttonListOrientation: any(named: 'buttonListOrientation'),
        textPositive: any(named: 'textPositive'),
        onClickPositive: any(named: 'onClickPositive'),
        textNegative: any(named: 'textNegative'),
        onClickNegative: any(named: 'onClickNegative'),
      ),
    ).thenAnswer((_) async {});

    when(
      () => EvoDialogHelper().showDialogConfirm(
        isDismissible: any(named: 'isDismissible'),
        title: any(named: 'title'),
        content: any(named: 'content'),
        dialogId: any(named: 'dialogId'),
        isShowButtonClose: any(named: 'isShowButtonClose'),
        buttonListOrientation: any(named: 'buttonListOrientation'),
        textPositive: any(named: 'textPositive'),
        onClickPositive: any(named: 'onClickPositive'),
        textNegative: any(named: 'textNegative'),
        onClickNegative: any(named: 'onClickNegative'),
      ),
    ).thenAnswer((_) async {});
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    // Initialize mocks
    mockPaymentResultCubit = MockPaymentResultCubit();

    when(() => mockFeatureToggle.enableEventTrackingFeature).thenReturn(true);
    when(() => mockFeatureToggle.enableEmiManagementFeature).thenReturn(true);
    when(() => mockFeatureToggle.enableInstantCashbackFeature).thenReturn(true);
    when(() => mockFeatureToggle.enableRequestReviewRatingFeature).thenReturn(true);
  });

  tearDownAll(() {
    paymentResultStreamController.close();
  });

  group('verify PaymentResultArg', () {
    test('PaymentResultArg can be instantiated with parameters', () {
      final PaymentResultArg arg = PaymentResultArg(
        transactionId: 'test_id',
        needPollingProcessingStatus: true,
      );
      expect(arg.transactionId, 'test_id');
      expect(arg.needPollingProcessingStatus, true);
    });

    test('PaymentResultArg can be instantiated with default values', () {
      final PaymentResultArg arg = PaymentResultArg(transactionId: 'test_id');
      expect(arg.transactionId, 'test_id');
      expect(arg.needPollingProcessingStatus, false);
    });

    test(
        'PaymentResultScreen.pushReplacementNamed should call pushReplacementNamed on navigatorContext',
        () {
      when(() => commonNavigator.pushReplacementNamed(
            any(),
            any(),
            extra: any(named: 'extra'),
          )).thenAnswer((_) => Future<void>.value());

      PaymentResultScreen.pushReplacementNamed(
        transactionId: 'test_id',
        needPollingProcessingStatus: true,
      );

      verify(() => commonNavigator.pushReplacementNamed(
            any(),
            any(),
            extra: any(named: 'extra'),
          )).called(1);
    });
  });

  group('PaymentResultScreen verify state', () {
    test('PaymentResultLoadingState can be instantiated', () {
      final PaymentResultLoadingState state = PaymentResultLoadingState();
      expect(state, isA<PaymentResultState>());
    });

    test('PaymentResultLoadedState can be instantiated', () {
      final PaymentResultLoadedState state =
          PaymentResultLoadedState(mockPaymentResultTransactionEntity);
      expect(state, isA<PaymentResultState>());
      expect(state.transaction, mockPaymentResultTransactionEntity);
    });

    test('PaymentResultErrorState can be instantiated', () {
      final errorModel = ErrorUIModel(userMessage: 'Test error');
      final state = PaymentResultErrorState(
        errorUIModel: errorModel,
        savedTransaction: mockPaymentResultTransactionEntity,
      );
      expect(state, isA<PaymentResultState>());
      expect(state.errorUIModel, errorModel);
      expect(state.savedTransaction, mockPaymentResultTransactionEntity);
    });

    test('PaymentResultPosLimitState can be instantiated', () {
      final PaymentResultPosLimitState state =
          PaymentResultPosLimitState(mockPaymentResultTransactionEntity);
      expect(state, isA<PaymentResultState>());
      expect(state.transaction, mockPaymentResultTransactionEntity);
    });

    test('PaymentResultCubit can be instantiated', () {
      final AppState appState = AppState();

      final PaymentResultCubit cubit = PaymentResultCubit(mockCheckOutRepo, appState);
      expect(cubit, isA<PaymentResultCubit>());
      expect(cubit.state, isA<PaymentResultLoadingState>());
    });
  });

  Future<void> pumpScreen(
    WidgetTester tester, {
    required PaymentResultCubit cubit,
    PaymentResultArg? arg,
    Key? key,
  }) async {
    final TestPaymentResultScreen testScreen = TestPaymentResultScreen(
      cubit: cubit,
      dialogEnablePosLimitHandler: mockDialogEnablePosLimitHandler,
      arg: arg,
    );

    final Widget screen = MaterialApp(
      key: key,
      navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
      scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
      home: Scaffold(
        body: testScreen,
      ),
    );

    when(() => mockNavigatorContext.widget).thenReturn(screen);

    await tester.runAsync(() async {
      await tester.pumpWidget(screen);
    });
  }

  group('verify PaymentResultScreen', () {
    setUp(() {
      when(() => mockPaymentResultCubit.emit(any())).thenAnswer((_) => Future<void>.value());

      when(() => mockPaymentResultCubit.stream)
          .thenAnswer((_) => paymentResultStreamController.stream);
      when(() => mockPaymentResultCubit.state).thenAnswer((_) => PaymentResultLoadingState());
      when(() => mockPaymentResultCubit.close()).thenAnswer((_) async {});

      when(() => mockPaymentResultCubit.getPaymentResult(
            transactionId: any(named: 'transactionId'),
            isRefresh: any(named: 'isRefresh'),
          )).thenAnswer((_) async {});

      when(() => mockPaymentResultCubit.clearPaymentSharedData())
          .thenAnswer((_) => Future<void>.value());

      when(() => mockPaymentResultCubit.enableRatingPrompt).thenReturn(true);
    });

    tearDown(() {
      reset(mockPaymentResultCubit);
    });

    testWidgets('screens displays correctly', (WidgetTester tester) async {
      await pumpScreen(
        tester,
        cubit: mockPaymentResultCubit,
        arg: PaymentResultArg(
          transactionId: '',
          needPollingProcessingStatus: true,
        ),
      );

      await tester.pump();

      expect(find.byType(EvoLoadingWidget), findsOneWidget);

      await tester.pumpWidget(SizedBox());
    });

    testWidgets('screens displays PaymentResultLoadedState correctly', (WidgetTester tester) async {
      when(() => mockPaymentResultCubit.stream).thenAnswer((_) =>
          Stream<PaymentResultLoadedState>.value(
              PaymentResultLoadedState(mockPaymentResultTransactionEntity)));

      await pumpScreen(
        tester,
        cubit: mockPaymentResultCubit,
        arg: PaymentResultArg(
          transactionId: '',
          needPollingProcessingStatus: true,
        ),
      );

      mockPaymentResultCubit.emit(PaymentResultLoadedState(mockPaymentResultTransactionEntity));

      await tester.pump();

      expect(find.byType(PaymentResultContainerWidget), findsOneWidget);
      expect(find.byType(PaymentResultTitleAndAmountWidget), findsOneWidget);
      expect(find.byType(PaymentResultNoteWidget), findsOneWidget);
      expect(find.byType(OrderSummaryWidget), findsOneWidget);
      expect(find.byType(CTAWithPoweredByWidget), findsOneWidget);

      final PaymentResultContainerWidget resultWidget =
          tester.widget(find.byType(PaymentResultContainerWidget));
      resultWidget.onRefresh?.call();

      verify(() => mockPaymentResultCubit.getPaymentResult(
            transactionId: any(named: 'transactionId'),
            isRefresh: any(named: 'isRefresh'),
          )).called(2);

      final PaymentResultTitleAndAmountWidget titleWidget =
          tester.widget(find.byType(PaymentResultTitleAndAmountWidget));
      titleWidget.onMoreDetail?.call();

      verify(() => getIt<CommonNavigator>().pushNamed(
            any(),
            Screen.transactionHistoryDetailScreen.name,
            extra: any(named: 'extra'),
          )).called(1);

      when(() => mockFeatureToggle.enableEmiManagementFeature).thenReturn(false);

      final PaymentResultNoteWidget noteWidget =
          tester.widget(find.byType(PaymentResultNoteWidget));
      noteWidget.args?.onTapHeaderInfo?.call();

      verify(() => getIt<CommonNavigator>().pushNamed(
            any(),
            Screen.transactionHistoryDetailScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('screens displays PaymentResultErrorState correctly', (WidgetTester tester) async {
      final ErrorUIModel errorModel = ErrorUIModel(userMessage: 'Test error');
      final PaymentResultErrorState errorState = PaymentResultErrorState(
        errorUIModel: errorModel,
        savedTransaction: mockPaymentResultTransactionEntity,
      );

      when(() => mockPaymentResultCubit.stream)
          .thenAnswer((_) => Stream<PaymentResultErrorState>.value(errorState));

      await pumpScreen(
        tester,
        cubit: mockPaymentResultCubit,
        arg: PaymentResultArg(
          transactionId: '',
          needPollingProcessingStatus: true,
        ),
      );

      mockPaymentResultCubit.emit(errorState);

      await tester.pump();

      expect(find.byType(PaymentResultContainerWidget), findsOneWidget);
      expect(find.byType(PaymentResultTitleAndAmountWidget), findsOneWidget);
      expect(find.byType(PaymentResultNoteWidget), findsOneWidget);
      expect(find.byType(OrderSummaryWidget), findsOneWidget);
      expect(find.byType(CTAWithPoweredByWidget), findsOneWidget);

      verify(() => mockEvoSnackBar.show(
            any(),
            typeSnackBar: any(named: 'typeSnackBar'),
            durationInSec: any(named: 'durationInSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);

      final PaymentResultNoteWidget noteWidget =
          tester.widget(find.byType(PaymentResultNoteWidget));
      noteWidget.args?.onTapHeaderInfo?.call();

      verify(() => getIt<CommonNavigator>().pushNamed(
            any(),
            Screen.emiManagementDetailScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('screens displays PaymentResultPosLimitState correctly',
        (WidgetTester tester) async {
      final PaymentResultPosLimitState posLimitState =
          PaymentResultPosLimitState(mockPaymentResultTransactionEntity);

      when(() => mockPaymentResultCubit.stream)
          .thenAnswer((_) => Stream<PaymentResultPosLimitState>.value(posLimitState));
      when(() => mockPaymentResultCubit.state).thenAnswer((_) => posLimitState);

      when(() => mockDialogEnablePosLimitHandler.isDialogEnablePosLimitShown).thenReturn(false);

      when(() => mockDialogEnablePosLimitHandler.handleDialogEnablePosLimitShowing(
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
          )).thenAnswer((_) => Future<void>.value());

      await pumpScreen(
        tester,
        cubit: mockPaymentResultCubit,
        arg: PaymentResultArg(
          transactionId: '',
          needPollingProcessingStatus: true,
        ),
      );

      mockPaymentResultCubit.emit(posLimitState);

      await tester.pump();

      expect(find.byType(PaymentResultContainerWidget), findsOneWidget);
      expect(find.byType(PaymentResultTitleAndAmountWidget), findsOneWidget);
      expect(find.byType(PaymentResultNoteWidget), findsOneWidget);
      expect(find.byType(OrderSummaryWidget), findsOneWidget);
      expect(find.byType(CTAWithPoweredByWidget), findsOneWidget);

      await tester.tap(find.byType(CTAWithPoweredByWidget));
      await tester.pumpAndSettle();

      verify(() => mockDialogEnablePosLimitHandler.handleDialogEnablePosLimitShowing(
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });

    testWidgets('didPopNext starts polling tasks', (WidgetTester tester) async {
      await pumpScreen(
        tester,
        cubit: mockPaymentResultCubit,
        arg: PaymentResultArg(transactionId: 'test_transaction_id'),
      );
      final TestPaymentResultScreenState state = tester.state(find.byType(TestPaymentResultScreen));

      state.cashBackResultPollingHandler = mockTaskPollingHandler;

      state.didPopNext();

      verify(() => mockTaskPollingHandler.startPollingAllTasks()).called(1);
    });

    testWidgets('didPushNext pauses polling tasks', (WidgetTester tester) async {
      await pumpScreen(
        tester,
        cubit: mockPaymentResultCubit,
        arg: PaymentResultArg(transactionId: 'test_transaction_id'),
      );
      final TestPaymentResultScreenState state = tester.state(find.byType(TestPaymentResultScreen));

      state.cashBackResultPollingHandler = mockTaskPollingHandler;

      state.didPushNext();

      verify(() => mockTaskPollingHandler.pausePollingAllTasks()).called(1);
    });

    testWidgets('onPaused pauses polling tasks and sets _isAppOnForeGround to false',
        (WidgetTester tester) async {
      await pumpScreen(
        tester,
        cubit: mockPaymentResultCubit,
        arg: PaymentResultArg(transactionId: 'test_transaction_id'),
      );
      final TestPaymentResultScreenState state = tester.state(find.byType(TestPaymentResultScreen));

      state.cashBackResultPollingHandler = mockTaskPollingHandler;
      state.isAppOnForeGround = true;

      state.onPaused();

      verify(() => mockTaskPollingHandler.pausePollingAllTasks()).called(1);
      expect(state.isAppOnForeGround, false);
    });
  });
}
