import 'package:evoapp/feature/payment/amount_validator.dart';
import 'package:evoapp/feature/payment/payment_config.dart';
import 'package:evoapp/feature/payment/payment_shared_data.dart';
import 'package:evoapp/feature/payment/qrcode_scanner/model/qr_code_type.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/generate_random_text.dart';

class MockAppState extends Mock implements AppState {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

void main() {
  late final AmountValidator amountValidator;
  late final MockAppState mockAppState;
  const String testCurrency = 'đ';

  setUpAll(() {
    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());

    mockAppState = MockAppState();
    getIt.registerLazySingleton<AppState>(() => mockAppState);

    when(() => mockAppState.paymentSharedData).thenReturn(PaymentSharedData());

    amountValidator = AmountValidator(mockAppState);
  });

  group('test validateAmount() function', () {
    test('Test amount wrong format', () {
      final String? error =
          amountValidator.getAmountError(amount: 'amount', productCode: 'productCode');
      expect(error, EvoStrings.paymentAmountInvalid);
    });

    test('Test amount < minPaymentAmount', () {
      final String? error =
          amountValidator.getAmountError(amount: '999', productCode: 'productCode');
      expect(error, '${EvoStrings.paymentAmountMinInvalid} 1,000đ');
    });

    test('Test amount > minPaymentAmount, if minPaymentAmount is not default value', () {
      when(() => mockAppState.paymentSharedData).thenReturn(
          PaymentSharedData()..paymentConfig = PaymentConfigModel(minOrderAmount: 5000));

      final String? error =
          amountValidator.getAmountError(amount: '5001', productCode: 'productCode');
      expect(error, isNull);
    });

    test('Test amount < minPaymentAmount, if minPaymentAmount is not default value', () {
      when(() => mockAppState.paymentSharedData).thenReturn(
          PaymentSharedData()..paymentConfig = PaymentConfigModel(minOrderAmount: 5000));

      final String? error =
          amountValidator.getAmountError(amount: '4999', productCode: 'productCode');
      expect(error, '${EvoStrings.paymentAmountMinInvalid} 5,000đ');
    });

    test('Test amount > maxPaymentAmount', () {
      final String? error =
          amountValidator.getAmountError(amount: '75.000.001', productCode: 'productCode');
      expect(error, '${EvoStrings.paymentAmountMaxInvalid} 75,000,000đ');
    });

    test('Test amount > maxPaymentAmount, if maxPaymentAmount is not default value', () {
      when(() => mockAppState.paymentSharedData).thenReturn(
          PaymentSharedData()..paymentConfig = PaymentConfigModel(maxOrderAmount: 1000000));

      final String? error =
          amountValidator.getAmountError(amount: '5.000.000', productCode: 'productCode');
      expect(error, '${EvoStrings.paymentAmountMaxInvalid} 1,000,000đ');
    });

    test('Test amount valid', () {
      final String? error =
          amountValidator.getAmountError(amount: '1.000', productCode: 'productCode');
      expect(error, null);
    });

    test('Give amount with currency and currencySuffix, should return null', () {
      const String amount = '1.000$testCurrency';
      final String? error = amountValidator.getAmountError(
          amount: amount, currencySuffix: testCurrency, productCode: 'productCode');
      expect(error, null);
    });

    test('Give amount with currency, should return error', () {
      const String amount = '1.000$testCurrency';
      final String? error =
          amountValidator.getAmountError(amount: amount, productCode: 'productCode');
      expect(error, EvoStrings.paymentAmountInvalid);
    });

    test('Test amount < minPaymentAmount, and product code is vn pay', () {
      final String? error =
          amountValidator.getAmountError(amount: '999', productCode: QrProductCode.vn01String);
      expect(error, null);
    });

    test('Test amount > maxPaymentAmount, and product code is vn pay', () {
      final String? error =
          amountValidator.getAmountError(amount: '99000000', productCode: QrProductCode.vn01String);
      expect(error, null);
    });
  });

  group('test validateAmount', () {
    setUp(() {
      final PaymentSharedData paymentSharedData = PaymentSharedData();
      when(() => mockAppState.paymentSharedData).thenReturn(paymentSharedData);
    });

    test('should return true when amount = minPaymentAmount', () {
      const int amount = PaymentConfig.defaultMinPaymentAmount;

      final bool result = amountValidator.validateAmount(amount, QrProductCode.pa02String);

      expect(result, true);
    });

    test('should return true when amount > minPaymentAmount && amount < maxPaymentAmount', () {
      final int amount = generateRandomNumber(
        min: PaymentConfig.defaultMinPaymentAmount,
        max: PaymentConfig.defaultMaxPaymentAmount,
      );

      final bool result = amountValidator.validateAmount(amount, QrProductCode.pa02String);

      expect(result, true);
    });

    test('should return true when amount = maxPaymentAmount', () {
      const int amount = PaymentConfig.defaultMaxPaymentAmount;

      final bool result = amountValidator.validateAmount(amount, QrProductCode.pa02String);

      expect(result, true);
    });

    test('should return false when amount < minPaymentAmount', () {
      const int amount = PaymentConfig.defaultMinPaymentAmount - 1;

      final bool result = amountValidator.validateAmount(amount, QrProductCode.pa02String);

      expect(result, false);
    });

    test('should return false when amount > maxPaymentAmount', () {
      const int amount = PaymentConfig.defaultMaxPaymentAmount + 1;
      final PaymentSharedData paymentSharedData = PaymentSharedData();
      when(() => mockAppState.paymentSharedData).thenReturn(paymentSharedData);

      final bool result = amountValidator.validateAmount(amount, QrProductCode.pa02String);

      expect(result, false);
    });

    test('should return false when amount < minPaymentAmount and product code is VNPay', () {
      final bool result = amountValidator.validateAmount(1, QrProductCode.vn01String);

      expect(result, true);
    });

    test('should return true when amount > minPaymentAmount and product code is VNPay', () {
      final bool result = amountValidator.validateAmount(100000000, QrProductCode.vn01String);

      expect(result, true);
    });

    test('should return true when amount = custom minPaymentAmount', () {
      const int customMinAmount = 5000;
      when(() => mockAppState.paymentSharedData).thenReturn(
          PaymentSharedData()..paymentConfig = PaymentConfigModel(minOrderAmount: customMinAmount));

      final bool result = amountValidator.validateAmount(customMinAmount, QrProductCode.pa02String);

      expect(result, true);
    });

    test('should return false when amount < custom minPaymentAmount', () {
      const int customMinAmount = 5000;

      when(() => mockAppState.paymentSharedData).thenReturn(
          PaymentSharedData()..paymentConfig = PaymentConfigModel(minOrderAmount: customMinAmount));

      final bool result =
          amountValidator.validateAmount(customMinAmount - 1, QrProductCode.pa02String);

      expect(result, false);
    });

    test('should return true when amount = custom maxPaymentAmount', () {
      const int customMaxAmount = 50000000;

      when(() => mockAppState.paymentSharedData).thenReturn(
          PaymentSharedData()..paymentConfig = PaymentConfigModel(maxOrderAmount: customMaxAmount));

      final bool result = amountValidator.validateAmount(customMaxAmount, QrProductCode.pa02String);

      expect(result, true);
    });

    test('should return false when amount > custom maxPaymentAmount', () {
      const int customMaxAmount = 50000000;
      when(() => mockAppState.paymentSharedData).thenReturn(
          PaymentSharedData()..paymentConfig = PaymentConfigModel(maxOrderAmount: customMaxAmount));

      final bool result =
          amountValidator.validateAmount(customMaxAmount + 1, QrProductCode.pa02String);

      expect(result, false);
    });

    test('should return true when amount is between custom min and max values', () {
      const int customMinAmount = 5000;
      const int customMaxAmount = 50000000;

      when(() => mockAppState.paymentSharedData).thenReturn(PaymentSharedData()
        ..paymentConfig = PaymentConfigModel(
          maxOrderAmount: customMaxAmount,
          minOrderAmount: customMinAmount,
        ));

      final int middleAmount = (customMinAmount + customMaxAmount) ~/ 2;
      final bool result = amountValidator.validateAmount(middleAmount, QrProductCode.pa02String);

      expect(result, true);
    });

    test(
        'should bypass max amount check for VNPay QR code with custom maxPaymentAmount and minPaymentAmount',
        () {
      const int customMaxAmount = 50000000;
      const int customMinAmount = 1000000;

      when(() => mockAppState.paymentSharedData).thenReturn(PaymentSharedData()
        ..paymentConfig = PaymentConfigModel(
          maxOrderAmount: customMaxAmount,
          minOrderAmount: customMinAmount,
        ));

      final bool result =
          amountValidator.validateAmount(customMaxAmount * 2, QrProductCode.vn01String);

      expect(result, true);

      final bool resultMin =
          amountValidator.validateAmount(customMinAmount - 100, QrProductCode.vn01String);

      expect(resultMin, true);
    });
  });
}
