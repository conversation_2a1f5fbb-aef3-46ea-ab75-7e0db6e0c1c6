import 'package:evoapp/data/response/qr_code_parse_entity.dart';
import 'package:evoapp/feature/payment/qrcode_scanner/utils/qr_code_utils.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('test getTitlePopUpFromVerdictScanQrCode() function', () {
    test('getTitlePopUpFromVerdictScanQrCode() called with verdict = null', () {
      final String result = QrCodeUiUtils.getTitlePopUpFromVerdict();

      expect(result, EvoStrings.titleErrorPopupInvalidFormat);
    });

    test(
        'getTitlePopUpFromVerdictScanQrCode() called with verdict = [QrCodeParseEntity.verdictInvalidQRFormat]',
        () {
      final String result =
          QrCodeUiUtils.getTitlePopUpFromVerdict(verdict: QrCodeParseEntity.verdictInvalidQRFormat);

      expect(result, EvoStrings.titleErrorPopupInvalidFormat);
    });

    test(
        'getTitlePopUpFromVerdictScanQrCode() called with verdict = [QrCodeParseEntity.verdictOrderExpired]',
        () {
      final String result =
          QrCodeUiUtils.getTitlePopUpFromVerdict(verdict: QrCodeParseEntity.verdictOrderExpired);

      expect(result, EvoStrings.titleErrorPopupOrderExpired);
    });

    test(
        'getTitlePopUpFromVerdictScanQrCode() called with verdict = [QrCodeParseEntity.verdictOrderInPayment]',
        () {
      final String result =
          QrCodeUiUtils.getTitlePopUpFromVerdict(verdict: QrCodeParseEntity.verdictOrderInPayment);

      expect(result, EvoStrings.titleErrorPopupOrderInPayment);
    });

    test(
        'getTitlePopUpFromVerdictScanQrCode() called with verdict = [QrCodeParseEntity.verdictPayTimesExceeded]',
        () {
      final String result = QrCodeUiUtils.getTitlePopUpFromVerdict(
          verdict: QrCodeParseEntity.verdictPayTimesExceeded);

      expect(result, EvoStrings.titleErrorPopupPayTimesExceeded);
    });

    test(
        'getTitlePopUpFromVerdictScanQrCode() called with verdict = [QrCodeParseEntity.verdictInvalidOrderStatus]',
        () {
      final String result = QrCodeUiUtils.getTitlePopUpFromVerdict(
          verdict: QrCodeParseEntity.verdictInvalidOrderStatus);

      expect(result, EvoStrings.titleErrorPopupInvalidOrderStatus);
    });

    test(
        'getTitlePopUpFromVerdictScanQrCode() called with verdict = [QrCodeParseEntity.verdictOrderNotFound]',
        () {
      final String result =
          QrCodeUiUtils.getTitlePopUpFromVerdict(verdict: QrCodeParseEntity.verdictOrderNotFound);

      expect(result, EvoStrings.titleErrorPopupOrderInvalid);
    });

    test(
        'getTitlePopUpFromVerdictScanQrCode() called with verdict = [QrCodeParseEntity.verdictInvalidProductCode]',
        () {
      final String result = QrCodeUiUtils.getTitlePopUpFromVerdict(
          verdict: QrCodeParseEntity.verdictInvalidProductCode);

      expect(result, EvoStrings.titleErrorPopupOrderInvalid);
    });

    test(
        'getTitlePopUpFromVerdictScanQrCode() called with verdict = [QrCodeParseEntity.verdictStoreInactive]',
        () {
      final String result =
          QrCodeUiUtils.getTitlePopUpFromVerdict(verdict: QrCodeParseEntity.verdictStoreInactive);

      expect(result, EvoStrings.titleErrorPopupOrderInvalid);
    });

    test(
        'getTitlePopUpFromVerdictScanQrCode() called with verdict = [QrCodeParseEntity.verdictMerchantInactive]',
        () {
      final String result = QrCodeUiUtils.getTitlePopUpFromVerdict(
          verdict: QrCodeParseEntity.verdictMerchantInactive);

      expect(result, EvoStrings.titleErrorPopupOrderInvalid);
    });

    test(
        'getTitlePopUpFromVerdictScanQrCode() called with verdict = [QrCodeParseEntity.verdictStoreNotFound]',
        () {
      final String result =
          QrCodeUiUtils.getTitlePopUpFromVerdict(verdict: QrCodeParseEntity.verdictStoreNotFound);

      expect(result, EvoStrings.titleErrorPopupOrderInvalid);
    });

    test(
        'getTitlePopUpFromVerdictScanQrCode() called with verdict = [QrCodeParseEntity.verdictOrderSucceeded]',
        () {
      final String result =
          QrCodeUiUtils.getTitlePopUpFromVerdict(verdict: QrCodeParseEntity.verdictOrderSucceeded);

      expect(result, EvoStrings.titleErrorPopupOrderSucceeded);
    });

    test(
        'getTitlePopUpFromVerdictScanQrCode() called with verdict = [QrCodeParseEntity.verdictOrderFailed]',
        () {
      final String result =
          QrCodeUiUtils.getTitlePopUpFromVerdict(verdict: QrCodeParseEntity.verdictOrderFailed);

      expect(result, EvoStrings.titleErrorPopupOrderFailed);
    });

    test(
        'getTitlePopUpFromVerdictScanQrCode() called with verdict = [QrCodeParseEntity.verdictOrderCancelled]',
        () {
      final String result =
          QrCodeUiUtils.getTitlePopUpFromVerdict(verdict: QrCodeParseEntity.verdictOrderCancelled);

      expect(result, EvoStrings.titleErrorPopupOrderCancelled);
    });

    test(
        'getTitlePopUpFromVerdictScanQrCode() called with verdict = [QrCodeParseEntity.verdictOrderPending]',
        () {
      final String result =
          QrCodeUiUtils.getTitlePopUpFromVerdict(verdict: QrCodeParseEntity.verdictOrderPending);

      expect(result, EvoStrings.titleErrorPopupOrderPending);
    });
  });

  group('test getContentPopUpFromVerdictScanQrCode() function', () {
    test('getContentPopUpFromVerdictScanQrCode() called with message = null', () {
      final String result = QrCodeUiUtils.getContentPopUpFromVerdict();

      expect(result, EvoStrings.errorQrCodeDefaultErrorDescription);
    });
    test('getContentPopUpFromVerdictScanQrCode() called with message != null', () {
      const String message = 'test_message';
      final String result = QrCodeUiUtils.getContentPopUpFromVerdict(message: message);

      expect(result, message);
    });
  });
}
