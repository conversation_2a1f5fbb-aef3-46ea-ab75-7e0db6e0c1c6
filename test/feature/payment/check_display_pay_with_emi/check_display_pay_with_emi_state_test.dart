import 'package:evoapp/feature/payment/check_display_pay_with_emi/check_display_pay_with_emi_cubit.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CheckDisplayWithEMIInitial', () {
    test('should return correct type', () {
      final CheckDisplayWithEMIInitial state = CheckDisplayWithEMIInitial();

      expect(state, isA<CheckDisplayWithEMIState>());
    });
  });

  group('PayWithEMIIsDisplayed', () {
    test('should return correct type', () {
      final PayWithEMIIsDisplayed state = PayWithEMIIsDisplayed();

      expect(state, isA<CheckDisplayWithEMIState>());
    });
  });

  group('PayWithEMIIsHidden', () {
    test('should return correct type', () {
      final PayWithEMIIsHidden state = PayWithEMIIsHidden();

      expect(state, isA<CheckDisplayWithEMIState>());
    });
  });
}
