import 'package:evoapp/data/repository/announcement_repo.dart';
import 'package:evoapp/data/request/reward_request.dart';
import 'package:evoapp/data/response/announcement_list_entity.dart';
import 'package:evoapp/feature/announcement/model/announcement_info.dart';
import 'package:evoapp/feature/announcement/utils/unread_announcement_checker.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAnnouncementRepo extends Mock implements AnnouncementRepo {}

class MockAppState extends Mock implements AppState {}

class MockAnnouncementInfo extends Mock implements AnnouncementInfo {}

void main() {
  late UnreadAnnouncementChecker unreadAnnouncementChecker;
  late MockAnnouncementRepo mockAnnouncementRepo;
  late MockAppState mockAppState;
  late MockAnnouncementInfo mockAnnouncementInfo;

  setUp(() {
    mockAnnouncementRepo = MockAnnouncementRepo();
    registerFallbackValue(AnnouncementRequest(nextCursor: '', limit: null, status: ''));
    registerFallbackValue(AnnouncementStatus.hasUnreadItems);
    mockAppState = MockAppState();
    mockAnnouncementInfo = MockAnnouncementInfo();
    unreadAnnouncementChecker = UnreadAnnouncementChecker(mockAppState, mockAnnouncementRepo);
  });

  group('UnreadAnnouncementChecker', () {
    test('initialization test', () {
      expect(unreadAnnouncementChecker, isNotNull);
    });

    test('checkUnreadAnnouncement when user is logged in and has unread announcements', () async {
      when(() => mockAppState.isUserLogIn).thenReturn(true);
      when(() => mockAnnouncementRepo.getAnnouncements(any(), mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) async => AnnouncementListEntity.fromBaseResponse(
                  BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
                'data': <String, dynamic>{
                  'notifications': <Map<String, dynamic>>[
                    <String, dynamic>{'id': 1}
                  ]
                }
              })));
      when(() => mockAppState.announcementInfo).thenReturn(mockAnnouncementInfo);
      when(() => mockAnnouncementInfo.updateStatus(any())).thenAnswer((_) => <String, dynamic>{});

      await unreadAnnouncementChecker.checkUnreadAnnouncement();

      verify(() => mockAnnouncementInfo.updateStatus(AnnouncementStatus.hasUnreadItems)).called(1);
    });

    test('checkUnreadAnnouncement when user is logged in and has error when calling api', () async {
      when(() => mockAppState.isUserLogIn).thenReturn(true);
      when(() => mockAnnouncementRepo.getAnnouncements(any(), mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) async => AnnouncementListEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST, response: <String, dynamic>{})));
      when(() => mockAppState.announcementInfo).thenReturn(mockAnnouncementInfo);

      await unreadAnnouncementChecker.checkUnreadAnnouncement();

      verifyNever(() => mockAnnouncementInfo.updateStatus(AnnouncementStatus.noUnreadItems));
    });

    test('checkUnreadAnnouncement when user is not logged in', () async {
      when(() => mockAppState.isUserLogIn).thenReturn(false);

      await unreadAnnouncementChecker.checkUnreadAnnouncement();

      verifyNever(() => mockAppState.announcementInfo.updateStatus(any()));
    });
  });
}
