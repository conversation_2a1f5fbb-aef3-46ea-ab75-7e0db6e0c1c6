import 'package:evoapp/feature/campaign_list/other_widgets/skeleton_campaign_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const double expectedHeight4 = 4;
  const double expectedHeight48 = 48;
  const double expectedHeight16 = 16;
  const double expectedSizeImage = 100;
  const double expectedCornerRadiusImage = 10;
  const double expectedWidth16 = 16;

  final BoxDecoration expectedDecoration = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(16),
  );

  final BoxDecoration expectedDecorationImage = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(expectedCornerRadiusImage),
  );

  void verifyContain(WidgetTester widgetTester, {required double expectedHeight}) {
    final Finder containerFinder = find.byWidgetPredicate((Widget widget) {
      if (widget is Container) {
        final RenderBox renderObject = widgetTester.renderObject(find.byWidget(widget));
        final bool isMatchHeight = renderObject.size.height == expectedHeight;
        final bool isMatchDecoration = widget.decoration == expectedDecoration;
        return isMatchHeight && isMatchDecoration;
      }
      return false;
    });

    expect(containerFinder, findsOneWidget);
  }

  group('test UI SkeletonCampaignItemWidget', () {
    testWidgets('verify UI SkeletonCampaignItemWidget', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SkeletonCampaignItemWidget(
              sizeImage: expectedSizeImage,
              cornerRadiusImage: expectedCornerRadiusImage,
            ),
          ),
        ),
      );

      // verify shimmerType
      final SkeletonCampaignItemWidget skeletonCampaignItemWidget =
          widgetTester.widget(find.byType(SkeletonCampaignItemWidget));
      expect(skeletonCampaignItemWidget.shimmerType, expectedDecoration);

      // verify container image
      final Finder containerImageFinder = find.byWidgetPredicate((Widget widget) {
        if (widget is Container) {
          final RenderBox renderObject = widgetTester.renderObject(find.byWidget(widget));
          final Size actualSize = renderObject.size;
          const Size expectedSize = Size(expectedSizeImage, expectedSizeImage);
          return actualSize == expectedSize;
        }
        return false;
      });
      expect(containerImageFinder, findsOneWidget);

      final Container containerImage = widgetTester.widget(containerImageFinder);
      expect(containerImage.decoration, expectedDecorationImage);

      // verify sized box width 16
      final Finder sizedBoxWidth16Finder = find.byWidgetPredicate((Widget widget) {
        if (widget is SizedBox) {
          return widget.width == expectedWidth16;
        }
        return false;
      });
      expect(sizedBoxWidth16Finder, findsOneWidget);

      // verify sized box height 4
      final Finder sizedBoxHeight4Finder = find.byWidgetPredicate((Widget widget) {
        if (widget is SizedBox) {
          return widget.height == expectedHeight4;
        }
        return false;
      });
      expect(sizedBoxHeight4Finder, findsOneWidget);

      // verify Container height 48
      verifyContain(widgetTester, expectedHeight: expectedHeight48);

      // verify Container height 16
      verifyContain(widgetTester, expectedHeight: expectedHeight16);
    });
  });
}
