import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/biometric_pin_confirm/confirm_pin/confirm_pin_popup_cubit.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late ConfirmPinPopupCubit confirmPinPopupCubit;

  setUp(() {
    confirmPinPopupCubit = ConfirmPinPopupCubit();
  });

  group('test init ConfirmPinPopupCubit', () {
    test('should confirmPinPopupCubit.state is InitialEnterPinState', () {
      expect(confirmPinPopupCubit.state, isA<InitialEnterPinState>());
    });

    test('should get obscurePinTextValue = true', () {
      expect(confirmPinPopupCubit.obscurePinTextValue, isTrue);
    });
  });

  group('verify toggleObscurePinText() method', () {
    blocTest<ConfirmPinPopupCubit, ConfirmPinPopupState>(
      'should emit ObscureEnterPinState with obscure = false when obscurePinText = true',
      setUp: () {
        confirmPinPopupCubit.obscurePinText = true;
      },
      build: () => confirmPinPopupCubit,
      act: (ConfirmPinPopupCubit cubit) => cubit.toggleObscurePinText(),
      expect: () => <dynamic>[
        isA<ObscureEnterPinState>().having(
          (ObscureEnterPinState state) => state.obscure,
          'verify obscurePinText is false',
          isFalse,
        )
      ],
      verify: (_) {
        expect(confirmPinPopupCubit.obscurePinTextValue, false);
      },
    );

    blocTest<ConfirmPinPopupCubit, ConfirmPinPopupState>(
      'should emit ObscureEnterPinState with obscure = true when obscurePinText = false',
      setUp: () {
        confirmPinPopupCubit.obscurePinText = false;
      },
      build: () => confirmPinPopupCubit,
      act: (ConfirmPinPopupCubit cubit) => cubit.toggleObscurePinText(),
      expect: () => <dynamic>[
        isA<ObscureEnterPinState>().having(
          (ObscureEnterPinState state) => state.obscure,
          'verify obscurePinText is true',
          isTrue,
        )
      ],
      verify: (_) {
        expect(confirmPinPopupCubit.obscurePinTextValue, true);
      },
    );
  });
}
