import 'package:evoapp/data/response/emi_package_entity.dart';
import 'package:evoapp/data/response/emi_record_entity.dart';
import 'package:evoapp/data/response/emi_tenor_offer_entity.dart';
import 'package:evoapp/data/response/order_info_entity.dart';
import 'package:evoapp/feature/payment/widget/title_and_description_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:evoapp/feature/emi_management/widgets/emi_transaction_info_widget.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider commonImageProvider;
  late EvoUtilFunction mockEvoUtilFunction;

  const String fakeFormatCurrency = '50.0';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    commonImageProvider = getIt.get<CommonImageProvider>();
    mockEvoUtilFunction = getIt<EvoUtilFunction>();

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());

    when(() => mockEvoUtilFunction.evoFormatCurrency(
          any(),
          currencySymbol: any(named: 'currencySymbol'),
        )).thenReturn(fakeFormatCurrency);
  });

  group('EmiTransactionInfoWidget', () {
    testWidgets('should render correctly with null item', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EmiTransactionInfoWidget(),
          ),
        ),
      );

      expect(find.byType(TitleAndDescriptionWidget), findsNWidgets(6));
    });

    testWidgets('should render correctly with item', (WidgetTester tester) async {
      final EmiRecordEntity item = EmiRecordEntity(
        orderInfo: OrderInfoEntity(
          orderNumber: '12345',
          partnerReferenceLabel: 'fakePartnerReferenceLabel',
        ),
        createdAt: '2021-09-01T00:00:00Z',
        emiPackage: EmiPackageEntity(
          offer: EmiTenorOfferEntity(
            tenor: 12,
            interestRate: 5.0,
          ),
          monthlyInstallmentAmount: 1000,
          conversionFee: 50,
        ),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EmiTransactionInfoWidget(item: item),
          ),
        ),
      );

      expect(find.byType(TitleAndDescriptionWidget), findsNWidgets(7));

      expect(find.text(EvoStrings.paymentResultEmiTransactionCode), findsOneWidget);
      expect(find.text(EvoStrings.paymentPartnerReferenceLabel), findsOneWidget);
      expect(find.text(EvoStrings.paymentResultEmiTime), findsOneWidget);
      expect(find.text(EvoStrings.paymentResultEmiTenor), findsOneWidget);
      expect(find.text(EvoStrings.paymentResultEmiMonthlyPay), findsOneWidget);
      expect(find.text(EvoStrings.paymentResultEmiConversionFee), findsOneWidget);
      expect(find.text(EvoStrings.paymentResultEmiRate), findsOneWidget);
    });
  });
}
