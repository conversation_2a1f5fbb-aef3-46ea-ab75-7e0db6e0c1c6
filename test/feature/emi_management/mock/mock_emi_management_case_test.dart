import 'package:evoapp/feature/emi_management/mock/mock_emi_management_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MockEmiManagementCase', () {
    test('getMockEmiManagementCaseFileName should return correct file name for success case', () {
      final String fileName = getMockEmiManagementCaseFileName(MockEmiManagementCase.success);
      expect(fileName, 'emi_management_case_success.json');
    });

    test('getMockEmiManagementCaseFileName should return correct file name for fail case', () {
      final String fileName = getMockEmiManagementCaseFileName(MockEmiManagementCase.fail);
      expect(fileName, 'emi_management_case_fail.json');
    });
  });
}
