import 'package:evoapp/feature/activated_pos_limit/mock/mock_activate_card_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('getMockCardConfirmActivationCaseFileName', () {
    test('returns correct file name for success case', () {
      expect(getMockCardConfirmActivationCaseFileName(MockCardConfirmActivationCase.success),
          'card_confirm_activation_case_success.json');
    });

    test('returns correct file name for fail case', () {
      expect(getMockCardConfirmActivationCaseFileName(MockCardConfirmActivationCase.fail),
          'card_confirm_activation_case_fail.json');
    });
  });
}
