import 'package:evoapp/feature/activated_pos_limit/widgets/activated_pos_limit_second_button_widget.dart';
import 'package:evoapp/feature/activated_pos_limit/widgets/active_card_or_pos_limit_guide_widget.dart';
import 'package:evoapp/feature/activated_pos_limit/widgets/active_pos_limit_slider_widget.dart';
import 'package:evoapp/feature/dop_native/util/dop_functions.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockContext extends Mock implements BuildContext {}

class MockCommonImageProvider extends Mock implements CommonImageProvider {}

class MockDOPUtilFunctions extends Mock implements DOPUtilFunctions {}

void main() {
  late CommonImageProvider mockCommonImageProvider;
  late DOPUtilFunctions mockDOPUtilFunctions;

  const List<String> images = <String>['assets/image1.png', 'assets/image2.png'];
  const String title = 'Test Title';
  const String description = 'Test Description';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(MockContext());

    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getItRegisterColor();

    getItRegisterMockCommonUtilFunctionAndImageProvider();
    setUtilsMockInstanceForTesting();
    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    getIt.registerLazySingleton<DOPUtilFunctions>(() => MockDOPUtilFunctions());
    mockDOPUtilFunctions = getIt.get<DOPUtilFunctions>();

    when(() => mockDOPUtilFunctions.openTPBAppLink()).thenAnswer((_) => Future<void>.value());

    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());

    when(() => EvoUiUtils().calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(100);

    when(() => EvoUiUtils().calculateHorizontalSpace(
          context: any(named: 'context'),
          widthPercentage: any(named: 'widthPercentage'),
        )).thenReturn(50);
  });

  testWidgets('ActivePosLimitGuideWidget displays title, description, images and press button',
      (WidgetTester tester) async {
    bool buttonPressed = false;

    await tester.pumpWidget(
      MaterialApp(
        home: Material(
          child: ActiveCardOrPosLimitGuideWidget(
            images: images,
            title: title,
            description: description,
            onPayAgain: () {
              buttonPressed = true;
            },
          ),
        ),
      ),
    );

    expect(find.text(title), findsOneWidget);
    expect(find.text(description), findsOneWidget);
    expect(find.byType(ActivePosLimitSliderWidget), findsOneWidget);

    verify(() => mockCommonImageProvider.asset(
          EvoImages.icActiveCardArrowRight,
          width: 16,
          height: 16,
        )).called(1);

    final Finder payAgainButton = find.text(EvoStrings.activePosLimitPayAgainCTA);
    expect(payAgainButton, findsOneWidget);
    final Finder activatedPOSLimitSecondButtonFinder = find.byType(ActivatedPOSLimitSecondButtonWidget);
    expect(activatedPOSLimitSecondButtonFinder, findsOneWidget);

    await tester.tap(payAgainButton);
    await tester.pump();

    expect(buttonPressed, true);

    final Finder openTpBankAppFinder = find.text(EvoStrings.activePosLimitSetupTpBankCTA);
    expect(openTpBankAppFinder, findsOneWidget);

    await tester.tap(openTpBankAppFinder);
    await tester.pump();

    verify(() => mockDOPUtilFunctions.openTPBAppLink()).called(1);
  });
}
