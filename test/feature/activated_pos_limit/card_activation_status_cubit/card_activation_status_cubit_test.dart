import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/response/card_activation_status_entity.dart';
import 'package:evoapp/feature/activated_pos_limit/card_activation_status_cubit/card_activation_status_cubit.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';

class MockUserRepo extends Mock implements UserRepo {}

void main() {
  late CardActivationStatusCubit cubit;
  late MockUserRepo mockUserRepo;

  setUp(() {
    mockUserRepo = MockUserRepo();
    cubit = CardActivationStatusCubit(userRepo: mockUserRepo);
  });

  group('Test CardActivationStatusCubit', () {
    test('initial state is CardActivationStatusInitialState', () {
      expect(cubit.state, isA<CardActivationStatusInitialState>());
    });

    blocTest<CardActivationStatusCubit, CardActivationStatusState>(
      'emits [CardActivationStatusLoadingState, GetCardActivationStatusSucceedState] when getCardActivationStatus succeeds',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(() => mockUserRepo.getCardActivationStatus(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async => CardActivationStatusEntity.fromBaseResponse(BaseResponse(
                  statusCode: CommonHttpClient.SUCCESS,
                  response: <String, dynamic>{
                    'data': <String, dynamic>{
                      'card_status': CardStatus.activated.value,
                      'allow_activate_card': true,
                    }
                  },
                )));
      },
      act: (CardActivationStatusCubit cubit) => cubit.getCardActivationStatus(),
      expect: () => <dynamic>[
        isA<CardActivationStatusLoadingState>(),
        isA<GetCardActivationStatusSucceedState>().having(
          (GetCardActivationStatusSucceedState p) => p.entity,
          'verify entity',
          isA<CardActivationStatusEntity>()
              .having(
                (CardActivationStatusEntity p) => p.cardStatus,
                'verify card status',
                CardStatus.activated.value,
              )
              .having(
                (CardActivationStatusEntity p) => p.allowActivateCard,
                'verify allow activate card',
                true,
              ),
        ),
      ],
      verify: (_) {
        verify(() => mockUserRepo.getCardActivationStatus(mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );

    blocTest<CardActivationStatusCubit, CardActivationStatusState>(
      'emits [CardActivationStatusLoadingState, GetCardActivationStatusFailedState] when getCardActivationStatus fails',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(() => mockUserRepo.getCardActivationStatus(mockConfig: any(named: 'mockConfig')))
            .thenAnswer((_) async => CardActivationStatusEntity.fromBaseResponse(BaseResponse(
                  statusCode: CommonHttpClient.BAD_REQUEST,
                  response: <String, dynamic>{},
                )));
      },
      act: (CardActivationStatusCubit cubit) => cubit.getCardActivationStatus(),
      expect: () => <Object>[
        isA<CardActivationStatusLoadingState>(),
        isA<GetCardActivationStatusFailedState>().having(
          (GetCardActivationStatusFailedState p) => p.errorUIModel,
          'verify error ui model',
          isA<ErrorUIModel>(),
        ),
      ],
      verify: (_) {
        verify(() => mockUserRepo.getCardActivationStatus(mockConfig: any(named: 'mockConfig')))
            .called(1);
      },
    );
  });
}
