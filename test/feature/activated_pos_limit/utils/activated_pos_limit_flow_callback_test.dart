import 'package:evoapp/feature/activated_pos_limit/models/activated_pos_limit_flow_failed_reason.dart';
import 'package:evoapp/feature/activated_pos_limit/utils/activated_pos_limit_flow_callback.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:mocktail/mocktail.dart';

class MockBuildContext extends Mock implements BuildContext {}

class MockActivatedPOSLimitFlowPayload extends Mock implements ActivatedPOSLimitFlowPayload {}

void main() {
  late ActivatedPOSLimitFlowCallback callback;
  late MockBuildContext mockContext;
  late MockActivatedPOSLimitFlowPayload mockPayload;

  setUp(() {
    mockContext = MockBuildContext();
    mockPayload = MockActivatedPOSLimitFlowPayload();
  });

  test('onSuccess should be called with correct parameters', () {
    bool onSuccessCalled = false;
    callback = ActivatedPOSLimitFlowCallback(
      onSuccess: (BuildContext context, ActivatedPOSLimitFlowPayload? payload) {
        onSuccessCalled = true;
        expect(context, mockContext);
        expect(payload, mockPayload);
      },
    );

    callback.onSuccess?.call(mockContext, mockPayload);
    expect(onSuccessCalled, true);
  });

  test('onFailed should be called with correct parameters', () {
    bool onFailedCalled = false;
    final ActivatedPOSLimitFlowFailedReason reason =
        ActivatedPOSLimitFlowFailedReason.userCancelled;
    final String userMessage = 'User cancelled';

    callback = ActivatedPOSLimitFlowCallback(
      onFailed: (
        BuildContext context,
        ActivatedPOSLimitFlowFailedReason failedReason,
        String? message,
      ) {
        onFailedCalled = true;
        expect(context, mockContext);
        expect(failedReason, reason);
        expect(message, userMessage);
      },
    );

    callback.onFailed?.call(mockContext, reason, userMessage);
    expect(onFailedCalled, true);
  });
}
