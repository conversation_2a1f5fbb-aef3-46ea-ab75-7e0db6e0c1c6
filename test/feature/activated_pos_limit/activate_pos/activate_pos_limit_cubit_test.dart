import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/response/card_activate_entity.dart';
import 'package:evoapp/data/response/setup_pos_limit_entity.dart';
import 'package:evoapp/feature/activated_pos_limit/activate_pos/cubit/activate_pos_limit_cubit.dart';
import 'package:evoapp/feature/activated_pos_limit/utils/activated_pos_limit_state.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';
import '../../../util/flutter_test_config.dart';

class MockUserRepo extends Mock implements UserRepo {}

class MockAppState extends Mock implements AppState {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockFeatureToggle extends Mock implements FeatureToggle {}

void main() {
  late ActivatePosLimitCubit cubit;
  late UserRepo mockUserRepo;
  late AppState mockAppState;
  final MockEvoLocalStorageHelper mockEvoLocalStorageHelper = MockEvoLocalStorageHelper();

  setUpAll(() {
    getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());

    getIt.registerSingleton<UserRepo>(MockUserRepo());
    mockUserRepo = getIt.get<UserRepo>();

    getIt.registerSingleton<AppState>(MockAppState());
    mockAppState = getIt.get<AppState>();
  });

  setUp(() {
    cubit = ActivatePosLimitCubit(
      userRepo: mockUserRepo,
      appState: mockAppState,
      evoLocalStorageHelper: mockEvoLocalStorageHelper,
    );
  });

  test('initial state is ActivatePOSInitial', () {
    expect(cubit.state, isA<ActivatePOSInitial>());
  });

  group('test onChangePosLimitAmount function', () {
    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
      'emits [ActivatePOSValidateFailed] when amount is null',
      build: () => cubit,
      setUp: () {
        when(() => evoUtilFunction.getAmountFromStr(any())).thenReturn(null);

        when(() => mockAppState.activatedPOSLimitState).thenReturn(ActivatedPOSLimitState(
          orderAmount: 3000000,
        ));
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (ActivatePosLimitCubit cubit) => cubit.onChangePosLimitAmount(
        amount: null,
        posLimitAllow: 10000000,
      ),
      expect: () => <dynamic>[isA<ActivatePOSValidateFailed>()],
    );

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
      'emits [ActivatePOSValidateFailed] when amount is not number',
      build: () => cubit,
      setUp: () {
        when(() => evoUtilFunction.getAmountFromStr(any())).thenReturn(null);

        when(() => mockAppState.activatedPOSLimitState).thenReturn(ActivatedPOSLimitState(
          orderAmount: 3000000,
        ));
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (ActivatePosLimitCubit cubit) => cubit.onChangePosLimitAmount(
        amount: 'abc',
        posLimitAllow: 10000000,
      ),
      expect: () => <dynamic>[isA<ActivatePOSValidateFailed>()],
    );

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
      'emits [ActivatePOSValidateFailed] when amount is less than orderAmount',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(() => evoUtilFunction.getAmountFromStr(any())).thenReturn(2000000);
        when(() => mockAppState.activatedPOSLimitState).thenReturn(ActivatedPOSLimitState(
          orderAmount: 3000000,
        ));
      },
      act: (ActivatePosLimitCubit cubit) => cubit.onChangePosLimitAmount(
        amount: '2000000',
        posLimitAllow: 20000000,
      ),
      expect: () => <dynamic>[isA<ActivatePOSValidateFailed>()],
    );

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
      'emits [ActivatePOSValidateFailed] when amount is greater than maxPosLimitAllow',
      build: () => cubit,
      setUp: () {
        when(() => evoUtilFunction.getAmountFromStr(any())).thenReturn(60000000);
        when(() => mockAppState.activatedPOSLimitState).thenReturn(ActivatedPOSLimitState(
          orderAmount: 3000000,
        ));
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (ActivatePosLimitCubit cubit) =>
          cubit.onChangePosLimitAmount(amount: '60000000', posLimitAllow: 50000000),
      expect: () => <dynamic>[isA<ActivatePOSValidateFailed>()],
    );

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
      'emits [ActivatePOSValidateFailed] when orderAmount is null',
      build: () => cubit,
      setUp: () {
        when(() => evoUtilFunction.getAmountFromStr(any())).thenReturn(60000000);
        when(() => mockAppState.activatedPOSLimitState).thenReturn(ActivatedPOSLimitState());
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (ActivatePosLimitCubit cubit) => cubit.onChangePosLimitAmount(
        amount: '60000000',
        posLimitAllow: 600000000,
      ),
      expect: () => <dynamic>[isA<ActivatePOSValidateSuccess>()],
    );

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
      'emits [ActivatePOSValidateSuccess] when amount is within the valid range',
      setUp: () {
        when(() => evoUtilFunction.getAmountFromStr(any())).thenReturn(4000000);
        when(() => mockAppState.activatedPOSLimitState).thenReturn(ActivatedPOSLimitState(
          orderAmount: 3000000,
        ));
      },
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (ActivatePosLimitCubit cubit) => cubit.onChangePosLimitAmount(
        amount: '4000000',
        posLimitAllow: 50000000,
      ),
      expect: () => <dynamic>[isA<ActivatePOSValidateSuccess>()],
    );
  });

  group('test setupPosLimit', () {
    final int fakeAmount = 1000000;

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
      'activatePosLimit success',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(() => mockUserRepo.setPOSLimit(
              posLimit: any(named: 'posLimit'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => SetPOSLimitEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: <String, dynamic>{
              'data': <String, dynamic>{'pos_limit_status': 'success'}
            },
          )),
        );
      },
      act: (ActivatePosLimitCubit cubit) => cubit.activatePosLimit(fakeAmount),
      expect: () => <dynamic>[
        isA<ActivatePOSLoading>(),
        isA<ActivatePOSSucceed>().having(
          (ActivatePOSSucceed p0) => p0.entity,
          'verify entity',
          isA<SetPOSLimitEntity>().having(
            (SetPOSLimitEntity p) => p.posLimitStatus,
            'verify POS status',
            SetPOSLimitStatus.success.value,
          ),
        ),
      ],
      verify: (_) {
        expect(
            verify(() => mockUserRepo.setPOSLimit(
                posLimit: captureAny(named: 'posLimit'),
                mockConfig: any(named: 'mockConfig'))).captured,
            <dynamic>[fakeAmount]);
      },
    );

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
      'activatePosLimit fail',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(() => mockUserRepo.setPOSLimit(
              posLimit: any(named: 'posLimit'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => SetPOSLimitEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: <String, dynamic>{'data': <String, dynamic>{}},
          )),
        );
      },
      act: (ActivatePosLimitCubit cubit) => cubit.activatePosLimit(fakeAmount),
      expect: () => <dynamic>[
        isA<ActivatePOSLoading>(),
        isA<ActivatePOSError>().having(
          (ActivatePOSError p0) => p0.error,
          'verify error',
          isA<ErrorUIModel>(),
        ),
      ],
      verify: (_) {
        expect(
            verify(() => mockUserRepo.setPOSLimit(
                posLimit: captureAny(named: 'posLimit'),
                mockConfig: any(named: 'mockConfig'))).captured,
            <dynamic>[fakeAmount]);
      },
    );

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
      'activatePosLimit fail with verdict redirect_tpb_app',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(() => mockUserRepo.setPOSLimit(
              posLimit: any(named: 'posLimit'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => SetPOSLimitEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: <String, dynamic>{
              'verdict': SetPOSLimitEntity.verdictRedirectTPBApp,
              'data': <String, dynamic>{},
            },
          )),
        );
      },
      act: (ActivatePosLimitCubit cubit) => cubit.activatePosLimit(fakeAmount),
      expect: () => <dynamic>[
        isA<ActivatePOSLoading>(),
        isA<ActivatePOSRedirectToTPBAppState>(),
      ],
      verify: (_) {
        expect(
            verify(() => mockUserRepo.setPOSLimit(
                posLimit: captureAny(named: 'posLimit'),
                mockConfig: any(named: 'mockConfig'))).captured,
            <dynamic>[fakeAmount]);
      },
    );
  });

  group('test activate card', () {
    final int fakeAmount = 1000000;
    final String fakeRedirectUrl = 'https://www.example.com';

    setUpAll(() {
      when(() => mockEvoLocalStorageHelper.setLastTimeRequest3DSCardActivation(any()))
          .thenAnswer((_) async => Future<void>.value());
    });

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
      'activateCard success',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(() => mockUserRepo.activateCard(
              posLimit: any(named: 'posLimit'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => CardActivateEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: <String, dynamic>{
              'data': <String, dynamic>{'redirect_url': fakeRedirectUrl}
            },
          )),
        );
      },
      act: (ActivatePosLimitCubit cubit) => cubit.activateCard(fakeAmount),
      expect: () => <dynamic>[
        isA<ActivatePOSLoading>(),
        isA<ActivateCardSucceed>().having(
          (ActivateCardSucceed p0) => p0.entity,
          'verify entity',
          isA<CardActivateEntity>().having(
            (CardActivateEntity p) => p.redirectUrl,
            'verify redirectUrl',
            fakeRedirectUrl,
          ),
        ),
      ],
      verify: (_) {
        expect(
            verify(() => mockUserRepo.activateCard(
                posLimit: captureAny(named: 'posLimit'),
                mockConfig: any(named: 'mockConfig'))).captured,
            <dynamic>[fakeAmount]);

        verify(() => mockEvoLocalStorageHelper.setLastTimeRequest3DSCardActivation(any()))
            .called(1);
      },
    );

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
      'activateCard fail',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(() => mockUserRepo.activateCard(
              posLimit: any(named: 'posLimit'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => CardActivateEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: <String, dynamic>{'data': <String, dynamic>{}},
          )),
        );
      },
      act: (ActivatePosLimitCubit cubit) => cubit.activateCard(fakeAmount),
      expect: () => <dynamic>[
        isA<ActivatePOSLoading>(),
        isA<ActivateCardError>().having(
          (ActivateCardError p0) => p0.error,
          'verify error',
          isA<ErrorUIModel>(),
        ),
      ],
      verify: (_) {
        expect(
            verify(() => mockUserRepo.activateCard(
                posLimit: captureAny(named: 'posLimit'),
                mockConfig: any(named: 'mockConfig'))).captured,
            <dynamic>[fakeAmount]);
        verifyNever(() => mockEvoLocalStorageHelper.setLastTimeRequest3DSCardActivation(any()));
      },
    );

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
      'activateCard fail with verdict redirect_tpb_app',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(() => mockUserRepo.activateCard(
              posLimit: any(named: 'posLimit'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => CardActivateEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: <String, dynamic>{
              'verdict': CardActivateEntity.verdictRedirectTPBApp,
              'data': <String, dynamic>{},
            },
          )),
        );
      },
      act: (ActivatePosLimitCubit cubit) => cubit.activateCard(fakeAmount),
      expect: () => <dynamic>[
        isA<ActivatePOSLoading>(),
        isA<ActivatePOSRedirectToTPBAppState>(),
      ],
      verify: (_) {
        expect(
            verify(() => mockUserRepo.activateCard(
                posLimit: captureAny(named: 'posLimit'),
                mockConfig: any(named: 'mockConfig'))).captured,
            <dynamic>[fakeAmount]);
        verifyNever(() => mockEvoLocalStorageHelper.setLastTimeRequest3DSCardActivation(any()));
      },
    );

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
      'activateCard fail with status 409 and verdict is invalid state',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      setUp: () {
        when(() => mockUserRepo.activateCard(
              posLimit: any(named: 'posLimit'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => CardActivateEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.DUPLICATE,
            response: <String, dynamic>{'verdict': CardActivateEntity.verdictInvalidState},
          )),
        );
      },
      act: (ActivatePosLimitCubit cubit) => cubit.activateCard(fakeAmount),
      expect: () => <dynamic>[
        isA<ActivatePOSLoading>(),
        isA<ActivateCardInvalidState>(),
      ],
      verify: (_) {
        expect(
            verify(() => mockUserRepo.activateCard(
                posLimit: captureAny(named: 'posLimit'),
                mockConfig: any(named: 'mockConfig'))).captured,
            <dynamic>[fakeAmount]);

        verifyNever(() => mockEvoLocalStorageHelper.setLastTimeRequest3DSCardActivation(any()));
      },
    );
  });

  group('test Check show waiting popup', () {
    final MockFeatureToggle mockFeatureToggle = MockFeatureToggle();

    setUpAll(() {
      getIt.registerSingleton<FeatureToggle>(mockFeatureToggle);
    });

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
        'emits [NoNeedShowWaitingPopup] when savedTime is null',
        build: () => cubit,
        setUp: () {
          when(() => mockFeatureToggle.enableActivatePOSLimitFeature).thenReturn(true);
          when(() => mockEvoLocalStorageHelper.getLastTimeRequest3DSCardActivation())
              .thenAnswer((_) async => null);
        },
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (ActivatePosLimitCubit cubit) => cubit.checkShowWaitingPopUp(),
        expect: () => <dynamic>[
              isA<NoNeedShowWaitingPopup>(),
            ],
        verify: (_) {
          verify(() => mockEvoLocalStorageHelper.getLastTimeRequest3DSCardActivation()).called(1);
          verifyNever(() => mockEvoLocalStorageHelper.setLastTimeRequest3DSCardActivation(any()));
        });

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
        'emits [NeedShowWaitingPopup] when diffTime is less than secondsNeedToWaitAfterCardActivation',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        setUp: () {
          final int savedTime = DateTime.now().millisecondsSinceEpoch - 1000;
          when(() => mockEvoLocalStorageHelper.getLastTimeRequest3DSCardActivation())
              .thenAnswer((_) async => savedTime);

          when(() => mockFeatureToggle.enableActivatePOSLimitFeature).thenReturn(true);
        },
        act: (ActivatePosLimitCubit cubit) => cubit.checkShowWaitingPopUp(),
        expect: () => <dynamic>[
              isA<NeedShowWaitingPopup>().having(
                  (NeedShowWaitingPopup state) => state.remainingTime, 'verify remaining time', 59),
            ],
        verify: (_) {
          verify(() => mockEvoLocalStorageHelper.getLastTimeRequest3DSCardActivation()).called(1);
        });

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
        'emits [NoNeedShowWaitingPopup] when diffTime is greater than or equal to secondsNeedToWaitAfterCardActivation',
        build: () => cubit,
        setUp: () {
          final int savedTime = DateTime.now().millisecondsSinceEpoch - 70000;
          when(() => mockEvoLocalStorageHelper.getLastTimeRequest3DSCardActivation())
              .thenAnswer((_) async => savedTime);
          when(() => mockEvoLocalStorageHelper.setLastTimeRequest3DSCardActivation(any()))
              .thenAnswer((_) async => Future<void>.value());

          when(() => mockFeatureToggle.enableActivatePOSLimitFeature).thenReturn(true);
        },
        act: (ActivatePosLimitCubit cubit) => cubit.checkShowWaitingPopUp(),
        expect: () => <dynamic>[
              isA<NoNeedShowWaitingPopup>(),
            ],
        verify: (_) {
          verify(() => mockEvoLocalStorageHelper.getLastTimeRequest3DSCardActivation()).called(1);
          verify(() => mockEvoLocalStorageHelper.setLastTimeRequest3DSCardActivation(null))
              .called(1);
        });

    blocTest<ActivatePosLimitCubit, ActivatePOSState>(
        'emits [NoNeedShowWaitingPopup] when feature activatePOSLimitFeature is false',
        build: () => cubit,
        setUp: () {
          when(() => mockFeatureToggle.enableActivatePOSLimitFeature).thenReturn(false);
        },
        act: (ActivatePosLimitCubit cubit) => cubit.checkShowWaitingPopUp(),
        expect: () => <dynamic>[
              isA<NoNeedShowWaitingPopup>(),
            ],
        verify: (_) {
          verifyNever(() => mockEvoLocalStorageHelper.getLastTimeRequest3DSCardActivation());
          verifyNever(() => mockEvoLocalStorageHelper.setLastTimeRequest3DSCardActivation(any()));
        });
  });
}
