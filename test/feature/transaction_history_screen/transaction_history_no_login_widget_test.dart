import 'package:evoapp/feature/transaction_history_screen/transaction_history_no_login_widget.dart';
import 'package:evoapp/resources/button_dimensions.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_action_handler.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:evoapp/widget/empty_data_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class MockBuildContext extends Fake implements BuildContext {}

class MockEvoImageProvider extends Mock implements CommonImageProvider {}


void main() {
  late Widget widget;
  late CommonImageProvider commonImageProvider;
  late TransactionHistoryNoLoginWidget widgetNeedTest;

  setUpAll(() {
    registerFallbackValue(MockBuildContext());
    getIt.registerLazySingleton<EvoColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonButtonStyles>(() => CommonButtonStyles());
    getIt.registerLazySingleton<CommonButtonDimensions>(() => EvoButtonDimensions());
    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    commonImageProvider = getIt.get<CommonImageProvider>();
    setUtilsMockInstanceForTesting();
    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());

    when(() => EvoActionHandler().openAuthenticationScreen())
        .thenAnswer((_) async => Future<void>.value());
  });

  setUp(() {
    widgetNeedTest = const TransactionHistoryNoLoginWidget();
    widget = MaterialApp(
      home: Scaffold(
        body: widgetNeedTest,
      ),
    );

    when(() => EvoUiUtils().calculateVerticalSpace(
            context: any(named: 'context'), heightPercentage: any(named: 'heightPercentage')))
        .thenAnswer((_) => widgetNeedTest.paddingTopPercentage * 312);
  });

  tearDownAll(() {
    resetUtilMockToOriginalInstance();
    getIt.reset();
  });

  group('verify TransactionHistoryEmptyWidget', () {
    testWidgets('renders empty data container with correct text', (WidgetTester tester) async {
      await tester.pumpWidget(widget);

      final Finder emptyDataContainerFinder = find.byType(EmptyDataContainer);
      expect(emptyDataContainerFinder, findsOneWidget);

      final EmptyDataContainer emptyDataContainerWidget = tester.widget(emptyDataContainerFinder);
      expect(emptyDataContainerWidget.assetName, EvoImages.bgTransactionHistoryNoLogin);
      expect(emptyDataContainerWidget.text, EvoStrings.transactionHistoryNotLogin);
      expect(emptyDataContainerWidget.fitImage, BoxFit.fitWidth);
      expect(emptyDataContainerWidget.widthImage, double.infinity);

      // Test bottomWidgets
      final List<Widget>? bottomWidgets = emptyDataContainerWidget.bottomWidgets;

      // Test button widget of bottomWidgets
      final CommonButton buttonWidget = bottomWidgets?.last as CommonButton;
      expect(buttonWidget.child, isA<Text>());
      final Text textWidget = buttonWidget.child as Text;
      expect(textWidget.data, EvoStrings.bottomBarLoginLabel);

      // Test text widget of bottomWidgets
      final Finder textDecFinder = find.text(EvoStrings.transactionHistoryNotLoginDesc);
      final Text textDecWidget = tester.widget(textDecFinder);
      expect(textDecWidget.style, evoTextStyles.bodyMedium(evoColors.textPassive));
      expect(textDecWidget.textAlign, TextAlign.center);

      // Test paddingTop of EmptyDataContainer
      final double expectedPaddingTop = EvoUiUtils().calculateVerticalSpace(
        context: tester.element(find.byType(TransactionHistoryNoLoginWidget)),
        heightPercentage: widgetNeedTest.paddingTopPercentage,
      );
      expect(emptyDataContainerWidget.paddingTop, expectedPaddingTop);

      // Test value of spacingBetweenTextAndImage
      final double spacingBetweenTextAndImage = emptyDataContainerWidget.spacingBetweenTextAndImage;
      expect(spacingBetweenTextAndImage, widgetNeedTest.spacingBetweenTextAndImage);
    });
  });

  testWidgets('TransactionHistoryNoLoginWidget opens authentication screen on button press',
      (WidgetTester tester) async {
    // Build the widget tree
    await tester.pumpWidget(widget);

    // Tap the login button
    await tester.tap(find.text(EvoStrings.bottomBarLoginLabel));
    await tester.pumpAndSettle();

    //final BuildContext context = tester.element(find.byType(TransactionHistoryNoLoginWidget));
    verify(() => EvoActionHandler().openAuthenticationScreen()).called(1);
  });
}
