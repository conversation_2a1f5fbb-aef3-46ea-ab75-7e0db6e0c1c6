import 'package:evoapp/feature/transaction_history_screen/dialog/mock/mock_cashback_transaction_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MockCashbackTransactionCase', () {
    test('success case should have the correct file name', () {
      final String fileName =
          getMockCashbackTransactionFileName(MockCashbackTransactionCase.success);

      expect(fileName, equals('cashback_transaction_case_success.json'));
    });

    test('fail case should have the correct file name', () {
      final String fileName = getMockCashbackTransactionFileName(MockCashbackTransactionCase.fail);

      expect(fileName, equals('cashback_transaction_case_fail.json'));
    });

    test('enum values should match expected values', () {
      expect(MockCashbackTransactionCase.success.value, 'cashback_transaction_case_success.json');
      expect(MockCashbackTransactionCase.fail.value, 'cashback_transaction_case_fail.json');
    });
  });
}
