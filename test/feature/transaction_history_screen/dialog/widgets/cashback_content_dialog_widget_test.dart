import 'package:evoapp/data/response/current_cashback_info_entity.dart';
import 'package:evoapp/feature/emi_management/widgets/emi_custom_progressbar_widget.dart';
import 'package:evoapp/feature/transaction_history_screen/dialog/widgets/cashback_content_dialog_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<EvoColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());

    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
        )).thenAnswer((_) => Container());
  });

  group('CashbackContentDialogWidget', () {
    testWidgets('renders correctly with null records', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(
        home: CashbackContentDialogWidget(
          currentCashback: null,
          previousCashbackRecords: null,
        ),
      ));

      expect(find.byType(SizedBox), findsWidgets);
    });

    testWidgets('renders correctly with empty records', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(
        home: CashbackContentDialogWidget(
          currentCashback: null,
          previousCashbackRecords: <CurrentCashbackInfoEntity>[],
        ),
      ));

      expect(find.text('-/-'), findsOneWidget);
      expect(find.byType(EmiCustomProgressbarWidget), findsNothing);
    });

    testWidgets('renders correctly with non-empty records', (WidgetTester tester) async {
      final int currentMonth = DateTime.now().month;
      final int currentYear = DateTime.now().year;
      final CurrentCashbackInfoEntity cashback1 = CurrentCashbackInfoEntity(
        month: currentMonth,
        year: currentYear,
        amount: 1000,
        limit: 2000,
      );

      final int previousMonth = currentMonth - 1;
      final CurrentCashbackInfoEntity cashback2 = CurrentCashbackInfoEntity(
        month: previousMonth,
        year: currentYear,
        amount: 3000,
        limit: 4000,
      );

      final int previousYear = currentYear - 1;
      final CurrentCashbackInfoEntity cashback3 = CurrentCashbackInfoEntity(
        month: currentMonth,
        year: previousYear,
        amount: 1500,
        limit: 2500,
      );

      await tester.pumpWidget(MaterialApp(
        home: CashbackContentDialogWidget(
          currentCashback: cashback1,
          previousCashbackRecords: <CurrentCashbackInfoEntity>[
            cashback2,
            cashback3,
          ],
        ),
      ));
      await tester.pumpAndSettle();

      expect(find.text('Kỳ tháng $currentMonth'), findsOneWidget);
      expect(find.text('1,000đ/2,000đ'), findsOneWidget);
      expect(
        find.text(
            'Khoản tiền hoàn sẽ được ghi có vào tài khoản thẻ tín dụng chậm nhất vào ngày 20 của tháng kế tiếp kỳ sao kê có phát sinh giao dịch.'),
        findsOneWidget,
      );
      expect(find.byType(EmiCustomProgressbarWidget), findsOneWidget);
      expect(find.text('Ứng dụng EVO'), findsNWidgets(2));

      expect(find.text('Kỳ tháng $previousMonth'), findsNothing);
      expect(find.text('Kỳ tháng $previousMonth/$currentYear'), findsOneWidget);
      expect(find.text('+3,000đ'), findsOneWidget);
      expect(find.text('Kỳ tháng $currentMonth/$previousYear'), findsOneWidget);
      expect(find.text('+1,500đ'), findsOneWidget);

      final Finder listView = find.byType(ListView);
      expect(listView, findsOneWidget);
    });
  });
}
