import 'package:evoapp/data/response/payment_method_entity.dart';
import 'package:evoapp/data/response/payment_result_transaction_entity.dart';
import 'package:evoapp/data/response/store_info_entity.dart';
import 'package:evoapp/feature/transaction_history_screen/transaction_list/widget/transaction_history_info_item.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../transaction_history_util.dart';

void main() {
  late CommonUtilFunction commonUtilFunction;
  late CommonImageProvider imageProvider;

  const String createAtValue = '2023-03-31T16:52:02+07:00';
  const String createAtExpect = '31/03/2023 - 16:52';

  setUpAll(() {
    TransactionHistoryUtil.instance.initSetUpAll();
    commonUtilFunction = getIt.get<CommonUtilFunction>();
    imageProvider = getIt.get<CommonImageProvider>();

    when(() => imageProvider.asset(any(),
        height: any(named: 'height'),
        width: any(named: 'width'),
        fit: any(named: 'fit'),
        cornerRadius: any(named: 'cornerRadius'))).thenAnswer(
      (_) => Container(),
    );

    when(() => commonUtilFunction.toDateTime(any())).thenReturn(DateTime(2023, 3, 31, 16, 52));
  });

  Widget buildMaterialApp(PaymentResultTransactionEntity transaction) {
    return MaterialApp(
      home: Scaffold(
        body: TransactionHistoryInfoItem(transaction: transaction),
      ),
    );
  }

  testWidgets('verify UI with status processing', (WidgetTester widgetTester) async {
    final PaymentResultTransactionEntity transaction = PaymentResultTransactionEntity(
      status: 'processing',
      storeInfo: StoreInfoEntity(
        thumbnail: 'thumbnail',
        address: 'fake_address',
      ),
      paymentMethod: PaymentMethodEntity(
        sourceName: 'fake_source_name',
      ),
      userChargeAmount: 1000,
      paymentService: 'emi',
      createdAt: createAtValue,
    );

    await widgetTester.pumpWidget(buildMaterialApp(transaction));

    final Finder finderItemRightContent = find.byType(TransactionHistoryInfoItem);
    expect(finderItemRightContent, findsOneWidget);

    verify(() => imageProvider.asset(
          EvoImages.icTransactionHistoryProcessing,
          height: 10,
          width: 10,
          fit: BoxFit.fill,
        )).called(1);

    final Finder finderTextProcessing = find.text(EvoStrings.transactionHistoryProcessing);
    expect(finderTextProcessing, findsOneWidget);

    final Text textProcessing = widgetTester.widget(finderTextProcessing);
    expect(
        textProcessing.style,
        evoTextStyles.h100(
          color: evoColors.transactionHistoryProcessing,
        ));

    final Finder finderTextAddress = find.text('fake_address');
    expect(finderTextAddress, findsOneWidget);
    final Text textAddress = widgetTester.widget(finderTextAddress);
    expect(textAddress.style, evoTextStyles.bodySmall().copyWith(height: 1.3));
    expect(textAddress.maxLines, 2);
    expect(textAddress.overflow, TextOverflow.ellipsis);

    final Finder finderTextCreateAt = find.text(createAtExpect);
    expect(finderTextCreateAt, findsOneWidget);

    final Text textCreateAt = widgetTester.widget(finderTextCreateAt);
    expect(textCreateAt.style, evoTextStyles.bodyXSmall(color: evoColors.textPassive2));

    verify(() => commonUtilFunction.toDateTime(createAtValue)).called(1);
  });

  testWidgets('verify UI with status success', (WidgetTester widgetTester) async {
    final PaymentResultTransactionEntity transaction = PaymentResultTransactionEntity(
      status: 'success',
      storeInfo: StoreInfoEntity(
        thumbnail: 'thumbnail',
        address: 'fake_address',
      ),
      paymentMethod: PaymentMethodEntity(
        sourceName: 'fake_source_name',
      ),
      userChargeAmount: 1000,
      paymentService: 'emi',
      createdAt: createAtValue,
    );

    await widgetTester.pumpWidget(buildMaterialApp(transaction));

    final Finder finderItemRightContent = find.byType(TransactionHistoryInfoItem);
    expect(finderItemRightContent, findsOneWidget);

    verify(() => imageProvider.asset(
          EvoImages.icTransactionHistorySuccess,
          height: 10,
          width: 10,
          fit: BoxFit.fill,
        )).called(1);

    final Finder finderTextProcessing = find.text(EvoStrings.transactionHistorySuccess);
    expect(finderTextProcessing, findsOneWidget);

    final Text textProcessing = widgetTester.widget(finderTextProcessing);
    expect(
        textProcessing.style,
        evoTextStyles.h100(
          color: evoColors.transactionHistorySuccess,
        ));

    final Finder finderTextAddress = find.text('fake_address');
    expect(finderTextAddress, findsOneWidget);
    final Text textAddress = widgetTester.widget(finderTextAddress);
    expect(textAddress.style, evoTextStyles.bodySmall().copyWith(height: 1.3));
    expect(textAddress.maxLines, 2);
    expect(textAddress.overflow, TextOverflow.ellipsis);

    final Finder finderTextCreateAt = find.text(createAtExpect);
    expect(finderTextCreateAt, findsOneWidget);

    final Text textCreateAt = widgetTester.widget(finderTextCreateAt);
    expect(textCreateAt.style, evoTextStyles.bodyXSmall(color: evoColors.textPassive2));

    verify(() => commonUtilFunction.toDateTime(createAtValue)).called(1);
  });

  testWidgets('verify UI with status failure', (WidgetTester widgetTester) async {
    final PaymentResultTransactionEntity transaction = PaymentResultTransactionEntity(
      status: 'failure',
      storeInfo: StoreInfoEntity(
        thumbnail: 'thumbnail',
        address: 'fake_address',
      ),
      paymentMethod: PaymentMethodEntity(
        sourceName: 'fake_source_name',
      ),
      userChargeAmount: 1000,
      paymentService: 'emi',
      createdAt: createAtValue,
    );

    await widgetTester.pumpWidget(buildMaterialApp(transaction));

    final Finder finderItemRightContent = find.byType(TransactionHistoryInfoItem);
    expect(finderItemRightContent, findsOneWidget);

    verify(() => imageProvider.asset(
          EvoImages.icTransactionHistoryFailure,
          height: 10,
          width: 10,
          fit: BoxFit.fill,
        )).called(1);

    final Finder finderTextProcessing = find.text(EvoStrings.transactionHistoryFailure);
    expect(finderTextProcessing, findsOneWidget);

    final Text textProcessing = widgetTester.widget(finderTextProcessing);
    expect(
        textProcessing.style,
        evoTextStyles.h100(
          color: evoColors.transactionHistoryFailure,
        ));

    final Finder finderTextAddress = find.text('fake_address');
    expect(finderTextAddress, findsOneWidget);
    final Text textAddress = widgetTester.widget(finderTextAddress);
    expect(textAddress.style, evoTextStyles.bodySmall().copyWith(height: 1.3));
    expect(textAddress.maxLines, 2);
    expect(textAddress.overflow, TextOverflow.ellipsis);

    final Finder finderTextCreateAt = find.text(createAtExpect);
    expect(finderTextCreateAt, findsOneWidget);

    final Text textCreateAt = widgetTester.widget(finderTextCreateAt);
    expect(textCreateAt.style, evoTextStyles.bodyXSmall(color: evoColors.textPassive2));

    verify(() => commonUtilFunction.toDateTime(createAtValue)).called(1);
  });
}
