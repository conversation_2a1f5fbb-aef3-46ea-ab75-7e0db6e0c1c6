import 'package:evoapp/data/response/current_cashback_info_entity.dart';
import 'package:evoapp/feature/emi_management/widgets/emi_custom_progressbar_widget.dart';
import 'package:evoapp/feature/transaction_history_screen/transaction_list/widget/cashback_statistic_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/colors.dart';
import 'package:flutter_common_package/resources/text_styles.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<EvoColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());

    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
        )).thenAnswer((_) => Container());
  });

  group('CashbackStatisticWidget', () {
    testWidgets('renders empty when cashback is null', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: CashbackStatisticWidget(
          cashback: null,
          onPressed: () {},
        ),
      ));

      expect(find.byType(SizedBox), findsOneWidget);
    });

    testWidgets('renders content when cashback is not null', (WidgetTester tester) async {
      final CurrentCashbackInfoEntity cashback = CurrentCashbackInfoEntity(
        month: 1,
        amount: 1000,
        limit: 2000,
      );

      bool isOnPress = false;

      await tester.pumpWidget(MaterialApp(
        home: CashbackStatisticWidget(
          cashback: cashback,
          onPressed: () {
            isOnPress = true;
          },
        ),
      ));

      expect(find.text(EvoStrings.transactionHistoryCashbackTitle), findsOneWidget);
      expect(find.text('Kỳ tháng 1:'), findsOneWidget);
      expect(find.text('1,000đ/2,000đ'), findsOneWidget);
      expect(find.byType(EmiCustomProgressbarWidget), findsOneWidget);

      verify(() => mockCommonImageProvider.asset(
            EvoImages.icSmallRightEmi,
            height: 16,
            width: 16,
          )).called(1);

      final Finder inkWell = find.byType(InkWell);
      expect(inkWell, findsOneWidget);
      await tester.tap(inkWell);
      await tester.pumpAndSettle();
      expect(isOnPress, true);
    });

    testWidgets('cannot show progress bar', (WidgetTester tester) async {
      final CurrentCashbackInfoEntity cashback = CurrentCashbackInfoEntity(
        month: 1,
      );

      await tester.pumpWidget(MaterialApp(
        home: CashbackStatisticWidget(
          cashback: cashback,
          onPressed: () {},
        ),
      ));

      expect(find.byType(EmiCustomProgressbarWidget), findsNothing);
    });
  });
}
