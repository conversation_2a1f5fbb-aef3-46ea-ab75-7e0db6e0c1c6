// ignore_for_file: depend_on_referenced_packages

import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/url_launcher_uri_wrapper.dart';
import 'package:flutter_common_package/common_package/url_launcher.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';
import 'package:url_launcher_platform_interface/url_launcher_platform_interface.dart';

/// Ref link https://pub.dev/packages/plugin_platform_interface#mocking-or-faking-platform-interfaces
class MockUrlLauncher extends Mock with MockPlatformInterfaceMixin implements UrlLauncherPlatform {}

void main() {
  final MockUrlLauncher mockUrlLauncher = MockUrlLauncher();
  final Uri uri = Uri(path: 'test');
  const LaunchOptions options = LaunchOptions(mode: PreferredLaunchMode.externalApplication);

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getIt.registerLazySingleton<UrlLauncherWrapper>(() => UrlLauncherWrapper());

    UrlLauncherPlatform.instance = mockUrlLauncher;
  });

  test('Test url_launcher.canLaunch is called', () {
    when(() => mockUrlLauncher.canLaunch(any())).thenAnswer((_) async {
      return true;
    });

    urlLauncherWrapper.canLaunchUrl(uri);

    expect(verify(() => mockUrlLauncher.canLaunch(captureAny())).captured.single, uri.toString());
  });

  test('Test url_launcher.launchUrl is called', () {
    registerFallbackValue(options);
    when(() => mockUrlLauncher.launchUrl(any(), any())).thenAnswer((_) async {
      return true;
    });

    urlLauncherWrapper.launchUrl(uri, mode: LaunchMode.externalApplication);

    expect(
      verify(() => mockUrlLauncher.launchUrl(captureAny(), captureAny())).captured,
      <dynamic>[
        isA<String>().having((String url) => url, 'test url', uri.toString()),
        isA<LaunchOptions>().having(
          (LaunchOptions p0) => p0.mode,
          'test LaunchOptions',
          options.mode,
        ),
      ],
    );
  });
}
