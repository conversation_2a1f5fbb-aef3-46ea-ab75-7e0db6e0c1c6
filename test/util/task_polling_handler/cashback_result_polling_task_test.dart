import 'package:evoapp/data/repository/cashback_repo.dart';
import 'package:evoapp/data/response/cashback_result_entity.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/task_polling_handler/cashback_result_polling_task.dart';
import 'package:evoapp/util/task_polling_handler/polling_task.dart';
import 'package:evoapp/util/task_polling_handler/task_polling_handler.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../flutter_test_config.dart';

class MockCashbackRepo extends Mock implements CashbackRepo {}

class MockEvoDialogHelper extends Mock implements EvoDialogHelper {}

class MockDeleteTaskCallback extends Mock {
  void call(PollingTask task);
}

void main() {
  late CashBackResultPollingTask task;
  late MockCashbackRepo mockCashbackRepo;
  late MockDeleteTaskCallback mockDeleteTaskCallback;

  setUpAll(() {
    registerFallbackValue(EvoDialogId.cashbackResultBottomSheet);
    registerFallbackValue(BoxFit.cover);
  });

  setUp(() {
    mockCashbackRepo = MockCashbackRepo();
    EvoDialogHelper.setInstanceForTesting(MockEvoDialogHelper());
    mockDeleteTaskCallback = MockDeleteTaskCallback();

    // Register mocks with GetIt
    getIt.registerSingleton<CashbackRepo>(mockCashbackRepo);
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterTextStyle();
    getItRegisterColor();
    getItRegisterButtonStyle();
    setupMockImageProvider();

    when(() => getIt<EvoUtilFunction>().evoFormatCurrency(
          any(),
          currencySymbol: any(named: 'currencySymbol'),
        )).thenAnswer((_) => 'fakeFormatCurrency');

    task = CashBackResultPollingTask(
      id: 'transaction123',
      type: PollingTaskType.cashBackResult,
      deleteTaskCallBack: mockDeleteTaskCallback.call,
      createdTime: DateTime.now(),
    );
  });

  tearDown(() {
    getIt.reset();
    EvoDialogHelper.resetToOriginalInstance();
  });

  test('initial values are set correctly', () {
    expect(task.intervalInSeconds, 5);
    expect(task.maxTaskPollingHourSinceCreated, 24);
    expect(task.transactionId, 'transaction123');
  });

  group('callApi', () {
    test('should handle success status correctly', () async {
      // Arrange
      when(() => mockCashbackRepo.getCashbackResult(
            transactionId: any(named: 'transactionId'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => CashBackResultEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
            'data': <String, dynamic>{
              'status': 'success',
              'amount': 10000,
              'merchant_name': 'MWG'
            }
          })));

      when(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: any(named: 'title'),
            titleTextStyle: any(named: 'titleTextStyle'),
            dialogId: any(named: 'dialogId'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            textPositive: any(named: 'textPositive'),
            titleTextAlign: any(named: 'titleTextAlign'),
            contentSpacing: any(named: 'contentSpacing'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
          )).thenAnswer((_) async {});

      // Start the task to initialize the timer
      task.start();
      await Future<void>.delayed(Duration(milliseconds: 50));
      // Assert
      verify(() => mockCashbackRepo.getCashbackResult(
            transactionId: 'transaction123',
            mockConfig: any(named: 'mockConfig'),
          )).called(1);

      // Verify dialog is shown
      verify(() => EvoDialogHelper().showDialogBottomSheet(
            header: any(named: 'header'),
            title: any(named: 'title'),
            titleTextStyle: any(named: 'titleTextStyle'),
            dialogId: EvoDialogId.cashbackResultBottomSheet,
            isShowButtonClose: true,
            textPositive: any(named: 'textPositive'),
            titleTextAlign: any(named: 'titleTextAlign'),
            contentSpacing: any(named: 'contentSpacing'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);

      // Clean up
      task.stop();
    });

    test('should handle failed status correctly', () async {
      // Arrange
      when(() => mockCashbackRepo.getCashbackResult(
            transactionId: any(named: 'transactionId'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => CashBackResultEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
            'data': <String, dynamic>{
              'id': '0000',
              'status': 'failed',
            }
          })));

      // Act
      await task.callApi();

      // Assert
      verify(() => mockCashbackRepo.getCashbackResult(
            transactionId: 'transaction123',
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
      verify(() => mockDeleteTaskCallback(task)).called(1);
    });

    test('should handle processing status correctly', () async {
      // Arrange
      when(() => mockCashbackRepo.getCashbackResult(
            transactionId: any(named: 'transactionId'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => CashBackResultEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
            'data': <String, dynamic>{
              'id': '0000',
              'status': 'processing',
            }
          })));

      // Act
      await task.callApi();

      // Assert
      verify(() => mockCashbackRepo.getCashbackResult(
            transactionId: 'transaction123',
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
      verifyNever(() => mockDeleteTaskCallback(task));
    });

    test('should handle NOT_FOUND status code correctly', () async {
      // Arrange
      when(() => mockCashbackRepo.getCashbackResult(
                transactionId: any(named: 'transactionId'),
                mockConfig: any(named: 'mockConfig'),
              ))
          .thenAnswer((_) async => CashBackResultEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.NOT_FOUND, response: <String, dynamic>{})));

      // Act
      await task.callApi();

      // Assert
      verify(() => mockCashbackRepo.getCashbackResult(
            transactionId: 'transaction123',
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
      verify(() => mockDeleteTaskCallback(task)).called(1);
    });

    test('should delete task if created more than 24 hours ago', () async {
      // Arrange
      task = CashBackResultPollingTask(
        id: 'transaction123',
        type: PollingTaskType.cashBackResult,
        deleteTaskCallBack: mockDeleteTaskCallback.call,
        createdTime: DateTime.now().subtract(Duration(hours: 25)),
      );

      when(() => mockCashbackRepo.getCashbackResult(
            transactionId: any(named: 'transactionId'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => CashBackResultEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
            'data': <String, dynamic>{
              'id': '0000',
              'status': 'processing',
            }
          })));

      // Act
      await task.callApi();

      // Assert
      verify(() => mockCashbackRepo.getCashbackResult(
            transactionId: 'transaction123',
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
      verify(() => mockDeleteTaskCallback(task)).called(1);
    });
  });
}

class MockOnTaskSuccessCallback extends Mock {
  void call(dynamic data);
}
