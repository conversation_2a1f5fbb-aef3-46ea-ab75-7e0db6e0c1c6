// ignore_for_file: depend_on_referenced_packages

import 'package:flutter_test/flutter_test.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class FakePathProviderPlatform extends Fake
    with MockPlatformInterfaceMixin
    implements PathProviderPlatform {
  static const String kTemporaryPath = 'temporaryPath';
  static const String kApplicationSupportPath = 'applicationSupportPath';
  static const String kDownloadsPath = 'downloadsPath';
  static const String kLibraryPath = 'libraryPath';
  static const String kApplicationDocumentsPath = 'applicationDocumentsPath';
  static const String kExternalCachePath = 'externalCachePath';
  static const String kExternalStoragePath = 'externalStoragePath';

  final String? externalStoragePath;

  FakePathProviderPlatform({
    this.externalStoragePath = kExternalStoragePath,
  });

  @override
  Future<String?> getTemporaryPath() async {
    return kTemporaryPath;
  }

  @override
  Future<String?> getApplicationSupportPath() async {
    return kApplicationSupportPath;
  }

  @override
  Future<String?> getLibraryPath() async {
    return kLibraryPath;
  }

  @override
  Future<String?> getApplicationDocumentsPath() async {
    return kApplicationDocumentsPath;
  }

  @override
  Future<String?> getExternalStoragePath() async {
    return externalStoragePath;
  }

  @override
  Future<List<String>?> getExternalCachePaths() async {
    return <String>[kExternalCachePath];
  }

  @override
  Future<List<String>?> getExternalStoragePaths({
    StorageDirectory? type,
  }) async {
    return <String>[externalStoragePath ?? kExternalStoragePath];
  }

  @override
  Future<String?> getDownloadsPath() async {
    return kDownloadsPath;
  }
}
