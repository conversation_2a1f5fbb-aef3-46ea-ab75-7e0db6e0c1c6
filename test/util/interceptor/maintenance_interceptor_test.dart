import 'package:dio/dio.dart';
import 'package:evoapp/data/response/maintenance_info_entity.dart';
import 'package:evoapp/feature/maintenance/maintenance_handler.dart';
import 'package:evoapp/util/interceptor/maintenance_interceptor.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockMaintenanceHandler extends Mock implements MaintenanceHandler {}

class MockResponseInterceptorHandler extends Mock implements ResponseInterceptorHandler {}

void main() {
  late MaintenanceInterceptor interceptor;
  late MockMaintenanceHandler mockMaintenanceHandler;
  late MockResponseInterceptorHandler mockHandler;

  setUp(() {
    mockMaintenanceHandler = MockMaintenanceHandler();
    mockHandler = MockResponseInterceptorHandler();
    interceptor = MaintenanceInterceptor(mockMaintenanceHandler);
  });

  group('MaintenanceInterceptor Tests', () {
    setUpAll(() {
      registerFallbackValue(MaintenanceInfoEntity());
    });

    test('should emit maintenance info when response has maintenance verdict', () {
      // Arrange
      final Response<dynamic> response = Response<dynamic>(
        data: <String, dynamic>{
          'verdict': 'maintenance_mode',
          'data': <String, dynamic>{
            'start': '2025-03-24T15:00:05+07:00',
            'end': '2025-03-24T15:00:05+07:00',
          },
        },
        statusCode: CommonHttpClient.SUCCESS,
        requestOptions: RequestOptions(),
      );

      // Act
      interceptor.onResponse(response, mockHandler);

      // Assert
      verify(() => mockMaintenanceHandler.emitMaintenance(any())).called(1);
      verifyNever(() => mockHandler.next(response));
    });

    test('should not emit maintenance info when response has different verdict', () {
      // Arrange
      final Response<dynamic> response = Response<dynamic>(
        data: <String, dynamic>{
          'verdict': 'success',
          'data': <String, dynamic>{},
        },
        statusCode: CommonHttpClient.SUCCESS,
        requestOptions: RequestOptions(),
      );

      // Act
      interceptor.onResponse(response, mockHandler);

      // Assert
      verifyNever(() => mockMaintenanceHandler.emitMaintenance(any()));
      verify(() => mockHandler.next(response)).called(1);
    });

    test('should not emit maintenance info when status code is not success', () {
      // Arrange
      final Response<dynamic> response = Response<dynamic>(
        data: <String, dynamic>{
          'verdict': 'maintenance_mode',
          'data': <String, dynamic>{},
        },
        statusCode: 400,
        requestOptions: RequestOptions(),
      );

      // Act
      interceptor.onResponse(response, mockHandler);

      // Assert
      verifyNever(() => mockMaintenanceHandler.emitMaintenance(any()));
      verify(() => mockHandler.next(response)).called(1);
    });

    test('should handle null data in response', () {
      // Arrange
      final Response<dynamic> response = Response<dynamic>(
        statusCode: CommonHttpClient.SUCCESS,
        requestOptions: RequestOptions(),
      );

      // Act
      interceptor.onResponse(response, mockHandler);

      // Assert
      verifyNever(() => mockMaintenanceHandler.emitMaintenance(any()));
      verify(() => mockHandler.next(response)).called(1);
    });

    test('should handle null verdict in response', () {
      // Arrange
      final Response<dynamic> response = Response<dynamic>(
        data: <String, dynamic>{
          'data': <String, dynamic>{},
        },
        statusCode: CommonHttpClient.SUCCESS,
        requestOptions: RequestOptions(),
      );

      // Act
      interceptor.onResponse(response, mockHandler);

      // Assert
      verifyNever(() => mockMaintenanceHandler.emitMaintenance(any()));
      verify(() => mockHandler.next(response)).called(1);
    });

    test('should handle malformed data in response', () {
      // Arrange
      final Response<dynamic> response = Response<dynamic>(
        data: <String, dynamic>{
          'verdict': 'maintenance_mode',
          'data': 'not a map',
        },
        statusCode: CommonHttpClient.SUCCESS,
        requestOptions: RequestOptions(),
      );

      // Act
      interceptor.onResponse(response, mockHandler);

      // Assert
      verifyNever(() => mockMaintenanceHandler.emitMaintenance(any()));
      verify(() => mockHandler.next(response)).called(1);
    });

    test('should properly parse maintenance info from response', () {
      // Arrange
      final Response<dynamic> response = Response<dynamic>(
        data: <String, dynamic>{
          'verdict': 'maintenance_mode',
          'data': <String, dynamic>{
            'start': '2025-03-24T15:00:05+07:00',
            'end': '2025-03-24T15:00:05+07:00',
          },
        },
        statusCode: CommonHttpClient.SUCCESS,
        requestOptions: RequestOptions(),
      );

      // Act
      interceptor.onResponse(response, mockHandler);

      // Assert

      final List<dynamic> captureData =
          verify(() => mockMaintenanceHandler.emitMaintenance(captureAny())).captured;

      final MaintenanceInfoEntity captured = captureData[0];
      expect(captured.start, equals('2025-03-24T15:00:05+07:00'));
      expect(captured.end, equals('2025-03-24T15:00:05+07:00'));
    });
  });
}
