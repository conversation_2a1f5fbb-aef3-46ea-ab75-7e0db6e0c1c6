import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/ui_utils/evo_ui_utils.dart';
import 'package:evoapp/widget/hud_loading/widgets/hub_loading.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../feature/splash_screen/splash_cubit_test.dart';
import '../flutter_test_config.dart';

class MockFeatureToggle extends Mock implements FeatureToggle {}

class MockHudLoading extends Mock implements HudLoading {}

void main() {
  late EvoFlutterWrapper mockEvoFlutterWrapper;
  late FlutterSecureStorage secureStorage;

  setUp(() {
    HudLoading.setInstanceForTesting(MockHudLoading());
  });

  tearDown(() {
    /// reset local data after each test
    secureStorage.deleteAll();
    HudLoading.resetToOriginalInstance();
  });

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    secureStorage = testFlutterSecureStorageExecutable();

    getIt.registerLazySingleton<EvoFlutterWrapper>(() => MockEvoPlatformWrapper());
    mockEvoFlutterWrapper = getIt.get<EvoFlutterWrapper>();
  });

  tearDown(() {
    /// reset local data after each test
    secureStorage.deleteAll();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('test getScrollPhysics() function', () {
    test('test getScrollPhysics on iOS OS', () {
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(true);
      expect(EvoUiUtils().getScrollPhysics(), const BouncingScrollPhysics());
    });

    test('test getScrollPhysics on Android', () {
      when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(false);
      expect(EvoUiUtils().getScrollPhysics(), const ClampingScrollPhysics());
    });
  });

  group('test calculateVerticalSpace function', () {
    const double mockScreenWidth = 375;
    const double mockScreenHeight = 812;

    testWidgets('heightPercentage < 0', (WidgetTester tester) async {
      const double heightPercentage = -1;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              expect(
                () => EvoUiUtils().calculateVerticalSpace(
                  context: context,
                  heightPercentage: heightPercentage,
                ),
                throwsAssertionError,
              );
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('0 < heightPercentage < 1', (WidgetTester tester) async {
      const double heightPercentage = 0.1;
      const double expectedHeight = 81.2;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              final double calculatedSpace = EvoUiUtils().calculateVerticalSpace(
                context: context,
                heightPercentage: heightPercentage,
              );
              expect(
                calculatedSpace,
                expectedHeight,
              );
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('heightPercentage == 1', (WidgetTester tester) async {
      const double heightPercentage = 1;
      const double expectedHeight = 812;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              final double calculatedSpace = EvoUiUtils().calculateVerticalSpace(
                context: context,
                heightPercentage: heightPercentage,
              );
              expect(
                calculatedSpace,
                expectedHeight,
              );
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('heightPercentage > 1', (WidgetTester tester) async {
      const double heightPercentage = 1.1;
      const double expectedHeight = 893.2;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              final double calculatedSpace = EvoUiUtils().calculateVerticalSpace(
                context: context,
                heightPercentage: heightPercentage,
              );
              expect(
                calculatedSpace,
                expectedHeight,
              );
              return Container();
            },
          ),
        ),
      );
    });
  });

  group('test calculateHorizontalSpace function', () {
    const double mockScreenWidth = 375;
    const double mockScreenHeight = 812;

    testWidgets('heightPercentage < 0', (WidgetTester tester) async {
      const double widthPercentage = -1;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              expect(
                () => EvoUiUtils().calculateHorizontalSpace(
                  context: context,
                  widthPercentage: widthPercentage,
                ),
                throwsAssertionError,
              );
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('0 < heightPercentage < 1', (WidgetTester tester) async {
      const double widthPercentage = 0.1;
      const double expectedWidth = 37.5;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              final double calculatedSpace = EvoUiUtils().calculateHorizontalSpace(
                context: context,
                widthPercentage: widthPercentage,
              );
              expect(
                calculatedSpace,
                expectedWidth,
              );
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('heightPercentage == 1', (WidgetTester tester) async {
      const double widthPercentage = 1;
      const double expectedWidth = 375;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              final double calculatedSpace = EvoUiUtils().calculateHorizontalSpace(
                context: context,
                widthPercentage: widthPercentage,
              );
              expect(
                calculatedSpace,
                expectedWidth,
              );
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('heightPercentage > 1', (WidgetTester tester) async {
      const double widthPercentage = 1.25;
      const double expectedWidth = 468.75;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              final double calculatedSpace = EvoUiUtils().calculateHorizontalSpace(
                context: context,
                widthPercentage: widthPercentage,
              );
              expect(
                calculatedSpace,
                expectedWidth,
              );
              return Container();
            },
          ),
        ),
      );
    });
  });

  group('calculateVerticalOffsetRatio', () {
    testWidgets('should return null when the offset is null', (WidgetTester tester) async {
      tester.view.physicalSize = const Size(50, 600);
      tester.view.devicePixelRatio = 1.0;

      /// Resets the screen to its original size after the test end
      addTearDown(() => tester.view.resetPhysicalSize());

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (BuildContext context) {
              final double? result = EvoUiUtils().calculateVerticalOffsetRatio(context, null);
              expect(result, isNull);
              return const Placeholder();
            },
          ),
        ),
      );
    });

    testWidgets('should calculate the vertical offset ratio correctly',
        (WidgetTester tester) async {
      tester.view.physicalSize = const Size(50, 600);
      tester.view.devicePixelRatio = 1.0;

      /// Resets the screen to its original size after the test end
      addTearDown(() => tester.view.resetPhysicalSize());

      const Offset offset = Offset(50, 540);

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (BuildContext context) {
              final double? result = EvoUiUtils().calculateVerticalOffsetRatio(context, offset);
              expect(result, equals(0.1));
              return const Placeholder();
            },
          ),
        ),
      );
    });
  });

  group('test showHudLoading', () {
    test('should show loading', () async {
      when(() => HudLoading().show(
            overlayWidget: any(named: 'overlayWidget'),
          )).thenAnswer((_) => Future<void>.value());

      await EvoUiUtils().showHudLoading();

      verify(() => HudLoading().show(
            overlayWidget: any(named: 'overlayWidget'),
          )).called(1);
    });
  });

  group('test hideHudLoading', () {
    test('should dismiss loading', () async {
      when(() => HudLoading().dismiss()).thenAnswer((_) => Future<void>.value());

      await EvoUiUtils().hideHudLoading();

      verify(() => HudLoading().dismiss()).called(1);
    });
  });

  group('test hideKeyboard', () {
    testWidgets('hideKeyboard() does nothing when no widget is focused',
        (WidgetTester tester) async {
      FocusManager.instance.primaryFocus?.unfocus();
      expect(FocusManager.instance.primaryFocus, isNull);
      EvoUiUtils().hideKeyboard();
      expect(FocusManager.instance.primaryFocus, isNull);
    });
  });
}
