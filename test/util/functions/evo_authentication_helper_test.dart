import 'package:evoapp/data/response/card_status_entity.dart';
import 'package:evoapp/data/response/credit_limit_widget_config_entity.dart';
import 'package:evoapp/data/response/cta_widget_config_entity.dart';
import 'package:evoapp/data/response/user_information_entity.dart';
import 'package:evoapp/feature/biometric/model/biometric_ui_model.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/task_polling_handler/task_polling_handler.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../feature/splash_screen/splash_cubit_test.dart';
import '../flutter_test_config.dart';

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}
class MockFeatureToggle extends Mock implements FeatureToggle {}


void main() {
  late FlutterSecureStorage secureStorage;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
    getIt.registerLazySingleton<AppState>(() => AppState());
    getIt.registerLazySingleton<CommonSharedPreferencesHelper>(() => MockCommonSharedPreferencesHelper());
    secureStorage = testFlutterSecureStorageExecutable();
  });

  tearDown(() {
    /// reset local data after each test
    secureStorage.deleteAll();
  });

  tearDownAll(() {
    getIt.reset();
  });

  test('test clearUserInfoAppState function', () {
    final AppState appState = getIt.get<AppState>();

    /// Set login status to AppState
    appState.isUserLogIn = true;
    appState.userInfo.value = const UserInformationEntity();
    appState.cardStatus = CardStatusEntity(
      creditLimitWidgetConfig: CreditLimitWidgetConfigEntity(
        display: false,
        creditLimit: 75000000,
        creditStatus: 'waiting_for_approval',
      ),
      ctaWidgetConfig: CtaWidgetConfigEntity(
        display: false,
        linkedCard: false,
        activatedCard: false,
      ),
      display: false,
    );

    expect(appState.isUserLogIn, isTrue);
    expect(appState.userInfo.value, isA<UserInformationEntity>());
    expect(appState.cardStatus, isA<CardStatusEntity>());

    /// clear user info in App State
    EvoAuthenticationHelper().clearUserInfoAppState();

    expect(appState.isUserLogIn, false);
    expect(appState.userInfo.value, null);
    expect(appState.cardStatus, null);
  });

  group('test getBiometricUIModel() function', () {
    test('test model is TsBiometricType.Face', () {
      final BiometricTypeUIModel model =
      EvoAuthenticationHelper().getBiometricUIModel(TsBiometricType.face);
      expect(model.biometricTypeName, EvoStrings.faceText);
      expect(model.iconPath, EvoImages.icFaceId);
      expect(model.iconSettingPath, EvoImages.icSettingFaceId);
    });

    test('test model is TsBiometricType.Finger', () {
      final BiometricTypeUIModel model =
      EvoAuthenticationHelper().getBiometricUIModel(TsBiometricType.finger);
      expect(model.biometricTypeName, EvoStrings.fingerText);
      expect(model.iconPath, EvoImages.icFingerId);
      expect(model.iconSettingPath, EvoImages.icSettingFingerId);
    });

    test('test model is BiometricTypeUIModel.faceAndFinger on android OS', () {
      final BiometricTypeUIModel model =
      EvoAuthenticationHelper().getBiometricUIModel(TsBiometricType.androidBio);
      expect(model.biometricTypeName, EvoStrings.faceFingerText);
      expect(model.iconPath, EvoImages.icFaceFingerId);
      expect(model.iconSettingPath, EvoImages.icSettingFaceFingerId);
    });

    test('test model is BiometricTypeUIModel.faceAndFinger on unknown', () {
      final BiometricTypeUIModel model =
      EvoAuthenticationHelper().getBiometricUIModel(TsBiometricType.unknown);
      expect(model.biometricTypeName, EvoStrings.faceFingerText);
      expect(model.iconPath, EvoImages.icFaceFingerId);
      expect(model.iconSettingPath, EvoImages.icSettingFaceFingerId);
    });
  });

  group('test function clearAllUserData', () {
    late MockOneSignal mockOneSignal;
    late MockCommonUtilFunction mockCommonUtilFunction;
    late EvoLocalStorageHelper mockEvoLocalStorageHelper;

    setUpAll(() {
      /// Only use mock [CommonUtilFunction] in this test group
      if (getIt.isRegistered<CommonUtilFunction>()) {
        getIt.unregister<CommonUtilFunction>();
      }
      mockCommonUtilFunction = MockCommonUtilFunction();
      getIt.registerLazySingleton<CommonUtilFunction>(() => mockCommonUtilFunction);

      mockOneSignal = testOneSignalExecutable();

      mockEvoLocalStorageHelper = MockEvoLocalStorageHelper();
      getIt.registerLazySingleton<EvoLocalStorageHelper>(() => mockEvoLocalStorageHelper);
      when(()=> getIt<CommonSharedPreferencesHelper>().removeKey(any())).thenAnswer((_) async => true);
    });

    tearDownAll(() {
      getIt.unregister<EvoLocalStorageHelper>();
      reset(mockOneSignal);

      /// Only use mock [CommonUtilFunction] in this test group, so we need to reset it for other groups
      getIt.unregister<CommonUtilFunction>();
      getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
    });

    test('should clear all user data', () async {
      // Setup
      final MockOneSignal mockOneSignal = testOneSignalExecutable();

      // Configure the mock objects
      when(() => mockEvoLocalStorageHelper.clearAllUserData())
          .thenAnswer((_) => Future<void>.value());
      when(() => mockCommonUtilFunction.clearDataOnTokenInvalid(
          clearAllNotifications: any(named: 'clearAllNotifications')))
          .thenAnswer((_) => Future<void>.value());

      // Call the function
      await EvoAuthenticationHelper().clearAllUserData(
        oneSignal: mockOneSignal,
      );

      // Verify the interactions
      verify(() => mockCommonUtilFunction.clearDataOnTokenInvalid(clearAllNotifications: true))
          .called(1);
      verify(() => mockOneSignal.removeExternalUserId()).called(1);
      verify(() => mockEvoLocalStorageHelper.clearAllUserData()).called(1);
      verify(()=> getIt<CommonSharedPreferencesHelper>().removeKey(TaskPollingHandler.pollingTasksStorageKey)).called(1);
    });
  });
}
