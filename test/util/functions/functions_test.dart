import 'package:evoapp/data/response/payment_info_entity.dart';
import 'package:evoapp/data/response/payment_method_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/logging/evo_logging_event.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/navigator/evo_router_navigator.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../feature/biometric/biometric_token_module/biometric_change_mixin_test.dart';
import '../../feature/splash_screen/splash_cubit_test.dart';
import '../flutter_test_config.dart';

class MockEvoNavigator extends Mock implements EvoRouterNavigator {}

class MockFeatureToggle extends Mock implements FeatureToggle {}

class MockEditableTextState extends Mock implements EditableTextState {
  bool hasCalledPasteText = false;

  @override
  Future<void> pasteText(SelectionChangedCause cause) async {
    hasCalledPasteText = true;
  }

  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.info}) {
    return super.toString();
  }
}

void main() {
  late EvoFlutterWrapper mockEvoFlutterWrapper;
  late LoggingRepo mockLoggingRepo;
  late FlutterSecureStorage secureStorage;
  late MockDeviceInfoPlugin deviceInfoPlugin;
  late FeatureToggle mockFeatureToggle;

  const String deviceId = 'device_id';
  const Map<String, dynamic> iosUtsnameMap = <String, dynamic>{
    'release': 'release',
    'version': 'version',
    'machine': 'machine',
    'sysname': 'sysname',
    'nodename': 'nodename',
  };
  const Map<String, dynamic> iosDeviceInfoMap = <String, dynamic>{
    'name': 'name',
    'model': 'model',
    'utsname': iosUtsnameMap,
    'systemName': 'systemName',
    'isPhysicalDevice': false,
    'systemVersion': 'systemVersion',
    'localizedModel': 'localizedModel',
    'identifierForVendor': deviceId,
    'isiOSAppOnMac': false,
    'modelName': 'model name'
  };

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
    getIt.registerLazySingleton<AppState>(() => AppState());

    getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
    mockLoggingRepo = getIt.get<LoggingRepo>();

    secureStorage = testFlutterSecureStorageExecutable();

    getIt.registerLazySingleton<EvoFlutterWrapper>(() => MockEvoPlatformWrapper());
    mockEvoFlutterWrapper = getIt.get<EvoFlutterWrapper>();

    deviceInfoPlugin = MockDeviceInfoPlugin();
    getIt.registerLazySingleton<DeviceInfoPlugin>(() => deviceInfoPlugin);

    getIt.registerLazySingleton<FeatureToggle>(() => MockFeatureToggle());
    mockFeatureToggle = getIt.get<FeatureToggle>();
    when(() => mockFeatureToggle.enableEventTrackingFeature).thenReturn(true);
  });

  tearDown(() {
    /// reset local data after each test
    secureStorage.deleteAll();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('verify detectReinstallAppOnIOSDevice()', () {
    late MockEvoLocalStorageHelper mockEvoLocalStorageHelper;

    setUpAll(() {
      mockEvoLocalStorageHelper = MockEvoLocalStorageHelper();
      getIt.registerLazySingleton<EvoLocalStorageHelper>(() => mockEvoLocalStorageHelper);

      when(() => deviceInfoPlugin.iosInfo).thenAnswer((_) {
        return Future<IosDeviceInfo>.value(IosDeviceInfo.fromMap(iosDeviceInfoMap));
      });
    });

    setUp(() {
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(false);
    });

    tearDown(() {
      reset(mockEvoFlutterWrapper);
    });

    tearDownAll(() {
      getIt.unregister<EvoLocalStorageHelper>();
    });

    test('return detectReinstallAppOnIOSDevice() is false on Android', () async {
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);

      final bool isReInstallApp = await evoUtilFunction.detectReinstallAppOnIOSDevice();

      expect(isReInstallApp, false);
    });

    test('return detectReinstallAppOnIOSDevice() is true', () async {
      when(() => mockEvoLocalStorageHelper.getDeviceId())
          .thenAnswer((_) => Future<String?>.value());

      final bool isReInstallApp = await evoUtilFunction.detectReinstallAppOnIOSDevice();

      expect(isReInstallApp, true);
    });

    test('return detectReinstallAppOnIOSDevice() is false', () async {
      when(() => mockEvoLocalStorageHelper.getDeviceId())
          .thenAnswer((_) => Future<String>.value(deviceId));

      final bool isReInstallApp = await evoUtilFunction.detectReinstallAppOnIOSDevice();

      expect(isReInstallApp, false);
    });
  });

  group('test getNewDeviceId() function', () {
    late EvoLocalStorageHelper evoLocalStorageHelper;

    setUpAll(() {
      evoLocalStorageHelper = testEvoSecureStorageHelperExecutable(secureStorage: secureStorage);
    });

    tearDownAll(() {
      getIt.unregister<EvoLocalStorageHelper>();
      reset(mockEvoFlutterWrapper);
    });

    test('verify currentDeviceId and call getNewDeviceId() function on Android', () async {
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);

      final String? result = await evoUtilFunction.getNewDeviceId();

      expect(result, null);
    });

    test('verify currentDeviceId and call getNewDeviceId() function on iOS', () async {
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(false);

      ///verify deviceId is null
      final String? currentDeviceId = await evoLocalStorageHelper.getDeviceId();

      expect(currentDeviceId, null);

      when(() => deviceInfoPlugin.iosInfo).thenAnswer((_) {
        return Future<IosDeviceInfo>.value(IosDeviceInfo.fromMap(iosDeviceInfoMap));
      });

      await evoUtilFunction.setNewDeviceId();

      expect((await commonUtilFunction.getIosInfo()).identifierForVendor, deviceId);

      ///verify newDeviceId
      final String? newDeviceId = await evoUtilFunction.getNewDeviceId();

      expect(newDeviceId, deviceId);
    });
  });

  group('test encode query parameters', () {
    test('test encode query parameter with Map is empty', () {
      final Map<String, String> params = <String, String>{};

      final String? encodeParam = evoUtilFunction.encodeQueryParameters(params);
      const String expectedString = '';
      expect(encodeParam, expectedString);
    });

    test('test encode query parameter with one field is Empty', () {
      final Map<String, String> params = <String, String>{'subject': ''};

      final String? encodeParam = evoUtilFunction.encodeQueryParameters(params);
      const String expectedString = 'subject=';
      expect(encodeParam, expectedString);
    });

    test('test encode query parameter with one field has content', () {
      final Map<String, String> params = <String, String>{'subject': 'Gop y'};

      final String? encodeParam = evoUtilFunction.encodeQueryParameters(params);
      const String expectedString = 'subject=Gop%20y';
      expect(encodeParam, expectedString);
    });

    test('test encode query parameter with two fields have content', () {
      final Map<String, String> params = <String, String>{'subject': 'Gop y', 'body': 'Mo the Evo'};

      final String? encodeParam = evoUtilFunction.encodeQueryParameters(params);
      const String expectedString = 'subject=Gop%20y&body=Mo%20the%20Evo';
      expect(encodeParam, expectedString);
    });

    test('test encode query parameter with two fields and having one field is empty', () {
      final Map<String, String> params = <String, String>{'subject': 'Gop y', 'body': ''};

      final String? encodeParam = evoUtilFunction.encodeQueryParameters(params);
      const String expectedString = 'subject=Gop%20y&body=';
      expect(encodeParam, expectedString);
    });

    test('test encode query parameter with Vietnamese characters', () {
      final Map<String, String> params = <String, String>{'subject': 'Góp ý'};

      final String? encodeParam = evoUtilFunction.encodeQueryParameters(params);
      const String expectedString = 'subject=G%C3%B3p%20%C3%BD';
      expect(encodeParam, expectedString);
    });
  });

  group('test formatBirthday', () {
    test('test format birthday with birthday is null', () {
      final String? birthday = evoUtilFunction.formatBirthday(null);
      expect(null, birthday);
    });

    test('test format birthday with birthday has value', () {
      final String? birthday = evoUtilFunction.formatBirthday('1900-01-02 15:04:05.000');
      expect('02/01/1900', birthday);
    });

    test('test format birthday with birthday is wrong', () {
      when(() => mockLoggingRepo.logErrorEvent(
          errorType: any(named: 'errorType'),
          args: any(named: 'args'))).thenAnswer((_) => Future<void>.value());

      final String? birthday = evoUtilFunction.formatBirthday('1900/01-02 15:04:05+0700');
      expect(null, birthday);
      verify(() => mockLoggingRepo.logErrorEvent(
          errorType: EvoEventType.commonPlatformException.name,
          args: any(named: 'args'))).called(1);
    });
  });

  group('test get phone number from data paste clipboard', () {
    test('test phone number with blank', () {
      final String phoneNumber = evoUtilFunction.getPhoneFromPasteClipboard('************');
      expect('0901231234', phoneNumber);
    });

    test('test phone number with multi blank', () {
      final String phoneNumber = evoUtilFunction.getPhoneFromPasteClipboard('09 0 12 3 12 34');
      expect('0901231234', phoneNumber);
    });

    test('test phone number with char plus', () {
      final String phoneNumber = evoUtilFunction.getPhoneFromPasteClipboard('+09 0 123 12 34');
      expect('0901231234', phoneNumber);
    });

    test('test phone number with char plus and prefix national', () {
      final String phoneNumber = evoUtilFunction.getPhoneFromPasteClipboard('+849 0 123 12 34');
      expect('0901231234', phoneNumber);
    });

    test('test phone number without char plus and prefix national', () {
      final String phoneNumber = evoUtilFunction.getPhoneFromPasteClipboard('849 0 123 12 34');
      expect('0901231234', phoneNumber);
    });
  });

  group('test function eovFormatCurrency', () {
    test('currency == null', () {
      final String result = evoUtilFunction.evoFormatCurrency(null);
      expect(result, '-');
    });

    test('currency != null', () {
      final String result = evoUtilFunction.evoFormatCurrency(124);
      final String resultCurrencySymbol =
          evoUtilFunction.evoFormatCurrency(124, currencySymbol: 'đ');
      expect(result, '124');
      expect(resultCurrencySymbol, '124đ');
    });

    test('currency < 0', () {
      final String result = evoUtilFunction.evoFormatCurrency(-1);
      final String resultCurrencySymbol =
          evoUtilFunction.evoFormatCurrency(-1, currencySymbol: 'đ');
      expect(result, '-1');
      expect(resultCurrencySymbol, '-1đ');
    });

    test('currency = 0', () {
      final String result = evoUtilFunction.evoFormatCurrency(0);
      final String resultCurrencySymbol = evoUtilFunction.evoFormatCurrency(0, currencySymbol: 'đ');
      expect(result, '0');
      expect(resultCurrencySymbol, '0đ');
    });

    /// Test currency > 0
    test('currency > 1000', () {
      final String result = evoUtilFunction.evoFormatCurrency(1100);
      final String resultCurrencySymbol =
          evoUtilFunction.evoFormatCurrency(1100, currencySymbol: 'đ');
      expect(result, '1,100');
      expect(resultCurrencySymbol, '1,100đ');
    });

    test('currency > 1.000.000', () {
      final String result = evoUtilFunction.evoFormatCurrency(4460000);
      final String resultCurrencySymbol =
          evoUtilFunction.evoFormatCurrency(4460000, currencySymbol: 'đ');
      expect(result, '4,460,000');
      expect(resultCurrencySymbol, '4,460,000đ');
    });
    test('currency > 10.000.000', () {
      final String result = evoUtilFunction.evoFormatCurrency(71850000);
      final String resultCurrencySymbol =
          evoUtilFunction.evoFormatCurrency(71850000, currencySymbol: 'đ');
      expect(result, '71,850,000');
      expect(resultCurrencySymbol, '71,850,000đ');
    });
    test('currency > 100.000.000', () {
      final String result = evoUtilFunction.evoFormatCurrency(999444555);
      final String resultCurrencySymbol =
          evoUtilFunction.evoFormatCurrency(999444555, currencySymbol: 'đ');
      expect(result, '999,444,555');
      expect(resultCurrencySymbol, '999,444,555đ');
    });

    test('currency > 1.000.000.000', () {
      final String result = evoUtilFunction.evoFormatCurrency(1554678000);
      final String resultCurrencySymbol =
          evoUtilFunction.evoFormatCurrency(1554678000, currencySymbol: 'đ');
      expect(result, '1,554,678,000');
      expect(resultCurrencySymbol, '1,554,678,000đ');
    });
  });

  group('test parse payment amount from string', () {
    test(
      'parse valid value',
      () {
        const String amountStr = '1.000';

        final int? amount = evoUtilFunction.getAmountFromStr(amountStr);

        expect(amount, 1000);
      },
    );
    test(
      'parse invalid value',
      () {
        const String amountStr = '1a000';

        final int? amount = evoUtilFunction.getAmountFromStr(amountStr);

        expect(amount, null);
      },
    );

    test('Give number without suffix and currencySuffix, should return right amount', () {
      const String amountStr = '1.000';
      const String suffix = 'đ';
      final int? amount = evoUtilFunction.getAmountFromStr(amountStr, currencySuffix: suffix);
      expect(amount, 1000);
    });

    test('Give number with suffix and currencySuffix, should return right amount', () {
      const String amountStr = '1.000đ';
      const String suffix = 'đ';
      final int? amount = evoUtilFunction.getAmountFromStr(amountStr, currencySuffix: suffix);
      expect(amount, 1000);
    });
  });

  group('test getDefaultPaymentMethod method', () {
    test('test has payment method', () {
      final PaymentInfoEntity paymentInfoEntity = PaymentInfoEntity(
        paymentMethodId: 'card1',
        paymentMethods: <PaymentMethodEntity>[
          PaymentMethodEntity(id: 'card1'),
          PaymentMethodEntity(id: 'card2'),
        ],
      );

      expect(evoUtilFunction.getDefaultPaymentMethod(paymentInfoEntity), isNotNull);
      expect(evoUtilFunction.getDefaultPaymentMethod(paymentInfoEntity)?.id, 'card1');
    });
    test('test has not payment method because do not matching id', () {
      final PaymentInfoEntity paymentInfoEntity = PaymentInfoEntity(
        paymentMethodId: 'card3',
        paymentMethods: <PaymentMethodEntity>[
          PaymentMethodEntity(id: 'card1'),
          PaymentMethodEntity(id: 'card2'),
        ],
      );

      expect(evoUtilFunction.getDefaultPaymentMethod(paymentInfoEntity), null);
    });
    test('test has not payment method because paymentMethods is empty', () {
      final PaymentInfoEntity paymentInfoEntity = PaymentInfoEntity(
        paymentMethodId: 'card1',
        paymentMethods: <PaymentMethodEntity>[],
      );

      expect(evoUtilFunction.getDefaultPaymentMethod(paymentInfoEntity), null);
    });
    test('test has not payment method because paymentMethodId is null', () {
      final PaymentInfoEntity paymentInfoEntity = PaymentInfoEntity(
        // paymentMethodId: null,
        paymentMethods: <PaymentMethodEntity>[
          PaymentMethodEntity(id: 'card1'),
          PaymentMethodEntity(id: 'card2'),
        ],
      );
      expect(evoUtilFunction.getDefaultPaymentMethod(paymentInfoEntity), null);
    });
  });

  group('test cropStringWithMaxLength method', () {
    test('content length < maxLength', () {
      const String content = 'test';
      const int maxLength = 10;
      expect(evoUtilFunction.cropStringWithMaxLength(content, maxLength), content);
    });

    test('content length == maxLength', () {
      const String content = '0123456789';
      const int maxLength = 10;
      expect(evoUtilFunction.cropStringWithMaxLength(content, maxLength), content);
    });

    test('content length > maxLength', () {
      const String content = '0123456789test';
      const int maxLength = 10;
      const String matcher = '0123456789 ...';
      expect(evoUtilFunction.cropStringWithMaxLength(content, maxLength), matcher);
    });

    test('content length > maxLength, ellipsis == false', () {
      const String content = '0123456789test';
      const int maxLength = 10;
      const bool ellipsis = false;
      const String matcher = '0123456789';
      expect(
          evoUtilFunction.cropStringWithMaxLength(content, maxLength, ellipsis: ellipsis), matcher);
    });

    test('maxLength <= 0', () {
      const String content = 'test';
      const int maxLength = 0;
      expect(evoUtilFunction.cropStringWithMaxLength(content, maxLength), content);
    });
  });

  group('test validateMinMaxNationalId', () {
    test('length nationalId < 9', () {
      const String content = '124234';
      expect(evoUtilFunction.validateMinMaxLengthNationalId(content), false);
    });

    test('length nationalId > 12', () {
      const String content = '124234124234124234124234124234124234';
      expect(evoUtilFunction.validateMinMaxLengthNationalId(content), false);
    });

    test('length nationalId = 9', () {
      const String content = '124234234';
      expect(evoUtilFunction.validateMinMaxLengthNationalId(content), true);
    });
    test('length nationalId = 12', () {
      const String content = '124234234234';
      expect(evoUtilFunction.validateMinMaxLengthNationalId(content), true);
    });
  });

  group('test getNationalIdWithMaxLength', () {
    test('nationalId empty', () {
      const String nationalId = '';
      expect(evoUtilFunction.getNationalIdWithMaxLength(nationalId), nationalId);
    });

    test('nationalId length < maxLength', () {
      const String nationalId = '123456';
      expect(evoUtilFunction.getNationalIdWithMaxLength(nationalId), nationalId);
    });

    test('nationalId length == maxLength', () {
      const String nationalId = '123456789789';
      expect(evoUtilFunction.getNationalIdWithMaxLength(nationalId), nationalId);
    });

    test('nationalId length > maxLength', () {
      const String nationalId = '123456789123645675512412523423';
      expect(evoUtilFunction.getNationalIdWithMaxLength(nationalId), '123456789123');
    });
  });

  group('test convertStringToObscureText', () {
    test('convert empty to obscure text', () {
      const String input = '';
      expect(evoUtilFunction.convertStringToObscureText(input, '•').isEmpty, true);
    });

    test('convert correct string without obscure text', () {
      const String input = '123';
      expect(evoUtilFunction.convertStringToObscureText(input, '•') == '•••', true);
    });

    test('convert correct string with obscure text', () {
      const String input = '123';
      expect(evoUtilFunction.convertStringToObscureText(input, '-') == '---', true);
    });

    test('convert incorrect string with obscure text', () {
      const String input = '123';
      expect(evoUtilFunction.convertStringToObscureText(input, '-') == '•••', false);
    });
  });

  group('test setNewDeviceId() function', () {
    late EvoLocalStorageHelper evoLocalStorageHelper;

    setUpAll(() {
      evoLocalStorageHelper = testEvoSecureStorageHelperExecutable(secureStorage: secureStorage);
    });

    tearDownAll(() {
      getIt.unregister<EvoLocalStorageHelper>();
      reset(mockEvoFlutterWrapper);
    });

    test('verify currentDeviceId and call setNewDeviceId() function on Android', () async {
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);

      await evoUtilFunction.setNewDeviceId();

      ///verify newDeviceId
      final String? newDeviceId = await evoLocalStorageHelper.getDeviceId();

      expect(newDeviceId, null);
    });

    test('verify currentDeviceId and call setNewDeviceId() function on iOS', () async {
      const String deviceId = 'device_id';
      const Map<String, dynamic> iosUtsnameMap = <String, dynamic>{
        'release': 'release',
        'version': 'version',
        'machine': 'machine',
        'sysname': 'sysname',
        'nodename': 'nodename',
      };
      const Map<String, dynamic> iosDeviceInfoMap = <String, dynamic>{
        'name': 'name',
        'model': 'model',
        'utsname': iosUtsnameMap,
        'systemName': 'systemName',
        'isPhysicalDevice': false,
        'systemVersion': 'systemVersion',
        'localizedModel': 'localizedModel',
        'identifierForVendor': deviceId,
        'isiOSAppOnMac': false,
        'modelName': 'model name'
      };

      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(false);

      ///verify deviceId is null
      final String? currentDeviceId = await evoLocalStorageHelper.getDeviceId();

      expect(currentDeviceId, null);

      when(() => deviceInfoPlugin.iosInfo).thenAnswer((_) {
        return Future<IosDeviceInfo>.value(IosDeviceInfo.fromMap(iosDeviceInfoMap));
      });

      await evoUtilFunction.setNewDeviceId();

      expect((await commonUtilFunction.getIosInfo()).identifierForVendor, deviceId);

      ///verify newDeviceId
      final String? newDeviceId = await evoLocalStorageHelper.getDeviceId();

      expect(newDeviceId, deviceId);
    });
  });

  group('test deleteAllData() function', () {
    late EvoLocalStorageHelper evoLocalStorageHelper;

    setUpAll(() {
      evoLocalStorageHelper = testEvoSecureStorageHelperExecutable(secureStorage: secureStorage);
    });

    tearDownAll(() {
      getIt.unregister<EvoLocalStorageHelper>();
    });

    test('test function deleteAllData()', () async {
      const String deviceId = 'device_id';

      ///init device id
      await evoLocalStorageHelper.setDeviceId(deviceId);
      expect(await evoLocalStorageHelper.getDeviceId(), deviceId);

      await evoUtilFunction.deleteAllData();

      ///verify storage deleted
      expect(await evoLocalStorageHelper.getDeviceId(), null);
      expect(await evoLocalStorageHelper.getRefreshToken(), null);
      expect(await evoLocalStorageHelper.getAccessToken(), null);
    });
  });

  group('test validateMaxLengthPin() function', () {
    test('validate correct length', () {
      const String data = '123456';
      expect(evoUtilFunction.validateMaxLengthPin(data), isTrue);
    });

    test('validate incorrect, data isEmpty', () {
      const String data = '';
      expect(evoUtilFunction.validateMaxLengthPin(data), isFalse);
    });

    test('validate incorrect, length pin too short', () {
      const String data = '123';
      expect(evoUtilFunction.validateMaxLengthPin(data), isFalse);
    });

    test('validate incorrect, length pin too long', () {
      const String data = '12345653485734895';
      expect(evoUtilFunction.validateMaxLengthPin(data), isFalse);
    });
  });

  group('test handleBackOrGoHomeScreen() function', () {
    late MockContext mockNavigatorContext;

    setUpAll(() {
      getIt.registerLazySingleton<CommonNavigator>(() => MockEvoNavigator());
      mockNavigatorContext = MockContext();
      setUpMockGlobalKeyProvider(mockNavigatorContext);
    });

    tearDownAll(() {
      getIt.unregister<CommonNavigator>();
      getIt.unregister<GlobalKeyProvider>();
    });

    test('test can pop back', () {
      when(() => mockNavigatorContext.maybePop()).thenAnswer((_) => true);

      when(() => mockNavigatorContext.popBack()).thenAnswer((_) => true);

      evoUtilFunction.handleBackOrGoHomeScreen();

      verify(() => mockNavigatorContext.maybePop()).called(1);

      verify(() => mockNavigatorContext.popBack()).called(1);

      expect(evoUtilFunction.moveToMainScreen, isFalse);
    });

    test('test can not pop back and move to MainScreen', () async {
      when(() => mockNavigatorContext.maybePop()).thenAnswer((_) => false);

      when(() => mockNavigatorContext.popBack()).thenAnswer((_) => true);

      when(() => mockNavigatorContext.goNamed(any())).thenAnswer((_) => Future<void>.value());

      expect(evoUtilFunction.moveToMainScreen, isFalse);

      evoUtilFunction.handleBackOrGoHomeScreen();

      verify(() => mockNavigatorContext.maybePop()).called(1);

      verifyNever(() => mockNavigatorContext.popBack());

      expect(evoUtilFunction.moveToMainScreen, isTrue);
    });
  });

  group('verify getCurrentTime() method', () {
    test('test getCurrentTime() method', () {
      final DateTime currentTime = DateTime.now();
      final DateTime result = evoUtilFunction.getCurrentTime();
      const String dateFormat = 'yyyy-MM-dd HH:mm:ss';

      expect(result.toStringFormat(dateFormat), currentTime.toStringFormat(dateFormat));
    });
  });

  group('verify getCurrentTimeString() method', () {
    test('test getCurrentTimeString() method', () {
      const String dateFormat = 'yyyy-MM-dd HH:mm:ss';
      final String currentTime = DateTime.now().toStringFormat(dateFormat);
      final String result = evoUtilFunction.getCurrentTimeString();
      final String resultFormat = DateTime.parse(result).toStringFormat(dateFormat);

      expect(resultFormat, currentTime);
    });
  });

  group('verify getFileNamesFromDownloadPath() method', () {
    const String fakeFileName = 'file_name';
    const String downloadUrl = 'downloadUrl/$fakeFileName';

    test('verify getFileNamesFromDownloadPath() with fileExtension is null', () {
      final String fileName = evoUtilFunction.getFileNamesFromDownloadPath(downloadUrl);

      expect(fileName, fakeFileName);
    });

    test('verify getFileNamesFromDownloadPath() with fileExtension is not null', () {
      final String fileName =
          evoUtilFunction.getFileNamesFromDownloadPath(downloadUrl, fileExtension: '.pdf');

      expect(fileName, '$fakeFileName.pdf');
    });

    test(
        'verify getFileNamesFromDownloadPath() with downloadUrl has extension the same with fileExtension',
        () {
      const String fakeExtension = '.pdf';
      const String fakeDownloadUrl = '$downloadUrl$fakeExtension';

      final String fileName = evoUtilFunction.getFileNamesFromDownloadPath(
        fakeDownloadUrl,
        fileExtension: fakeExtension,
      );

      expect(fileName, '$fakeFileName.pdf');
    });
  });

  group('iOSAppId', () {
    test('returns correct App ID for staging flavor', () {
      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );
      expect(evoUtilFunction.iOSEvoAppId, '1662848066');
    });

    test('returns correct App ID for UAT flavor', () {
      FlavorConfig(
        flavor: FlavorType.uat.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.uat).getFlavorValue(),
      );
      expect(evoUtilFunction.iOSEvoAppId, '1665449377');
    });

    test('returns correct App ID for production flavor', () {
      FlavorConfig(
        flavor: FlavorType.prod.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.prod).getFlavorValue(),
      );
      expect(evoUtilFunction.iOSEvoAppId, '1665449531');
    });

    test('returns staging App ID for unknown flavor', () {
      FlavorConfig(
        flavor: FlavorType.stag.name,
        values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
      );
      expect(evoUtilFunction.iOSEvoAppId, '1662848066');
    });
  });

  group('CommonAdaptiveTextSelectionToolbarImpl - createPasteClipboard', () {
    late MockEditableTextState editableTextState;

    setUp(() {
      editableTextState = MockEditableTextState();

      when(() => editableTextState.contextMenuAnchors)
          .thenReturn(const TextSelectionToolbarAnchors(primaryAnchor: Offset.zero));
    });

    testWidgets('createPasteClipboard calls handlePaste with onPaste callback',
        (WidgetTester tester) async {
      bool onPasteCalled = false;
      final AdaptiveTextSelectionToolbar result = evoUtilFunction.createPasteClipboard(
        editableTextState: editableTextState,
        onPaste: (TextSelectionDelegate delegate) {
          onPasteCalled = true;
        },
      );

      result.buttonItems?.first.onPressed?.call();
      expect(onPasteCalled, true);
    });

    testWidgets('createPasteClipboard calls handlePaste without onPaste callback',
        (WidgetTester tester) async {
      final AdaptiveTextSelectionToolbar result = evoUtilFunction.createPasteClipboard(
        editableTextState: editableTextState,
      );

      result.buttonItems?.first.onPressed?.call();
      editableTextState.hasCalledPasteText = true;
    });
  });

  group('Test function processUserStatus', () {
    late AppState appState;

    setUpAll(() {
      appState = getIt.get<AppState>();
    });

    setUp(() {
      appState.loginSharedData.clear();
    });

    tearDown(() {
      reset(mockFeatureToggle);
    });

    test('Test with enableDeleteAccountFeatureVersion = version_2', () {
      when(() => mockFeatureToggle.enableDeleteAccountFeatureVersion)
          .thenReturn(DeleteAccountFeatureVersion.version_2);

      evoUtilFunction.updateProcessUserStatus('normal');

      expect(appState.loginSharedData.userStatus?.value, 'normal');
      verify(() => mockFeatureToggle.enableDeleteAccountFeatureVersion).called(1);
    });

    test('Test with enableDeleteAccountFeatureVersion = version_1', () {
      when(() => mockFeatureToggle.enableDeleteAccountFeatureVersion)
          .thenReturn(DeleteAccountFeatureVersion.version_1);

      evoUtilFunction.updateProcessUserStatus('normal');

      expect(appState.loginSharedData.userStatus, null);
      verify(() => mockFeatureToggle.enableDeleteAccountFeatureVersion).called(1);
    });
  });

  test('verify getFacialVerificationVersion()', () {
    when(() => mockFeatureToggle.facialVerificationVersion).thenReturn(
      FacialVerificationVersion.version_3,
    );

    final FacialVerificationVersion result = evoUtilFunction.getFacialVerificationVersion();

    expect(result, FacialVerificationVersion.version_3);
    verify(() => mockFeatureToggle.facialVerificationVersion).called(1);
  });

  group('test isGoEvoURL function', () {
    final Map<String, bool> testCases = <String, bool>{
      /// correct
      'https://goevo.vn': true,
      'https://goevo.vn/': true,
      'https://www.goevo.vn': true,
      'https://www.goevo.vn/': true,
      'https://subdomain.goevo.vn': true,
      'https://subdomain.goevo.vn/': true,
      'https://www.subdomain.goevo.vn': true,
      'https://www.subdomain.goevo.vn/': true,
      'https://sub.subdomain.goevo.vn': true,
      'https://sub.subdomain.goevo.vn/': true,
      'https://goevo.vn/path/to/resource': true,
      'https://subdomain.goevo.vn/path/to/resource': true,
      'https://subdomain.goevo.vn/path/to/resource?query=abc': true,
      'https://sub.subdomain.goevo.vn/path/to/resource': true,
      'https://sub.subdomain.goevo.vn/path/to/resource?query=abc': true,

      /// incorrect
      'http://goevo.vn': false,
      'http://subdomain.goevo.vn': false,
      'http://www.subdomain.goevo.vn': false,
      'http://sub.subdomain.goevo.vn': false,
      'http://goevo.vn/path/to/resource': false,
      'http://subdomain.goevo.vn/path/to/resource': false,
      'http://sub.subdomain.goevo.vn/path/to/resource': false,
      'http://sub.subdomain.goevo.vn/path/to/resource?query=abc': false,
      'http://goevo.com': false,
      'http://example.com': false,
      'http://goevo.vn.com': false,
      'ftp://goevo.vn': false,
      'https://goevo.com': false,
      'https://goevo.com?asd=123': false,
      'https://example.com': false,
      'https://goevo.vn.com': false,
      'https://subdomain.goevo.vn.com': false,

      /// specific subdomains to exclude
      'https://test-merchant-api.goevo.vn': false,
      'https://merchant-api.goevo.vn': false,
      'https://www.test-merchant-api.goevo.vn': false,
      'https://www.merchant-api.goevo.vn': false,
      'https://test-merchant-api.goevo.vn/web/gateway/?order=xyz': false,
      'https://merchant-api.goevo.vn/web/gateway/?order=xyz': false,
      'https://www.test-merchant-api.goevo.vn/web/gateway/?order=xyz': false,
      'https://www.merchant-api.goevo.vn/web/gateway/?order=xyz': false,
    };
    test('isGoEvoURL return correct result of test cases', () {
      testCases.forEach((String key, bool value) {
        expect(evoUtilFunction.isGoEvoURL(key), value);
      });
    });
  });
  group('getDDMMYYFromDate', () {
    test('should return formatted date string for valid DateTime', () {
      final DateTime testDate = DateTime(2025, 3, 17);
      final String expectedOutput = '17/03/2025';

      final String result = evoUtilFunction.getDDMMYYFormatFromDateTime(testDate);

      expect(result, expectedOutput);
    });

    test('should handle DateTime with single-digit day and month correctly', () {
      final DateTime testDate = DateTime(2025, 1, 7);
      final String expectedOutput = '07/01/2025';

      final String result = evoUtilFunction.getDDMMYYFormatFromDateTime(testDate);

      expect(result, expectedOutput);
    });
  });
}
