import 'package:evoapp/data/response/card_status_entity.dart';
import 'package:evoapp/data/response/credit_limit_widget_config_entity.dart';
import 'package:evoapp/data/response/user_information_entity.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../feature/splash_screen/splash_cubit_test.dart';
import '../flutter_test_config.dart';

class CommonUtilFunctionMock extends Mock implements CommonUtilFunction {}

void main() {
  late CommonUtilFunctionMock commonUtilFunctionMock;
  late FlutterSecureStorage secureStorage;
  late EvoSecureStorageHelperImpl evoSecureStorageHelper;
  late MockOneSignal mockOneSignal;

  const String refreshTokenKey = EvoSecureStorageHelperImpl.refreshTokenKey;
  const String refreshTokenValue = 'refresh_token_value';

  const String accessTokenKey = EvoSecureStorageHelperImpl.accessTokenKey;
  const String accessTokenValue = 'access_token_value';

  const String deviceTokenKey = EvoSecureStorageHelperImpl.deviceTokenKey;
  const String deviceTokenValue = 'device_token_value';

  const String userPhoneNumberKey = EvoSecureStorageHelperImpl.userPhoneNumberKey;
  const String userPhoneNumberValue = 'user_phone_number_value';

  const String biometricTokenKey = EvoSecureStorageHelperImpl.biometricTokenKey;
  const String biometricTokenValue = 'biometric_token_value';

  const String decreeConsentStatusKey = EvoSecureStorageHelperImpl.decreeConsentStatusKey;
  const bool decreeConsentStatusValue = true;

  const String isNewDeviceKey = EvoSecureStorageHelperImpl.isNewDeviceKey;
  const bool isNewDeviceKeyValue = false;

  const String isCreditLimitMaskedKey = EvoSecureStorageHelperImpl.isCreditLimitMaskedKey;
  const bool isCreditLimitMaskedValue = true;

  setUpAll(() {
    getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());
    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    getIt.registerLazySingleton<CommonSharedPreferencesHelper>(() => MockCommonSharedPreferencesHelper());
    when(()=> getIt<CommonSharedPreferencesHelper>().removeKey(any())).thenAnswer((_) async => true);
    commonUtilFunctionMock = CommonUtilFunctionMock();
    getIt.registerLazySingleton<CommonUtilFunction>(() => commonUtilFunctionMock);
    getIt.registerLazySingleton<AppState>(() => AppState());
    secureStorage = testFlutterSecureStorageExecutable();
    testSecureDataSourceExecutable(secureStorage: secureStorage);
    evoSecureStorageHelper = testEvoSecureStorageHelperExecutable(secureStorage: secureStorage);
  });

  setUp(() {
    mockOneSignal = testOneSignalExecutable();

    when(() => commonUtilFunctionMock.clearDataOnTokenInvalid(
          clearAllNotifications: any(named: 'clearAllNotifications'),
        )).thenAnswer((_) async {
      return Future<void>.value();
    });
  });

  tearDown(() {
    reset(mockOneSignal);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('test method clearEvoDataOnLogout()', () {
    setUpAll(() {
      testDioClientImplExecutable();
    });

    test('should_call_method_clearEvoDataOnLogout_correctly', () async {
      expect(await secureStorage.containsKey(key: refreshTokenKey), false);
      expect(await secureStorage.containsKey(key: accessTokenKey), false);
      expect(await secureStorage.containsKey(key: deviceTokenKey), false);

      /// Setup
      await evoSecureStorageHelper.setRefreshToken(refreshTokenValue);
      await evoSecureStorageHelper.setAccessToken(accessTokenValue);
      await evoSecureStorageHelper.setDeviceToken(deviceTokenValue);
      await evoSecureStorageHelper.setUserPhoneNumber(userPhoneNumberValue);
      await evoSecureStorageHelper.setBiometricToken(biometricTokenValue);
      await evoSecureStorageHelper.setDecreeConsentStatus(decreeConsentStatusValue);
      await evoSecureStorageHelper.setNewDevice(isNewDeviceKeyValue);
      await evoSecureStorageHelper.setCreditLimitMasked(isCreditLimitMaskedValue);

      expect(await secureStorage.containsKey(key: refreshTokenKey), true);
      expect(await secureStorage.read(key: refreshTokenKey), refreshTokenValue);

      expect(await secureStorage.containsKey(key: accessTokenKey), true);
      expect(await secureStorage.read(key: accessTokenKey), accessTokenValue);

      expect(await secureStorage.containsKey(key: deviceTokenKey), true);
      expect(await secureStorage.read(key: deviceTokenKey), deviceTokenValue);

      await EvoAuthenticationHelper().clearAllUserData(oneSignal: mockOneSignal);

      verify(() => commonUtilFunction.clearDataOnTokenInvalid(clearAllNotifications: true))
          .called(1);

      /// verify access token
      expect(await evoSecureStorageHelper.getAccessToken(), null);
      expect(await secureStorage.containsKey(key: accessTokenKey), false);
      expect(await secureStorage.read(key: accessTokenKey), null);

      /// verify refresh token
      expect(await evoSecureStorageHelper.getRefreshToken(), null);
      expect(await secureStorage.containsKey(key: refreshTokenKey), false);
      expect(await secureStorage.read(key: refreshTokenKey), null);

      /// verify device token
      expect(await secureStorage.containsKey(key: deviceTokenKey), false);
      expect(await secureStorage.read(key: deviceTokenKey), null);
      expect(await evoSecureStorageHelper.getDeviceToken(), null);

      /// verify device token
      expect(await secureStorage.containsKey(key: deviceTokenKey), false);
      expect(await secureStorage.read(key: deviceTokenKey), null);
      expect(await evoSecureStorageHelper.getDeviceToken(), null);

      /// verify phone number
      expect(await secureStorage.containsKey(key: userPhoneNumberKey), false);
      expect(await secureStorage.read(key: userPhoneNumberKey), null);
      expect(await evoSecureStorageHelper.getUserPhoneNumber(), null);

      /// verify biometric token
      expect(await secureStorage.containsKey(key: biometricTokenKey), false);
      expect(await secureStorage.read(key: biometricTokenKey), null);
      expect(await evoSecureStorageHelper.getBiometricToken(), null);

      /// verify decree consent status
      expect(await secureStorage.containsKey(key: decreeConsentStatusKey), false);
      expect(await secureStorage.read(key: decreeConsentStatusKey), null);
      expect(await evoSecureStorageHelper.getDecreeConsentStatus(), false);

      /// verify is new device
      expect(await secureStorage.containsKey(key: isNewDeviceKey), false);
      expect(await secureStorage.read(key: isNewDeviceKey), null);
      expect(await evoSecureStorageHelper.isNewDevice(), true);

      /// verify if credit limit masked
      expect(await secureStorage.containsKey(key: isCreditLimitMaskedKey), false);
      expect(await secureStorage.read(key: isCreditLimitMaskedKey), null);
      expect(await evoSecureStorageHelper.isCreditLimitMasked(), null);
    });

    test('should_call_method_clearDataOnTokenInvalid_with_clear_all_notification_is_true_correctly',
        () async {
      await EvoAuthenticationHelper().clearAllUserData(oneSignal: mockOneSignal);

      verify(() => commonUtilFunction.clearDataOnTokenInvalid(
            clearAllNotifications: true,
          )).called(1);
    });

    test('should_clear_user_data_on_memory', () async {
      // setup
      final AppState appState = getIt.get<AppState>();
      appState.isUserLogIn = true;
      appState.userInfo.value = const UserInformationEntity(fullName: 'evo');
      appState.cardStatus = CardStatusEntity(
        creditLimitWidgetConfig: CreditLimitWidgetConfigEntity(creditLimit: 1000),
      );

      // action
      await EvoAuthenticationHelper().clearAllUserData(oneSignal: mockOneSignal);

      // verify
      expect(appState.isUserLogIn, false);
      expect(appState.userInfo.value?.fullName, isNull);
      expect(appState.cardStatus, isNull);
    });
  });
}
