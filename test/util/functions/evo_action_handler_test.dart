import 'package:evoapp/feature/activated_pos_limit/activate_card_introduction/activate_card_introduction_screen.dart';
import 'package:evoapp/feature/activated_pos_limit/setup_pos_limit_introduction/setup_pos_limit_introduction_screen.dart';
import 'package:evoapp/feature/deep_link/deep_link_handler.dart';
import 'package:evoapp/feature/deep_link/deep_link_utils.dart';
import 'package:evoapp/feature/deep_link/model/deep_link_model.dart';
import 'package:evoapp/feature/deep_link/model/deep_link_shared_data.dart';
import 'package:evoapp/feature/dop_native/features/introduction/dop_native_introduction_screen.dart';
import 'package:evoapp/feature/emi_management/detail_screen/emi_management_detail_screen.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/main_screen/main_screen.dart';
import 'package:evoapp/feature/main_screen/main_screen_controller.dart';
import 'package:evoapp/feature/promotion_list/promotion_list_page.dart';
import 'package:evoapp/feature/referral_program/referral_sharing/referral_sharing_screen.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:evoapp/model/evo_action_model.dart';
import 'package:evoapp/model/user_info_notifier.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_action_handler.dart';
import 'package:evoapp/util/evo_authentication_helper.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:evoapp/util/web_link_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/clipboard_wrapper.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../flutter_test_config.dart';

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockAppState extends Mock implements AppState {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockJwtHelper extends Mock implements JwtHelper {}

class MockDeepLinkHandler extends Mock implements DeepLinkHandler {}

void main() {
  late EvoSnackBar mockSnackBar;
  late CommonUtilFunction mockCommonUtilFunction;
  late MockBuildContext mockBuildContext;
  late CommonNavigator mockCommonNavigator;
  late AppState mockAppState;
  late EvoLocalStorageHelper mockEvoLocalStorageHelper;
  late JwtHelper mockJwtHelper;
  late BuildContext mockNavigatorContext;
  late DeepLinkHandler deepLinkHandler;

  setUpAll(() {
    setUpOneLinkDeepLinkRegExForTest();

    getIt.registerLazySingleton<WebLinkUtils>(() => WebLinkUtils());
    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());
    getIt.registerLazySingleton<AppState>(() => MockAppState());
    mockAppState = getIt.get<AppState>();

    getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());
    mockCommonNavigator = getIt.get<CommonNavigator>();

    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonUtilFunction>(() => MockCommonUtilFunction());
    mockCommonUtilFunction = getIt.get<CommonUtilFunction>();

    getIt.registerLazySingleton<ClipboardWrapper>(() => ClipboardWrapper());

    getIt.registerLazySingleton<EvoSnackBar>(() => MockEvoSnackBar());
    mockSnackBar = getIt.get<EvoSnackBar>();

    getIt.registerLazySingleton<EvoLocalStorageHelper>(() => MockEvoLocalStorageHelper());
    mockEvoLocalStorageHelper = getIt.get<EvoLocalStorageHelper>();

    getIt.registerLazySingleton<JwtHelper>(() => MockJwtHelper());
    mockJwtHelper = getIt.get<JwtHelper>();

    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);

    deepLinkHandler = getIt.registerSingleton(MockDeepLinkHandler());

    FlavorConfig(
      flavor: FlavorType.stag.name,
      values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
    );
  });

  setUp(() {
    when(() => mockAppState.isUserLogIn).thenReturn(true);
    when(() => mockAppState.userInfo).thenReturn(UserInfoNotifier(null));

    mockBuildContext = MockBuildContext();

    when(() => mockBuildContext.pushNamed(
          any(),
          extra: any(named: 'extra'),
        )).thenAnswer((_) async => Future<void>.value());

    when(() => mockBuildContext.pushReplacementNamed(
          any(),
          extra: any(named: 'extra'),
        )).thenAnswer((_) async => Future<void>.value());

    when(() => mockJwtHelper.isCanUse(any())).thenReturn(true);

    when(() => mockEvoLocalStorageHelper.getRefreshToken()).thenAnswer((_) async {
      return Future<String>.value('refresh_token');
    });

    when(() => mockCommonUtilFunction.clearDataOnTokenInvalid(
          clearAllNotifications: any(named: 'clearAllNotifications'),
        )).thenAnswer((_) async {
      return Future<void>.value();
    });

    when(() => mockEvoLocalStorageHelper.delete(key: any(named: 'key'))).thenAnswer((_) async {
      return Future<void>.value();
    });
  });

  tearDown(() {
    reset(mockAppState);
    reset(mockSnackBar);
    reset(mockCommonUtilFunction);
    reset(mockCommonNavigator);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('test handleCopyPromotionCode() method', () {
    const String code = 'code';

    setUpAll(() {
      when(() => mockSnackBar.show(any(), durationInSec: any(named: 'durationInSec')))
          .thenAnswer((_) async {
        return Future<bool?>.value(true);
      });

      when(() => mockCommonUtilFunction.copyToClipboard(any())).thenAnswer((_) async {
        return Future<void>.value();
      });
    });

    test('verify method with code', () async {
      await EvoActionHandler().handleCopyPromotionCode(code);

      verify(() => mockCommonUtilFunction.copyToClipboard(code)).called(1);

      verify(() => mockSnackBar.show(EvoStrings.copyPromotionCodeSuccess,
          durationInSec: any(named: 'durationInSec'))).called(1);
    });
  });

  group('verify evoHandleAction() method', () {
    const String liveToken = 'live_token';

    setUp(() {
      when(() => mockCommonUtilFunction.handleAction(
            any(),
            arg: any(named: 'arg'),
          )).thenAnswer((_) async {
        return Future<bool>.value(false);
      });

      when(() => mockCommonUtilFunction.copyToClipboard(any())).thenAnswer((_) async {
        return Future<void>.value();
      });

      when(() => mockSnackBar.show(any(), durationInSec: any(named: 'durationInSec')))
          .thenAnswer((_) async {
        return Future<bool?>.value(true);
      });
    });

    test('case action_type = null', () async {
      final bool canOpenPage = await EvoActionHandler().handle(
        EvoActionModel(
          args: EvoArgs(),
        ),
      );

      expect(canOpenPage, false);
    });

    test('case action_type = promotion_copy_mark_used', () async {
      const String voucherCode = '12345';
      final bool canOpenPage = await EvoActionHandler().handle(
        EvoActionModel(
          type: EvoActionModel.promotionCopyAndMarkUsed,
          args: EvoArgs(parameters: EvoParameters(code: voucherCode)),
        ),
      );

      verify(() => mockCommonUtilFunction.copyToClipboard(voucherCode)).called(1);

      verify(() => mockSnackBar.show(EvoStrings.copyPromotionCodeSuccess,
          durationInSec: any(named: 'durationInSec'))).called(1);

      expect(canOpenPage, true);
    });

    test('case action_type = promotion_scan_to_pay', () async {
      const String voucherCode = '12345';
      final bool canOpenPage = await EvoActionHandler().handle(
        EvoActionModel(
          type: EvoActionModel.promotionScanToPay,
          args: EvoArgs(parameters: EvoParameters(code: voucherCode)),
        ),
      );

      verify(() => mockCommonNavigator.popUntilNamed(
            mockNavigatorContext,
            Screen.qrCodeScannerScreen.name,
            onPageFound: any(named: 'onPageFound'),
            onPageNotFound: any(named: 'onPageNotFound'),
          )).called(1);

      expect(canOpenPage, true);
    });

    group('case action_type = openAppScreen', () {
      test('screen_name is null', () async {
        final bool canOpenPage = await EvoActionHandler().handle(
          EvoActionModel(
            type: EvoActionModel.openAppScreen,
            args: EvoArgs(),
          ),
        );

        expect(canOpenPage, false);
      });

      group('screen_name = Screen.loginScreen', () {
        test('on old device', () async {
          // mock login on old device
          when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) {
            return Future<String?>.value(liveToken);
          });

          expect(await EvoAuthenticationHelper().isCanLogInOnOldDevice(), true);

          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.loginScreen.name),
            ),
          );

          verify(() => mockCommonNavigator.pushNamed(
                mockNavigatorContext,
                Screen.loginOnOldDeviceScreen.name,
                extra: any(named: 'extra'),
              )).called(1);

          expect(canOpenPage, true);
        });

        test('on new device', () async {
          when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) {
            return Future<String?>.value();
          });

          when(() => mockJwtHelper.isCanUse(any())).thenReturn(false);

          when(() => mockEvoLocalStorageHelper.getUserPhoneNumber()).thenAnswer((_) {
            return Future<String?>.value();
          });

          when(() => mockEvoLocalStorageHelper.clearAllUserData()).thenAnswer((_) {
            return Future<String?>.value();
          });

          expect(await EvoAuthenticationHelper().isCanLogInOnOldDevice(), false);

          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.loginScreen.name),
            ),
          );

          verify(() => mockEvoLocalStorageHelper.getUserPhoneNumber()).called(1);

          verify(() => mockEvoLocalStorageHelper.clearAllUserData()).called(1);

          verify(() => mockCommonNavigator.pushNamed(
                mockNavigatorContext,
                Screen.inputPhoneNumberScreen.name,
                extra: any(named: 'extra'),
              )).called(1);

          expect(canOpenPage, true);
        });
      });

      group('screen_name = Screen.homeScreen', () {
        test('opening Home screen', () async {
          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.homeScreen.name),
            ),
          );

          verify(() => mockCommonNavigator.pushReplacementNamed(
                mockBuildContext,
                Screen.homeScreen.name,
                extra: MainScreenArg(
                  isLoggedIn: mockAppState.isUserLogIn,
                  initialPage: any(named: 'initialPage'),
                ),
              )).called(1);

          expect(canOpenPage, true);
        });
      });

      group('screen_name = Screen.allPromotionListScreen', () {
        test('when user is logged', () async {
          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.allPromotionListScreen.name),
            ),
          );

          expect(
            verify(() => mockCommonNavigator.pushReplacementNamed(
                  mockNavigatorContext,
                  Screen.mainScreen.name,
                  extra: captureAny(named: 'extra'),
                )).captured,
            <dynamic>[
              isA<MainScreenArg>()
                  .having(
                    (MainScreenArg arg) => arg.initialPage,
                    'test MainScreenChild.promotion',
                    MainScreenChild.promotion,
                  )
                  .having(
                    (MainScreenArg arg) => arg.isLoggedIn,
                    'test isLoggedIn',
                    true,
                  ),
            ],
          );

          expect(mockAppState.actionAfterLogin, null);
          expect(canOpenPage, true);
        });

        test('when user is NOT logged', () async {
          when(() => mockAppState.isUserLogIn).thenReturn(false);

          expectedActionToBeHandledAfterLoggedIn() async {
            return Future<void>.value();
          }

          when(() => mockAppState.actionAfterLogin)
              .thenReturn(expectedActionToBeHandledAfterLoggedIn);

          // mock login on old device
          when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) {
            return Future<String?>.value(liveToken);
          });

          expect(await EvoAuthenticationHelper().isCanLogInOnOldDevice(), true);

          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.allPromotionListScreen.name),
            ),
          );

          expect(mockAppState.actionAfterLogin, expectedActionToBeHandledAfterLoggedIn);

          verify(() => mockCommonNavigator.goNamed(
                mockNavigatorContext,
                Screen.loginOnOldDeviceScreen.name,
                extra: any(named: 'extra'),
              )).called(1);

          expect(canOpenPage, true);
        });
      });

      group('screen_name = Screen.campaignListScreen', () {
        test('opening Campaign screen', () async {
          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.campaignListScreen.name),
            ),
          );

          verify(() => mockCommonNavigator.pushReplacementNamed(
                mockNavigatorContext,
                Screen.mainScreen.name,
                extra: MainScreenArg(
                  isLoggedIn: mockAppState.isUserLogIn,
                  initialPage: MainScreenChild.promotion,
                ),
              )).called(1);

          expect(mockAppState.actionAfterLogin, null);
          expect(canOpenPage, true);
        });
      });

      group('screen_name = Screen.myPromotionListScreen', () {
        test('when user is logged', () async {
          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.myPromotionListScreen.name),
            ),
          );

          expect(
            verify(() => mockCommonNavigator.pushReplacementNamed(
                  mockNavigatorContext,
                  Screen.mainScreen.name,
                  extra: captureAny(named: 'extra'),
                )).captured,
            <dynamic>[
              isA<MainScreenArg>()
                  .having(
                    (MainScreenArg arg) => arg.initialPage,
                    'test MainScreenChild.promotion',
                    MainScreenChild.promotion,
                  )
                  .having(
                    (MainScreenArg arg) => arg.isLoggedIn,
                    'test isLoggedIn',
                    true,
                  )
                  .having(
                    (MainScreenArg arg) => arg.initialPromotionTab,
                    'test initialPromotionTab',
                    PromotionTabType.myVoucher,
                  ),
            ],
          );

          expect(mockAppState.actionAfterLogin, null);
          expect(canOpenPage, true);
        });

        test('when user is NOT logged', () async {
          when(() => mockAppState.isUserLogIn).thenReturn(false);

          expectedActionToBeHandledAfterLoggedIn() async {
            return Future<void>.value();
          }

          when(() => mockAppState.actionAfterLogin)
              .thenReturn(expectedActionToBeHandledAfterLoggedIn);

          // mock login on old device
          when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) {
            return Future<String?>.value(liveToken);
          });

          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.myPromotionListScreen.name),
            ),
          );

          expect(mockAppState.actionAfterLogin, expectedActionToBeHandledAfterLoggedIn);

          verify(() => mockCommonNavigator.goNamed(
                mockNavigatorContext,
                Screen.loginOnOldDeviceScreen.name,
                extra: any(named: 'extra'),
              )).called(1);

          expect(canOpenPage, true);
        });
      });

      group('screen_name = Screen.transactionHistoryListScreen', () {
        test('when user is logged in', () async {
          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.transactionHistoryListScreen.name),
            ),
          );

          expect(
            verify(() => mockCommonNavigator.pushReplacementNamed(
              mockNavigatorContext,
              Screen.mainScreen.name,
              extra: captureAny(named: 'extra'),
            )).captured,
            <dynamic>[
              isA<MainScreenArg>()
                  .having(
                    (MainScreenArg arg) => arg.initialPage,
                'test MainScreenChild.history',
                MainScreenChild.history,
              )
                  .having(
                    (MainScreenArg arg) => arg.isLoggedIn,
                'test isLoggedIn',
                true,
              ),
            ],
          );

          expect(mockAppState.actionAfterLogin, null);
          expect(canOpenPage, true);
        });

        test('when user is NOT logged in', () async {
          when(() => mockAppState.isUserLogIn).thenReturn(false);

          expectedActionToBeHandledAfterLoggedIn() async {
            return Future<void>.value();
          }

          when(() => mockAppState.actionAfterLogin)
              .thenReturn(expectedActionToBeHandledAfterLoggedIn);

          // mock login on old device
          when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) {
            return Future<String?>.value(liveToken);
          });

          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.transactionHistoryListScreen.name),
            ),
          );

          expect(mockAppState.actionAfterLogin, expectedActionToBeHandledAfterLoggedIn);

          verify(() => mockCommonNavigator.goNamed(
            mockNavigatorContext,
            Screen.loginOnOldDeviceScreen.name,
            extra: any(named: 'extra'),
          )).called(1);

          expect(canOpenPage, true);
        });
      });

      group('screen_name = Screen.transactionHistoryDetailScreen', () {
        test('when user is logged', () async {
          const String transactionId = '1';

          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(
                screenName: Screen.transactionHistoryDetailScreen.name,
                parameters: EvoParameters(id: transactionId),
              ),
            ),
          );

          verify(() => mockCommonNavigator.pushNamed(
                mockNavigatorContext,
                Screen.transactionHistoryDetailScreen.name,
                extra: any(named: 'extra'),
              )).called(1);

          expect(mockAppState.actionAfterLogin, null);
          expect(canOpenPage, true);
        });

        test('when user is NOT logged', () async {
          const String transactionId = '1';

          when(() => mockAppState.isUserLogIn).thenReturn(false);

          expectedActionToBeHandledAfterLoggedIn() async {
            return Future<void>.value();
          }

          when(() => mockAppState.actionAfterLogin)
              .thenReturn(expectedActionToBeHandledAfterLoggedIn);

          // mock login on old device
          when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) {
            return Future<String?>.value(liveToken);
          });

          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(
                screenName: Screen.transactionHistoryDetailScreen.name,
                parameters: EvoParameters(id: transactionId),
              ),
            ),
          );

          expect(mockAppState.actionAfterLogin, expectedActionToBeHandledAfterLoggedIn);

          verify(() => mockCommonNavigator.goNamed(
                mockNavigatorContext,
                Screen.loginOnOldDeviceScreen.name,
                extra: any(named: 'extra'),
              )).called(1);

          expect(canOpenPage, true);
        });
      });

      group('screen_name = Screen.profileScreen', () {
        test('when user is logged', () async {
          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.profileScreen.name),
            ),
          );

          expect(
            verify(() => mockCommonNavigator.pushReplacementNamed(
                  mockNavigatorContext,
                  Screen.mainScreen.name,
                  extra: captureAny(named: 'extra'),
                )).captured,
            <dynamic>[
              isA<MainScreenArg>()
                  .having(
                    (MainScreenArg arg) => arg.initialPage,
                    'test MainScreenChild.account',
                    MainScreenChild.account,
                  )
                  .having(
                    (MainScreenArg arg) => arg.isLoggedIn,
                    'test isLoggedIn',
                    true,
                  ),
            ],
          );

          expect(mockAppState.actionAfterLogin, null);
          expect(canOpenPage, true);
        });

        test('when user NOT logged', () async {
          when(() => mockAppState.isUserLogIn).thenReturn(false);

          expectedActionToBeHandledAfterLoggedIn() async {
            return Future<void>.value();
          }

          when(() => mockAppState.actionAfterLogin)
              .thenReturn(expectedActionToBeHandledAfterLoggedIn);

          // mock login on old device
          when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) {
            return Future<String?>.value(liveToken);
          });

          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.profileScreen.name),
            ),
          );

          expect(mockAppState.actionAfterLogin, expectedActionToBeHandledAfterLoggedIn);

          verify(() => mockCommonNavigator.goNamed(
                mockNavigatorContext,
                Screen.loginOnOldDeviceScreen.name,
                extra: any(named: 'extra'),
              )).called(1);

          expect(canOpenPage, true);
        });
      });

      group('screen_name = Screen.qrCodeScannerScreen', () {
        test('when user is logged', () async {
          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.qrCodeScannerScreen.name),
            ),
          );

          verify(() => mockCommonNavigator.popUntilNamed(
                mockNavigatorContext,
                Screen.qrCodeScannerScreen.name,
                onPageFound: any(named: 'onPageFound'),
                onPageNotFound: any(named: 'onPageNotFound'),
              )).called(1);

          expect(mockAppState.actionAfterLogin, null);
          expect(canOpenPage, true);
        });

        test('when user is NOT logged', () async {
          when(() => mockAppState.isUserLogIn).thenReturn(false);

          // mock login on old device
          when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) {
            return Future<String?>.value(liveToken);
          });

          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.qrCodeScannerScreen.name),
            ),
          );

          verify(() => mockCommonNavigator.goNamed(
                mockNavigatorContext,
                Screen.loginOnOldDeviceScreen.name,
                extra: any(named: 'extra'),
              )).called(1);

          expect(canOpenPage, true);
        });
      });

      group('screen_name = Screen.announcementListScreen', () {
        test('opening Announcement List Screen', () async {
          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.announcementListScreen.name),
            ),
          );

          verify(() => mockCommonNavigator.pushNamed(
                mockNavigatorContext,
                Screen.announcementListScreen.name,
                extra: any(named: 'extra'),
              )).called(1);

          expect(canOpenPage, true);
        });
      });

      group('screen_name = Screen.tutorialScreen', () {
        test('opening Tutorial Screen', () async {
          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.tutorialScreen.name),
            ),
          );

          verify(() => mockCommonNavigator.goNamed(
                mockNavigatorContext,
                Screen.tutorialScreen.name,
                extra: any(named: 'extra'),
              )).called(1);

          expect(canOpenPage, true);
        });
      });

      group('screen_name = Screen.profileSettingScreen', () {
        test('when user is logged', () async {
          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.profileSettingScreen.name),
            ),
          );

          verify(() => mockCommonNavigator.pushNamed(
                mockNavigatorContext,
                Screen.profileSettingScreen.name,
                extra: any(named: 'extra'),
              )).called(1);

          expect(mockAppState.actionAfterLogin, null);
          expect(canOpenPage, true);
        });

        test('when user is NOT logged', () async {
          when(() => mockAppState.isUserLogIn).thenReturn(false);

          expectedActionToBeHandledAfterLoggedIn() async {
            return Future<void>.value();
          }

          when(() => mockAppState.actionAfterLogin)
              .thenReturn(expectedActionToBeHandledAfterLoggedIn);

          // mock login on old device
          when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) {
            return Future<String?>.value(liveToken);
          });

          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.profileSettingScreen.name),
            ),
          );

          expect(mockAppState.actionAfterLogin, expectedActionToBeHandledAfterLoggedIn);

          verify(() => mockCommonNavigator.goNamed(
                mockNavigatorContext,
                Screen.loginOnOldDeviceScreen.name,
                extra: any(named: 'extra'),
              )).called(1);

          expect(canOpenPage, true);
        });
      });

      group('screen_name = Screen.referralSharingScreen', () {
        test(
          'when user is logged',
          () async {
            const String campaignId = '1';

            final bool canOpenPage = await EvoActionHandler().handle(
              EvoActionModel(
                type: EvoActionModel.openAppScreen,
                args: EvoArgs(
                  screenName: Screen.referralSharingScreen.name,
                  parameters: EvoParameters(id: campaignId),
                ),
              ),
            );

            final List<dynamic> capturedData = verify(() => mockCommonNavigator.pushNamed(
                  mockNavigatorContext,
                  Screen.referralSharingScreen.name,
                  extra: captureAny(named: 'extra'),
                )).captured;

            expect(capturedData[0], isA<ReferralSharingScreenArg>());
            expect((capturedData[0] as ReferralSharingScreenArg).campaignId, campaignId);

            expect(mockAppState.actionAfterLogin, null);
            expect(canOpenPage, true);
          },
        );

        test('when user is NOT logged', () async {
          const String campaignId = '1';

          when(() => mockAppState.isUserLogIn).thenReturn(false);

          expectedActionToBeHandledAfterLoggedIn() async {
            return Future<void>.value();
          }

          when(() => mockAppState.actionAfterLogin)
              .thenReturn(expectedActionToBeHandledAfterLoggedIn);

          // mock login on old device
          when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) {
            return Future<String?>.value(liveToken);
          });

          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(
                screenName: Screen.referralSharingScreen.name,
                parameters: EvoParameters(id: campaignId),
              ),
            ),
          );

          expect(mockAppState.actionAfterLogin, expectedActionToBeHandledAfterLoggedIn);

          verify(() => mockCommonNavigator.goNamed(
                mockNavigatorContext,
                Screen.loginOnOldDeviceScreen.name,
                extra: any(named: 'extra'),
              )).called(1);

          expect(canOpenPage, true);
        });
      });

      group('screen_name = Screen.emiManagementListScreen', () {
        test('when user is logged', () async {
          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.emiManagementListScreen.name),
            ),
          );

          verify(() => mockCommonNavigator.pushNamed(
                mockNavigatorContext,
                Screen.emiManagementListScreen.name,
              )).called(1);

          expect(mockAppState.actionAfterLogin, null);
          expect(canOpenPage, true);
        });

        test('when user is NOT logged', () async {
          when(() => mockAppState.isUserLogIn).thenReturn(false);

          // mock login on old device
          when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) {
            return Future<String?>.value(liveToken);
          });

          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(screenName: Screen.emiManagementListScreen.name),
            ),
          );

          verify(() => mockCommonNavigator.goNamed(
                mockNavigatorContext,
                Screen.loginOnOldDeviceScreen.name,
                extra: any(named: 'extra'),
              )).called(1);

          expect(canOpenPage, true);
        });
      });

      group('screen_name = Screen.emiManagementDetailScreen', () {
        test(
          'when user is logged',
          () async {
            const String emiManagementDetailId = '1';

            final bool canOpenPage = await EvoActionHandler().handle(
              EvoActionModel(
                type: EvoActionModel.openAppScreen,
                args: EvoArgs(
                  screenName: Screen.emiManagementDetailScreen.name,
                  parameters: EvoParameters(id: emiManagementDetailId),
                ),
              ),
            );

            final List<dynamic> capturedData = verify(() => mockCommonNavigator.pushNamed(
                  mockNavigatorContext,
                  Screen.emiManagementDetailScreen.name,
                  extra: captureAny(named: 'extra'),
                )).captured;

            expect(capturedData[0], isA<EmiManagementDetailScreenArg>());
            expect((capturedData[0] as EmiManagementDetailScreenArg).id, emiManagementDetailId);

            expect(mockAppState.actionAfterLogin, null);
            expect(canOpenPage, true);
          },
        );

        test('when user is NOT logged', () async {
          const String emiManagementDetailId = '1';

          when(() => mockAppState.isUserLogIn).thenReturn(false);

          expectedActionToBeHandledAfterLoggedIn() async {
            return Future<void>.value();
          }

          when(() => mockAppState.actionAfterLogin)
              .thenReturn(expectedActionToBeHandledAfterLoggedIn);

          // mock login on old device
          when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) {
            return Future<String?>.value(liveToken);
          });

          final bool canOpenPage = await EvoActionHandler().handle(
            EvoActionModel(
              type: EvoActionModel.openAppScreen,
              args: EvoArgs(
                screenName: Screen.emiManagementDetailScreen.name,
                parameters: EvoParameters(id: emiManagementDetailId),
              ),
            ),
          );

          expect(mockAppState.actionAfterLogin, expectedActionToBeHandledAfterLoggedIn);

          verify(() => mockCommonNavigator.goNamed(
                mockNavigatorContext,
                Screen.loginOnOldDeviceScreen.name,
                extra: any(named: 'extra'),
              )).called(1);

          expect(canOpenPage, true);
        });
      });

      group('screen_name = Screen.dopNativeIntroductionScreen', () {
        const String fakeUniqueToken = 'unique_token_value';
        const String fakeUtmSource = 'utm_source_value';
        const String fakeUtmCampaign = 'utm_campaign_value';

        test(
          'open screen with utm_source != NULL & utm_campaign != NULL & unique_token != NULL',
          () async {
            // arrange
            final Map<String, String> params = <String, String>{
              'utm_source': fakeUtmSource,
              'utm_campaign': fakeUtmCampaign,
              'unique_token': fakeUniqueToken,
            };

            when(() => mockAppState.deepLinkSharedData).thenReturn(DeepLinkSharedData(
              deepLink: DeepLinkModel(
                params: params,
                screenName: Screen.dopNativeIntroductionScreen.name,
              ),
            ));

            when(() => deepLinkUtils.removeEvoDeepLinkKeysFromMap(any())).thenReturn(params);

            // action
            final bool isOpenPage = await EvoActionHandler().handle(
              EvoActionModel(
                type: EvoActionModel.openAppScreen,
                args: EvoArgs(
                  screenName: Screen.dopNativeIntroductionScreen.name,
                  additionalParams: params,
                ),
              ),
            );

            // assert
            expect(isOpenPage, true);

            final DOPNativeIntroductionScreenArg arg = verify(
              () => mockCommonNavigator.pushNamed(
                mockNavigatorContext,
                Screen.dopNativeIntroductionScreen.name,
                extra: captureAny(named: 'extra'),
              ),
            ).captured.single as DOPNativeIntroductionScreenArg;

            expect(arg.uniqueToken, fakeUniqueToken);
          },
        );

        test(
          'open screen with utm_source = NULL & utm_campaign = NULL & unique_token = NULL',
          () async {
            when(() => mockAppState.deepLinkSharedData).thenReturn(DeepLinkSharedData(
              deepLink: DeepLinkModel(
                params: <String, String>{},
                screenName: Screen.dopNativeIntroductionScreen.name,
              ),
            ));
            when(() => deepLinkUtils.removeEvoDeepLinkKeysFromMap(any()))
                .thenReturn(<String, String>{});
            // action
            final bool isOpenPage = await EvoActionHandler().handle(
              EvoActionModel(
                type: EvoActionModel.openAppScreen,
                args: EvoArgs(
                  screenName: Screen.dopNativeIntroductionScreen.name,
                  parameters: EvoParameters(
                    id: 'id',
                    code: 'code',
                  ),
                ),
              ),
            );

            // assert
            expect(isOpenPage, true);

            final DOPNativeIntroductionScreenArg arg = verify(
              () => mockCommonNavigator.pushNamed(
                mockNavigatorContext,
                Screen.dopNativeIntroductionScreen.name,
                extra: captureAny(named: 'extra'),
              ),
            ).captured.single as DOPNativeIntroductionScreenArg;

            expect(arg.uniqueToken, isNull);
          },
        );

        test(
          'open screen with unique_token != NULL & other params = NULL',
          () async {
            final Map<String, String> params = <String, String>{
              'unique_token': fakeUniqueToken,
            };
            when(() => deepLinkUtils.removeEvoDeepLinkKeysFromMap(any())).thenReturn(params);
            when(() => mockAppState.deepLinkSharedData).thenReturn(DeepLinkSharedData(
              deepLink: DeepLinkModel(
                params: params,
                screenName: Screen.dopNativeIntroductionScreen.name,
              ),
            ));

            final bool isOpenPage = await EvoActionHandler().handle(
              EvoActionModel(
                type: EvoActionModel.openAppScreen,
                args: EvoArgs(
                  screenName: Screen.dopNativeIntroductionScreen.name,
                  additionalParams: params,
                ),
              ),
            );

            // assert
            expect(isOpenPage, true);

            final DOPNativeIntroductionScreenArg arg = verify(
              () => mockCommonNavigator.pushNamed(
                mockNavigatorContext,
                Screen.dopNativeIntroductionScreen.name,
                extra: captureAny(named: 'extra'),
              ),
            ).captured.single as DOPNativeIntroductionScreenArg;

            expect(arg.uniqueToken, fakeUniqueToken);
          },
        );

        test(
          'open screen with unique_token = NULL & other params != NULL',
          () async {
            final Map<String, String> params = <String, String>{
              'utm_source': fakeUtmSource,
              'utm_campaign': fakeUtmCampaign,
            };
            when(() => deepLinkUtils.removeEvoDeepLinkKeysFromMap(any())).thenReturn(params);
            when(() => mockAppState.deepLinkSharedData).thenReturn(DeepLinkSharedData(
              deepLink: DeepLinkModel(
                params: params,
                screenName: Screen.dopNativeIntroductionScreen.name,
              ),
            ));

            final bool isOpenPage = await EvoActionHandler().handle(
              EvoActionModel(
                type: EvoActionModel.openAppScreen,
                args: EvoArgs(
                  screenName: Screen.dopNativeIntroductionScreen.name,
                  additionalParams: params,
                ),
              ),
            );

            // assert
            expect(isOpenPage, true);

            final DOPNativeIntroductionScreenArg arg = verify(
              () => mockCommonNavigator.pushNamed(
                mockNavigatorContext,
                Screen.dopNativeIntroductionScreen.name,
                extra: captureAny(named: 'extra'),
              ),
            ).captured.single as DOPNativeIntroductionScreenArg;

            expect(arg.uniqueToken, isNull);
          },
        );
      });
    });

    test('opening screen_name = Screen.activateCardScreen', () async {
      final bool canOpenPage = await EvoActionHandler().handle(
        EvoActionModel(
          type: EvoActionModel.openAppScreen,
          args: EvoArgs(
            screenName: Screen.activateCardScreen.name,
            parameters: EvoParameters(
              posLimitAllow: 1000,
              creditLimit: 2000,
            ),
          ),
        ),
      );

      expect(
        verify(() => mockCommonNavigator.pushNamed(
              mockNavigatorContext,
              Screen.activateCardScreen.name,
              extra: captureAny(named: 'extra'),
            )).captured,
        <dynamic>[
          isA<ActivateCardIntroductionScreenArg>()
              .having(
                (ActivateCardIntroductionScreenArg arg) => arg.posLimit,
                'verify posLimit',
                1000,
              )
              .having(
                (ActivateCardIntroductionScreenArg arg) => arg.creditLimit,
                'verify creditLimit',
                2000,
              ),
        ],
      );

      expect(canOpenPage, true);
    });

    test('opening screen_name = Screen.setupPosLimitScreen', () async {
      final bool canOpenPage = await EvoActionHandler().handle(EvoActionModel(
        type: EvoActionModel.openAppScreen,
        args: EvoArgs(
            screenName: Screen.setupPosLimitScreen.name,
            parameters: EvoParameters(posLimitAllow: 1000, creditLimit: 2000)),
      ));

      expect(
          verify(() => mockCommonNavigator.pushNamed(
                mockNavigatorContext,
                Screen.setupPosLimitScreen.name,
                extra: captureAny(named: 'extra'),
              )).captured,
          <dynamic>[
            isA<SetupPosLimitIntroductionScreenArg>()
                .having(
                  (SetupPosLimitIntroductionScreenArg arg) => arg.posLimit,
                  'verify posLimit',
                  1000,
                )
                .having(
                  (SetupPosLimitIntroductionScreenArg arg) => arg.creditLimit,
                  'verify creditLimit',
                  2000,
                )
          ]);

      expect(canOpenPage, true);
    });

    test('case action_type = open_deeplink', () async {
      when(() => deepLinkUtils.isEvoAppDeepLink(any())).thenReturn(true);
      when(() => deepLinkUtils.generateDeepLinkModel(
            deepLinkValue: any(named: 'deepLinkValue'),
            isDeferred: any(named: 'isDeferred'),
          )).thenReturn(DeepLinkModel(screenName: 'screenName', params: <String, String>{}));
      when(() => mockAppState.deepLinkSharedData).thenReturn(DeepLinkSharedData());
      when(() => deepLinkHandler.executeDeepLink(
            deepLink: any(named: 'deepLink'),
            isLoggedIn: any(named: 'isLoggedIn'),
          )).thenAnswer((_) async => true);

      final bool canOpenPage = await EvoActionHandler().handle(
        EvoActionModel(
          type: EvoActionModel.openDeepLink,
          args: EvoArgs(
            link: 'evoappvn://mobile/deeplinking?screen_name=webview&web_link=https://www.goevo.vn',
          ),
        ),
      );

      verify(() => deepLinkHandler.executeDeepLink(
            deepLink: any(named: 'deepLink'),
            isLoggedIn: any(named: 'isLoggedIn'),
          )).called(1);

      expect(canOpenPage, true);
    });
  });

  group('verify refreshTokenExpired() method', () {
    setUp(() {
      when(() => mockEvoLocalStorageHelper.getRefreshToken()).thenAnswer((_) async {
        return Future<String>.value('refresh_token');
      });
    });

    test('verify refresh_token is not expired', () async {
      when(() => mockJwtHelper.isCanUse(any())).thenReturn(true);

      final bool result = await EvoActionHandler().refreshTokenExpired();

      expect(result, false);
    });

    test('verify refresh_token is expired', () async {
      when(() => mockJwtHelper.isCanUse(any())).thenReturn(false);

      final bool result = await EvoActionHandler().refreshTokenExpired();

      expect(result, true);
    });

    test('verify refresh_token is null', () async {
      when(() => mockEvoLocalStorageHelper.getRefreshToken()).thenAnswer((_) async {
        return Future<String?>.value();
      });

      when(() => mockJwtHelper.isCanUse(any())).thenReturn(false);

      final bool result = await EvoActionHandler().refreshTokenExpired();

      expect(result, true);
    });
  });

  group('openDeepLink', () {
    test('should open page with a valid link', () async {
      when(() => deepLinkUtils.isEvoAppDeepLink(any())).thenReturn(true);
      when(() => deepLinkUtils.generateDeepLinkModel(
            deepLinkValue: any(named: 'deepLinkValue'),
            isDeferred: any(named: 'isDeferred'),
          )).thenReturn(DeepLinkModel(screenName: 'screenName', params: <String, String>{}));
      when(() => mockAppState.deepLinkSharedData).thenReturn(DeepLinkSharedData());

      when(() => deepLinkHandler.executeDeepLink(
            deepLink: any(named: 'deepLink'),
            isLoggedIn: any(named: 'isLoggedIn'),
          )).thenAnswer((_) async => true);

      const String link = 'evoappvn://mobile/deeplinking?screen_name=webview';
      final bool canOpenPage = await EvoActionHandler().openDeepLink(link);

      verify(() => deepLinkHandler.executeDeepLink(
            deepLink: any(named: 'deepLink'),
            isLoggedIn: any(named: 'isLoggedIn'),
          )).called(1);
      expect(canOpenPage, true);
    });

    test('should NOT open page with an invalid link', () async {
      when(() => deepLinkUtils.isEvoAppDeepLink(any())).thenReturn(false);
      when(() => mockAppState.deepLinkSharedData).thenReturn(DeepLinkSharedData());

      bool canOpenPage = await EvoActionHandler().openDeepLink(null);
      verifyNever(() => deepLinkHandler.executeDeepLink(
            deepLink: any(named: 'deepLink'),
            isLoggedIn: any(named: 'isLoggedIn'),
          ));
      expect(canOpenPage, false);

      canOpenPage = await EvoActionHandler().openDeepLink('');
      verifyNever(() => deepLinkHandler.executeDeepLink(
            deepLink: any(named: 'deepLink'),
            isLoggedIn: any(named: 'isLoggedIn'),
          ));
      expect(canOpenPage, false);

      canOpenPage = await EvoActionHandler().openDeepLink('evoappvn://invalid_deeplinking');
      verifyNever(() => deepLinkHandler.executeDeepLink(
            deepLink: any(named: 'deepLink'),
            isLoggedIn: any(named: 'isLoggedIn'),
          ));
      expect(canOpenPage, false);
    });
  });
}
