import 'package:app_settings/app_settings.dart';
import 'package:app_settings/app_settings_platform_interface.dart';
import 'package:evoapp/feature/biometric/model/biometric_ui_model.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/app_setting_util.dart';
import 'package:evoapp/util/ui_utils/evo_dialog_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'flutter_test_config.dart';

class MockNavigatorContext extends Mock implements BuildContext {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

class MockAppState extends Mock implements AppState {}

class AppSettingUtilImpl extends Object with AppSettingUtil {}

class MockAppSettingsPlatform extends Mock
    with MockPlatformInterfaceMixin
    implements AppSettingsPlatform {}

void main() {
  group('AppSettingUtil', () {
    late MockEvoUtilFunction mockEvoUtilFunction;
    late MockCommonUtilFunction mockCommonUtilFunction;
    late MockAppState mockAppState;
    late AppSettingsPlatform mockAppSettingsPlatform;

    final AppSettingUtilImpl appSettingUtil = AppSettingUtilImpl();

    final AndroidDeviceInfo androidDeviceInfo28 = AndroidDeviceInfo.fromMap(<String, dynamic>{
      'version': <String, dynamic>{
        'sdkInt': 28,
        'baseOS': 'baseOS',
        'codename': 'codename',
        'incremental': 'incremental',
        'previewSdkInt': 28,
        'release': 'release',
        'securityPatch': 'securityPatch',
      },
      'board': '.board',
      'bootloader': '.bootloader',
      'brand': '.brand',
      'device': '.device',
      'display': '.display',
      'fingerprint': '.fingerprint',
      'hardware': '.hardware',
      'host': '.host',
      'id': '.id',
      'manufacturer': '.manufacturer',
      'model': '.model',
      'product': '.product',
      'supported32BitAbis': <String>[],
      'supported64BitAbis': <String>[],
      'supportedAbis': <String>[],
      'tags': '.tags',
      'type': '.type',
      'isPhysicalDevice': false,
      'systemFeatures': <String>[],
      'serialNumber': 'serialNumber',
      'isLowRamDevice': false,
    });

    final AndroidDeviceInfo androidDeviceInfo29 = AndroidDeviceInfo.fromMap(<String, dynamic>{
      'version': <String, dynamic>{
        'sdkInt': 29,
        'baseOS': 'baseOS',
        'codename': 'codename',
        'incremental': 'incremental',
        'previewSdkInt': 29,
        'release': 'release',
        'securityPatch': 'securityPatch',
      },
      'board': '.board',
      'bootloader': '.bootloader',
      'brand': '.brand',
      'device': '.device',
      'display': '.display',
      'fingerprint': '.fingerprint',
      'hardware': '.hardware',
      'host': '.host',
      'id': '.id',
      'manufacturer': '.manufacturer',
      'model': '.model',
      'product': '.product',
      'supported32BitAbis': <String>[],
      'supported64BitAbis': <String>[],
      'supportedAbis': <String>[],
      'tags': '.tags',
      'type': '.type',
      'isPhysicalDevice': false,
      'systemFeatures': <String>[],
      'serialNumber': 'serialNumber',
      'isLowRamDevice': false,
    });

    setUpAll(() {
      registerFallbackValue(AppSettingsType.nfc);
      registerFallbackValue(AppSettingsPanelType.nfc);
      registerFallbackValue(EvoDialogId.askGoToDeviceSecuritySettingBottomSheet);

      mockAppSettingsPlatform = MockAppSettingsPlatform();
      AppSettingsPlatform.instance = mockAppSettingsPlatform;

      when(() => mockAppSettingsPlatform.openAppSettings(
          asAnotherTask: any(named: 'asAnotherTask'), type: any(named: 'type'))).thenAnswer(
        (_) => Future<void>.value(),
      );
      when(
        () => mockAppSettingsPlatform.openAppSettingsPanel(any()),
      ).thenAnswer((_) => Future<void>.value());

      getItRegisterTextStyle();
      getItRegisterColor();

      mockEvoUtilFunction = MockEvoUtilFunction();
      mockCommonUtilFunction = MockCommonUtilFunction();
      mockAppState = MockAppState();

      getIt.registerSingleton<AppState>(mockAppState);

      setUtilsMockInstanceForTesting();
      getIt.registerSingleton<CommonUtilFunction>(mockCommonUtilFunction);
    });

    tearDown(() {
      reset(mockEvoUtilFunction);
      reset(mockCommonUtilFunction);
      reset(mockAppState);
    });

    tearDownAll(() {
      resetUtilMockToOriginalInstance();
      getIt.reset();
    });

    test('static values', () {
      expect(AppSettingUtil.androidQSdkVersion, 29);
    });

    test('showPopupAskGoToDeviceSecuritySetting calls showDialogBottomSheet', () async {
      // Arrange
      final BiometricTypeUIModel bioTypeInfo = BiometricTypeUIModel.finger();
      when(() => mockAppState.bioTypeInfo).thenReturn(bioTypeInfo);

      when(
        () => EvoDialogHelper().showDialogBottomSheet(
          dialogId: any(named: 'dialogId'),
          title: any(named: 'title'),
          content: any(named: 'content'),
          footer: any(named: 'footer'),
          textPositive: any(named: 'textPositive'),
          textNegative: any(named: 'textNegative'),
          onClickPositive: any(named: 'onClickPositive'),
          onClickNegative: any(named: 'onClickNegative'),
        ),
      ).thenAnswer((_) async {});

      // Act
      await appSettingUtil.showPopupAskGoToDeviceSecuritySetting(
        title: 'Test Title',
        description: 'Test Description',
        footer: const Text('Test Footer'),
      );

      // Assert
      verify(
        () => EvoDialogHelper().showDialogBottomSheet(
          dialogId: EvoDialogId.askGoToDeviceSecuritySettingBottomSheet,
          title: 'Test Title',
          content: 'Test Description',
          footer: any(named: 'footer'),
          textPositive: EvoStrings.settingTitle,
          textNegative: EvoStrings.ignoreTitle,
          onClickPositive: any(named: 'onClickPositive'),
          onClickNegative: any(named: 'onClickNegative'),
        ),
      ).called(1);
    });

    group('getDefaultFooterSecuritySettingPopup', () {
      testWidgets('returns a RichText widget with expected text', (WidgetTester tester) async {
        // Mock
        final BiometricTypeUIModel bioTypeInfo = BiometricTypeUIModel.finger();
        when(() => mockAppState.bioTypeInfo).thenReturn(bioTypeInfo);

        // Act
        final Widget widget = appSettingUtil.getDefaultFooterSecuritySettingPopup();

        // Build the widget
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(body: widget),
          ),
        );

        final RichText richText = tester.widget<RichText>(find.byType(RichText));
        final TextSpan textSpan = richText.text as TextSpan;

        // Expect
        expect(
          textSpan.text,
          EvoStrings.noSetupFaceFingerIdDescPart1.replaceVariableByValue(
            <String>[bioTypeInfo.biometricTypeName],
          ),
        );
        expect(textSpan.children![0].toPlainText(), EvoStrings.setting);
        expect(textSpan.children![1].toPlainText(), EvoStrings.noSetupFaceFingerIdDescPart2);
      });
    });

    group('openAndroidNfcSetting', () {
      test('openAndroidNfcSetting opens panel setting for sdk >= 29', () async {
        // Arrange
        when(() => mockCommonUtilFunction.getAndroidInfo()).thenAnswer(
          (_) async => androidDeviceInfo29,
        );

        // Act
        await appSettingUtil.openAndroidNfcSetting();

        // Assert
        verify(
          () => mockAppSettingsPlatform.openAppSettingsPanel(AppSettingsPanelType.nfc),
        ).called(1);
      });

      test('openAndroidNfcSetting opens regular setting for sdk < 29', () async {
        // Arrange
        when(() => mockCommonUtilFunction.getAndroidInfo()).thenAnswer(
          (_) async => androidDeviceInfo28,
        );

        // Act
        await appSettingUtil.openAndroidNfcSetting();

        // Assert
        verify(
          () => mockAppSettingsPlatform.openAppSettings(
            type: AppSettingsType.nfc,
            asAnotherTask: any(named: 'asAnotherTask'),
          ),
        ).called(1);
      });
    });
  });
}
