import 'package:evoapp/util/mock_file_name_utils/mock_campaign_file_name.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Offers Mock File Names', () {
    test('Get success mock file name', () {
      final String fileName = getOffersMockFileName(mockCase: OffersMockFileName.success);
      expect(fileName, 'campaigns_information.json');
    });

    test('Get earn voucher mock file name', () {
      final String fileName = getOffersMockFileName(mockCase: OffersMockFileName.earnVoucher);
      expect(fileName, 'campaigns_information_earn_voucher.json');
    });
  });

  group('Vouchers Mock File Name', () {
    test('Get vouchers mock file name', () {
      final String fileName = getVouchersMockFileName();
      expect(fileName, 'my_vouchers.json');
    });
  });

  group('Qualification Voucher Mock File Name', () {
    test('Get qualification voucher mock file name', () {
      final String fileName = getQualificationVoucherMockFileName();
      expect(fileName, 'payment_promotions.json');
    });
  });

  group('Referral Campaign Mock File Name', () {
    test('Get referral campaign mock file name', () {
      final String fileName = getReferralCampaignMockFileName();
      expect(fileName, 'referral_campaigns_information.json');
    });
  });
}
