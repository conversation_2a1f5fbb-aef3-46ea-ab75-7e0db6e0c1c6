import 'package:evoapp/data/request/dop_native/dop_native_log_event_data_request.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeLogEventDataRequest toJson', () {
    test('converts to JSON correctly with all fields', () {
      final DOPNativeLogEventPayload payload = DOPNativeLogEventPayload(
        type: 'testType',
        leadSource: 'testSource',
        phoneNumber: '123456789',
        existingUniqueToken: 'token',
      );

      final DOPNativeLogEventDataRequest requestData = DOPNativeLogEventDataRequest(
        eventType: 'testEvent',
        token: 'testToken',
        data: payload,
      );

      final Map<String, dynamic>? json = requestData.toJson();

      expect(json, <String, dynamic>{
        'event_type': 'testEvent',
        'token': 'testToken',
        'data': <String, dynamic>{
          'type': 'testType',
          'lead_source': 'testSource',
          'phone_number': '123456789',
          'existing_unique_token': 'token'
        },
      });
    });

    test('converts to JSON correctly with null data', () {
      final DOPNativeLogEventDataRequest requestData = DOPNativeLogEventDataRequest(
        eventType: 'testEvent',
        token: 'testToken',
      );

      final Map<String, dynamic>? json = requestData.toJson();

      expect(json, <String, dynamic>{
        'event_type': 'testEvent',
        'token': 'testToken',
      });
    });

    test('converts to JSON correctly with null fields', () {
      final DOPNativeLogEventDataRequest requestData = DOPNativeLogEventDataRequest();

      final Map<String, dynamic>? json = requestData.toJson();

      expect(json, isNull);
    });
  });
}
