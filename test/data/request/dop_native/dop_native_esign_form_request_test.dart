import 'package:evoapp/data/request/dop_native/dop_native_esign_form_request.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const LinkCardToEvo cardLinkToEvo = LinkCardToEvo(linkToken: true);

  const DOPNativeESignFormData eSignFormData = DOPNativeESignFormData(
    isUSNationality: true,
    isBornInUS: true,
    isInstructDepositToOrWithdrawFromUSAddress: true,
    isHaveUSAddressOrUSNumber: true,
    isDelegateToUSAddress: true,
    isHaveUniqueUSMailAddress: true,
    payment: cardLinkToEvo,
  );

  group('DOPNativeESignFormRequest', () {
    test('toJson should return correct map with all properties', () {
      const DOPNativeESignFormRequest request = DOPNativeESignFormRequest(
        changeState: true,
        formStep: 'step',
        formData: eSignFormData,
      );

      final Map<String, dynamic>? json = request.toJson();

      expect(json, <String, dynamic>{
        'change_state': true,
        'form_step': 'step',
        'form_data': <String, dynamic>{
          'isUSNationality': true,
          'isBornInUS': true,
          'isInstructDepositToOrWithdrawFromUSAddress': true,
          'isHaveUSAddressOrUSNumber': true,
          'isDelegateToUSAddress': true,
          'isHaveUniqueUSMailAddress': true,
          'payment': <String, bool>{
            'link_token': true,
          }
        },
      });
    });

    test('toJson should return correct map with null properties of formData', () {
      const DOPNativeESignFormRequest request = DOPNativeESignFormRequest(
        changeState: true,
        formStep: 'step',
        formData: DOPNativeESignFormData(
          isUSNationality: true,
          isBornInUS: true,
          isInstructDepositToOrWithdrawFromUSAddress: true,
        ),
      );

      final Map<String, dynamic>? json = request.toJson();

      expect(json, <String, dynamic>{
        'change_state': true,
        'form_step': 'step',
        'form_data': <String, dynamic>{
          'isUSNationality': true,
          'isBornInUS': true,
          'isInstructDepositToOrWithdrawFromUSAddress': true,
        },
      });
    });
  });

  group('DOPNativeESignFormData', () {
    test('toJson should return correct map with all properties', () {
      const DOPNativeESignFormData request = eSignFormData;

      final Map<String, dynamic>? json = request.toJson();

      expect(json, <String, dynamic>{
        'isUSNationality': true,
        'isBornInUS': true,
        'isInstructDepositToOrWithdrawFromUSAddress': true,
        'isHaveUSAddressOrUSNumber': true,
        'isDelegateToUSAddress': true,
        'isHaveUniqueUSMailAddress': true,
        'payment': <String, bool>{
          'link_token': true,
        }
      });
    });

    test('toJson should return correct map with have null properties', () {
      const DOPNativeESignFormData request = DOPNativeESignFormData(
        isUSNationality: true,
        isBornInUS: true,
        isInstructDepositToOrWithdrawFromUSAddress: true,
      );

      final Map<String, dynamic>? json = request.toJson();

      expect(json, <String, dynamic>{
        'isUSNationality': true,
        'isBornInUS': true,
        'isInstructDepositToOrWithdrawFromUSAddress': true,
      });
    });
  });
}
