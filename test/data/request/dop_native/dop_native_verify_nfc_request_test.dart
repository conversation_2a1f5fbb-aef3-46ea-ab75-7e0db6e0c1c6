import 'package:evoapp/data/request/dop_native/dop_native_verify_nfc_request.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeSod = 'fake_sod';
  const String fakeDg1 = 'fake_dg1';
  const String fakeDg13 = 'some_dg13';
  const String fakeCCCD = 'some_cccd';

  group('DOPNativeVerifyNFCRequest tests', () {
    test('toJson should return a non-null map with non-empty fields', () {
      const DOPNativeVerifyNFCRequest request = DOPNativeVerifyNFCRequest(
        sod: fakeSod,
        dg1: fakeDg1,
      );

      final Map<String, dynamic>? json = request.toJson();

      expect(json, isNotNull);
      expect(json?['sod'], fakeSod);
      expect(json?['dg1'], fakeDg1);
    });

    test('toJson should return null if all fields are null or empty', () {
      const DOPNativeVerifyNFCRequest request = DOPNativeVerifyNFCRequest(sod: null);

      final Map<String, dynamic>? json = request.toJson();

      expect(json, isNull);
    });

    test('toJson should handle conditional fields correctly', () {
      const DOPNativeVerifyNFCRequest requestWithDg13 = DOPNativeVerifyNFCRequest(
        sod: fakeSod,
        dg13: fakeDg13,
      );

      final Map<String, dynamic>? jsonWithDg13 = requestWithDg13.toJson();
      expect(jsonWithDg13?['dg13'], fakeDg13);
      expect(jsonWithDg13?['cccd'], null);
    });

    test('toJson should handle conditional field cccd correctly', () {
      const DOPNativeVerifyNFCRequest requestWithCccd = DOPNativeVerifyNFCRequest(
        sod: fakeSod,
        cccd: fakeCCCD,
      );

      final Map<String, dynamic>? jsonWithCccd = requestWithCccd.toJson();
      expect(jsonWithCccd?['dg13'], null);
      expect(jsonWithCccd?['cccd'], fakeCCCD);
    });
  });
}
