import 'package:evoapp/data/request/voucher_request.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('VoucherRequest', () {
    test('toJson returns correct map with provided values', () {
      final VoucherRequest request = VoucherRequest(
        perPage: 20,
        pageId: 1,
        status: 'active',
      );

      final Map<String, dynamic> json = request.toJson();

      expect(json, <String, dynamic>{
        'page_id': 1,
        'per_page': 20,
        'status': 'active',
      });
    });

    test('to<PERSON><PERSON> handles null values', () {
      final VoucherRequest request = VoucherRequest(
        perPage: 20,
      );

      final Map<String, dynamic> json = request.toJson();

      expect(json, <String, dynamic>{
        'page_id': null,
        'per_page': 20,
        'status': null,
      });
    });
  });
}
