import 'package:evoapp/data/request/face_auth_request.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FaceAuthRequest', () {
    test('toJson returns correct map when all fields are non-null', () {
      final FaceAuthRequest request = FaceAuthRequest(
        imageIds: <String>['image1', 'image2'],
        videoIds: <String>['video1'],
        selfieType: 'front',
      );

      final Map<String, dynamic> json = request.toJson();

      expect(
          json,
          equals(<String, Object>{
            'face_image_ids': <String>['image1', 'image2'],
            'video_ids': <String>['video1'],
            'selfie_type': 'front',
          }));
    });

    test('toJson omits null fields', () {
      final FaceAuthRequest request = FaceAuthRequest(
        imageIds: null,
        videoIds: <String>['video1'],
        selfieType: null,
      );

      final Map<String, dynamic> json = request.toJson();

      expect(
          json,
          equals(<String, List<String>>{
            'video_ids': <String>['video1'],
          }));
    });

    test('toJ<PERSON> returns empty map when all fields are null', () {
      final FaceAuthRequest request = FaceAuthRequest(
        imageIds: null,
        videoIds: null,
        selfieType: null,
      );

      final Map<String, dynamic> json = request.toJson();

      expect(json, equals(<String, dynamic>{}));
    });

    test('toJson handles empty lists', () {
      final FaceAuthRequest request = FaceAuthRequest(
        imageIds: <String>[],
        videoIds: <String>[],
        selfieType: 'profile',
      );

      final Map<String, dynamic> json = request.toJson();

      expect(
          json,
          equals(<String, Object>{
            'face_image_ids': <String>[],
            'video_ids': <String>[],
            'selfie_type': 'profile',
          }));
    });
  });
}
