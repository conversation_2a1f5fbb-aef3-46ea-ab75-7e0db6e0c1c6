import 'package:evoapp/data/request/reward_request.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AnnouncementRequest', () {
    test('toJ<PERSON> returns a properly formatted map', () {
      // Arrange
      final AnnouncementRequest announcementRequest = AnnouncementRequest(
        nextCursor: 'cursor123',
        limit: 10,
        status: 'active',
      );

      // Act
      final Map<String, dynamic> json = announcementRequest.toJson();

      // Assert
      expect(json, <String, dynamic>{
        'next_cursor': 'cursor123',
        'limit': 10,
        'status': 'active',
      });
    });

    test('to<PERSON><PERSON> handles empty nextCursor', () {
      // Arrange
      final AnnouncementRequest announcementRequest = AnnouncementRequest(
        nextCursor: '',
        limit: 10,
        status: 'active',
      );

      // Act
      final Map<String, dynamic> json = announcementRequest.toJson();

      // Assert
      expect(json, <String, dynamic>{
        'next_cursor': '',
        'limit': 10,
        'status': 'active',
      });
    });
  });
}
