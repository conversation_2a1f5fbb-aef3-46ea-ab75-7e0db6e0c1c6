import 'package:evoapp/data/request/payment_promotion_request.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PaymentPromotionRequest', () {
    test('to<PERSON><PERSON> returns correct map', () {
      final PaymentPromotionRequest request = PaymentPromotionRequest(
        sessionId: 'test_session',
        pageId: 1,
        perPage: 10,
      );

      final Map<String, dynamic> json = request.toJson();

      expect(json, <String, dynamic>{
        'page_id': 1,
        'per_page': 10,
        'session_id': 'test_session',
      });
    });

    test('to<PERSON><PERSON> handles null values', () {
      final PaymentPromotionRequest request = PaymentPromotionRequest(
        sessionId: 'test_session',
      );

      final Map<String, dynamic> json = request.toJson();

      expect(json, <String, dynamic>{
        'page_id': null,
        'per_page': null,
        'session_id': 'test_session',
      });
    });
  });
}
