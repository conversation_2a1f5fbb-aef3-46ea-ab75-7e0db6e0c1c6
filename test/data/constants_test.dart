import 'package:evoapp/data/constants.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('HeaderKey constants have the expected values', () {
    expect(HeaderKey.sessionToken, 'X-SESSION');
    expect(HeaderKey.idempotencyKey, 'Idempotency-key');
    expect(HeaderKey.deviceToken, 'x-device-token');
    expect(HeaderKey.authorization, 'Authorization');

    /// DOP Native
    expect(HeaderKey.dopNativeAuthorization, 'Dop-Authorization');
  });

  test('Test FileExtensions', () {
    expect(FileExtensions.pdfExtension, '.pdf');
  });

  test('Test Duration Constants', () {
    expect(DurationConstants.resumeUserDropOffInMinutes, 5);
    expect(DurationConstants.facialVerificationTimeoutInSeconds, 30);
  });
}
