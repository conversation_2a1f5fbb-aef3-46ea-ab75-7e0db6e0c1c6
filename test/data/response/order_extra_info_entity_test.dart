// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/data/response/order_extra_info_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('OrderSessionExtraInfoEntity', () {
    test('initialization with parameters', () {
      const bool enableRatingPrompt = true;
      const bool autoApplyVoucher = true;

      final OrderExtraInfoEntity entity = OrderExtraInfoEntity(
        enableRatingPrompt: enableRatingPrompt,
        autoApplyVoucher: autoApplyVoucher,
      );

      expect(entity.enableRatingPrompt, enableRatingPrompt);
      expect(entity.autoApplyVoucher, autoApplyVoucher);
    });

    test('initialization with null parameters', () {
      final OrderExtraInfoEntity entity = OrderExtraInfoEntity();

      expect(entity.enableRatingPrompt, isNull);
      expect(entity.autoApplyVoucher, isNull);
    });

    test('fromJson with all fields', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'enable_rating_prompt': true,
        'auto_apply_voucher': true,
      };

      final OrderExtraInfoEntity entity = OrderExtraInfoEntity.fromJson(json);

      expect(entity.enableRatingPrompt, true);
      expect(entity.autoApplyVoucher, true);
    });

    test('fromJson with null fields', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'enable_rating_prompt': null,
        'auto_apply_voucher': null,
      };

      final OrderExtraInfoEntity entity = OrderExtraInfoEntity.fromJson(json);

      expect(entity.enableRatingPrompt, isNull);
      expect(entity.autoApplyVoucher, isNull);
    });

    test('fromJson with missing fields', () {
      final Map<String, dynamic> json = <String, dynamic>{};

      final OrderExtraInfoEntity entity = OrderExtraInfoEntity.fromJson(json);

      expect(entity.enableRatingPrompt, isNull);
      expect(entity.autoApplyVoucher, isNull);
    });

    test('toJson with all fields', () {
      final OrderExtraInfoEntity entity = OrderExtraInfoEntity(
        enableRatingPrompt: true,
        autoApplyVoucher: true,
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json['enable_rating_prompt'], true);
      expect(json['auto_apply_voucher'], true);
    });

    test('toJson with null fields', () {
      final OrderExtraInfoEntity entity = OrderExtraInfoEntity();

      final Map<String, dynamic> json = entity.toJson();

      expect(json['enable_rating_prompt'], isNull);
      expect(json['auto_apply_voucher'], isNull);
    });
  });
}
