import 'package:evoapp/data/response/store_info_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('StoreInfoEntity', () {
    const String expectAddress = 'Example Address';
    const String expectBanner = 'Sample Banner';
    const String expectId = 'Sample ID';
    const String expectLargeBanner = 'Sample Large Banner';
    const String expectMerchantId = 'Sample Merchant ID';
    const String expectMerchantName = 'Sample Merchant Name';
    const String expectStatus = 'Sample Status';
    const String expectThumbnail = 'Sample Thumbnail';
    const String expectName = 'Sample Name';

    test('fromJson should return a valid StoreInfoEntity', () {
      const int minOrderAmount = 10000;
      const int maxOrderAmount = 5000000;

      final Map<String, dynamic> json = <String, dynamic>{
        'address': expectAddress,
        'banner': expectBanner,
        'id': expectId,
        'large_banner': expectLargeBanner,
        'merchant_id': expectMerchantId,
        'merchant_name': expectMerchantName,
        'status': expectStatus,
        'thumbnail': expectThumbnail,
        'name': expectName,
        'payment': <String, dynamic>{
          'min_order_amount': minOrderAmount,
          'max_order_amount': maxOrderAmount,
        },
      };

      final StoreInfoEntity result = StoreInfoEntity.fromJson(json);

      expect(result.address, expectAddress);
      expect(result.banner, expectBanner);
      expect(result.id, expectId);
      expect(result.largeBanner, expectLargeBanner);
      expect(result.merchantId, expectMerchantId);
      expect(result.merchantName, expectMerchantName);
      expect(result.status, expectStatus);
      expect(result.thumbnail, expectThumbnail);
      expect(result.name, expectName);
      expect(result.payment, isNotNull);
      expect(result.payment?.minOrderAmount, minOrderAmount);
      expect(result.payment?.maxOrderAmount, maxOrderAmount);
    });

    test('toJson should return a valid JSON representation', () {
      const int minOrderAmount = 10000;
      const int maxOrderAmount = 5000000;

      final StoreInfoEntity storeInfoEntity = StoreInfoEntity(
        address: expectAddress,
        banner: expectBanner,
        id: expectId,
        largeBanner: expectLargeBanner,
        merchantId: expectMerchantId,
        merchantName: expectMerchantName,
        status: expectStatus,
        thumbnail: expectThumbnail,
        name: expectName,
        payment: PaymentConfigEntity(
          minOrderAmount: minOrderAmount,
          maxOrderAmount: maxOrderAmount,
        ),
      );

      final Map<String, dynamic> json = storeInfoEntity.toJson();

      expect(json['address'], expectAddress);
      expect(json['banner'], expectBanner);
      expect(json['id'], expectId);
      expect(json['large_banner'], expectLargeBanner);
      expect(json['merchant_id'], expectMerchantId);
      expect(json['merchant_name'], expectMerchantName);
      expect(json['status'], expectStatus);
      expect(json['thumbnail'], expectThumbnail);
      expect(json['name'], expectName);
      expect(json['payment']['min_order_amount'], minOrderAmount);
      expect(json['payment']['max_order_amount'], maxOrderAmount);
    });
  });

  group('PaymentConfigEntity', () {
    const int expectMinOrderAmount = 10000;
    const int expectMaxOrderAmount = 5000000;

    test('constructor should initialize properties correctly', () {
      final PaymentConfigEntity paymentConfig = PaymentConfigEntity(
        minOrderAmount: expectMinOrderAmount,
        maxOrderAmount: expectMaxOrderAmount,
      );

      expect(paymentConfig.minOrderAmount, expectMinOrderAmount);
      expect(paymentConfig.maxOrderAmount, expectMaxOrderAmount);
    });

    test('fromJson should return a valid PaymentConfigEntity', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'min_order_amount': expectMinOrderAmount,
        'max_order_amount': expectMaxOrderAmount,
      };

      final PaymentConfigEntity result = PaymentConfigEntity.fromJson(json);

      expect(result.minOrderAmount, expectMinOrderAmount);
      expect(result.maxOrderAmount, expectMaxOrderAmount);
    });

    test('toJson should return a valid JSON representation', () {
      final PaymentConfigEntity paymentConfig = PaymentConfigEntity(
        minOrderAmount: expectMinOrderAmount,
        maxOrderAmount: expectMaxOrderAmount,
      );

      final Map<String, dynamic> json = paymentConfig.toJson();

      expect(json['min_order_amount'], expectMinOrderAmount);
      expect(json['max_order_amount'], expectMaxOrderAmount);
    });

    test('should handle null values correctly', () {
      final PaymentConfigEntity paymentConfig = PaymentConfigEntity();

      expect(paymentConfig.minOrderAmount, isNull);
      expect(paymentConfig.maxOrderAmount, isNull);

      final Map<String, dynamic> json = paymentConfig.toJson();

      expect(json['min_order_amount'], isNull);
      expect(json['max_order_amount'], isNull);
    });
  });
}
