import 'package:evoapp/data/response/dop_native/dop_native_process_id_card_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Test fromJson/toJson', () {
    final BaseResponse baseResponse = BaseResponse(
      statusCode: CommonHttpClient.SUCCESS,
      response: <String, dynamic>{
        'data': <String, dynamic>{
          'job_id': '12345',
        }
      },
    );
    final DOPNativeProcessIDCardEntity checkIDCardEntity =
        DOPNativeProcessIDCardEntity.fromBaseResponse(baseResponse);

    expect(checkIDCardEntity.jobId, '12345');

    final Map<String, dynamic> jsonMap = checkIDCardEntity.toJson();

    expect(jsonMap['job_id'], '12345');
  });

  test('Test unserializable', () {
    final DOPNativeProcessIDCardEntity checkIDCardEntity =
        DOPNativeProcessIDCardEntity.unserializable();

    expect(checkIDCardEntity.jobId, null);
    expect(checkIDCardEntity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
  });
}
