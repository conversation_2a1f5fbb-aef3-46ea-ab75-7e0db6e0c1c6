import 'package:evoapp/data/response/dop_native/dop_native_contact_info_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_ocr_base_data_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_ocr_data_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_personal_info_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeOCRBaseDataEntity', () {
    test('should handle unserializable data', () {
      final DOPNativeOCRBaseDataEntity entity = DOPNativeOCRBaseDataEntity.unserializable();

      expect(entity.ocr, isNull);
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('fromBaseResponse should initialize fields correctly with non-null ocr data', () async {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'ocr': <String, dynamic>{
              'contactInfo': <String, dynamic>{
                'familyAddress': 'A10 05 C/C 262/15 17 LBBÍCH',
                'familyBookAddressDistId': '767',
                'familyBookAddressProvinceId': '79',
                'familyBookAddressWardId': '27034'
              },
              'personalInfo': <String, dynamic>{
                'birthday': '1989-01-01',
                'fullName': 'PHAN THỊ AN',
                'gender': '2',
                'idCard': '011111123456',
                'idIssueDate': '2022-07-08',
                'idIssuePlaceId': '998',
                'oldIDCard': '123456789'
              }
            }
          },
          'message': 'get ocr data successfully',
          'time': '2024-03-25T15:44:53+07:00',
          'verdict': 'success'
        },
      );

      final DOPNativeOCRBaseDataEntity entity = DOPNativeOCRBaseDataEntity.fromBaseResponse(
        baseResponse,
      );

      expect(entity.ocr, isNotNull);
      expect(entity.ocr?.contactInfo, isNotNull);
      expect(entity.ocr?.personalInfo, isNotNull);
    });

    test('fromBaseResponse should initialize fields correctly with null ocr data', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.BAD_REQUEST,
        response: null,
      );

      final DOPNativeOCRBaseDataEntity entity =
          DOPNativeOCRBaseDataEntity.fromBaseResponse(baseResponse);

      expect(entity.ocr, null);
    });

    test('toJson should convert object to json correctly', () {
      final DOPNativeOCRDataEntity ocrDataEntity = DOPNativeOCRDataEntity(
        contactInfo: const DOPNativeContactInfoEntity(
          familyAddress: '123 Main St',
          familyBookAddressDistId: 'dist1',
          familyBookAddressProvinceId: 'prov1',
          familyBookAddressWardId: 'ward1',
        ),
        personalInfo: const DOPNativePersonalInfoEntity(
          birthday: '1989-01-01',
          fullName: 'PHAN THỊ AN',
        ),
      );

      final DOPNativeOCRBaseDataEntity entity = DOPNativeOCRBaseDataEntity(ocr: ocrDataEntity);

      final Map<String, dynamic> json = entity.toJson();

      expect(json['ocr'], isNotNull);
      expect(json['ocr']['contactInfo'], isNotNull);
      expect(json['ocr']['personalInfo'], isNotNull);
    });

    test('toJson should handle null ocr values correctly', () {
      final DOPNativeOCRBaseDataEntity entity = DOPNativeOCRBaseDataEntity();

      final Map<String, dynamic> json = entity.toJson();

      expect(json['ocr'], isNull);
    });
  });
}
