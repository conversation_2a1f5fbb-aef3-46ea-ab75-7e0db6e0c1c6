import 'package:evoapp/data/response/dop_native/dop_native_upload_image_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeUploadImageEntity', () {
    test('toJson should return a map with the correct imageId', () {
      const String imageId = 'test-image-id';
      final DOPNativeUploadImageEntity entity = DOPNativeUploadImageEntity(imageId: imageId);

      final Map<String, dynamic> json = entity.toJson();

      expect(json, containsPair('image_id', imageId));
    });

    test('toJson should handle null imageId correctly', () {
      final DOPNativeUploadImageEntity entity = DOPNativeUploadImageEntity();

      final Map<String, dynamic> json = entity.toJson();

      expect(json.containsKey('image_id'), true);
      expect(json['image_id'], null);
    });

    test(
        'unserializable constructor should create an entity with null imageId and correct localExceptionCode',
        () {
      final DOPNativeUploadImageEntity entity = DOPNativeUploadImageEntity.unserializable();

      expect(entity.imageId, null);
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('fromBaseResponse constructor should extract imageId from the base response', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{'image_id': 'base-response-image-id'}
        },
      );

      final DOPNativeUploadImageEntity entity =
          DOPNativeUploadImageEntity.fromBaseResponse(baseResponse);

      expect(entity.imageId, 'base-response-image-id');
    });
  });
}
