import 'package:evoapp/data/response/dop_native/dop_native_upload_face_id_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeUploadFaceOtpIdEntity', () {
    test('should create an instance with given values', () {
      final DOPNativeUploadFaceOtpIdEntity entity = DOPNativeUploadFaceOtpIdEntity(
        imageId: '123',
        signature: 'abc',
      );

      expect(entity.imageId, '123');
      expect(entity.signature, 'abc');
    });

    test('should create an unserializable instance', () {
      final DOPNativeUploadFaceOtpIdEntity entity = DOPNativeUploadFaceOtpIdEntity.unserializable();

      expect(entity.imageId, isNull);
      expect(entity.signature, isNull);
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('should create an instance from BaseResponse', () {
      final BaseResponse response = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'image_id': '456',
            'signature': 'def',
          },
        },
      );

      final DOPNativeUploadFaceOtpIdEntity entity =
          DOPNativeUploadFaceOtpIdEntity.fromBaseResponse(response);

      expect(entity.imageId, '456');
      expect(entity.signature, 'def');
    });

    test('should convert to JSON correctly', () {
      final DOPNativeUploadFaceOtpIdEntity entity = DOPNativeUploadFaceOtpIdEntity(
        imageId: '789',
        signature: 'ghi',
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json['image_id'], '789');
      expect(json['signature'], 'ghi');
    });
  });
}
