import 'package:evoapp/data/response/dop_native/dop_native_contact_info_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_form_data_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_personal_info_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeFormDataEntity', () {
    test('fromJson should properly construct entity from JSON', () {
      final Map<String, dynamic> jsonMap = <String, dynamic>{
        'cardInfo': <String, dynamic>{'cardCategory': 'Debit'},
        'cifInfo': <String, dynamic>{'idCard': '1234567890', 'useNewCif': true},
        'referenceInfo': <String, dynamic>{'referencePhone1': '0123', 'referencePhone2': '4567'},
        'salemanInfo': <String, dynamic>{'salemanID': '0123', 'salemanIDConfirm': '0123'},
      };

      final DOPNativeFormDataEntity formDataEntity = DOPNativeFormDataEntity.fromJson(jsonMap);

      expect(formDataEntity.cardInfo?.cardCategory, 'Debit');
      expect(formDataEntity.cifInfo?.idCard, '1234567890');
      expect(formDataEntity.cifInfo?.useNewCif, true);
      expect(formDataEntity.referenceInfo?.referencePhone1, '0123');
      expect(formDataEntity.referenceInfo?.referencePhone2, '4567');
      expect(formDataEntity.salesmanInfo?.salesmanID, '0123');
      expect(formDataEntity.salesmanInfo?.salesmanIDConfirm, '0123');
    });

    test('fromJson should handle null values for nested entities', () {
      final Map<String, dynamic> jsonMap = <String, dynamic>{
        'cardInfo': null,
        'cifInfo': null,
        'referenceInfo': null,
        'salemanInfo': null,
      };

      final DOPNativeFormDataEntity formDataEntity = DOPNativeFormDataEntity.fromJson(jsonMap);

      expect(formDataEntity.cardInfo, null);
      expect(formDataEntity.cifInfo, null);
      expect(formDataEntity.personalInfo, null);
      expect(formDataEntity.salesmanInfo, null);
    });

    test('copyWith should return correctly', () {
      final DOPNativeFormDataEntity entity = DOPNativeFormDataEntity(
        personalInfo: const DOPNativePersonalInfoEntity(fullName: 'a'),
        contactInfo: const DOPNativeContactInfoEntity(),
      );

      final DOPNativeFormDataEntity entity1 = entity.copyWith(
        personalInfo: const DOPNativePersonalInfoEntity(fullName: 'b'),
      );

      expect(entity.contactInfo, entity1.contactInfo);
      expect(entity.personalInfo == entity1.personalInfo, false);
    });
  });
}
