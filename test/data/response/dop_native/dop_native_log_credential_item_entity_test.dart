import 'package:evoapp/data/response/dop_native/dop_native_log_credential_item_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeLogCredentialItemEntity', () {
    test('constructor sets properties correctly', () {
      const String accessToken = 'access_token_value';
      const String expireAt = 'expire_at_value';
      const String secret = 'secret_value';

      final DOPNativeLogCredentialItemEntity credentialItem = DOPNativeLogCredentialItemEntity(
        accessToken: accessToken,
        expireAt: expireAt,
        secret: secret,
      );

      expect(credentialItem.accessToken, accessToken);
      expect(credentialItem.expireAt, expireAt);
      expect(credentialItem.secret, secret);
    });

    test('fromJson constructor creates an instance from JSON map', () {
      final Map<String, dynamic> jsonMap = <String, dynamic>{
        'access_token': 'access_token_value',
        'expire_at': 'expire_at_value',
        'secret': 'secret_value',
      };

      final DOPNativeLogCredentialItemEntity credentialItem =
          DOPNativeLogCredentialItemEntity.fromJson(jsonMap);

      expect(credentialItem.accessToken, jsonMap['access_token']);
      expect(credentialItem.expireAt, jsonMap['expire_at']);
      expect(credentialItem.secret, jsonMap['secret']);
    });

    test('toJson method serializes to JSON map correctly', () {
      const String accessToken = 'access_token_value';
      const String expireAt = 'expire_at_value';
      const String secret = 'secret_value';

      final DOPNativeLogCredentialItemEntity credentialItem = DOPNativeLogCredentialItemEntity(
        accessToken: accessToken,
        expireAt: expireAt,
        secret: secret,
      );

      final Map<String, dynamic> jsonMap = credentialItem.toJson();

      expect(jsonMap['access_token'], accessToken);
      expect(jsonMap['expire_at'], expireAt);
      expect(jsonMap['secret'], secret);
    });
  });
}
