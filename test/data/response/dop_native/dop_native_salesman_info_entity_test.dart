import 'package:evoapp/data/response/dop_native/dop_native_salesman_info_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DOPNativeSalesmanInfoEntity', () {
    test('fromJson should initialize fields correctly', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'salemanID': 'staff_code',
        'salemanIDConfirm': 'staff_code_confirm',
      };

      final DOPNativeSalesmanInfoEntity entity = DOPNativeSalesmanInfoEntity.fromJson(json);

      expect(entity.salesmanID, json['salemanID']);
      expect(entity.salesmanIDConfirm, json['salemanIDConfirm']);
    });

    test('toJson should convert object to json correctly', () {
      const DOPNativeSalesmanInfoEntity entity = DOPNativeSalesmanInfoEntity(
        salesmanID: 'staff_code',
        salesmanIDConfirm: 'staff_code_confirm',
      );

      final Map<String, dynamic>? json = entity.toJson();

      expect(json?['salemanID'], 'staff_code');
      expect(json?['salemanIDConfirm'], 'staff_code_confirm');
    });

    test('toJson ignore null field', () {
      const DOPNativeSalesmanInfoEntity entity = DOPNativeSalesmanInfoEntity(
        salesmanID: 'salemanID',
      );

      final Map<String, dynamic>? json = entity.toJson();
      expect(json, isNot(contains('salemanIDConfirm')));
    });

    test('toJson return null if all field are null', () {
      const DOPNativeSalesmanInfoEntity entity = DOPNativeSalesmanInfoEntity();

      final Map<String, dynamic>? json = entity.toJson();
      expect(json, isNull);
    });
  });
}
