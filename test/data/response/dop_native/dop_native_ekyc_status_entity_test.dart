import 'package:evoapp/data/response/dop_native/dop_native_ekyc_status_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Test DOPNativeSubmitStatusEntity', () {
    test('Test constant values', () {
      expect(DOPNativeEkycStatusEntity.statusInProgress, 'in_progress');
      expect(DOPNativeEkycStatusEntity.statusPending, 'pending');
      expect(DOPNativeEkycStatusEntity.statusSuccess, 'success');
      expect(DOPNativeEkycStatusEntity.statusFailure, 'failure');
    });

    test('Test fromJson/toJson without accessToken', () {
      final BaseResponse baseResponse =
          BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
        'data': <String, dynamic>{
          'status': 'success',
          'error_code': '123',
          'label': 'label1',
          'qr_readable': true,
        }
      });
      final DOPNativeEkycStatusEntity submitStatusEntity =
          DOPNativeEkycStatusEntity.fromBaseResponse(baseResponse);

      expect(submitStatusEntity.statusCode, CommonHttpClient.SUCCESS);
      expect(submitStatusEntity.status, 'success');
      expect(submitStatusEntity.errorCode, '123');
      expect(submitStatusEntity.label, 'label1');
      expect(submitStatusEntity.qrReadable, true);
      expect(submitStatusEntity.accessToken, null);

      final Map<String, dynamic> jsonMap = submitStatusEntity.toJson();

      expect(jsonMap['status'], 'success');
      expect(jsonMap['error_code'], '123');
      expect(jsonMap['label'], 'label1');
      expect(jsonMap['qr_readable'], true);
      expect(jsonMap['access_token'], null);
    });

    test('Test fromJson/toJson with accessToken', () {
      final BaseResponse baseResponse =
          BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
        'data': <String, dynamic>{
          'status': 'success',
          'error_code': '123',
          'label': 'label1',
          'qr_readable': true,
          'access_token': 'fake_access_token',
        }
      });
      final DOPNativeEkycStatusEntity submitStatusEntity =
          DOPNativeEkycStatusEntity.fromBaseResponse(baseResponse);

      expect(submitStatusEntity.statusCode, CommonHttpClient.SUCCESS);
      expect(submitStatusEntity.status, 'success');
      expect(submitStatusEntity.errorCode, '123');
      expect(submitStatusEntity.label, 'label1');
      expect(submitStatusEntity.qrReadable, true);
      expect(submitStatusEntity.accessToken, 'fake_access_token');

      final Map<String, dynamic> jsonMap = submitStatusEntity.toJson();

      expect(jsonMap['status'], 'success');
      expect(jsonMap['error_code'], '123');
      expect(jsonMap['label'], 'label1');
      expect(jsonMap['qr_readable'], true);
      expect(jsonMap['access_token'], 'fake_access_token');
    });

    test('Test unserializable', () {
      final DOPNativeEkycStatusEntity submitStatusEntity =
          DOPNativeEkycStatusEntity.unserializable();

      expect(submitStatusEntity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      expect(submitStatusEntity.status, null);
      expect(submitStatusEntity.errorCode, null);
      expect(submitStatusEntity.label, null);
      expect(submitStatusEntity.qrReadable, null);
      expect(submitStatusEntity.accessToken, null);
    });
  });
}
