import 'package:evoapp/data/response/dop_native/dop_native_log_credential_entity.dart';
import 'package:evoapp/data/response/dop_native/dop_native_log_credential_item_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeAccessToken = 'fake_access_token';
  const String fakeSecret = 'fake_secret';

  group('DOPNativeLogCredentialEntity', () {
    test('unserializable constructor sets properties correctly', () {
      final DOPNativeLogCredentialEntity entity = DOPNativeLogCredentialEntity.unserializable();

      expect(entity.credential, isNull);
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('fromBaseResponse constructor initializes properties correctly', () {
      const String fakeValue = 'value';

      final Map<String, dynamic> baseResponseData = <String, dynamic>{
        'data': <String, dynamic>{
          'credential': <String, dynamic>{'access_token': fakeValue}
        }
      };

      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: baseResponseData,
      );

      final DOPNativeLogCredentialEntity entity =
          DOPNativeLogCredentialEntity.fromBaseResponse(baseResponse);

      expect(entity.credential, isA<DOPNativeLogCredentialItemEntity>());
      expect(entity.credential?.accessToken, fakeValue);
    });

    test('toJson method serializes properties correctly', () {
      final DOPNativeLogCredentialItemEntity credential = DOPNativeLogCredentialItemEntity(
        accessToken: fakeAccessToken,
        secret: fakeSecret,
        expireAt: '2024-04-12T03:37:54.292004471Z',
      );

      final DOPNativeLogCredentialEntity entity =
          DOPNativeLogCredentialEntity(credential: credential);

      final Map<String, dynamic> json = entity.toJson();

      expect(json['credential'], credential.toJson());
    });
  });
}
