import 'package:evoapp/data/response/user_entity.dart';
import 'package:evoapp/data/response/user_information_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeFullName = 'Example Full Name';

  group('verify UserEntity', () {
    test('UserEntity.unserializable creates a UserEntity with null userInformation', () {
      final UserEntity userEntity = UserEntity.unserializable();

      expect(userEntity.userInformation, isNull);
    });

    test('UserEntity.fromBaseResponse creates a valid UserEntity object', () {
      final Map<String, dynamic> baseResponseData = <String, dynamic>{
        'data': <String, dynamic>{
          'user_information': <String, dynamic>{
            'full_name': fakeFullName,
          },
        },
      };

      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: baseResponseData,
      );

      final UserEntity userEntity = UserEntity.fromBaseResponse(baseResponse);

      expect(userEntity.userInformation, isNotNull);
      expect(userEntity.userInformation?.fullName, fakeFullName);
    });

    test('UserEntity.toJson returns a valid JSON representation', () {
      const UserInformationEntity userInformation = UserInformationEntity(
        fullName: fakeFullName,
      );

      final UserEntity userEntity = UserEntity(userInformation: userInformation);

      final Map<String, dynamic> json = userEntity.toJson();

      expect(json, isNotNull);
      expect(json['user_information'], isNotNull);
      expect(json['user_information']['full_name'], fakeFullName);
    });

    test('toString', () {
      const UserInformationEntity userInformation = UserInformationEntity(
        fullName: fakeFullName,
      );

      final UserEntity userEntity = UserEntity(userInformation: userInformation);
      final String str = userEntity.toString();

      expect(str,
          'CurrentUserEntity{userInformation: UserInformation{fullName: Example Full Name, gender: null, identityCardIssueDate: null, identityCardNumber: null, phoneNumber: null}}');
    });
  });
}
