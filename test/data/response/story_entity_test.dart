import 'package:evoapp/data/response/action_entity.dart';
import 'package:evoapp/data/response/story_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeTitle = 'Test Story';
  const String fakeDescription = 'This is a test story description.';
  const String fakeThemeImageUrl = 'http://example.com/image.png';
  const int fakeDisplayDurationMs = 5000;
  const String fakeActionType = 'sample_action_type';
  const String fakeNavigationLink = 'fake_navigation_link';
  const String fakeNavigationScreenName = 'fake_navigation_screen_name';
  const String fakeFooterLink = 'fake_footer_link';
  const String fakeFooterScreenName = 'fake_footer_screen_name';

  final Map<String, dynamic> mockJson = <String, dynamic>{
    'title': fakeTitle,
    'description': fakeDescription,
    'navigation': <String, dynamic>{
      'type': fakeActionType,
      'args': <String, dynamic>{
        'link': fakeNavigationLink,
        'screen_name': fakeNavigationScreenName,
        'action_label': null,
        'parameters': null,
        'next_action': null,
      },
    },
    'theme_image_url': fakeThemeImageUrl,
    'footer': <String, dynamic>{
      'type': fakeActionType,
      'args': <String, dynamic>{
        'link': fakeFooterLink,
        'screen_name': fakeFooterScreenName,
        'action_label': null,
        'parameters': null,
        'next_action': null,
      },
    },
    'display_duration_ms': fakeDisplayDurationMs,
  };

  test('fromJson should correctly deserialize JSON to StoryEntity', () {
    // Deserialize JSON to StoryEntity
    final StoryEntity storyEntity = StoryEntity.fromJson(mockJson);

    // Check if the deserialized StoryEntity matches the expected values
    expect(storyEntity.title, fakeTitle);
    expect(storyEntity.description, fakeDescription);
    expect(storyEntity.navigation?.type, fakeActionType);
    expect(storyEntity.navigation?.args?.link, fakeNavigationLink);
    expect(storyEntity.navigation?.args?.screenName, fakeNavigationScreenName);
    expect(storyEntity.themeImageUrl, fakeThemeImageUrl);
    expect(storyEntity.footer?.type, fakeActionType);
    expect(storyEntity.footer?.args?.link, fakeFooterLink);
    expect(storyEntity.footer?.args?.screenName, fakeFooterScreenName);
    expect(storyEntity.displayDurationMs, fakeDisplayDurationMs);
  });

  test('toJson should correctly serialize StoryEntity to JSON', () {
    final ActionEntity fakeNavigationActionEntity = ActionEntity(
        type: fakeActionType,
        args: ArgsEntity(link: fakeNavigationLink, screenName: fakeNavigationScreenName));

    final ActionEntity fakeFooterActionEntity = ActionEntity(
        type: fakeActionType,
        args: ArgsEntity(link: fakeFooterLink, screenName: fakeFooterScreenName));

    // Create a StoryEntity
    final StoryEntity storyEntity = StoryEntity(
      title: fakeTitle,
      description: fakeDescription,
      navigation: fakeNavigationActionEntity,
      themeImageUrl: fakeThemeImageUrl,
      footer: fakeFooterActionEntity,
      displayDurationMs: fakeDisplayDurationMs,
    );

    // Serialize StoryEntity to JSON
    final Map<String, dynamic> json = storyEntity.toJson();

    // Check if the serialized JSON matches the expected values
    expect(json, mockJson);
  });
}
