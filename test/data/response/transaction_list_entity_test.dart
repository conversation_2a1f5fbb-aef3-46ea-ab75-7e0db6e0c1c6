import 'package:evoapp/data/response/transaction_entity.dart';
import 'package:evoapp/data/response/transaction_list_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const int fakeId1 = 1;
  const int fakeId2 = 2;

  group('TransactionListEntity', () {
    test('unserializable', () {
      final TransactionListEntity result = TransactionListEntity.unserializable();

      expect(result.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('fromJson & toJson', () {
      final Map<String, dynamic> baseResponseData = <String, dynamic>{
        'data': <String, dynamic>{
          'notifications': <dynamic>[
            <String, dynamic>{
              'id': fakeId1,
            },
            <String, dynamic>{
              'id': fakeId2,
            },
          ],
        }
      };

      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: baseResponseData,
      );

      final TransactionListEntity transactionListEntity =
          TransactionListEntity.fromBaseResponse(baseResponse);

      expect(transactionListEntity.transactions, isNotNull);
      expect(transactionListEntity.transactions?.length, 2);
      expect(transactionListEntity.transactions?.first.id, 1);
      expect(transactionListEntity.transactions?.last.id, 2);
    });

    test('verify toJson()', () {
      final TransactionListEntity transactionHistoryEntity = TransactionListEntity(
        transactions: <TransactionEntity>[
          TransactionEntity(
            id: fakeId1,
          ),
          TransactionEntity(
            id: fakeId2,
          ),
        ],
      );

      final Map<String, dynamic> json = transactionHistoryEntity.toJson();

      expect(json['notifications'], isNotNull);
      expect((json['notifications'] as List<Map<String, dynamic>>).length, 2);
    });
  });
}
