import 'package:evoapp/data/response/emi_record_detail_entity.dart';
import 'package:evoapp/data/response/emi_record_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EmiRecordDetailEntity', () {
    test('fromBaseResponse() should correctly parse BaseResponse', () {
      final Map<String, dynamic> responseData = <String, dynamic>{
        'data': <String, dynamic>{
          'record': <String, dynamic>{
            'id': '123',
            'user_id': 123,
          },
        }
      };

      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: responseData,
      );

      final EmiRecordDetailEntity entity = EmiRecordDetailEntity.fromBaseResponse(baseResponse);

      expect(entity.record?.id, '123');
      expect(entity.record?.userId, 123);
    });

    test('unserializable() should create an entity with null values', () {
      final EmiRecordDetailEntity entity = EmiRecordDetailEntity.unserializable();

      expect(entity.record, isNull);
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('toJson() should correctly convert to JSON', () {
      final EmiRecordEntity record = EmiRecordEntity(
        id: '123',
        userId: 123,
      );

      final EmiRecordDetailEntity entity = EmiRecordDetailEntity(
        record: record,
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json['record']['id'], '123');
      expect(json['record']['user_id'], 123);
    });
  });
}
