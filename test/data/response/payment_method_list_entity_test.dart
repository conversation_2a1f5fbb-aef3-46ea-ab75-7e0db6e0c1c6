import 'package:evoapp/data/response/payment_method_entity.dart';
import 'package:evoapp/data/response/payment_method_list_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PaymentMethodListEntity', () {
    const Map<String, dynamic> fakePaymentMethodJson = <String, dynamic>{
      'id': 'test_id',
      'psp_code': 'test_psp_code',
      'card_info': <String, dynamic>{
        'brand': '1234',
        'issuer': '12/23',
      },
      'type': 'test_type',
      'source_name': 'test_source_name',
      'status': 'test_status',
      'source_of_fund': <String, dynamic>{
        'name': 'test_fund',
      },
    };

    test('fromBaseResponse creates a valid object', () {
      final BaseResponse response = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'payment_methods': <dynamic>[fakePaymentMethodJson],
          }
        },
      );

      final PaymentMethodListEntity entity = PaymentMethodListEntity.fromBaseResponse(response);

      expect(entity.paymentMethods, isNotNull);
      expect(entity.paymentMethods?.first.id, 'test_id');
      expect(entity.paymentMethods?.first.pspCode, 'test_psp_code');
    });

    test('toJson returns a valid JSON representation', () {
      final PaymentMethodEntity paymentMethod = PaymentMethodEntity.fromJson(fakePaymentMethodJson);

      final PaymentMethodListEntity entity = PaymentMethodListEntity(
        paymentMethods: <PaymentMethodEntity>[paymentMethod],
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json, isNotNull);
      expect(json['payment_methods'], isNotNull);
    });

    test('unserializable constructor sets localExceptionCode', () {
      final PaymentMethodListEntity entity = PaymentMethodListEntity.unserializable();

      expect(entity.paymentMethods, isNull);
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });
  });
}
