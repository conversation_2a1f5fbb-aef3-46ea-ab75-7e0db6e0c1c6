import 'package:evoapp/data/response/payment_info_entity.dart';
import 'package:evoapp/data/response/payment_method_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PaymentInfoEntity', () {
    const String fakePaymentMethodId = 'test_payment_method_id';
    const Map<String, dynamic> fakePaymentMethodJson = <String, dynamic>{
      'id': 'test_id',
      'source_name': 'test_name',
    };

    test('from<PERSON><PERSON> creates a valid object', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'payment_method_id': fakePaymentMethodId,
        'payment_methods': <dynamic>[fakePaymentMethodJson],
      };

      final PaymentInfoEntity entity = PaymentInfoEntity.fromJson(json);

      expect(entity.paymentMethodId, fakePaymentMethodId);
      expect(entity.paymentMethods, isNotNull);
      expect(entity.paymentMethods?.first?.id, 'test_id');
      expect(entity.paymentMethods?.first?.sourceName, 'test_name');
    });

    test('toJson returns a valid JSON representation', () {
      final PaymentMethodEntity paymentMethod = PaymentMethodEntity(
        id: 'test_id',
        sourceName: 'test_name',
      );

      final PaymentInfoEntity entity = PaymentInfoEntity(
        paymentMethodId: fakePaymentMethodId,
        paymentMethods: <PaymentMethodEntity?>[paymentMethod],
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json, isNotNull);
      expect(json['payment_method_id'], fakePaymentMethodId);
      expect(json['payment_methods'], isNotNull);
    });
  });
}
