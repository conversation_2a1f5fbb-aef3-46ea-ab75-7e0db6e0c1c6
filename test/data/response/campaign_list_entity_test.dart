import 'package:evoapp/data/response/campaign_entity.dart';
import 'package:evoapp/data/response/campaign_list_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeBanner = 'https://fake.com/banner.png';
  const String fakeCampaignType = 'fake_campaign_type';

  group('CampaignListEntity', () {
    test('unserializable creates a CampaignListEntity with localExceptionCode', () {
      final CampaignListEntity entity = CampaignListEntity.unserializable();
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      expect(entity.campaigns, null);
    });

    test('fromBaseResponse creates a valid CampaignListEntity', () {
      final BaseResponse response = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'campaigns': <dynamic>[
              <String, dynamic>{
                'banner': fakeBanner,
                'campaign_type': fakeCampaignType,
              },
            ],
          }
        },
      );

      final CampaignListEntity entity = CampaignListEntity.fromBaseResponse(response);
      expect(entity.campaigns, isNotNull);
      expect(entity.campaigns?.length, 1);
      expect(entity.campaigns?[0].banner, fakeBanner);
      expect(entity.campaigns?[0].campaignType, fakeCampaignType);
    });

    test('toJson returns a valid Map', () {
      final CampaignListEntity entity = CampaignListEntity(
        campaigns: <CampaignEntity>[
          CampaignEntity(
            banner: fakeBanner,
            campaignType: fakeCampaignType,
            // Add more attributes for CampaignEntity
          ),
        ],
      );

      final Map<String, dynamic> json = entity.toJson();
      expect(json['campaigns'], isNotNull);
      expect(json['campaigns'].length, 1);
      expect(json['campaigns'][0]['banner'], fakeBanner);
      expect(json['campaigns'][0]['campaign_type'], fakeCampaignType);
    });
  });
}
