import 'package:evoapp/data/response/action_entity.dart';
import 'package:evoapp/data/response/offer_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('OfferEntity', () {
    const String fakeId = 'fake_id';
    const String fakeTypeAction = 'fake_type_action';
    const String fakeBanner = 'fake_banner';
    const String fakeThumbnail = 'fake_thumbnail';
    const String fakeCode = 'fake_code';
    const int fakeDiscountAmount = 10;
    const String fakeEndAt = 'fake_end_at';
    const String fakeFormattedExpiry = 'fake_formatted_expiry';
    const String fakeBenefitType = 'fake_benefit_type';
    const String fakeDescription = 'fake_description';
    const String fakeStartAt = 'fake_start_at';
    const String fakeTerms = 'fake_terms';
    const String fakeTitle = 'fake_title';

    test('from<PERSON>son creates a valid OfferEntity object', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'id': fakeId,
        'action': <String, dynamic>{'type': fakeTypeAction},
        'banner': fakeBanner,
        'thumbnail': fakeThumbnail,
        'code': fakeCode,
        'discount_amount': fakeDiscountAmount,
        'end_at': fakeEndAt,
        'formatted_expiry': fakeFormattedExpiry,
        'benefit_type': fakeBenefitType,
        'description': fakeDescription,
        'start_at': fakeStartAt,
        'terms': fakeTerms,
        'title': fakeTitle,
      };

      final OfferEntity offerEntity = OfferEntity.fromJson(json);

      expect(offerEntity.id, fakeId);
      expect(offerEntity.action?.type, fakeTypeAction);
      expect(offerEntity.banner, fakeBanner);
      expect(offerEntity.thumbnail, fakeThumbnail);
      expect(offerEntity.code, fakeCode);
      expect(offerEntity.discountAmount, fakeDiscountAmount);
      expect(offerEntity.endAt, fakeEndAt);
      expect(offerEntity.formattedExpiry, fakeFormattedExpiry);
      expect(offerEntity.benefitType, fakeBenefitType);
      expect(offerEntity.description, fakeDescription);
      expect(offerEntity.startAt, fakeStartAt);
      expect(offerEntity.terms, fakeTerms);
      expect(offerEntity.title, fakeTitle);
    });

    test('toJson creates a valid JSON object from OfferEntity', () {
      final OfferEntity offerEntity = OfferEntity(
        id: fakeId,
        action: ActionEntity(type: fakeTypeAction),
        banner: fakeBanner,
        thumbnail: fakeThumbnail,
        code: fakeCode,
        discountAmount: fakeDiscountAmount,
        endAt: fakeEndAt,
        formattedExpiry: fakeFormattedExpiry,
        benefitType: fakeBenefitType,
        description: fakeDescription,
        startAt: fakeStartAt,
        terms: fakeTerms,
        title: fakeTitle,
      );

      final Map<String, dynamic> json = offerEntity.toJson();

      expect(json['id'], fakeId);
      expect(json['action']['type'], fakeTypeAction);
      expect(json['banner'], fakeBanner);
      expect(json['thumbnail'], fakeThumbnail);
      expect(json['code'], fakeCode);
      expect(json['discount_amount'], fakeDiscountAmount);
      expect(json['end_at'], fakeEndAt);
      expect(json['formatted_expiry'], fakeFormattedExpiry);
      expect(json['benefit_type'], fakeBenefitType);
      expect(json['description'], fakeDescription);
      expect(json['start_at'], fakeStartAt);
      expect(json['terms'], fakeTerms);
      expect(json['title'], fakeTitle);
    });
  });
}
