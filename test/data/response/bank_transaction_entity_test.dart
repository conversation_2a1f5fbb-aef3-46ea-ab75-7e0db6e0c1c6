import 'package:evoapp/data/response/bank_transaction_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeName = 'Bank Name';
  const String fakeLogo = 'https://example.com/logo.png';

  group('BankTransactionEntity', () {
    test('from<PERSON>son creates a valid BankTransactionEntity', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'name': fakeName,
        'logo': fakeLogo,
      };

      final BankTransactionEntity bankTransactionEntity = BankTransactionEntity.fromJson(json);
      expect(bankTransactionEntity.name, fakeName);
      expect(bankTransactionEntity.logo, fakeLogo);
    });

    test('toJ<PERSON> returns a valid Map', () {
      final BankTransactionEntity bankTransactionEntity = BankTransactionEntity(
        name: fakeName,
        logo: fakeLogo,
      );

      final Map<String, dynamic> json = bankTransactionEntity.toJson();
      expect(json['name'], fakeName);
      expect(json['logo'], fakeLogo);
    });
  });
}
