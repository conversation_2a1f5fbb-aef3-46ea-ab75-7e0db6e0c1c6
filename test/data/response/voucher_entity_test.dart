import 'package:evoapp/data/response/action_entity.dart';
import 'package:evoapp/data/response/visualization_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

void main() {
  const String fakeActionType = 'Example Action Type';
  const String fakeBurnActionType = 'Example Burn Action Type';
  const String fakeBanner = 'Example Banner';
  const String fakeThumbnail = 'Example Thumbnail';
  const String fakeCode = 'Example Code';
  const String fakeDescription = 'Example Description';
  const String fakeFormattedExpiry = 'Example Formatted Expiry';
  const int fakeId = 1;
  const String fakeStatus = 'Example Status';
  const String fakeTerms = 'Example Terms';
  const String fakeTitle = 'Example Title';
  const String fakeValidFrom = '2021-12-01T00:00:00.000Z';
  const String fakeValidTo = '2021-12-31T00:00:00.000Z';
  const bool fakeIsUsed = true;
  const bool fakeIsQualified = true;
  const String fakeVisualType = 'Example Visual Type';

  late CommonUtilFunction commonUtilFunction;
  final DateTime expectedDateTime = DateTime(2023, 1, 2, 3, 4, 5);

  setUpAll(() {
    getIt.registerSingleton<CommonUtilFunction>(MockCommonUtilFunction());
    commonUtilFunction = getIt.get<CommonUtilFunction>();

    when(() => commonUtilFunction.toDateTime(any())).thenReturn(expectedDateTime);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('verify VoucherEntity', () {
    test('VoucherEntity.fromJson creates a valid object', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'action': <String, dynamic>{
          'type': fakeActionType,
        },
        'burn_action': <String, dynamic>{
          'type': fakeBurnActionType,
        },
        'banner': fakeBanner,
        'thumbnail': fakeThumbnail,
        'code': fakeCode,
        'description': fakeDescription,
        'formatted_expiry': fakeFormattedExpiry,
        'id': fakeId,
        'status': fakeStatus,
        'terms': fakeTerms,
        'title': fakeTitle,
        'valid_from': fakeValidFrom,
        'valid_to': fakeValidTo,
        'visualizations': <dynamic>[
          <String, dynamic>{
            'visual_type': fakeVisualType,
          },
        ],
        'used': fakeIsUsed,
        'is_qualified': fakeIsQualified,
      };

      final VoucherEntity voucherEntity = VoucherEntity.fromJson(json);

      expect(voucherEntity.action?.type, fakeActionType);
      expect(voucherEntity.burnAction?.type, fakeBurnActionType);
      expect(voucherEntity.banner, fakeBanner);
      expect(voucherEntity.thumbnail, fakeThumbnail);
      expect(voucherEntity.code, fakeCode);
      expect(voucherEntity.description, fakeDescription);
      expect(voucherEntity.formattedExpiry, fakeFormattedExpiry);
      expect(voucherEntity.id, fakeId);
      expect(voucherEntity.status, fakeStatus);
      expect(voucherEntity.terms, fakeTerms);
      expect(voucherEntity.title, fakeTitle);
      expect(voucherEntity.validFrom, fakeValidFrom);
      expect(voucherEntity.validTo, fakeValidTo);
      expect(voucherEntity.visualizations?.first.visualType, fakeVisualType);
      expect(voucherEntity.isUsed, fakeIsUsed);
      expect(voucherEntity.isQualified, fakeIsQualified);
    });

    test('VoucherEntity.toJson returns a valid JSON representation', () {
      final VoucherEntity voucherEntity = VoucherEntity(
        action: ActionEntity(
          type: fakeActionType,
        ),
        burnAction: ActionEntity(
          type: fakeBurnActionType,
        ),
        banner: fakeBanner,
        thumbnail: fakeThumbnail,
        code: fakeCode,
        description: fakeDescription,
        formattedExpiry: fakeFormattedExpiry,
        id: fakeId,
        status: fakeStatus,
        terms: fakeTerms,
        title: fakeTitle,
        validFrom: fakeValidFrom,
        validTo: fakeValidTo,
        visualizations: <VisualizationEntity>[
          VisualizationEntity(
            visualType: fakeVisualType,
          ),
        ],
        isUsed: fakeIsUsed,
        isQualified: fakeIsQualified,
      );

      final Map<String, dynamic> json = voucherEntity.toJson();

      expect(json['action']['type'], fakeActionType);
      expect(json['burn_action']['type'], fakeBurnActionType);
      expect(json['banner'], fakeBanner);
      expect(json['thumbnail'], fakeThumbnail);
      expect(json['code'], fakeCode);
      expect(json['description'], fakeDescription);
      expect(json['formatted_expiry'], fakeFormattedExpiry);
      expect(json['id'], fakeId);
      expect(json['status'], fakeStatus);
      expect(json['terms'], fakeTerms);
      expect(json['title'], fakeTitle);
      expect(json['valid_from'], fakeValidFrom);
      expect(json['valid_to'], fakeValidTo);
      expect(json['visualizations'][0]['visual_type'], fakeVisualType);
      expect(json['used'], fakeIsUsed);
      expect(json['is_qualified'], fakeIsQualified);
    });

    test('verify validFromDateTime', () {
      final VoucherEntity voucherEntity = VoucherEntity(
        id: 1,
        validFrom: '2022-01-01T00:00:00.000Z',
      );

      final DateTime? validFormDate = voucherEntity.validFromDateTime;
      expect(validFormDate, isNotNull);
      expect(validFormDate?.year, expectedDateTime.year);
      expect(validFormDate?.month, expectedDateTime.month);
      expect(validFormDate?.day, expectedDateTime.day);
      expect(validFormDate?.hour, expectedDateTime.hour);
      expect(validFormDate?.minute, expectedDateTime.minute);
      expect(validFormDate?.second, expectedDateTime.second);

      verify(() => commonUtilFunction.toDateTime(voucherEntity.validFrom)).called(1);
    });

    test('verify validToDateTime', () {
      final VoucherEntity voucherEntity = VoucherEntity(
        id: 1,
        validTo: '2022-01-01T00:00:00.000Z',
      );

      final DateTime? validToDate = voucherEntity.validToDateTime;
      expect(validToDate, isNotNull);
      expect(validToDate?.year, expectedDateTime.year);
      expect(validToDate?.month, expectedDateTime.month);
      expect(validToDate?.day, expectedDateTime.day);
      expect(validToDate?.hour, expectedDateTime.hour);
      expect(validToDate?.minute, expectedDateTime.minute);
      expect(validToDate?.second, expectedDateTime.second);

      verify(() => commonUtilFunction.toDateTime(voucherEntity.validTo)).called(1);
    });
  });
}
