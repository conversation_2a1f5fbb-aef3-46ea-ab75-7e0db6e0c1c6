import 'package:evoapp/data/response/bar_code_entity.dart';
import 'package:evoapp/data/response/plain_code_entity.dart';
import 'package:evoapp/data/response/qr_code_entity.dart';
import 'package:evoapp/data/response/visualization_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeBarcode = 'Example Barcode';
  const String fakePlainCode = 'Example Plain Code';
  const String fakeQrCode = 'Example Qr Code';
  const String fakeVisualType = 'Example Visual Type';

  group('verify VisualizationEntity', () {
    test('VisualizationEntity.fromJson creates a valid object', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'barcode': <String, dynamic>{
          'barcode': fakeBarcode,
        },
        'plain_code': <String, dynamic>{
          'plain_code': fakePlainCode,
        },
        'qr_code': <String, dynamic>{
          'qr_code': fakeQrCode,
        },
        'visual_type': fakeVisualType,
      };

      final VisualizationEntity visualizationEntity = VisualizationEntity.fromJson(json);

      expect(visualizationEntity.barcode?.barcode, fakeBarcode);
      expect(visualizationEntity.plainCode?.plainCode, fakePlainCode);
      expect(visualizationEntity.qrCode?.qrCode, fakeQrCode);
      expect(visualizationEntity.visualType, fakeVisualType);
    });

    test('VisualizationEntity.toJson returns a valid JSON representation', () {
      final VisualizationEntity visualizationEntity = VisualizationEntity(
        barcode: BarcodeEntity(
          barcode: fakeBarcode,
        ),
        plainCode: PlainCodeEntity(
          plainCode: fakePlainCode,
        ),
        qrCode: QrCodeEntity(
          qrCode: fakeQrCode,
        ),
        visualType: fakeVisualType,
      );

      // Convert the VisualizationEntity object to JSON
      final Map<String, dynamic> json = visualizationEntity.toJson();

      expect(json, isNotNull);
      expect(json['barcode']['barcode'], fakeBarcode);
      expect(json['plain_code']['plain_code'], fakePlainCode);
      expect(json['qr_code']['qr_code'], fakeQrCode);
      expect(json['visual_type'], fakeVisualType);
    });
  });
}
