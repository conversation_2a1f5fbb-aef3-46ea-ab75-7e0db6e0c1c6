import 'package:evoapp/data/response/sign_in_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify constant', () {
    expect(SignInEntity.verdictSuccess, 'success');
    expect(SignInEntity.verdictUserNotExisted, 'record_not_found');
    expect(SignInEntity.verdictUnsupportedMobileNumber, 'unsupported_telco');
    expect(SignInEntity.verdictInvalidParameter, 'invalid_parameters');
    expect(SignInEntity.verdictDeletedUser, 'deleted_user');
    expect(SignInEntity.verdictLimitExceeded, 'limit_exceeded');
    expect(SignInEntity.verdictNoEKYCData, 'no_ekyc_data');
    expect(SignInEntity.verdictNotFinishedDOP, 'not_finished_dop');
    expect(SignInEntity.defaultResendOtpIntervalTimeInSecs, 60);
  });

  group('verify SignInEntity', () {
    const String fakeChallengeType = 'sample_challenge_type';
    const int fakeOtpResendSecs = 50;
    const int fakeOtpValiditySecs = 300;
    const String fakeSessionToken = 'sample_session_token';

    test('unserializable()', () {
      final SignInEntity result = SignInEntity.unserializable();

      expect(result.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('fromBaseResponse should return a valid SignInEntity', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'challenge_type': fakeChallengeType,
            'otp_resend_secs': fakeOtpResendSecs,
            'otp_validity_secs': fakeOtpValiditySecs,
            'session_token': fakeSessionToken,
          },
        },
      );

      final SignInEntity result = SignInEntity.fromBaseResponse(baseResponse);

      expect(result.challengeType, fakeChallengeType);
      expect(result.otpResendSecs, fakeOtpResendSecs);
      expect(result.otpValiditySecs, fakeOtpValiditySecs);
      expect(result.sessionToken, fakeSessionToken);
    });

    test('toJson should return a valid JSON representation', () {
      final SignInEntity signInEntity = SignInEntity(
        challengeType: fakeChallengeType,
        otpResendSecs: fakeOtpResendSecs,
        otpValiditySecs: fakeOtpValiditySecs,
        sessionToken: fakeSessionToken,
      );

      final Map<String, dynamic> json = signInEntity.toJson();

      // Assert
      expect(json['challenge_type'], fakeChallengeType);
      expect(json['otp_resend_secs'], fakeOtpResendSecs);
      expect(json['otp_validity_secs'], fakeOtpValiditySecs);
      expect(json['session_token'], fakeSessionToken);
    });
  });
}
