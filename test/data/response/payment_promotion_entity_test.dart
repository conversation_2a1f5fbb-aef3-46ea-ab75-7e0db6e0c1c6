import 'package:evoapp/data/response/payment_promotion_entity.dart';
import 'package:evoapp/data/response/voucher_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';

void main() {
  group('PaymentPromotionEntity', () {
    const Map<String, dynamic> fakeVoucherJson = <String, dynamic>{
      'id': 1,
      'code': 'test_name',
    };

    test('fromBaseResponse creates a valid object', () {
      final BaseResponse response = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'vouchers': <dynamic>[fakeVoucherJson],
          }
        },
      );

      final PaymentPromotionEntity entity = PaymentPromotionEntity.fromBaseResponse(response);

      expect(entity.vouchers, isNotNull);
      expect(entity.vouchers?.first.id, 1);
      expect(entity.vouchers?.first.code, 'test_name');
    });

    test('toJson returns a valid JSON representation', () {
      final VoucherEntity voucher = VoucherEntity.fromJson(fakeVoucherJson);

      final PaymentPromotionEntity entity = PaymentPromotionEntity(
        vouchers: <VoucherEntity>[voucher],
      );

      final Map<String, dynamic> json = entity.toJson();

      expect(json, isNotNull);
      expect(json['vouchers'], isNotNull);
    });

    test('unserializable constructor sets localExceptionCode', () {
      final PaymentPromotionEntity entity = PaymentPromotionEntity.unserializable();

      expect(entity.vouchers, isNull);
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });
  });
}
