import 'package:evoapp/data/response/credit_limit_widget_config_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('constructor', () {
    final CreditLimitWidgetConfigEntity entity = CreditLimitWidgetConfigEntity(
      display: true,
      creditLimit: 1000,
      creditStatus: 'creditStatus',
    );
    expect(entity.display, true);
    expect(entity.creditLimit, 1000);
    expect(entity.creditStatus, 'creditStatus');
  });

  test('fromJson', () {
    final Map<String, dynamic> json = <String, dynamic>{
      'display': true,
      'credit_limit': 1000,
      'status': 'creditStatus',
    };
    final CreditLimitWidgetConfigEntity entity = CreditLimitWidgetConfigEntity.fromJson(json);
    expect(entity.display, true);
    expect(entity.creditLimit, 1000);
    expect(entity.creditStatus, 'creditStatus');
  });

  test('toJson', () {
    final CreditLimitWidgetConfigEntity entity = CreditLimitWidgetConfigEntity(
      display: true,
      creditLimit: 1000,
      creditStatus: 'creditStatus',
    );
    final Map<String, dynamic> json = entity.toJson();
    expect(json['display'], true);
    expect(json['credit_limit'], 1000);
    expect(json['status'], 'creditStatus');
  });

  group('Test CreditLimitWidgetConfigEntityExt', () {
    test('Case creditStatus = null', () {
      final CreditLimitWidgetConfigEntity entity = CreditLimitWidgetConfigEntity();

      expect(entity.isCardApprovedOrOutOfSync, false);
      expect(entity.isCardReadyForPayment, false);
      expect(entity.isOutOfSyncWithDOP, false);
      expect(entity.isCardNotReadyForPayment, false);
    });

    test('Case creditStatus = waiting_for_approval', () {
      final CreditLimitWidgetConfigEntity entity = CreditLimitWidgetConfigEntity(
        creditStatus: 'waiting_for_approval',
      );

      expect(entity.isCardApprovedOrOutOfSync, false);
      expect(entity.isCardReadyForPayment, false);
      expect(entity.isOutOfSyncWithDOP, false);
      expect(entity.isCardNotReadyForPayment, false);
    });

    test('Case creditStatus = ready_for_payment', () {
      final CreditLimitWidgetConfigEntity entity = CreditLimitWidgetConfigEntity(
        creditStatus: 'ready_for_payment',
      );

      expect(entity.isCardApprovedOrOutOfSync, true);
      expect(entity.isCardReadyForPayment, true);
      expect(entity.isOutOfSyncWithDOP, false);
      expect(entity.isCardNotReadyForPayment, false);
    });

    test('Case creditStatus = not_ready_for_payment', () {
      final CreditLimitWidgetConfigEntity entity = CreditLimitWidgetConfigEntity(
        creditStatus: 'not_ready_for_payment',
      );

      expect(entity.isCardApprovedOrOutOfSync, true);
      expect(entity.isCardReadyForPayment, false);
      expect(entity.isOutOfSyncWithDOP, false);
      expect(entity.isCardNotReadyForPayment, true);
    });

    test('Case creditStatus = out_of_sync', () {
      final CreditLimitWidgetConfigEntity entity = CreditLimitWidgetConfigEntity(
        creditStatus: 'out_of_sync',
      );

      expect(entity.isCardApprovedOrOutOfSync, true);
      expect(entity.isCardReadyForPayment, false);
      expect(entity.isOutOfSyncWithDOP, true);
      expect(entity.isCardNotReadyForPayment, false);
    });
  });
}
