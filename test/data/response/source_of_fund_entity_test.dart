import 'package:evoapp/data/response/source_of_fund_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('SourceOfFundEntity', () {
    const String fakeName = 'Example Name';
    const String fakeProductCode = 'Sample Product Code';
    const String fakeStatus = 'Sample Status';

    test('fromJson should return a valid SourceOfFundEntity', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'name': fakeName,
        'product_code': fakeProductCode,
        'status': fakeStatus,
      };

      final SourceOfFundEntity result = SourceOfFundEntity.fromJson(json);

      expect(result.name, fakeName);
      expect(result.productCode, fakeProductCode);
      expect(result.status, fakeStatus);
    });

    test('toJson should return a valid JSON representation', () {
      final SourceOfFundEntity sourceOfFundEntity = SourceOfFundEntity(
        name: fakeName,
        productCode: fakeProductCode,
        status: fakeStatus,
      );

      final Map<String, dynamic> json = sourceOfFundEntity.toJson();

      expect(json['name'], fakeName);
      expect(json['product_code'], fakeProductCode);
      expect(json['status'], fakeStatus);
    });
  });
}
