import 'package:evoapp/data/response/remote_config_common_base_url_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeDescription = 'fake_description';
  const String fakeValue = 'fake_value';

  group('RemoteConfigCommonBaseUrlEntity', () {
    test('from<PERSON><PERSON> creates a valid RemoteConfigCommonBaseUrlEntity object', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'description': fakeDescription,
        'value': fakeValue,
      };

      final RemoteConfigCommonBaseUrlEntity remoteConfigCommonBaseUrlEntity =
          RemoteConfigCommonBaseUrlEntity.fromJson(json);

      expect(remoteConfigCommonBaseUrlEntity.description, fakeDescription);
      expect(remoteConfigCommonBaseUrlEntity.value, fakeValue);
    });

    test('to<PERSON><PERSON> creates a valid JSON object from RemoteConfigCommonBaseUrlEntity', () {
      final RemoteConfigCommonBaseUrlEntity remoteConfigCommonBaseUrlEntity =
          RemoteConfigCommonBaseUrlEntity(
        description: fakeDescription,
        value: fakeValue,
      );

      final Map<String, dynamic> json = remoteConfigCommonBaseUrlEntity.toJson();

      expect(json['description'], fakeDescription);
      expect(json['value'], fakeValue);
    });
  });
}
