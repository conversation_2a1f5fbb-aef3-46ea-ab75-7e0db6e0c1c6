import 'package:evoapp/data/response/announcement_status_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const int fakeId = 1;
  const String fakeStatus = 'example_status';

  group('AnnouncementStatusEntity', () {
    test('unserializable creates an AnnouncementStatusEntity with localExceptionCode', () {
      final AnnouncementStatusEntity entity = AnnouncementStatusEntity.unserializable();
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      expect(entity.id, null);
      expect(entity.status, null);
    });

    test('fromBaseResponse creates a valid AnnouncementStatusEntity', () {
      final BaseResponse response = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'id': fakeId,
            'status': fakeStatus,
          }
        },
      );

      final AnnouncementStatusEntity entity = AnnouncementStatusEntity.fromBaseResponse(response);
      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.id, fakeId);
      expect(entity.status, fakeStatus);
    });

    test('toJson returns a valid Map', () {
      final AnnouncementStatusEntity entity = AnnouncementStatusEntity(
        id: fakeId,
        status: fakeStatus,
      );

      final Map<String, dynamic> json = entity.toJson();
      expect(json['id'], fakeId);
      expect(json['status'], fakeStatus);
    });
  });
}
