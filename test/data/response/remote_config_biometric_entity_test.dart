import 'package:evoapp/data/response/remote_config_biometric_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('RemoteConfigBiometricEntity', () {
    test('from<PERSON><PERSON> creates a valid RemoteConfigBiometricEntity object', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'fingering_supported_devices': <dynamic>['device1', 'device2', 'device3'],
      };

      final RemoteConfigBiometricEntity remoteConfigBiometricEntity =
          RemoteConfigBiometricEntity.fromJson(json);

      expect(remoteConfigBiometricEntity.fingeringSupportedDevice, isNotNull);
      expect(remoteConfigBiometricEntity.fingeringSupportedDevice?.length, 3);
      expect(remoteConfigBiometricEntity.fingeringSupportedDevice,
          <dynamic>['device1', 'device2', 'device3']);
    });

    test('to<PERSON><PERSON> creates a valid JSON object from RemoteConfigBiometricEntity', () {
      final RemoteConfigBiometricEntity remoteConfigBiometricEntity = RemoteConfigBiometricEntity(
        fingeringSupportedDevice: <String>['device1', 'device2', 'device3'],
      );

      final Map<String, dynamic> json = remoteConfigBiometricEntity.toJson();

      expect(json['fingering_supported_devices'], '[device1, device2, device3]');
    });
  });
}
