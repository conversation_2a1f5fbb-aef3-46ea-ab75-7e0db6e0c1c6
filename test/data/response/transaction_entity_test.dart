import 'package:evoapp/data/response/bank_transaction_entity.dart';
import 'package:evoapp/data/response/transaction_entity.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

void main() {
  late CommonUtilFunction commonUtilFunction;
  final DateTime expectedDateTime = DateTime(2023, 1, 2, 3, 4, 5);

  setUpAll(() {
    getIt.registerSingleton<EvoColors>(EvoColors());

    getIt.registerSingleton<CommonUtilFunction>(MockCommonUtilFunction());
    commonUtilFunction = getIt.get<CommonUtilFunction>();

    when(() => commonUtilFunction.toDateTime(any())).thenReturn(expectedDateTime);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('TransactionStatus', () {
    test('Enum values should match the string values', () {
      expect(TransactionStatus.success.value, 'success');
      expect(TransactionStatus.error.value, 'error');
      expect(TransactionStatus.pending.value, 'pending');
    });
  });

  group('TransactionEntity', () {
    const String fakeImage = 'sample_image.png';
    const int fakeId = 1;
    const String fakeAddress = 'sample_address';
    const int fakePrice = 100;
    const String fakeCreatedAt = '2022-01-01';
    const String fakeStatus = 'pending';
    const String fakeBankName = 'sample_bank_name';
    const String fakeBankLogo = 'sample_bank_logo.png';

    test('fromJson & toJson', () {
      final Map<String, dynamic> json = <String, dynamic>{
        'image': fakeImage,
        'id': fakeId,
        'address': fakeAddress,
        'price': fakePrice,
        'created_at': fakeCreatedAt,
        'status': fakeStatus,
        'bank': <String, dynamic>{
          'name': fakeBankName,
          'logo': fakeBankLogo,
        },
      };

      final TransactionEntity transactionEntity = TransactionEntity.fromJson(json);

      expect(transactionEntity.image, fakeImage);
      expect(transactionEntity.id, fakeId);
      expect(transactionEntity.address, fakeAddress);
      expect(transactionEntity.price, fakePrice);
      expect(transactionEntity.createdAt, fakeCreatedAt);
      expect(transactionEntity.status, fakeStatus);
      expect(transactionEntity.bankEntity?.name, fakeBankName);
      expect(transactionEntity.bankEntity?.logo, fakeBankLogo);
    });

    test('verify toJson() method', () {
      final TransactionEntity transactionEntity = TransactionEntity(
        image: fakeImage,
        id: fakeId,
        address: fakeAddress,
        price: fakePrice,
        createdAt: fakeCreatedAt,
        status: fakeStatus,
        bankEntity: BankTransactionEntity(
          name: fakeBankName,
          logo: fakeBankLogo,
        ),
      );

      final Map<String, dynamic> json = transactionEntity.toJson();

      expect(json['image'], fakeImage);
      expect(json['id'], fakeId);
      expect(json['address'], fakeAddress);
      expect(json['price'], fakePrice);
      expect(json['created_at'], fakeCreatedAt);
      expect(json['status'], fakeStatus);
      expect(json['bank']['name'], fakeBankName);
      expect(json['bank']['logo'], fakeBankLogo);
    });

    test('copyWith', () {
      final TransactionEntity transactionEntity = TransactionEntity(
        image: fakeImage,
        id: fakeId,
        address: fakeAddress,
        price: fakePrice,
        createdAt: fakeCreatedAt,
        status: fakeStatus,
        bankEntity: BankTransactionEntity(
          name: fakeBankName,
          logo: fakeBankLogo,
        ),
      );

      final TransactionEntity updatedTransactionEntity = transactionEntity.copyWith(
        id: 2,
        image: 'updated_image.png',
        address: 'updated_address',
        price: 200,
        createdAt: '2022-01-02',
        status: 'success',
      );

      expect(updatedTransactionEntity.id, 2);
      expect(updatedTransactionEntity.image, 'updated_image.png');
      expect(updatedTransactionEntity.address, 'updated_address');
      expect(updatedTransactionEntity.price, 200);
      expect(updatedTransactionEntity.createdAt, '2022-01-02');
      expect(updatedTransactionEntity.status, 'success');
    });

    test('copyWith with params are null', () {
      final TransactionEntity transactionEntity = TransactionEntity(
        image: fakeImage,
        id: fakeId,
        address: fakeAddress,
        price: fakePrice,
        createdAt: fakeCreatedAt,
        status: fakeStatus,
        bankEntity: BankTransactionEntity(
          name: fakeBankName,
          logo: fakeBankLogo,
        ),
      );

      final TransactionEntity updatedTransactionEntity = transactionEntity.copyWith();

      expect(updatedTransactionEntity.id, fakeId);
      expect(updatedTransactionEntity.image, fakeImage);
      expect(updatedTransactionEntity.address, fakeAddress);
      expect(updatedTransactionEntity.price, fakePrice);
      expect(updatedTransactionEntity.createdAt, fakeCreatedAt);
      expect(updatedTransactionEntity.status, fakeStatus);
    });

    test('Status related methods', () {
      final TransactionEntity transactionEntitySuccessStatus =
          TransactionEntity(id: 1, status: TransactionStatus.success.value);
      expect(transactionEntitySuccessStatus.getIconByStatus(), EvoImages.icPaymentSuccess);
      expect(transactionEntitySuccessStatus.getStringByStatus(), EvoStrings.statusSuccess);
      expect(transactionEntitySuccessStatus.getColorByStatus(), evoColors.primary);

      final TransactionEntity transactionEntityPendingStatus =
          TransactionEntity(id: 2, status: TransactionStatus.pending.value);
      expect(transactionEntityPendingStatus.getIconByStatus(), EvoImages.icPaymentPending);
      expect(transactionEntityPendingStatus.getStringByStatus(), EvoStrings.statusPending);
      expect(transactionEntityPendingStatus.getColorByStatus(), evoColors.transactionPending);

      final TransactionEntity transactionEntityErrorStatus =
          TransactionEntity(id: 3, status: TransactionStatus.error.value);
      expect(transactionEntityErrorStatus.getIconByStatus(), EvoImages.icPaymentError);
      expect(transactionEntityErrorStatus.getStringByStatus(), EvoStrings.statusFailed);
      expect(transactionEntityErrorStatus.getColorByStatus(), evoColors.error);
    });

    test('verify createdAtDateTime', () {
      final TransactionEntity transactionEntity = TransactionEntity(
        id: 1,
        createdAt: '2022-01-01T00:00:00.000Z',
      );

      final DateTime? createdAtDateTime = transactionEntity.createdAtDateTime;
      expect(createdAtDateTime, isNotNull);
      expect(createdAtDateTime?.year, expectedDateTime.year);
      expect(createdAtDateTime?.month, expectedDateTime.month);
      expect(createdAtDateTime?.day, expectedDateTime.day);
      expect(createdAtDateTime?.hour, expectedDateTime.hour);
      expect(createdAtDateTime?.minute, expectedDateTime.minute);
      expect(createdAtDateTime?.second, expectedDateTime.second);

      verify(() => commonUtilFunction.toDateTime(transactionEntity.createdAt)).called(1);
    });
  });
}
