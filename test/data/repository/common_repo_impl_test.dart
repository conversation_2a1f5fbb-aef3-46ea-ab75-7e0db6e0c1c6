import 'package:evoapp/data/repository/common_repo_impl.dart';
import 'package:evoapp/data/response/force_update_entity.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/test_util.dart';
import 'mock_common_http_client.dart';

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockEvoFlutterWrapper extends Mock implements EvoFlutterWrapper {}

void main() {
  late CommonRepoImpl commonRepo;
  late MockCommonHttpClient httpClient;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    httpClient = MockCommonHttpClient();
    commonRepo = CommonRepoImpl(httpClient);

    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
    getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
    getIt.registerLazySingleton<EvoFlutterWrapper>(() => MockEvoFlutterWrapper());
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('test getForceUpdate function', () {
    test(
        'return correct data if mock is enable. Response data have hasNewerVersion == TRUE && latestVersion = 1.0.1',
        () async {
      const String mockFileName = 'check_force_update_is_false.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() =>
              httpClient.get(CommonRepoImpl.checkForceUpdate, mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });

      final ForceUpdateEntity response = await commonRepo.getForceUpdate(
          mockConfig: const MockConfig(enable: true, fileName: mockFileName));

      verifyNever(() => httpClient.get(CommonRepoImpl.checkForceUpdate));
      expect(response.statusCode, CommonHttpClient.SUCCESS);
      expect(response.forceToUpdate, false);
      expect(response.hasNewerVersion, true);
      expect(response.latestVersion, '1.0.1');
    });

    test(
        'return correct data if mock is disable & json response is VALID && force_to_update = TRUE',
        () async {
      when(() => httpClient.get(CommonRepoImpl.checkForceUpdate)).thenAnswer((_) async {
        return BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: await TestUtil.getResponseMock('check_force_update_is_true.json'),
        );
      });

      final ForceUpdateEntity response = await commonRepo.getForceUpdate();

      verify(() => httpClient.get(CommonRepoImpl.checkForceUpdate)).called(1);
      expect(response.statusCode, CommonHttpClient.SUCCESS);
      expect(response.forceToUpdate, true);
      expect(response.hasNewerVersion, true);
      expect(response.latestVersion, '1.0.1');
    });

    test('called Logging API if mock is disable & json response is INVALID', () async {
      /// [force_to_update], [has_newer_version] must be Boolean type
      when(() => httpClient.get(CommonRepoImpl.checkForceUpdate)).thenAnswer((_) async {
        return BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: await TestUtil.getResponseMock('check_force_update_wrong_type.json'),
        );
      });

      when(() => getIt.get<LoggingRepo>().logErrorEvent(
          errorType: any(named: 'errorType'), args: any(named: 'args'))).thenAnswer((_) {
        return Future<void>.value();
      });

      final ForceUpdateEntity response = await commonRepo.getForceUpdate();

      verify(() => httpClient.get(CommonRepoImpl.checkForceUpdate)).called(1);
      expect(response.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      expect(response.statusCode, null);
      expect(response.forceToUpdate, null);
      expect(response.hasNewerVersion, null);
      expect(response.latestVersion, null);
      verify(() => getIt
          .get<LoggingRepo>()
          .logErrorEvent(errorType: any(named: 'errorType'), args: any(named: 'args'))).called(1);
    });
  });
}
