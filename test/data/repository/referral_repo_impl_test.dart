import 'package:evoapp/data/repository/referral_repo.dart';
import 'package:evoapp/data/repository/referral_repo_impl.dart';
import 'package:evoapp/data/response/referral_link_entity.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/test_util.dart';
import 'mock_common_http_client.dart';

class MockLoggingRepo extends Mock implements LoggingRepo {}

void main() {
  late MockCommonHttpClient httpClient;
  late ReferralRepo referralRepo;
  late LoggingRepo mockLoggingRepo;

  const Map<String, dynamic> fakeUnSerializableData = <String, dynamic>{'data': 123};
  final BaseResponse responseUnSerializableData =
      BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: fakeUnSerializableData);

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    httpClient = MockCommonHttpClient();
    referralRepo = ReferralRepoImpl(httpClient);

    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());

    getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
    mockLoggingRepo = getIt.get<LoggingRepo>();

    when(() => mockLoggingRepo.logErrorEvent(
        errorType: any(named: 'errorType'), args: any(named: 'args'))).thenAnswer((_) async {
      return Future<void>.value();
    });
  });

  tearDownAll(() {
    getIt.reset();
  });

  test('verify referralLinkUrl is correct', () {
    expect(ReferralRepoImpl.referralLinkUrl, 'referral-link');
  });

  group('test getReferralLink() function', () {
    const String fakeCampaignId = 'campaign_id';

    test('verify that calling getReferralLink() return referral-link & sharing message', () async {
      const String mockFileName = 'referral_link_success.json';
      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() => httpClient.get(ReferralRepoImpl.referralLinkUrl, params: any(named: 'params')))
          .thenAnswer((_) async =>
              BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));

      final ReferralLinkEntity entity =
          await referralRepo.getReferralLink(campaignId: fakeCampaignId);

      verify(() => httpClient.get(
            ReferralRepoImpl.referralLinkUrl,
            params: <String, dynamic>{'campaign_id': fakeCampaignId},
          )).called(1);

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.verdict, ReferralLinkEntity.verdictSuccess);
      expect(entity.referralLink, isNotNull);
      expect(entity.shareContent, isNotNull);
    });

    test('verify that calling getReferralLink() if user not qualified', () async {
      const String mockFileName = 'referral_link_user_not_qualified.json';
      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() => httpClient.get(ReferralRepoImpl.referralLinkUrl, params: any(named: 'params')))
          .thenAnswer((_) async =>
              BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));

      final ReferralLinkEntity entity =
          await referralRepo.getReferralLink(campaignId: fakeCampaignId);

      verify(() => httpClient.get(
            ReferralRepoImpl.referralLinkUrl,
            params: <String, dynamic>{'campaign_id': fakeCampaignId},
          )).called(1);

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.verdict, ReferralLinkEntity.verdictUserNotQualified);
      expect(entity.referralLink, isNull);
      expect(entity.shareContent, isNull);
    });

    test('verify that calling getReferralLink() if it return success but cannot parse json',
        () async {
      when(() => httpClient.get(ReferralRepoImpl.referralLinkUrl, params: any(named: 'params')))
          .thenAnswer((_) async => responseUnSerializableData);

      final ReferralLinkEntity entity =
          await referralRepo.getReferralLink(campaignId: fakeCampaignId);

      verify(() => httpClient.get(
            ReferralRepoImpl.referralLinkUrl,
            params: <String, dynamic>{'campaign_id': fakeCampaignId},
          )).called(1);

      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      expect(entity.referralLink, isNull);
      expect(entity.shareContent, isNull);
    });

    test('verify that calling getReferralLink() if it return a error', () async {
      when(() => httpClient.get(ReferralRepoImpl.referralLinkUrl, params: any(named: 'params')))
          .thenAnswer((_) async =>
              BaseResponse(statusCode: CommonHttpClient.UNKNOWN_ERRORS, response: null));

      final ReferralLinkEntity entity =
          await referralRepo.getReferralLink(campaignId: fakeCampaignId);

      verify(() => httpClient.get(
            ReferralRepoImpl.referralLinkUrl,
            params: <String, dynamic>{'campaign_id': fakeCampaignId},
          )).called(1);

      expect(entity.statusCode, CommonHttpClient.UNKNOWN_ERRORS);
      expect(entity.referralLink, isNull);
      expect(entity.shareContent, isNull);
    });
  });
}
