import 'package:evoapp/data/repository/campaign_repo.dart';
import 'package:evoapp/data/repository/campaign_repo_impl.dart';
import 'package:evoapp/data/request/voucher_request.dart';
import 'package:evoapp/data/response/campaign_list_entity.dart';
import 'package:evoapp/data/response/payment_promotion_entity.dart';
import 'package:evoapp/data/response/voucher_earning_entity.dart';
import 'package:evoapp/data/response/voucher_list_entity.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/test_util.dart';
import 'mock_common_http_client.dart';

class MockLoggingRepo extends Mock implements LoggingRepo {}

void main() {
  late MockCommonHttpClient httpClient;
  late CampaignRepoImpl campaignRepo;
  late LoggingRepo mockLoggingRepo;

  const String expectedActionType = 'open_web_view';
  const String expectedNextActionType = 'promotion_scan_to_pay';

  const Map<String, dynamic> fakeUnSerializableData = <String, dynamic>{'data': 123};
  final BaseResponse responseUnSerializableData =
      BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: fakeUnSerializableData);

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    httpClient = MockCommonHttpClient();
    campaignRepo = CampaignRepoImpl(httpClient);

    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());

    getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
    mockLoggingRepo = getIt.get<LoggingRepo>();

    when(() => mockLoggingRepo.logErrorEvent(
        errorType: any(named: 'errorType'), args: any(named: 'args'))).thenAnswer((_) async {
      return Future<void>.value();
    });
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('test getOffers() function', () {
    test('test getOffers function with mock is enable', () async {
      const String mockFileName = 'campaigns_information.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() => httpClient.get(CampaignRepoImpl.offersUrl,
          params: any(named: 'params'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });

      final CampaignListEntity entity = await campaignRepo.getOffers(
          flowType: FlowType.spotlight,
          mockConfig: const MockConfig(enable: true, fileName: mockFileName));

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.campaigns, isNotNull);
    });

    test(
        'test getOffers function with mock is disable and api return success but cannot parse json',
        () async {
      when(() => httpClient.get(CampaignRepoImpl.offersUrl, params: any(named: 'params')))
          .thenAnswer((_) async => responseUnSerializableData);

      final CampaignListEntity entity = await campaignRepo.getOffers(flowType: FlowType.spotlight);

      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('test getOffers function with mock is disable and api return fail', () async {
      when(() => httpClient.get(CampaignRepoImpl.offersUrl, params: any(named: 'params')))
          .thenAnswer(
              (_) async => BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: null));

      final CampaignListEntity entity = await campaignRepo.getOffers(flowType: FlowType.spotlight);

      expect(entity.statusCode, CommonHttpClient.BAD_REQUEST);
      expect(entity.campaigns, isNull);
    });

    test('test getOffers function with mock is disable and api return success but invalid data',
        () async {
      when(() => httpClient.get(CampaignRepoImpl.offersUrl, params: any(named: 'params')))
          .thenAnswer(
              (_) async => BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: null));

      final CampaignListEntity entity = await campaignRepo.getOffers(flowType: FlowType.spotlight);

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.campaigns, isNull);
    });

    test('test getOffers function with mock is disable, api return success and valid data',
        () async {
      const String mockFileName = 'campaigns_information.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() => httpClient.get(CampaignRepoImpl.offersUrl, params: any(named: 'params')))
          .thenAnswer((_) async =>
              BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));

      final CampaignListEntity entity = await campaignRepo.getOffers(flowType: FlowType.spotlight);

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.campaigns, isNotNull);
    });
  });

  group('test getVouchers() function', () {
    final VoucherRequest request = VoucherRequest(perPage: 100, status: 'status', pageId: 1);

    test('test getVouchers function with mock is enable', () async {
      const String mockFileName = 'my_vouchers.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() =>
          httpClient.get(CampaignRepoImpl.vouchersUrl,
              params: any(named: 'params'), mockConfig: any(named: 'mockConfig'))).thenAnswer(
          (_) async => BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));

      final VoucherListEntity entity = await campaignRepo.getVouchers(
          mockConfig: const MockConfig(enable: true, fileName: mockFileName));

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.vouchers, isNotNull);
    });

    test(
        'test getVouchers function with mock is disable and api return success but cannot parse json',
        () async {
      when(() => httpClient.get(CampaignRepoImpl.vouchersUrl, params: request.toJson()))
          .thenAnswer((_) async => responseUnSerializableData);

      final VoucherListEntity entity = await campaignRepo.getVouchers(request: request);

      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('test getVouchers function with mock is disable and api return fail', () async {
      when(() => httpClient.get(CampaignRepoImpl.vouchersUrl, params: request.toJson())).thenAnswer(
          (_) async => BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: null));

      final VoucherListEntity entity = await campaignRepo.getVouchers(request: request);

      expect(entity.statusCode, CommonHttpClient.BAD_REQUEST);
      expect(entity.vouchers, isNull);
    });

    test('test getVouchers function with mock is disable and api return success but invalid data',
        () async {
      when(() => httpClient.get(CampaignRepoImpl.vouchersUrl, params: request.toJson())).thenAnswer(
          (_) async => BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: null));

      final VoucherListEntity entity = await campaignRepo.getVouchers(request: request);

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.vouchers, isNull);
    });

    test('test getVouchers function with mock is disable, api return success and valid data',
        () async {
      const String mockFileName = 'my_vouchers.json';
      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() => httpClient.get(CampaignRepoImpl.vouchersUrl, params: any(named: 'params')))
          .thenAnswer((_) async =>
              BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));

      final VoucherListEntity entity = await campaignRepo.getVouchers(request: request);

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.vouchers, isNotNull);
    });
  });

  group('test getQualificationVoucher() function', () {
    test('test getQualificationVoucher function with mock is enable', () async {
      const String mockFileName = 'payment_promotions.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() =>
          httpClient.post(CampaignRepoImpl.vouchersQualificationUrl,
              data: any(named: 'data'), mockConfig: any(named: 'mockConfig'))).thenAnswer(
          (_) async => BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));

      final PaymentPromotionEntity entity = await campaignRepo.getQualificationVoucher(
          mockConfig: const MockConfig(enable: true, fileName: mockFileName));

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.vouchers, isNotNull);
    });

    test(
        'test getQualificationVoucher function with mock is disable and api return success but cannot parse json',
        () async {
      when(() =>
              httpClient.post(CampaignRepoImpl.vouchersQualificationUrl, data: any(named: 'data')))
          .thenAnswer((_) async => responseUnSerializableData);

      final PaymentPromotionEntity entity = await campaignRepo.getQualificationVoucher();

      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('test getQualificationVoucher function with mock is disable and api return fail',
        () async {
      when(() =>
              httpClient.post(CampaignRepoImpl.vouchersQualificationUrl, data: any(named: 'data')))
          .thenAnswer(
              (_) async => BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: null));

      final PaymentPromotionEntity entity = await campaignRepo.getQualificationVoucher();

      expect(entity.statusCode, CommonHttpClient.BAD_REQUEST);
      expect(entity.vouchers, isNull);
    });

    test(
        'test getQualificationVoucher function with mock is disable and api return success but invalid data',
        () async {
      when(() =>
              httpClient.post(CampaignRepoImpl.vouchersQualificationUrl, data: any(named: 'data')))
          .thenAnswer(
              (_) async => BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: null));

      final PaymentPromotionEntity entity = await campaignRepo.getQualificationVoucher();

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.vouchers, isNull);
    });

    test(
        'test getQualificationVoucher function with mock is disable, api return success and valid data',
        () async {
      const String mockFileName = 'payment_promotions.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() =>
              httpClient.post(CampaignRepoImpl.vouchersQualificationUrl, data: any(named: 'data')))
          .thenAnswer((_) async =>
              BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));

      final PaymentPromotionEntity entity = await campaignRepo.getQualificationVoucher();

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.vouchers, isNotNull);
    });
  });

  group('Test earnVoucherFromCampaign', () {
    test('Test earnVoucherFromCampaign with mock is enable', () async {
      const String mockFileName = 'voucher_earning_success_case_single_voucher.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() => httpClient.post(
            campaignRepo.earnVoucherFromCampaignUrl('campaignId'),
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: responseData,
          ));

      final VoucherEarningEntity entity = await campaignRepo.earnVoucherFromCampaign(
        campaignId: 'campaignId',
        mockConfig: const MockConfig(enable: true, fileName: mockFileName),
      );

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.action?.type, expectedActionType);
      expect(entity.action?.args?.nextAction?.type, expectedNextActionType);
    });

    test(
        'Test earnVoucherFromCampaign with mock is disable and api return success but cannot parse json',
        () async {
      when(() => httpClient.post(
            campaignRepo.earnVoucherFromCampaignUrl('campaignId'),
            data: any(named: 'data'),
          )).thenAnswer((_) async => responseUnSerializableData);

      final VoucherEarningEntity entity = await campaignRepo.earnVoucherFromCampaign(
        campaignId: 'campaignId',
      );

      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('Test earnVoucherFromCampaign with mock is disable and api return fail', () async {
      when(() => httpClient.post(
            campaignRepo.earnVoucherFromCampaignUrl('campaignId'),
            data: any(named: 'data'),
          )).thenAnswer((_) async => BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          ));

      final VoucherEarningEntity entity = await campaignRepo.earnVoucherFromCampaign(
        campaignId: 'campaignId',
      );

      expect(entity.statusCode, CommonHttpClient.BAD_REQUEST);
      expect(entity.action, isNull);
    });

    test(
        'Test earnVoucherFromCampaign with mock is disable and api return success but invalid data',
        () async {
      when(() => httpClient.post(
            campaignRepo.earnVoucherFromCampaignUrl('campaignId'),
            data: any(named: 'data'),
          )).thenAnswer((_) async => BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: null,
          ));

      final VoucherEarningEntity entity = await campaignRepo.earnVoucherFromCampaign(
        campaignId: 'campaignId',
      );

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.action, isNull);
    });

    test('Test earnVoucherFromCampaign with mock is disable, api return success and valid data',
        () async {
      const String mockFileName = 'voucher_earning_success_case_single_voucher.json';

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(mockFileName);

      when(() => httpClient.post(
            campaignRepo.earnVoucherFromCampaignUrl('campaignId'),
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: responseData,
          ));

      final VoucherEarningEntity entity = await campaignRepo.earnVoucherFromCampaign(
        campaignId: 'campaignId',
      );

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.action?.type, expectedActionType);
      expect(entity.action?.args?.nextAction?.type, expectedNextActionType);
    });
  });
}
