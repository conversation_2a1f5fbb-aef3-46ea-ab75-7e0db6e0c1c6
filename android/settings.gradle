pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        file("local.properties").withInputStream { properties.load(it) }
        def flutterSdkPath = properties.getProperty("flutter.sdk")
        assert flutterSdkPath != null, "flutter.sdk not set in local.properties"
        return flutterSdkPath
    }()

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version "8.1.0" apply false
    id "org.jetbrains.kotlin.android" version "1.8.0" apply false

    id "com.google.gms.google-services" version "4.3.15" apply false
    // START: FlutterFire Configuration
    id "com.google.firebase.crashlytics" version "2.8.1" apply false
    // END: FlutterFire Configuration
}

include ":app"

// Switch NFC SDK Provider - remove codes below if you want to switch to TrustVision
// FPT
def localProperties = new Properties()
def localPropertiesFile = file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
def pubCacheLocalPath = localProperties.getProperty('pubCacheLocalPath')
if (flutterRoot == null && pubCacheLocalPath == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

// same as pubspec yaml file
def fptSDKCommitHash = "f9aca86e0c2be74ee3a65fb6a13e58ea343134a7"

include ":nfc_plugin_flutter"
project(":nfc_plugin_flutter").projectDir = new File("${pubCacheLocalPath ?: flutterRoot}/.pub-cache/git/parevo-flutter-ekyc-fpt-sdk-$fptSDKCommitHash/android")

include ":libs"
project(":libs").projectDir = new File("${pubCacheLocalPath ?: flutterRoot}/.pub-cache/git/parevo-flutter-ekyc-fpt-sdk-$fptSDKCommitHash/android/libs")
