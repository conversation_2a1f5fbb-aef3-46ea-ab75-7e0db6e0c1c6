import 'package:datadog_flutter_plugin/datadog_flutter_plugin.dart';
import 'package:datadog_tracking_http_client/datadog_tracking_http_client.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';

import '../evo_flavor_value_config.dart';
import 'evo_flavor.dart';

class EvoFlavorStag extends EvoFlavor {
  @override
  CommonFlavorValues getFlavorValue() {
    return CommonFlavorValues(
      baseUrl: EvoFlavorValueConfig.baseUrlStag,
      initializeFirebaseSdk: true,
      // Staging - Onesignal account management: <EMAIL>
      oneSignalAppId: EvoFlavorValueConfig.oneSignalAppIdStag,
      commonDataDogConfig: CommonDataDogConfig(
        datadogConfiguration: DatadogConfiguration(
            clientToken: EvoFlavorValueConfig.dataDogClientTokenNonProd,
            env: EvoFlavorValueConfig.dataDogEnvNonProd,
            site: DatadogSite.us1,
            loggingConfiguration: DatadogLoggingConfiguration(),
            firstPartyHosts: <String>[EvoFlavorValueConfig.baseUrlStag],
            rumConfiguration: DatadogRumConfiguration(
              applicationId: EvoFlavorValueConfig.dataDogAppIdNonProd,
            ))
          ..enableHttpTracking(),
        logCustomTags: EvoFlavorValueConfig.dataDogLoggerTagsNonProd,
      ),
    );
  }
}
