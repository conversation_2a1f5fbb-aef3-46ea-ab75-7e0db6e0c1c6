class EvoFlavorValueConfig {
  /// Base url config
  static const String baseUrlStag = 'https://mobile-api-evo-vn-staging.trustingsocial.com/';
  static const String baseUrlUat = 'https://mobile-api-evo-vn-uat.trustingsocial.com/';
  static const String baseUrlProd = 'https://mobile-api.goevo.vn/';

  /// OneSignal App Id
  static const String oneSignalAppIdStag = '************************************';
  static const String oneSignalAppIdUat = '************************************';
  static const String oneSignalAppIdProd = '************************************';

  /// DataDog
  static const String dataDogAppIdNonProd = '66befbf4-d4de-4d4f-86da-3ba8a42ab086';
  static const String dataDogEnvNonProd = 'non-prod';
  static const String dataDogClientTokenNonProd = 'pubedde46b0f4fbcf2ea1641113c468fcb9';
  //this tag used for easier log filtering, as well as permission granting
  static const Map<String, String> dataDogLoggerTagsNonProd = <String, String>{
    'app_name': 'evo_mobile_app_non_prod',
  };

  static const String dataDogAppIdProd = '391dffa3-7a81-4e70-bdec-d35f6b7206cf';
  static const String dataDogEnvProd = 'prod';
  static const String dataDogClientTokenProd = 'pube2c934a3ca3bb833a0a45e0eccf7efaa';
  //this tag used for easier log filtering, as well as permission granting
  static const Map<String, String> dataDogLoggerTagsProd = <String, String>{
    'app_name': 'evo_mobile_app_prod',
  };
}
