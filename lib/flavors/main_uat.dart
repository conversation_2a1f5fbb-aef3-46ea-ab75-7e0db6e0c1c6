import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:firebase_performance/firebase_performance.dart';

import '../main.dart';
import '../prepare_for_app_initiation.dart';
import '../resources/global.dart';
import 'factory/evo_flavor_factory.dart';
import 'flavors_type.dart';

Future<void> main() async {
  final WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  FlavorConfig(
    flavor: FlavorType.uat.name,
    values: EvoFlavorFactory().getFlavor(FlavorType.uat).getFlavorValue(),
  );
  //init app first to make sure the firebase performance work well
  await Firebase.initializeApp(options: FlavorConfig.instance.values.firebaseOptions);
  FPAppStartMetricRecorder().startAppStartUpRecord(FirebasePerformance.instance);

  await prepareForAppInitiation();

  FPAppStartMetricRecorder()
      .recordMetric(AppStartPhase.prepareForAppInitiation.toFPMetricName());

  // lock screen Orientation to portrait
  SystemChrome.setPreferredOrientations(<DeviceOrientation>[DeviceOrientation.portraitUp])
      .then((_) {
    runApp(const MyApp());
  });
}
