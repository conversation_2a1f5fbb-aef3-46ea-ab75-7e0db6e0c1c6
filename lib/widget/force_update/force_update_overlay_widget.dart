import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../feature/check_force_update/force_update_ui_model.dart';
import '../../resources/resources.dart';

class ForceUpdateOverlayWidget extends StatefulWidget {
  final ForceUpdateUIModel uiModel;
  final VoidCallback? handleLogClickPositiveButton;

  const ForceUpdateOverlayWidget({
    required this.uiModel,
    this.handleLogClickPositiveButton,
    super.key,
  });

  @override
  State<ForceUpdateOverlayWidget> createState() => _ForceUpdateOverlayWidgetState();
}

class _ForceUpdateOverlayWidgetState extends State<ForceUpdateOverlayWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        color: evoColors.bottomSheetBackground,
      ),
      child: CommonDialogBottomSheet(
        header: widget.uiModel.imageHeader,
        title: widget.uiModel.title,
        content: widget.uiModel.content,
        dialogId: widget.uiModel.dialogId.id,
        textPositive: widget.uiModel.textPositive,
        onClickPositive: () {
          widget.handleLogClickPositiveButton?.call();
          widget.uiModel.onClickPositive?.call();
        },
        // We will disable logging event of dialog
        // Overlay will has its own logging event
        isEnableLoggingEvent: false,
      ),
    );
  }
}
