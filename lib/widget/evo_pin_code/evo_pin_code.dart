import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_theme.dart';
import 'package:flutter_common_package/widget/pin_code/pin_code_widget.dart';

import '../../resources/resources.dart';
import 'evo_pin_code_config.dart';

class EvoPinCodeWidget extends StatefulWidget {
  final TextEditingController textEditingController;
  final void Function(String)? onChange;
  final void Function(String)? onSubmit;
  final VoidCallback? onResetPin;
  final Widget? obscuringWidget;
  final String errorText;
  final String resetPinText;
  final TextStyle? errorStyle;
  final TextStyle? resetPinStyle;
  final bool autoFocus, autoUnFocus;
  final FocusNode? focusNode;

  const EvoPinCodeWidget({
    required this.textEditingController,
    super.key,
    this.onSubmit,
    this.onChange,
    this.resetPinText = '',
    this.errorStyle,
    this.onResetPin,
    this.resetPinStyle,
    this.autoFocus = true,
    this.autoUnFocus = false,
    this.errorText = '',
    this.focusNode,
    this.obscuringWidget,
  });

  @override
  State<EvoPinCodeWidget> createState() => EvoPinCodeWidgetState();
}

@visibleForTesting
class EvoPinCodeWidgetState extends State<EvoPinCodeWidget> {
  @visibleForTesting
  bool showObscureText = true;

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: const EdgeInsets.only(bottom: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _itemPinCode(),
            Padding(
              padding: const EdgeInsets.only(top: 8, bottom: 4),
              child: Text(widget.errorText,
                  style: widget.errorStyle ?? evoTextStyles.bodySmall(color: evoColors.error)),
            ),
            _itemErrorAndStatusObscure()
          ],
        ));
  }

  Widget _itemPinCode() {
    final bool hasError = widget.errorText.isNotEmpty;
    return CommonPinCode(
      focusNode: widget.focusNode,
      textController: widget.textEditingController,
      onChange: (String value) {
        widget.onChange?.call(value);
      },
      autoFocus: widget.autoFocus,
      autoUnFocus: widget.autoUnFocus,
      pinTheme: CommonPinTheme(
        fieldHeight: EvoPinCodeConfig.defaultPinCodeFieldHeight,
        inactiveColor: hasError ? evoColors.error : evoColors.inputUnfocusedColor,
        activeColor: hasError ? evoColors.error : evoColors.inputUnfocusedColor,
      ),
      onSubmit: widget.onSubmit,
      animationDuration: Duration.zero,
      obscuringWidget: showObscureText ? widget.obscuringWidget : null,
    );
  }

  Widget _itemErrorAndStatusObscure() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 8, top: 4),
            child: GestureDetector(
              onTap: widget.onResetPin,
              child: Text(widget.resetPinText,
                  style: widget.resetPinStyle ?? evoTextStyles.bodyLarge(evoColors.textPassive)),
            ),
          ),
        ),
        InkWell(
            onTap: () => setState(() => showObscureText = !showObscureText),
            child: Container(
              margin: const EdgeInsets.only(top: 4),
              padding: const EdgeInsets.all(4),
              height: 32,
              width: 32,
              child: evoImageProvider
                  .asset(showObscureText ? EvoImages.icShowOffPin : EvoImages.icShowOnPin),
            ))
      ],
    );
  }
}
