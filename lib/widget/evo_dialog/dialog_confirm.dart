import 'package:flutter/material.dart';
import 'package:flutter_common_package/ui_model/dialog_type.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../feature/feature_toggle.dart';
import '../../feature/logging/metadata_define/evo_event_metadata.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';

class EvoDialogConfirm extends StatelessWidget {
  final Widget? imageHeader;
  final String? title, textNegative;
  final String? content;
  final String textPositive;
  final Widget? footer;
  final VoidCallback? onClickPositive, onClickNegative;
  final ButtonStyle? positiveButtonStyle;
  final ButtonStyle? negativeButtonStyle;
  final TextStyle? titleTextStyle;
  final TextStyle? contentTextStyle;
  final String dialogId;
  final bool isShowButtonClose;
  final TextAlign? titleTextAlign;
  final TextAlign? contentTextAlign;
  final ButtonListOrientation? buttonListOrientation;
  final int? positiveDelayInSeconds;
  final Widget Function(int remainingTimeInSeconds)? positiveDelayOverlay;
  final Map<String, dynamic>? loggingEventOnShowMetaData;
  final Map<String, dynamic>? loggingEventMetaData;

  const EvoDialogConfirm({
    required this.textPositive,
    required this.dialogId,
    super.key,
    this.title,
    this.content,
    this.textNegative,
    this.onClickNegative,
    this.footer,
    this.onClickPositive,
    this.imageHeader,
    this.positiveButtonStyle,
    this.negativeButtonStyle,
    this.titleTextStyle,
    this.contentTextStyle,
    this.isShowButtonClose = false,
    this.titleTextAlign,
    this.contentTextAlign,
    this.buttonListOrientation,
    this.positiveDelayInSeconds,
    this.positiveDelayOverlay,
    this.loggingEventOnShowMetaData,
    this.loggingEventMetaData,
  });

  Map<String, dynamic> get preparedLoggingEventMetaData {
    final Map<String, dynamic> metaData = <String, dynamic>{
      ...?loggingEventMetaData,
    };
    final String? dopUniqueToken = getIt<AppState>().dopUniqueToken;
    if (dopUniqueToken != null) {
      metaData[EvoEventMetadataKey.uniqueToken] = dopUniqueToken;
    }
    return metaData;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(20),
      child: Container(
        decoration:
            BoxDecoration(borderRadius: BorderRadius.circular(24), color: evoColors.background),
        child: CommonDialogBottomSheet(
          title: title,
          content: content,
          textPositive: textPositive,
          textNegative: textNegative,
          footer: footer,
          onClickPositive: onClickPositive,
          onClickNegative: onClickNegative,
          header: imageHeader,
          positiveButtonStyle: positiveButtonStyle,
          negativeButtonStyle: negativeButtonStyle,
          titleTextStyle: titleTextStyle,
          contentTextStyle: contentTextStyle,
          dialogId: dialogId,
          dialogType: DialogType.dialog,
          isShowButtonClose: isShowButtonClose,
          titleTextAlign: titleTextAlign,
          contentTextAlign: contentTextAlign,
          buttonListOrientation:
              buttonListOrientation ?? ButtonListOrientation.horizontalLeftToRight,
          positiveDelayInSeconds: positiveDelayInSeconds,
          positiveDelayOverlay: positiveDelayOverlay,
          eventTrackingScreenId: getIt<AppState>().currentScreenId.name,
          loggingEventMetaData: preparedLoggingEventMetaData,
          isEnableLoggingEvent: getIt<FeatureToggle>().enableEventTrackingFeature,
          loggingEventOnShowMetaData: loggingEventOnShowMetaData,
        ),
      ),
    );
  }
}
