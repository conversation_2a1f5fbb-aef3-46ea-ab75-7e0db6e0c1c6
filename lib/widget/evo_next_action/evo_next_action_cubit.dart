import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../data/repository/campaign_repo.dart';

part 'evo_next_action_state.dart';

class EvoNextActionCubit extends CommonCubit<EvoNextActionState> {
  final CampaignRepo campaignRepo;

  EvoNextActionCubit({required this.campaignRepo}) : super(EvoNextActionMarkUsed(false));

  void updateUsedVoucher(bool? isUsed) {
    emit(EvoNextActionMarkUsed(isUsed));
  }
}
