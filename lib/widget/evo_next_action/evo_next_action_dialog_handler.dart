import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_dialog_bottom_sheet/common_dialog_bottom_sheet.dart';

import '../../data/response/action_entity.dart';
import '../../data/response/voucher_earning_entity.dart';
import '../../feature/main_screen/main_screen.dart';
import '../../feature/payment/utils/voucher_detail_action_helper.dart';
import '../../model/evo_action_model.dart';
import '../../model/evo_dialog_id.dart';
import '../../resources/resources.dart';
import '../../util/evo_action_handler.dart';
import '../../util/mapper.dart';
import '../../util/ui_utils/evo_dialog_helper.dart';

class EvoNextActionDialogHandler {
  /// BE will return the user_message and user_message_title in the SUCCESS response.
  /// Defined response here: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3442606082/Mobile+API+contract
  /// Defined the mapping of user_message and user_message_title here: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3455452330/Verdict+message+mapping
  Future<void> showVoucherEarningSuccessDialog(VoucherEarningEntity voucherEarningResult) {
    return _showBottomSheet(
      image: EvoImages.imgVoucherEarningSuccess,
      title: voucherEarningResult.userMessageTitle,
      description: voucherEarningResult.userMessage,
      ctaText: EvoStrings.voucherEarningSuccessCtaText,
      ctaCallback: () => _handleVoucherEarningSuccessCta(voucherEarningResult),
      dialogId: EvoDialogId.voucherEarningSuccessBottomSheet,
    );
  }

  Future<void> showVoucherEarningErrorDialog() {
    return _showBottomSheet(
      image: EvoImages.imgReSelectPromotion,
      title: EvoStrings.voucherEarningErrorTitle,
      ctaText: EvoStrings.moveToHome,
      ctaCallback: () => _gotoMainScreen(),
      dialogId: EvoDialogId.voucherEarningErrorBottomSheet,
    );
  }

  /// BE will return the user_message and user_message_title in the FAILED response.
  /// Defined response here: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3442606082/Mobile+API+contract
  /// Defined the mapping of user_message and user_message_title here: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3455452330/Verdict+message+mapping
  Future<void> showVoucherEarningFailedDialog(ErrorUIModel errorUIModel) {
    return _showBottomSheet(
      image: EvoImages.imgEkycError,
      title: errorUIModel.userMessageTitle,
      description: errorUIModel.userMessage,
      ctaText: EvoStrings.moveToHome,
      ctaCallback: () => _gotoMainScreen(),
      dialogId: EvoDialogId.voucherEarningFailedBottomSheet,
    );
  }

  Future<void> _showBottomSheet({
    required String image,
    required String ctaText,
    required VoidCallback ctaCallback,
    required EvoDialogId dialogId,
    String? title,
    String? description,
  }) {
    return EvoDialogHelper().showDialogBottomSheet(
      title: title,
      content: description,
      dialogId: dialogId,
      isShowButtonClose: true,
      header: evoImageProvider.asset(
        image,
        width: double.infinity,
        fit: BoxFit.fitWidth,
      ),
      buttonListOrientation: ButtonListOrientation.verticalDown,

      /// Positive button
      textPositive: ctaText,
      positiveButtonStyle: evoButtonStyles.primary(ButtonSize.xLarge),
      onClickPositive: () {
        /// Close the dialog
        navigatorContext?.maybePop();
        ctaCallback.call();
      },
    );
  }

  void _gotoMainScreen() {
    MainScreen.removeUntilAndPushReplacementNamed(isLoggedIn: true);
  }

  void _handleVoucherEarningSuccessCta(VoucherEarningEntity voucherEarningResult) {
    final ActionEntity? action = voucherEarningResult.action;
    if (action == null) {
      return;
    }

    final String? type = action.type;

    if (type == EvoActionModel.openAppScreen) {
      EvoActionHandler().handle(action.toEvoActionModel());
    } else {
      voucherDetailActionHelper.handleGotoVoucherDetailScreen(
        voucherEarningResult.toVoucherEntity(),
      );
    }
  }
}
