// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

import '../resources/resources.dart';

class EvoCheckboxWidget extends StatelessWidget {
  const EvoCheckboxWidget({
    required this.value,
    this.onTap,
    super.key,
    this.title,
  });

  final bool value;
  final VoidCallback? onTap;

  final String? title;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      child: Row(
        children: <Widget>[
          Container(
            width: 20,
            height: 20,
            padding: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: value ? evoColorsV2.borderPrimary : evoColorsV2.borderContainer,
                width: 1.5,
              ),
              color: value
                  ? evoColorsV2.backgroundPrimaryElement
                  : evoColorsV2.backgroundNeutralContainer,
            ),
            child: value ? evoImageProvider.asset(EvoImages.icCheck) : const SizedBox(),
          ),
          if (title?.isNotEmpty == true) ...<Widget>[
            SizedBox(
              width: 12,
            ),
            Text(
              title!,
              style: evoTextStylesV2.bodyMedium(
                evoColorsV2.checkBoxRadioDefault,
              ),
            )
          ]
        ],
      ),
    );
  }
}
