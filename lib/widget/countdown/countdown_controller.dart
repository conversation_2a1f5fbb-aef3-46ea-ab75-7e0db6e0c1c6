part of 'countdown_widget_builder.dart';

enum EmittingEventStatus {
  none,
  emitting,
  isEmitted,
}

class CountdownController {
  CountdownController({
    required this.onDone,
    this.onEmitEventIfNeed,
  });

  VoidCallback start = () {};
  VoidCallback cancel = () {};
  VoidCallback pause = () {};
  VoidCallback resume = () {};

  /// To check emitting event
  EmittingEventStatus emittingEventStatus = EmittingEventStatus.none;

  final VoidCallback? onEmitEventIfNeed;
  final VoidCallback onDone;
}
