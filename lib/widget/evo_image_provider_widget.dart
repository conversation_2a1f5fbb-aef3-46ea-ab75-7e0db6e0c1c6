import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';

import '../prepare_for_app_initiation.dart';
import '../resources/resources.dart';

enum TypeImage { thumbnail, banner }

class EvoNetworkImageProviderWidget extends StatelessWidget {
  final TypeImage typeImage;
  final String? imageUrl;
  final double? width;
  final double? height;
  final Color? color;
  final Color? colorBorder;
  final bool hasBorder;
  final BoxFit fit;
  final double cornerRadius;
  final Widget? placeholder;
  final Widget? errorWidget;

  const EvoNetworkImageProviderWidget(
    this.imageUrl, {
    super.key,
    this.typeImage = TypeImage.thumbnail,
    this.width,
    this.height,
    this.color,
    this.fit = BoxFit.contain,
    this.cornerRadius = 0,
    this.placeholder,
    this.errorWidget,
    this.colorBorder,
    this.hasBorder = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: hasBorder
          ? BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(cornerRadius)),
              border: Border.all(color: colorBorder ?? evoColors.placeholderBorder),
            )
          : null,
      child: evoImageProvider.network(
        imageUrl,
        width: width,
        height: height,
        color: color,
        fit: fit,
        cornerRadius: cornerRadius,
        placeholder: placeholder ?? _placeHolderWidget(),
        errorWidget: errorWidget ?? _placeHolderWidget(),
        onLoadError: _sendImageLoadErrorLoggingEvent,
      ),
    );
  }

  Widget _placeHolderWidget() {
    return evoImageProvider.asset(
      typeImage == TypeImage.thumbnail ? EvoImages.icPreThumbnailImage : EvoImages.icPreBannerImage,
      height: height,
      width: width,
      cornerRadius: cornerRadius,
      fit: fit,
    );
  }

  void _sendImageLoadErrorLoggingEvent(String url, dynamic error) {
    getIt.get<LoggingRepo>().logErrorEvent(
      errorType: 'network_image_loading_error',
      args: <String, String>{
        'url': url,
        'error': error.toString(),
      },
    );
  }
}
