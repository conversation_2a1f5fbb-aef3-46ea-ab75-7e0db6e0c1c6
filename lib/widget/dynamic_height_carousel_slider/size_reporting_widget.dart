import 'package:flutter/material.dart';

class SizeReportingWidget extends StatefulWidget {
  final Widget child;
  final ValueChanged<Size>? onSizeChange;

  const SizeReportingWidget({
    required this.child,
    super.key,
    this.onSizeChange,
  });

  @override
  State<SizeReportingWidget> createState() => _SizeReportingWidgetState();
}

class _SizeReportingWidgetState extends State<SizeReportingWidget> {
  final GlobalKey<State<StatefulWidget>> _widgetKey = GlobalKey();
  Size? _oldSize;

  @override
  void initState() {
    super.initState();
  }

  void _notifySize() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Size? size;
      final BuildContext? context = _widgetKey.currentContext;
      if (context == null) {
        return;
      }

      size = context.size;
      if (size == null) {
        return;
      }

      if (_oldSize != size) {
        _oldSize = size;
        widget.onSizeChange?.call(size);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    _notifySize();
    return NotificationListener<SizeChangedLayoutNotification>(
      onNotification: (_) {
        _notifySize();
        return true;
      },
      child: SizeChangedLayoutNotifier(
        child: Container(
          key: _widgetKey,
          child: widget.child,
        ),
      ),
    );
  }
}
