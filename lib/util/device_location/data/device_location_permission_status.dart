import 'package:flutter_common_package/common_package/common_package.dart';

enum DeviceLocationPermissionStatus {
  // user denied the permission, but we can ask again
  denied,
  // user denied the permission forever, we cannot ask again
  deniedForever,
  // user granted the permission
  granted,
  // unable to determine the permission status
  unableToDetermine;

  static DeviceLocationPermissionStatus fromPermissionStatus(PermissionStatus status) {
    return switch(status) {
      PermissionStatus.denied => DeviceLocationPermissionStatus.denied,
      PermissionStatus.granted ||
      PermissionStatus.limited =>
      DeviceLocationPermissionStatus.granted,
      PermissionStatus.permanentlyDenied ||
      PermissionStatus.restricted =>
      DeviceLocationPermissionStatus.deniedForever,
      PermissionStatus.provisional => DeviceLocationPermissionStatus.unableToDetermine,
    };
  }
}
