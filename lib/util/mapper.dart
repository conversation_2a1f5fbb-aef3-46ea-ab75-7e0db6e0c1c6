import '../feature/home_screen/non_user/v2/story/widgets/story_footer_widget.dart';
import '../data/response/action_entity.dart';
import '../data/response/emi_tenor_offer_entity.dart';
import '../data/response/story_entity.dart';
import '../data/response/voucher_earning_entity.dart';
import '../data/response/voucher_entity.dart';
import '../feature/home_screen/non_user/v2/story/model/story_content_model.dart';
import '../feature/home_screen/non_user/v2/story/model/story_view_model.dart';
import '../feature/home_screen/non_user/v2/story/widgets/story_header_widget.dart';
import '../feature/home_screen/non_user/v2/story/widgets/story_theme_widget.dart';
import '../feature/payment/models/emi_tenor_ui_model.dart';
import '../model/evo_action_model.dart';

extension ActionEntityEx on ActionEntity {
  EvoActionModel toEvoActionModel() => EvoActionModel(
      args: EvoArgs(
          link: args?.link,
          screenName: args?.screenName,
          actionLabel: args?.actionLabel,
          parameters: args?.parameters?.toParametersModel(),
          nextAction: args?.nextAction?.toEvoActionModel()),
      type: type);
}

extension ParametersEx on ParametersEntity {
  EvoParameters toParametersModel() => EvoParameters(
        id: id,
        code: code,
        creditLimit: creditLimit,
        posLimitAllow: posLimitAllow,
        shouldOpenCashbackSheet: shouldOpenCashbackSheet,
      );
}

extension EmiTenorOfferEntityEx on EmiTenorOfferEntity {
  EmiTenorUIModel toEmiTenorUIModel() {
    return EmiTenorUIModel(
      id: id,
      tenor: tenor,
      isRecommended: isRecommended ?? false,
    );
  }
}

extension VoucherEarningEntityEx on VoucherEarningEntity {
  VoucherEntity toVoucherEntity() {
    return VoucherEntity(
      id: int.tryParse(action?.args?.parameters?.id ?? ''),
      action: action,
    );
  }
}

extension StoryViewModelEx on StoryEntity {
  StoryViewModel toStoryViewModel() {
    return StoryViewModel(
      contentView: StoryThemeWidget(
        storyContentModel: StoryContentModel(
          title: title,
          description: description,
          image: themeImageUrl,
          navigation: navigation,
          footer: footer,
        ),
      ),
      footerView: StoryFooterWidget(action: footer?.toEvoActionModel(),),
      headerView: StoryHeaderWidget(),
      durationInMs: displayDurationMs ?? 0,
    );
  }
}
