import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../resources/resources.dart';
import '../../widget/animation/lottie_animation_widget.dart';
import '../../widget/hud_loading/hud_loading_simple_indicator.dart';
import '../../widget/hud_loading/widgets/hub_loading.dart';
import '../evo_flutter_wrapper.dart';

class EvoUiUtils {
  static EvoUiUtils? _instance;

  static final EvoUiUtils _originalInstance = EvoUiUtils._internal();

  factory EvoUiUtils() {
    return _instance ??= _originalInstance;
  }

  EvoUiUtils._internal();

  // Method to replace the singleton instance (for testing only)
  @visibleForTesting
  static void setInstanceForTesting(EvoUiUtils instance) {
    _instance = instance;
  }

  // Method to reset the singleton instance (for testing only)
  @visibleForTesting
  static void resetToOriginalInstance() {
    _instance = _originalInstance;
  }

  Future<void> showHudLoading({
    String? loadingText,
    String animationFile = EvoAnimation.animationHubLoading,
  }) {
    return HudLoading().show(
      overlayWidget: HubLoadingSimpleIndicator(
        animation: LottieAnimationWidget(animationFile),
        loadingText: loadingText,
      ),
    );
  }

  Future<void> hideHudLoading() {
    return HudLoading().dismiss();
  }

  void hideKeyboard() {
    FocusManager.instance.primaryFocus?.unfocus();
  }

  ScrollPhysics getScrollPhysics() {
    return evoFlutterWrapper.isIOS()
        ? const BouncingScrollPhysics()
        : const ClampingScrollPhysics();
  }

  double calculateVerticalSpace({
    required BuildContext context,
    required double heightPercentage,
  }) {
    assert(heightPercentage > 0);
    return heightPercentage * context.screenHeight;
  }

  double calculateHorizontalSpace({
    required BuildContext context,
    required double widthPercentage,
  }) {
    assert(widthPercentage > 0);
    return widthPercentage * context.screenWidth;
  }

  double? calculateVerticalOffsetRatio(BuildContext context, Offset? offset) {
    if (offset == null) {
      return null;
    }

    final double windowHeight = MediaQueryData.fromView(View.of(context)).size.height;
    final double verticalOffset = windowHeight - offset.dy - context.screenPadding.bottom;

    return verticalOffset / windowHeight;
  }
}
