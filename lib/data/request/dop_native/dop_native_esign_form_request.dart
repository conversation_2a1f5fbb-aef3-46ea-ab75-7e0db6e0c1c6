class DOPNativeESignFormRequest {
  final bool? changeState;
  final String? formStep;
  final DOPNativeESignFormData? formData;

  const DOPNativeESignFormRequest({
    this.changeState,
    this.formStep,
    this.formData,
  });

  Map<String, dynamic>? toJson() {
    final Map<String, dynamic> data = <String, dynamic>{
      'change_state': changeState,
      'form_step': formStep,
      'form_data': formData?.toJson(),
    };

    data.removeWhere((_, dynamic value) {
      return value == null;
    });
    if (data.isEmpty) {
      return null;
    }
    return data;
  }
}

class DOPNativeESignFormData {
  final bool? isUSNationality;
  final bool? isBornInUS;
  final bool? isInstructDepositToOrWithdrawFromUSAddress;
  final bool? isHaveUSAddressOrUSNumber;
  final bool? isDelegateToUSAddress;
  final bool? isHaveUniqueUSMailAddress;
  final LinkCardToEvo? payment;

  const DOPNativeESignFormData({
    this.isUSNationality,
    this.isBornInUS,
    this.isInstructDepositToOrWithdrawFromUSAddress,
    this.isHaveUSAddressOrUSNumber,
    this.isDelegateToUSAddress,
    this.isHaveUniqueUSMailAddress,
    this.payment,
  });

  Map<String, dynamic>? toJson() {
    final Map<String, dynamic> json = <String, dynamic>{
      'isUSNationality': isUSNationality,
      'isBornInUS': isBornInUS,
      'isInstructDepositToOrWithdrawFromUSAddress': isInstructDepositToOrWithdrawFromUSAddress,
      'isHaveUSAddressOrUSNumber': isHaveUSAddressOrUSNumber,
      'isDelegateToUSAddress': isDelegateToUSAddress,
      'isHaveUniqueUSMailAddress': isHaveUniqueUSMailAddress,
      'payment': payment?.toJson(),
    };
    json.removeWhere((_, dynamic value) {
      return value == null;
    });
    if (json.isEmpty) {
      return null;
    }
    return json;
  }
}

class LinkCardToEvo {
  final bool? linkToken;

  const LinkCardToEvo({this.linkToken});

  Map<String, dynamic>? toJson() {
    final Map<String, dynamic> data = <String, dynamic>{
      'link_token': linkToken,
    };
    data.removeWhere((_, dynamic value) {
      return value == null;
    });
    if (data.isEmpty) {
      return null;
    }
    return data;
  }
}
