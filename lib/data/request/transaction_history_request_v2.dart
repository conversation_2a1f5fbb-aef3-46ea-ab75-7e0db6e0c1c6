class TransactionHistoryRequestV2 {
  final int? perPage;
  final String? timeTo;

  TransactionHistoryRequestV2({
    this.perPage = 100,
    this.timeTo,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{
      'per_page': perPage,
      'time_to': timeTo,
    };
    json.removeWhere((_, dynamic value) {
      return value == null;
    });
    return json;
  }
}
