import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../response/card_benefits_entity.dart';
import '../response/hero_banner_entity.dart';
import '../response/home_page_entity.dart';
import '../response/reminder_entity.dart';
import 'base_repo.dart';
import 'home_repo.dart';

class HomeRepoImpl extends BaseRepo implements HomeRepo {
  static const String home = 'home';
  static const String cardBenefitsUrl = '$home/card-benefits';
  static const String banner = '$home/hero-banner';
  static const String latestReminder = '$home/latest_reminder';

  /// feature: visualize stories on homepage (non-login)
  /// refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3449454681/Mobile+Visualize+Stories+on+Homepage+non-login
  static const String homePageUrl = 'homepage';

  HomeRepoImpl(super.client);

  @override
  Future<CardBenefitsEntity> getHomeCardBenefits({MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.get(cardBenefitsUrl, mockConfig: mockConfig);
    final CardBenefitsEntity entity = commonUtilFunction.serialize(
            () => CardBenefitsEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        CardBenefitsEntity.unserializable();
    return entity;
  }

  @override
  Future<HeroBannerEntity> getHeroBanner({MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.get(banner, mockConfig: mockConfig);
    final HeroBannerEntity bannerEntity = commonUtilFunction.serialize(
            () => HeroBannerEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        HeroBannerEntity.unserializable();
    return bannerEntity;
  }

  @override
  Future<ReminderEntity> getLatestReminder({MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.get(latestReminder, mockConfig: mockConfig);
    final ReminderEntity reminderEntity = commonUtilFunction.serialize(
            () => ReminderEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        ReminderEntity.unserializable();
    return reminderEntity;
  }

  @override
  Future<HomepageEntity> getHomePage({MockConfig? mockConfig}) async {
    final BaseResponse response = await client.get(homePageUrl, mockConfig: mockConfig);
    final HomepageEntity storiesEntity = commonUtilFunction
            .serialize(() => HomepageEntity.fromBaseResponse(response), originalData: response) ??
        HomepageEntity.unserializable();
    return storiesEntity;
  }
}
