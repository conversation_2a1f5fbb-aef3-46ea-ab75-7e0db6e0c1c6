import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/common_request_option.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../feature/feature_toggle.dart';
import '../../util/functions.dart';
import '../../util/secure_storage_helper/secure_storage_helper.dart';
import '../constants.dart';
import '../request/face_auth_request.dart';
import '../response/reset_pin_entity.dart';
import '../response/sign_in_entity.dart';
import '../response/sign_in_otp_entity.dart';
import 'authentication_repo.dart';
import 'base_repo.dart';

class AuthenticationRepoImpl extends BaseRepo implements AuthenticationRepo {
  final CommonHttpClient nonAuthenticationEvoHttpClient;

  AuthenticationRepoImpl({
    required CommonHttpClient evoHttpClient,
    required EvoLocalStorageHelper evoLocalStorageHelper,
    required this.nonAuthenticationEvoHttpClient,
  }) : super(
          evoHttpClient,
          localStorageHelper: evoLocalStorageHelper,
        );

  // API urls
  static const String signInUrl = 'user/signin';
  static const String logoutUrl = 'user/signout';
  static const String resetPin = 'user/pin/reset';
  static const String changePin = 'user/pin/confirm-reset';

  // Refer API SPEC: https://portal-evo-vn-staging-internal.tsengineering.io/docs/#/SignIn/SigninDOP
  static const String signInFromDOPNativeUrl = 'user/dop/signin';

  // API url v1 for Non-Face OTP
  static const String verifySignInUrlV1 = '$signInUrl/verify';

  // API url v2 for Face OTP
  static const String verifySignInUrlV2 = 'v2/$signInUrl/verify';

  // API url v2 for Face Auth
  static const String verifySignInUrlV3 = 'v3/$signInUrl/verify';

  @override
  Future<BaseEntity?> logout({MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.post(logoutUrl, mockConfig: mockConfig);
    final BaseEntity baseEntity = commonUtilFunction.serialize(
          () => BaseEntity.fromBaseResponse(baseResponse),
          originalData: baseResponse,
        ) ??
        BaseEntity(localExceptionCode: CommonHttpClient.INVALID_FORMAT);
    return baseEntity;
  }

  @override
  Future<SignInEntity> verifyPhone(
      {required String phoneNumber, required TypeLogin type, MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.post(signInUrl,
        data: <String, dynamic>{
          SignInRequestApiType.phoneNumber.value: phoneNumber,
          SignInRequestApiType.type.value: type.value
        },
        mockConfig: mockConfig);

    final SignInEntity signInEntity = commonUtilFunction.serialize(
            () => SignInEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        SignInEntity.unserializable();

    return signInEntity;
  }

  @override
  Future<SignInOtpEntity> login(
    TypeLogin type, {
    FacialVerificationVersion facialVerificationVersion = FacialVerificationVersion.version_3,
    String? otp,
    String? pin,
    String? token,
    String? phoneNumber,
    String? selfieImageId,
    FaceAuthRequest? faceAuthRequest,
    String? sessionToken,
    MockConfig? mockConfig,
  }) async {
    final Map<String, dynamic> dataMap = <String, dynamic>{
      SignInRequestApiType.type.value: type.value,
      if (phoneNumber != null) SignInRequestApiType.phoneNumber.value: phoneNumber
    };
    if (type == TypeLogin.otp) {
      dataMap['otp'] = otp;
    } else if (type == TypeLogin.token) {
      dataMap['token'] = token;
    } else if (type == TypeLogin.faceOTP) {
      dataMap['selfie_image_id'] = selfieImageId;
    } else if (type == TypeLogin.faceAuth) {
      dataMap['face_auth_request'] = faceAuthRequest?.toJson();
    } else {
      dataMap['pin'] = pin;
    }

    final Map<String, dynamic> headers = <String, dynamic>{};

    final String? deviceToken = await localStorageHelper?.getDeviceToken();
    if (deviceToken?.isNotEmpty == true) {
      headers.addAll(_getDeviceTokenHeaders(deviceToken));
    }

    if (sessionToken?.isNotEmpty == true) {
      /// Store the sessionToken on memory to send to server as a header in further api in Authentication flow
      headers.addAll(_getSessionTokenHeaders(sessionToken));
    }

    /// this API handle 2 logic: face is live or not & face matching.
    /// in some case we need to wait for 30s to get result
    final Duration? receiveTimeout = type == TypeLogin.faceAuth || type == TypeLogin.faceOTP
        ? Duration(seconds: DurationConstants.facialVerificationTimeoutInSeconds)
        : null;

    final BaseResponse baseResponse = await client.post(
      switch (facialVerificationVersion) {
        FacialVerificationVersion.version_1 => verifySignInUrlV1,
        FacialVerificationVersion.version_2 => verifySignInUrlV2,
        FacialVerificationVersion.version_3 => verifySignInUrlV3,
      },
      requestOption: CommonRequestOption(
        headers: headers,
        receiveTimeout: receiveTimeout,
      ),
      data: dataMap,
      mockConfig: mockConfig,
    );

    final SignInOtpEntity signInOtpEntity = commonUtilFunction.serialize(
            () => SignInOtpEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        SignInOtpEntity.unserializable();

    if (signInOtpEntity.statusCode == CommonHttpClient.SUCCESS) {
      await _handleSignInSucceedData(
        accessToken: signInOtpEntity.accessToken,
        refreshToken: signInOtpEntity.refreshToken,
        biometricToken: signInOtpEntity.biometricToken,
        deviceToken: signInOtpEntity.deviceToken,
        userId: signInOtpEntity.userId,
        notificationAuthKey: signInOtpEntity.notificationAuthKey,
      );
    }
    return signInOtpEntity;
  }

  Map<String, String?> _getSessionTokenHeaders(String? sessionToken) {
    return <String, String?>{HeaderKey.sessionToken: sessionToken};
  }

  Map<String, String?> _getDeviceTokenHeaders(String? deviceToken) {
    return <String, String?>{HeaderKey.deviceToken: deviceToken};
  }

  /// This function mostly like [login] function.
  /// The different is [refreshToken]  function will use [nonAuthenticationEvoHttpClient] to make api call, instead of [client]
  /// We accept to duplicate some code but avoid confuse between [nonAuthenticationEvoHttpClient] and [client] if using the same [login] function
  @override
  Future<SignInOtpEntity> refreshToken(String? token) async {
    final Map<String, dynamic> dataMap = <String, dynamic>{
      'type': TypeLogin.token.value,
      'token': token,
    };

    final Map<String, dynamic> headers = <String, dynamic>{};

    final String? deviceToken = await localStorageHelper?.getDeviceToken();
    if (deviceToken?.isNotEmpty == true) {
      headers.addAll(_getDeviceTokenHeaders(deviceToken));
    }

    final BaseResponse baseResponse = await nonAuthenticationEvoHttpClient.post(
      switch (evoUtilFunction.getFacialVerificationVersion()) {
        FacialVerificationVersion.version_1 => verifySignInUrlV1,
        FacialVerificationVersion.version_2 => verifySignInUrlV2,
        FacialVerificationVersion.version_3 => verifySignInUrlV3,
      },
      requestOption: CommonRequestOption(headers: headers),
      data: dataMap,
    );

    final SignInOtpEntity signInOtpEntity = commonUtilFunction.serialize(
            () => SignInOtpEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        SignInOtpEntity.unserializable();

    if (signInOtpEntity.statusCode == CommonHttpClient.SUCCESS) {
      await _handleSignInSucceedData(
        accessToken: signInOtpEntity.accessToken,
        refreshToken: signInOtpEntity.refreshToken,
        deviceToken: signInOtpEntity.deviceToken,
        userId: signInOtpEntity.userId,
        notificationAuthKey: signInOtpEntity.notificationAuthKey,
      );
    }

    return signInOtpEntity;
  }

  @override
  Future<ResetPinEntity> requestResetPin(
    ResetPinType type, {
    String? phoneNumber,
    String? nationalId,
    String? otp,
    String? pin,
    String? sessionToken,
    MockConfig? mockConfig,
  }) async {
    String urlApi = resetPin;
    final Map<String, dynamic> dataMap = <String, dynamic>{'type': type.value};
    switch (type) {
      case ResetPinType.verifyNationalId:
        dataMap['national_id'] = nationalId;
        break;
      case ResetPinType.verifyOtp:
        dataMap['otp'] = otp;
        break;
      case ResetPinType.changePin:
        dataMap['phone_number'] = phoneNumber;
        dataMap['pin'] = pin;
        urlApi = changePin;
        break;
      case ResetPinType.resendOtp:
        dataMap['phone_number'] = phoneNumber;
        break;
      case ResetPinType.none:
        dataMap['phone_number'] = phoneNumber;
        break;
      default:
    }

    final Map<String, dynamic> headers = <String, dynamic>{};
    if (sessionToken?.isNotEmpty == true) {
      headers.addAll(_getSessionTokenHeaders(sessionToken));
    }

    final BaseResponse baseResponse = await client.patch(
      urlApi,
      requestOption: CommonRequestOption(headers: headers),
      data: dataMap,
      mockConfig: mockConfig,
    );

    final ResetPinEntity resetPinEntity = commonUtilFunction.serialize(
            () => ResetPinEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        ResetPinEntity.unSerializable();

    return resetPinEntity;
  }

  Future<void> _handleSignInSucceedData({
    required String? accessToken,
    required String? refreshToken,
    required String? deviceToken,
    String? biometricToken,
    int? userId,
    String? notificationAuthKey,
  }) async {
    return saveAuthenticationInfo(
        accessToken: accessToken,
        refreshToken: refreshToken,
        biometricToken: biometricToken,
        deviceToken: deviceToken,
        userId: userId,
        notificationAuthKey: notificationAuthKey);
  }

  /// Note: this API is not require authentication
  ///
  /// Using [nonAuthenticationEvoHttpClient] to avoid the [401] error code handled by [UnAuthorizedInterceptor]
  /// And avoid redundant Authorization header in the request
  /// This API not has the access_token of the EVO app in authentication header,
  @override
  Future<SignInOtpEntity> loginFromDOE({
    TypeLogin? type,
    String? pin,
    String? dopAccessToken,
    String? sessionToken,
    MockConfig? mockConfig,
  }) async {
    final Map<String, dynamic> dataMap = <String, dynamic>{};

    if (type != null) {
      dataMap[SignInRequestApiType.type.value] = type.value;
    }

    if (pin != null) {
      dataMap['pin'] = pin;
    }

    if (dopAccessToken != null) {
      dataMap['dop_access_token'] = dopAccessToken;
    }

    final String? deviceToken = await localStorageHelper?.getDeviceToken();
    if (deviceToken != null && type != TypeLogin.pin) {
      dataMap['device_token'] = deviceToken;
    }

    final Map<String, dynamic> headers = <String, dynamic>{};
    if (sessionToken?.isNotEmpty == true) {
      headers.addAll(_getSessionTokenHeaders(sessionToken));
    }

    final BaseResponse baseResponse = await nonAuthenticationEvoHttpClient.post(
      signInFromDOPNativeUrl,
      data: dataMap,
      requestOption: CommonRequestOption(headers: headers),
      mockConfig: mockConfig,
    );

    final SignInOtpEntity signInEntity = commonUtilFunction.serialize(
            () => SignInOtpEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        SignInOtpEntity.unserializable();

    if (signInEntity.statusCode == CommonHttpClient.SUCCESS) {
      await _handleSignInSucceedData(
        accessToken: signInEntity.accessToken,
        refreshToken: signInEntity.refreshToken,
        biometricToken: signInEntity.biometricToken,
        deviceToken: signInEntity.deviceToken,
        userId: signInEntity.userId,
        notificationAuthKey: signInEntity.authNotiToken,
      );
    }
    return signInEntity;
  }
}
