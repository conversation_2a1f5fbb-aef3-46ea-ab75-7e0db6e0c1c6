import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import '../../feature/feature_toggle.dart';
import '../response/biometric_token_entity.dart';
import '../response/card_activate_entity.dart';
import '../response/card_activation_status_entity.dart';
import '../response/card_status_entity.dart';
import '../response/link_card_submission_status_entity.dart';
import '../response/linked_card_list_entity.dart';
import '../response/linked_card_status_checking_entity.dart';
import '../response/payment_method_list_entity.dart';
import '../response/setup_pos_limit_entity.dart';
import '../response/sign_in_otp_entity.dart';
import '../response/submit_link_card_entity.dart';
import '../response/user_deletion_verification_entity.dart';
import '../response/user_entity.dart';

abstract class UserRepo {
  Future<UserEntity> getUserInfo({MockConfig? mockConfig});

  Future<SignInOtpEntity> createPin({
    required String pin,

    /// it is required with every other types but unnecessary with type = [ResetPinType.none]
    required String? sessionToken,
    MockConfig? mockConfig,
  });

  Future<BiometricTokenEntity> getBiometricTokenByPin({String? pin, MockConfig? mockConfig});

  Future<BaseEntity> deactivateAccount({MockConfig? mockConfig});

  Future<LinkedCardListEntity> getLinkedCards({MockConfig? mockConfig});

  Future<PaymentMethodListEntity> getPaymentMethods({MockConfig? mockConfig});

  Future<LinkedCardStatusCheckingEntity> checkLinkedCardsStatus({
    FacialVerificationVersion facialVerificationVersion = FacialVerificationVersion.version_3,
    MockConfig? mockConfig,
  });

  Future<SubmitLinkCardEntity> submitLinkCard({
    required String? linkCardSession,
    required String? linkCardRequestId,
    MockConfig? mockConfig,
  });

  Future<LinkCardSubmissionStatusEntity> checkLinkCardSubmissionStatus({
    String? requestId,
    MockConfig? mockConfig,
  });

  /// BE can obtain data from:
  ///   - The latest data saved in the EVO DB or
  ///   - Calling the DOP side to retrieve this data.
  /// Since calling to DOP is limited to 4 times per day, it should only be used in necessary cases.
  /// [renovateStatus] is used to indicate whether BE should call the DOP side.
  ///   - true: BE will call the DOP side.
  ///   - false: BE will retrieve the latest data saved in the EVO DB.
  Future<CardStatusEntity> getCardStatus({bool renovateStatus = false, MockConfig? mockConfig});

  Future<BaseEntity> confirmDeleteAccount({
    required String? sessionToken,
    required List<int>? reasons,
    MockConfig? mockConfig,
  });

  Future<UserDeletionVerificationEntity> verifyUserDeletionRequest({
    String? sessionToken,
    String? pin,
    MockConfig? mockConfig,
  });

  /// Confirm card activation after user goes to activated on TPBank app
  Future<BaseEntity> cardConfirmActivation({
    required String platform,
    MockConfig? mockConfig,
  });

  /// Activate card & set POS limit
  Future<CardActivateEntity> activateCard({
    required int? posLimit,
    MockConfig? mockConfig,
  });

  /// Set POS limit
  Future<SetPOSLimitEntity> setPOSLimit({
    required int? posLimit,
    MockConfig? mockConfig,
  });

  /// Get card activation Status
  Future<CardActivationStatusEntity> getCardActivationStatus({MockConfig? mockConfig});
}
