import '../response/remote_config_biometric_entity.dart';
import '../response/remote_config_common_entity.dart';
import '../response/remote_config_feature_toggle_entity.dart';

abstract class RemoteConfigRepo {
  Future<void> initConfig();

  Future<void> fetch();

  Future<RemoteConfigBiometricEntity> getBiometricConfigs();

  Future<RemoteConfigFeatureToggleEntity?> getFeatureToggle();

  Future<RemoteConfigCommonEntity> getCommonConfig();
}
