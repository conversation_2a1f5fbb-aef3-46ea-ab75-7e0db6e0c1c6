import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import '../../feature/feature_toggle.dart';
import '../request/face_auth_request.dart';
import '../response/reset_pin_entity.dart';
import '../response/sign_in_entity.dart';
import '../response/sign_in_otp_entity.dart';

enum SignInRequestApiType {
  type('type'),
  phoneNumber('phone_number');

  const SignInRequestApiType(this.value);

  final String value;
}

enum TypeLogin {
  none('none'),
  pin('verify_pin'),
  otp('otp'),
  token('token'),
  createPin('pin'),
  faceOTP('face_otp'),
  faceAuth('face_auth');

  const TypeLogin(this.value);

  final String value;
}

enum ResetPinType {
  none('none'),
  verifyNationalId('verify_national_id'),
  verifyOtp('verify_otp'),
  resendOtp('resend_otp'),
  changePin('change_pin');

  const ResetPinType(this.value);

  final String value;
}

abstract class AuthenticationRepo {
  Future<SignInEntity> verifyPhone({
    required String phoneNumber,
    required TypeLogin type,
    MockConfig? mockConfig,
  });

  Future<BaseEntity?> logout({MockConfig? mockConfig});

  Future<SignInOtpEntity> login(
    TypeLogin type, {
    FacialVerificationVersion facialVerificationVersion = FacialVerificationVersion.version_3,
    String? otp,
    String? pin,
    String? token,
    String? phoneNumber,
    String? selfieImageId,
    FaceAuthRequest? faceAuthRequest,

    /// it is required with every other types but unnecessary with type = [TypeLogin.token]
    String? sessionToken,
    MockConfig? mockConfig,
  });

  Future<SignInOtpEntity> refreshToken(String? token);

  Future<ResetPinEntity> requestResetPin(ResetPinType type,
      {String? phoneNumber,
      String? nationalId,
      String? otp,
      String? pin,
      String? sessionToken,

      /// it is required with every other types but unnecessary with type = [ResetPinType.none]
      MockConfig? mockConfig});

  /// API spec: https://portal-evo-vn-staging-internal.tsengineering.io/docs/#/SignIn/SigninDOP
  Future<SignInOtpEntity> loginFromDOE({
    TypeLogin? type,
    String? pin,
    String? dopAccessToken,
    String? sessionToken,
    MockConfig? mockConfig,
  });
}
