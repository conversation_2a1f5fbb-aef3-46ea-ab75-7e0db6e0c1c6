import 'package:flutter_common_package/data/http_client/mock_config.dart';

import '../response/card_benefits_entity.dart';
import '../response/hero_banner_entity.dart';
import '../response/home_page_entity.dart';
import '../response/reminder_entity.dart';

abstract class HomeRepo {
  Future<CardBenefitsEntity> getHomeCardBenefits({MockConfig? mockConfig});

  Future<HeroBannerEntity> getHeroBanner({MockConfig? mockConfig});

  Future<ReminderEntity> getLatestReminder({MockConfig? mockConfig});

  /// feature: visualize stories on homepage (non-login)
  /// refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3449454681/Mobile+Visualize+Stories+on+Homepage+non-login
  Future<HomepageEntity> getHomePage({MockConfig? mockConfig});
}
