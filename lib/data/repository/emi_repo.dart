import 'package:flutter_common_package/data/http_client/mock_config.dart';

import '../response/emi_record_change_entity.dart';
import '../response/emi_record_detail_entity.dart';
import '../response/emi_records_entity.dart';
import '../response/emi_rules_entity.dart';

abstract class EmiRepo {
  Future<EmiRulesEntity> getEmiRules({MockConfig? mockConfig});

  Future<EmiRecordsEntity> getEmiRecords({
    required int pageId,
    required int perPage,
    MockConfig? mockConfig,
  });

  Future<EmiRecordDetailEntity> getEmiRecordDetail({
    required String? id,
    MockConfig? mockConfig,
  });

  Future<EmiRecordRefreshEntity> getEmiRecordDetailRefresh({
    required String? id,
    MockConfig? mockConfig,
  });
}
