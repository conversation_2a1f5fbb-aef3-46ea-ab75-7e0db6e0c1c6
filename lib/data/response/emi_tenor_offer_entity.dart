class EmiTenorOfferEntity {
  final String? id;
  final int? tenor;
  final double? interestRate;
  final double? conversionFeeRate;
  final bool? isRecommended;
  final List<String>? information;

  EmiTenorOfferEntity({
    this.id,
    this.conversionFeeRate,
    this.information,
    this.interestRate,
    this.isRecommended,
    this.tenor,
  });

  EmiTenorOfferEntity.fromJson(Map<String, dynamic> json)
      : id = json['id'] as String?,
        tenor = json['tenor'] as int?,
        interestRate = double.tryParse(json['interest_rate'].toString()),
        conversionFeeRate = double.tryParse(json['conversion_fee_rate'].toString()),
        isRecommended = json['is_recommended'] as bool?,
        information =
            (json['information'] as List<dynamic>?)?.map((dynamic e) => e.toString()).toList();

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json.addAll(<String, dynamic>{
      'id': id,
      'tenor': tenor,
      'interest_rate': interestRate,
      'conversion_fee_rate': conversionFeeRate,
      'is_recommended': isRecommended,
      'information': information,
    });
    return json;
  }
}
