import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'action_entity.dart';

class HeroBannerEntity extends BaseEntity {
  final ActionEntity? action;
  final String? imageUrl;

  HeroBannerEntity({this.action, this.imageUrl});

  HeroBannerEntity.unserializable()
      : action = null,
        imageUrl = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{'image_url': imageUrl, 'action': action?.toJson()});
    return json;
  }

  HeroBannerEntity.fromBaseResponse(BaseResponse super.baseResponse)
      : action = (baseResponse.data?['action'] as Map<String, dynamic>?) != null
            ? ActionEntity.fromJson(baseResponse.data?['action'] as Map<String, dynamic>)
            : null,
        imageUrl = baseResponse.data?['image_url'] as String?,
        super.fromBaseResponse();
}
