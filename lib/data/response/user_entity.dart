import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'user_information_entity.dart';

class UserEntity extends BaseEntity {
  UserEntity({
    this.userInformation,
  });

  final UserInformationEntity? userInformation;

  UserEntity.fromBaseResponse(BaseResponse super.baseResponse)
      : userInformation = (baseResponse.data?['user_information'] as Map<String, dynamic>?) != null
            ? UserInformationEntity.fromJson(
                baseResponse.data?['user_information'] as Map<String, dynamic>)
            : null,
        super.fromBaseResponse();

  UserEntity.unserializable()
      : userInformation = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{'user_information': userInformation?.toJson()});
    return json;
  }

  @override
  String toString() {
    return 'CurrentUserEntity{userInformation: $userInformation}';
  }
}
