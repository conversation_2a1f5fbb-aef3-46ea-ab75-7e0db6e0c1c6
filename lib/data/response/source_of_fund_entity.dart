class SourceOfFundEntity {
  final String? name;
  final String? productCode;
  final String? status;

  SourceOfFundEntity({
    this.name,
    this.productCode,
    this.status,
  });

  factory SourceOfFundEntity.fromJson(Map<String, dynamic> json) => SourceOfFundEntity(
        name: json['name'] as String?,
        productCode: json['product_code'] as String?,
        status: json['status'] as String?,
      );

  Map<String, dynamic> toJson() => <String, dynamic>{
        'name': name,
        'product_code': productCode,
        'status': status,
      };
}
