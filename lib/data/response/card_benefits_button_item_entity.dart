import 'action_entity.dart';

class CardBenefitsButtonItemEntity {
  final ActionEntity? action;
  final String? title;

  CardBenefitsButtonItemEntity({this.action, this.title});

  CardBenefitsButtonItemEntity.fromJson(Map<String, dynamic> json)
      : action = (json['action'] as Map<String, dynamic>?) != null
            ? ActionEntity.fromJson(json['action'] as Map<String, dynamic>)
            : null,
        title = json['title'] as String?;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'action': action?.toJson(),
        'title': title,
      };
}
