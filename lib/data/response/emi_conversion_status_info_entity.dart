import 'dart:ui';

import '../../resources/resources.dart';
import 'emi_conversion_status_content_entity.dart';

// BE API status mapping:
// - https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3732537795/FS+EMI+EMI+Management#Resources
// - https://trustingsocial1.atlassian.net/browse/EMA-3257
enum EmiConversionStatusType {
  received('received'),
  autoApproved('auto_approved'),
  manualApproved('manual_approved'),
  rejected('rejected'),
  done('done'),

  /// This status is defined for using when the status from API response is difference with all.
  unknown('unknown');

  final String value;

  const EmiConversionStatusType(this.value);

  static EmiConversionStatusType fromValue(String? value) {
    switch (value) {
      case 'received':
        return EmiConversionStatusType.received;
      case 'auto_approved':
        return EmiConversionStatusType.autoApproved;
      case 'manual_approved':
        return EmiConversionStatusType.manualApproved;
      case 'rejected':
        return EmiConversionStatusType.rejected;
      case 'done':
        return EmiConversionStatusType.done;
      default:
        return EmiConversionStatusType.unknown;
    }
  }

  Color get titleColor => switch (this) {
        EmiConversionStatusType.received => evoColors.transactionHistoryEmiTitleReceived,
        EmiConversionStatusType.autoApproved ||
        EmiConversionStatusType.manualApproved =>
          evoColors.transactionHistoryEmiTitleApproved,
        EmiConversionStatusType.rejected => evoColors.transactionHistoryEmiTitleRejected,
        EmiConversionStatusType.done => evoColors.transactionHistoryEmiTitleDone,
        EmiConversionStatusType.unknown => evoColors.transactionHistoryEmiTitleUnknown,
      };

  Color get statusColor => switch (this) {
        EmiConversionStatusType.received => evoColors.transactionHistoryEmiStatusReceived,
        EmiConversionStatusType.autoApproved ||
        EmiConversionStatusType.manualApproved =>
          evoColors.transactionHistoryEmiStatusApproved,
        EmiConversionStatusType.rejected => evoColors.transactionHistoryEmiStatusRejected,
        EmiConversionStatusType.done => evoColors.transactionHistoryEmiStatusDone,
        EmiConversionStatusType.unknown => evoColors.transactionHistoryEmiStatusUnknown,
      };
}

class EmiConversionStatusInfoEntity {
  final EmiConversionStatusType? status;
  final EmiInfoConversionStatusContentEntity? content;

  EmiConversionStatusInfoEntity({this.status, this.content});

  EmiConversionStatusInfoEntity.fromJson(Map<String, dynamic> json)
      : status = EmiConversionStatusType.fromValue(json['status'] as String?),
        content = json['content'] != null
            ? EmiInfoConversionStatusContentEntity.fromJson(json['content'])
            : null;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'status': status?.value,
        'content': content?.toJson(),
      };
}
