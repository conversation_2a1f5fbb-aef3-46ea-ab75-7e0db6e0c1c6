class MerchantInfoEntity {
  final String? address;
  final String? banner;
  final String? id;
  final String? largeBanner;
  final String? name;
  final String? status;
  final String? thumbnail;

  MerchantInfoEntity({
    this.address,
    this.banner,
    this.id,
    this.largeBanner,
    this.name,
    this.status,
    this.thumbnail,
  });

  factory MerchantInfoEntity.fromJson(Map<String, dynamic> json) => MerchantInfoEntity(
        address: json['address'] as String?,
        banner: json['banner'] as String?,
        id: json['id'] as String?,
        largeBanner: json['large_banner'] as String?,
        name: json['name'] as String?,
        status: json['status'] as String?,
        thumbnail: json['thumbnail'] as String?,
      );

  Map<String, dynamic> toJson() => <String, dynamic>{
        'address': address,
        'banner': banner,
        'id': id,
        'large_banner': largeBanner,
        'name': name,
        'status': status,
        'thumbnail': thumbnail,
      };
}
