import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

class SignInEntity extends BaseEntity {
  /// Status code [CommonHttpClient.SUCCESS]
  static const String verdictSuccess = 'success';
  static const String verdictUserNotExisted = 'record_not_found';
  static const String verdictUnsupportedMobileNumber = 'unsupported_telco';

  /// Status code [CommonHttpClient.BAD_REQUEST]
  static const String verdictInvalidParameter = 'invalid_parameters';
  static const String verdictDeletedUser = 'deleted_user';
  static const String verdictNotFinishedDOP = 'not_finished_dop';

  ///if OTPs are generated too many times
  ///status code [CommonHttpClient.LIMIT_EXCEEDED]
  static const String verdictLimitExceeded = 'limit_exceeded';

  /// DOP drop-off prior to FaceOTP step
  /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/EVO/pages/3682140286/Using+FaceAuthen+FaceOTP+for+2FA+authentication#ii.-User-drops-off-prior-to-FaceOTP-step
  static const String verdictNoEKYCData = 'no_ekyc_data';

  static const int defaultResendOtpIntervalTimeInSecs = 60; // 60 seconds

  final String? challengeType;
  final int otpResendSecs;
  final int? otpValiditySecs;
  final String? sessionToken;

  SignInEntity({
    this.challengeType,
    this.otpResendSecs = defaultResendOtpIntervalTimeInSecs,
    this.otpValiditySecs,
    this.sessionToken,
  });

  SignInEntity.unserializable()
      : challengeType = null,
        otpResendSecs = 0,
        otpValiditySecs = null,
        sessionToken = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  SignInEntity.fromBaseResponse(BaseResponse super.baseResponse)
      : challengeType = baseResponse.data?['challenge_type'] as String?,
        otpResendSecs =
            baseResponse.data?['otp_resend_secs'] as int? ?? defaultResendOtpIntervalTimeInSecs,
        otpValiditySecs = baseResponse.data?['otp_validity_secs'] as int?,
        sessionToken = baseResponse.data?['session_token'] as String?,
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'challenge_type': challengeType,
      'otp_resend_secs': otpResendSecs,
      'otp_validity_secs': otpValiditySecs,
      'session_token': sessionToken
    });
    return json;
  }
}
