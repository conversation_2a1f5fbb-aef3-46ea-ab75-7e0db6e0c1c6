import 'package:flutter_common_package/util/utils.dart';

import 'action_entity.dart';
import 'visualization_entity.dart';

class VoucherEntity {
  VoucherEntity({
    this.id,
    this.action,
    this.burnAction,
    this.banner,
    this.thumbnail,
    this.code,
    this.description,
    this.formattedExpiry,
    this.status,
    this.terms,
    this.title,
    this.validFrom,
    this.validTo,
    this.visualizations,
    this.isUsed,
    this.isQualified,
  });

  final ActionEntity? action;
  final ActionEntity? burnAction;
  final String? banner;
  final String? code;
  final String? description;
  final String? thumbnail;
  final String? formattedExpiry;
  final int? id;
  final String? status;
  final String? terms;
  final String? title;
  final String? validFrom;
  final String? validTo;
  final List<VisualizationEntity>? visualizations;
  final bool? isUsed;
  final bool? isQualified;

  DateTime? get validFromDateTime => commonUtilFunction.toDateTime(validFrom);

  DateTime? get validToDateTime => commonUtilFunction.toDateTime(validTo);

  VoucherEntity.fromJson(Map<String, dynamic> json)
      : action = (json['action'] as Map<String, dynamic>?) != null
            ? ActionEntity.fromJson(json['action'] as Map<String, dynamic>)
            : null,
        burnAction = (json['burn_action'] as Map<String, dynamic>?) != null
            ? ActionEntity.fromJson(json['burn_action'] as Map<String, dynamic>)
            : null,
        banner = json['banner'] as String?,
        thumbnail = json['thumbnail'] as String?,
        code = json['code'] as String?,
        description = json['description'] as String?,
        formattedExpiry = json['formatted_expiry'] as String?,
        id = json['id'] as int?,
        status = json['status'] as String?,
        terms = json['terms'] as String?,
        title = json['title'] as String?,
        validFrom = json['valid_from'] as String?,
        validTo = json['valid_to'] as String?,
        visualizations = (json['visualizations'] as List<dynamic>?)
            ?.map((dynamic e) => VisualizationEntity.fromJson(e as Map<String, dynamic>))
            .toList(),
        isUsed = json['used'] as bool?,
        isQualified = json['is_qualified'] as bool?;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'action': action?.toJson(),
        'burn_action': burnAction?.toJson(),
        'banner': banner,
        'thumbnail': thumbnail,
        'code': code,
        'description': description,
        'formatted_expiry': formattedExpiry,
        'id': id,
        'status': status,
        'terms': terms,
        'title': title,
        'valid_from': validFrom,
        'valid_to': validTo,
        'visualizations': visualizations?.map((VisualizationEntity x) => x.toJson()).toList(),
        'used': isUsed,
        'is_qualified': isQualified,
      };
}
