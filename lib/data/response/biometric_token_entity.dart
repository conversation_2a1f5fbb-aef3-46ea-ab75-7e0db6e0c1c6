import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

class BiometricTokenEntity extends BaseEntity {
  /// Status code [CommonHttpClient.BAD_REQUEST]
  static const String verdictWarningLastTimeToVerify = 'one_last_try';
  static const String verdictInvalidCredential = 'invalid_credential';

  /// the user will be temporary blocked if P<PERSON> is verified failed 5 times
  ///status code [CommonHttpClient.LIMIT_EXCEEDED]
  static const String verdictLimitExceeded = 'limit_exceeded';

  final String? accessToken;
  final String? refreshToken;
  final String? deviceToken;
  final String? biometricToken;
  final String? challengeType;

  BiometricTokenEntity.unserializable()
      : accessToken = null,
        refreshToken = null,
        deviceToken = null,
        biometricToken = null,
        challengeType = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  BiometricTokenEntity.fromBaseResponse(BaseResponse super.baseResponse)
      : accessToken = baseResponse.data?['access_token'] as String?,
        refreshToken = baseResponse.data?['refresh_token'] as String?,
        deviceToken = baseResponse.data?['device_token'] as String?,
        biometricToken = baseResponse.data?['biometric_token'] as String?,
        challengeType = baseResponse.data?['challenge_type'] as String?,
        super.fromBaseResponse();

  @visibleForTesting
  BiometricTokenEntity.newInstance({
    this.accessToken,
    this.refreshToken,
    this.biometricToken,
    this.challengeType,
    this.deviceToken,
  });

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'device_token': deviceToken,
      'biometric_token': biometricToken,
      'challenge_type': challengeType
    });
    return json;
  }
}
