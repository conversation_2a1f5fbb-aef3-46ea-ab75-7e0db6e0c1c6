class VNPayQrInfoEntity {
  final String? mid;
  final String? storeLabel;
  final String? type;
  final int? amount;
  final String? merchantName;
  final String? qrCode;
  final String? referenceLabel;

  VNPayQrInfoEntity({
    this.type,
    this.mid,
    this.storeLabel,
    this.amount,
    this.merchantName,
    this.qrCode,
    this.referenceLabel,
  });

  VNPayQrInfoEntity.fromJson(Map<String, dynamic> response)
      : storeLabel = response['store_label'] as String?,
        merchantName = response['merchant_name'] as String?,
        mid = response['mid'] as String?,
        type = response['type'] as String?,
        qrCode = response['qr_code'] as String?,
        referenceLabel = response['reference_label'] as String?,
        amount = response['amount'] as int?;

  Map<String, dynamic> toJson() => <String, dynamic>{
        'store_label': storeLabel,
        'merchant_name': merchantName,
        'mid': mid,
        'type': type,
        'amount': amount,
        'qr_code': qrCode,
        'reference_label': referenceLabel,
      };

  @override
  String toString() {
    return 'VNPayQrInfoEntity { storeLabel: $storeLabel, merchantName: $merchantName ,mid: $mid, type: $type, amount: $amount, qr_code: $qrCode, referenceLabel: $referenceLabel}';
  }
}
