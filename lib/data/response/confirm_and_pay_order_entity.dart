import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'order_session_entity.dart';

class ConfirmAndPayOrderEntity extends BaseEntity {
  /// Status code [CommonHttpClient.BAD_REQUEST]
  static const String verdictExpiredToken = 'expired_token';
  static const String verdictInvalidCredential = 'invalid_credential';
  static const String verdictOneLastTry = 'one_last_try';
  static const String verdictSessionExpired = 'session_expired';
  static const String verdictSessionNotOpened = 'session_not_opened';
  static const String verdictUserInactive = 'user_inactive';
  static const String verdictPaymentMethodInvalid = 'payment_method_invalid';
  static const String verdictCreditLimitInsufficientLimit = 'credit_limit_insufficient';

  /// Status code [CommonHttpClient.LIMIT_EXCEEDED]
  static const String verdictLimitExceed = 'limit_exceed';
  static const String verdictTransactionTooSoon = 'transaction_too_soon';

  /// Show toast (snack bar) error
  static const String verdictMissingPaymentMethod = 'payment_invalid';
  static const String verdictPromotionInvalid = 'promotion_invalid';

  /// PRD: https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/3271655937/Payment+-+Response+code+V2#Update-%26-Confirm-Session
  /// The server does not return any verdicts below
  ///  However, it is mentioned in the PRD (part [Update & Confirm Session]), so it must be handled
  static const String verdictPromotionExpired = 'promotion_expired_data';
  static const String verdictPromotionUnqualified = 'promotion_unqualified';
  static const String verdictPromotionDuplicate = 'promotion_duplicate';
  static const String verdictPromotionPermissionDenied = 'promotion_permission_denied';

  final OrderSessionEntity? session;

  ConfirmAndPayOrderEntity({
    this.session,
  });

  ConfirmAndPayOrderEntity.fromBaseResponse(BaseResponse super.baseResponse)
      : session = (baseResponse.data?['session'] as Map<String, dynamic>?) != null
            ? OrderSessionEntity.fromJson(
                baseResponse.data?['session'] as Map<String, dynamic>,
              )
            : null,
        super.fromBaseResponse();

  ConfirmAndPayOrderEntity.unserializable()
      : session = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{'session': session?.toJson()});
    return json;
  }

  @override
  String toString() {
    return 'ConfirmAndPayOrderEntity{session: $session}';
  }
}
