class DOPNativePersonalInfoEntity {
  final String? birthday;
  final String? fullName;

  /// '1' : male, '2' female
  /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3534357392/S4+KH+ki+m+tra+th+ng+tin+OCR+v+nh+p+th+ng+tin+c+b+n#1.-Get-OCR-data
  final String? gender;
  final String? idCard;
  final String? idIssueDate;
  final String? idIssuePlaceId;
  final String? oldIDCard;
  final String? email;
  final String? cardOpenPurpose;
  final String? secretQuestion;
  final String? idExpiryDate;

  const DOPNativePersonalInfoEntity({
    this.birthday,
    this.fullName,
    this.gender,
    this.idCard,
    this.idIssueDate,
    this.idIssuePlaceId,
    this.oldIDCard,
    this.email,
    this.cardOpenPurpose,
    this.secretQuestion,
    this.idExpiryDate,
  });

  DOPNativePersonalInfoEntity.fromJson(Map<String, dynamic> json)
      : birthday = json['birthday'] as String?,
        fullName = json['fullName'] as String?,
        gender = json['gender'] as String?,
        idCard = json['idCard'] as String?,
        idIssueDate = json['idIssueDate'] as String?,
        idIssuePlaceId = json['idIssuePlaceId'] as String?,
        oldIDCard = json['oldIDCard'] as String?,
        email = json['email'] as String?,
        cardOpenPurpose = json['cardOpenPurpose'] as String?,
        secretQuestion = json['secretQuestion'] as String?,
        idExpiryDate = json['idExpiryDate'] as String?;

  /// Returned json should exclude null value field
  /// Refer: https://trustingsocial.slack.com/archives/C06BBSRR99P/p1714891289576959
  Map<String, dynamic>? toJson() {
    final Map<String, dynamic> json = <String, dynamic>{
      'birthday': birthday,
      'fullName': fullName,
      'gender': gender,
      'idCard': idCard,
      'idIssueDate': idIssueDate,
      'idIssuePlaceId': idIssuePlaceId,
      'oldIDCard': oldIDCard,
      'email': email,
      'cardOpenPurpose': cardOpenPurpose,
      'secretQuestion': secretQuestion,
      'idExpiryDate': idExpiryDate,
    };

    json.removeWhere((_, dynamic value) {
      return value == null;
    });
    if (json.isEmpty) {
      return null;
    }
    return json;
  }

  DOPNativePersonalInfoEntity copyWith({
    String? birthday,
    String? fullName,
    String? gender,
    String? idCard,
    String? idIssueDate,
    String? idIssuePlaceId,
    String? oldIDCard,
    String? email,
    String? cardOpenPurpose,
    String? secretQuestion,
    String? idExpiryDate,
  }) {
    return DOPNativePersonalInfoEntity(
      birthday: birthday ?? this.birthday,
      fullName: fullName ?? this.fullName,
      gender: gender ?? this.gender,
      idCard: idCard ?? this.idCard,
      idIssueDate: idIssueDate ?? this.idIssueDate,
      idIssuePlaceId: idIssuePlaceId ?? this.idIssuePlaceId,
      oldIDCard: oldIDCard ?? this.oldIDCard,
      email: email ?? this.email,
      cardOpenPurpose: cardOpenPurpose ?? this.cardOpenPurpose,
      secretQuestion: secretQuestion ?? this.secretQuestion,
      idExpiryDate: idExpiryDate ?? this.idExpiryDate,
    );
  }
}
