import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

class DOPNativeRegistrationCampaignEntity extends BaseEntity {
  final String? campaignCode;
  final String? challengeType;
  final String? source;
  final List<String>? uiFlow;
  final String? uiVersion;

  DOPNativeRegistrationCampaignEntity.unserializable()
      : campaignCode = null,
        challengeType = null,
        source = null,
        uiFlow = null,
        uiVersion = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  DOPNativeRegistrationCampaignEntity({
    this.campaignCode,
    this.challengeType,
    this.source,
    this.uiFlow,
    this.uiVersion,
  });

  DOPNativeRegistrationCampaignEntity.fromBaseResponse(BaseResponse super.baseResponse)
      : campaignCode = baseResponse.data?['campaign_code'] as String?,
        challengeType = baseResponse.data?['challenge_type'] as String?,
        source = baseResponse.data?['source'] as String?,
        uiFlow = (baseResponse.data?['ui_flow'] as List<dynamic>?)
            ?.map((dynamic e) => e as String)
            .toList(),
        uiVersion = baseResponse.data?['ui_version'] as String?,
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'campaign_code': campaignCode,
      'challenge_type': challengeType,
      'source': source,
      'ui_flow': uiFlow,
      'ui_version': uiVersion,
    });
    return json;
  }
}
