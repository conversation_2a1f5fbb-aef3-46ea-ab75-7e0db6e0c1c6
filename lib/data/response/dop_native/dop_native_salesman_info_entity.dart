class DOPNativeSalesmanInfoEntity {
  final String? salesmanID;
  final String? salesmanIDConfirm;

  const DOPNativeSalesmanInfoEntity({
    this.salesmanID,
    this.salesmanIDConfirm,
  });

  DOPNativeSalesmanInfoEntity.fromJson(Map<String, dynamic> json)
      : salesmanID = json['salemanID'] as String?,
        salesmanIDConfirm = json['salemanIDConfirm'] as String?;

  Map<String, dynamic>? toJson() {
    final Map<String, dynamic> json = <String, dynamic>{
      'salemanID': salesmanID,
      'salemanIDConfirm': salesmanIDConfirm,
    };
    json.removeWhere((_, dynamic value) {
      return value == null;
    });
    if (json.isEmpty) {
      return null;
    }
    return json;
  }
}
