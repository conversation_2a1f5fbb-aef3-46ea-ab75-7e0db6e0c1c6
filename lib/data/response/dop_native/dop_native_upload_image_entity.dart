import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

class DOPNativeUploadImageEntity extends BaseEntity {
  final String? imageId;

  DOPNativeUploadImageEntity({
    this.imageId,
  });

  DOPNativeUploadImageEntity.unserializable()
      : imageId = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  DOPNativeUploadImageEntity.fromBaseResponse(BaseResponse super.response)
      : imageId = response.data?['image_id'],
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'image_id': imageId,
    });
    return json;
  }
}
