class CardInfoEntity {
  final String? brand;
  final String? issuer;
  final String? last4;
  final String? productId;
  final String? type;

  CardInfoEntity({
    this.brand,
    this.issuer,
    this.last4,
    this.productId,
    this.type,
  });

  factory CardInfoEntity.fromJson(Map<String, dynamic> json) => CardInfoEntity(
        brand: json['brand'] as String?,
        issuer: json['issuer'] as String?,
        last4: json['last4'] as String?,
        productId: json['product_id'] as String?,
        type: json['type'] as String?,
      );

  Map<String, dynamic> toJson() => <String, dynamic>{
        'brand': brand,
        'issuer': issuer,
        'last4': last4,
        'product_id': productId,
        'type': type,
      };
}
