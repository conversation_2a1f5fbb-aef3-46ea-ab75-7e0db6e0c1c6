import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'emi_record_entity.dart';

class EmiRecordsEntity extends BaseEntity {
  final int? total;
  final List<EmiRecordEntity>? records;

  EmiRecordsEntity({
    this.total,
    this.records,
  });

  EmiRecordsEntity.unserializable()
      : total = null,
        records = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  EmiRecordsEntity.fromBaseResponse(BaseResponse super.response)
      : total = response.data?['total'] as int?,
        records = (response.data?['items'] as List<dynamic>?) != null
            ? (response.data?['items'] as List<dynamic>)
                .map((dynamic e) => EmiRecordEntity.fromJson(e as Map<String, dynamic>))
                .toList()
            : null,
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(
      <String, dynamic>{
        'total': total,
        'items': records?.map((EmiRecordEntity v) => v.toJson()).toList(),
      },
    );
    return json;
  }
}
