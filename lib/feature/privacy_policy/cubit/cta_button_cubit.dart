import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../resources/global.dart';
import 'cta_button_state.dart';

class CTAButtonCubit extends CommonCubit<CTAButtonState> {
  CTAButtonCubit() : super(CTAButtonInitState());

  void showCTAButton() {
    emit(CTAButtonLoadedState());
  }

  Future<void> enableCTAButton(int? decreeVersionId) async {
    emit(CTAButtonPendingEnableState());
    await Future<void>.delayed(const Duration(seconds: delayTimeEnableCTAPrivatePolicy));
    emit(CTAButtonLoadedState(enableCTAButton: true, decreeVersionId: decreeVersionId));
  }
}
