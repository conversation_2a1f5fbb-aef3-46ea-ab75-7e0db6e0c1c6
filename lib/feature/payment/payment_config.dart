import '../../prepare_for_app_initiation.dart';
import '../feature_toggle.dart';
import 'qrcode_scanner/model/qr_code_type.dart';

class PaymentConfig {
  static final PaymentConfig _singleton = PaymentConfig._internal();

  factory PaymentConfig() {
    return _singleton;
  }

  PaymentConfig._internal();

  static const int defaultMinPaymentAmount = 1000;
  static const int defaultMaxPaymentAmount = 75000000;

  /// Vietnamese, Number, Dot,Comma
  static const String allowPaymentDescChar =
      '[0-9a-zA-Z ., áàảãạăắằẳẵặâấầẩẫậđéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵÁÀẢÃẠĂẮẰẲẴẶÂẤẦẨẪẬĐÉÈẺẼẸÊẾỀỂỄỆÍÌỈĨỊÓÒỎÕỌÔỐỒỔỖỘƠỚỜỞỠỢÚÙỦŨỤƯỨỪỬỮỰÝỲỶỸỴ]';

  /// DO NOT change as it is in contract with server.
  /// Change this value will break feature.
  static const String paymentRedirectUrl = 'evo://payment-processing';
  static const int intervalPollingGetCheckOutDetailInSec = 1;

  /// Updated to 5 minutes = 300s
  /// https://trustingsocial1.atlassian.net/browse/EMA-3786
  static const int timeOutPollingGetCheckOutInSec = 300;
  static const int descriptionMaxLength = 135;
  static const int applyPromotionTimeOutInSec = 5;

  /// Remarks Max Length
  /// Refer: https://trustingsocial1.atlassian.net/browse/EMA-1132
  static const int remarksMaxLength = 100;

  /// Default dialogue box height is 2 lines of input, maximum height is 3 lines
  /// Refer to: https://trustingsocial1.atlassian.net/browse/EMA-1505
  static const int noteV2MinLine = 2;
  static const int noteV2MaxLine = 3;
  static const int noteTitleMaxLine = 3;

  RegExp getPaymentDescRegExp() {
    return RegExp(allowPaymentDescChar);
  }

  bool isQrProductCodeSupported(QrProductCode? code) {
    final FeatureToggle featureToggle = getIt.get<FeatureToggle>();

    /// TODO remove [featureToggle.enableScanDynamicQrCodeFeature] when the dynamic QR code scanning feature is released
    if (featureToggle.enableDynamicQrCodeFeature == QrCodeFeatureVersion.version_1) {
      return code == QrProductCode.pa02;
    }
    return code == QrProductCode.pa02 || code == QrProductCode.pa03 || code == QrProductCode.vn01;
  }

  ///EMI
  static const int paymentResultEmiNoteErrorMaxLines = 3;
  static const int paymentResultEmiDescriptionMaxLines = 3;
  static const int paymentResultEmiTitleMaxLines = 3;
  static const String paymentResultEmiFormatDatetime = 'HH:mm';
}
