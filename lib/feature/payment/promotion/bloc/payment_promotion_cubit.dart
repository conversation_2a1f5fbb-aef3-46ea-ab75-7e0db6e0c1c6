import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/campaign_repo.dart';
import '../../../../data/request/payment_promotion_request.dart';
import '../../../../data/response/payment_promotion_entity.dart';
import '../../../../data/response/voucher_entity.dart';
import '../../../../util/mock_file_name_utils/mock_campaign_file_name.dart';
import '../model/payment_promotion_model.dart';

part 'payment_promotion_state.dart';

class PaymentPromotionCubit extends CommonCubit<PaymentPromotionState> {
  final CampaignRepo _campaignRepo;

  final List<PaymentPromotionModel> _promotionsData = <PaymentPromotionModel>[];
  VoucherEntity? currentSelectedVoucher;

  @visibleForTesting
  List<PaymentPromotionModel> get promotionsData => _promotionsData;

  @visibleForTesting
  bool hasValidVoucher = false;

  PaymentPromotionCubit(this._campaignRepo) : super(PaymentPromotionInitial());

  Future<void> getPromotion(
    String? sessionId, {
    bool isShowLoading = false,
  }) async {
    if (isShowLoading) {
      emit(PaymentPromotionLoading());
    }

    final PaymentPromotionEntity paymentPromotionEntity =
        await _campaignRepo.getQualificationVoucher(
            request: PaymentPromotionRequest(sessionId: sessionId),
            mockConfig: MockConfig(
              enable: false,
              fileName: getQualificationVoucherMockFileName(),
            ));

    if (paymentPromotionEntity.statusCode == CommonHttpClient.SUCCESS) {
      if (_promotionsData.isNotEmpty) {
        _promotionsData.clear();
      }

      final List<PaymentPromotionModel> promotionsDataConverted =
          convertDataToModel(paymentPromotionEntity.vouchers);
      _promotionsData.addAll(promotionsDataConverted);

      emit(PaymentPromotionSuccess(
        vouchers: _promotionsData,
        hasValidVoucher: hasValidVoucher,
      ));

      /// We will check previous selected voucher is valid or not valid
      /// in 1st time get promotion list
      if (isShowLoading == true) {
        checkPreviousVoucherSelectedStatus();
      }
    } else {
      emit(PaymentPromotionError(ErrorUIModel.fromEntity(paymentPromotionEntity)));
    }
  }

  void updateUnselectedVoucher() {
    currentSelectedVoucher = null;

    for (final PaymentPromotionModel element in _promotionsData) {
      element.isCanSelected = true;
      element.isSelected = false;
    }
    emit(PaymentPromotionSuccess(
      vouchers: _promotionsData,
      hasValidVoucher: hasValidVoucher,
    ));
  }

  @visibleForTesting
  void checkPreviousVoucherSelectedStatus() {
    /// Because we always add selected voucher on the top of list.
    /// So if first item is not selected it's mean previous voucher is not valid
    if (_promotionsData.firstOrNull?.isSelected != true && currentSelectedVoucher != null) {
      emit(PaymentPromotionPreviousSelectedNotFound());
    }
  }

  List<PaymentPromotionModel> convertDataToModel(List<VoucherEntity>? vouchers) {
    final bool hasSelectedVoucher = vouchers?.firstWhereOrNull(
            (VoucherEntity voucherItem) => voucherItem.id == currentSelectedVoucher?.id) !=
        null;

    final List<PaymentPromotionModel> data = <PaymentPromotionModel>[];

    for (final VoucherEntity voucherItem in vouchers ?? <VoucherEntity>[]) {
      final bool isSelected = voucherItem.id == currentSelectedVoucher?.id;

      hasValidVoucher = hasValidVoucher || (voucherItem.isQualified ?? false);

      final PaymentPromotionModel paymentPromotionModel = PaymentPromotionModel(
        isSelected: isSelected,
        isCanSelected: !hasSelectedVoucher,
        voucher: voucherItem,
      );

      /// Add selected item on the top of list
      if (isSelected) {
        data.insert(0, paymentPromotionModel);
      } else {
        data.add(paymentPromotionModel);
      }
    }
    return data;
  }
}
