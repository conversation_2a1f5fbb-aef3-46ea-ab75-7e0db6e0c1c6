import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../../data/response/emi_package_entity.dart';
import '../../../../data/response/order_session_entity.dart';
import '../../../../data/response/voucher_entity.dart';
import '../../models/emi_option_cache_data_model.dart';
import '../../models/emi_option_ui_model.dart';
import '../../utils/payment_with_emi_utils.dart';
import 'emi_option_screen_state.dart';

class EmiOptionScreenCubit extends CommonCubit<EmiOptionScreenState> {
  final PaymentWithEMIUtils paymentWithEMIUtils;

  @visibleForTesting
  late final List<EmiPackageEntity> initialEmiPackages;

  @visibleForTesting
  late final OrderSessionEntity initialOrderSessionEntity;

  @visibleForTesting
  late List<EmiPackageEntity> processingEmiPackages;

  EmiOptionScreenCubit({
    required this.paymentWithEMIUtils,
  }) : super(EmiOptionScreenInitialState());

  @visibleForTesting
  final Map<String, EmiOptionCacheDataModel> emiOptionCacheDataMap =
      <String, EmiOptionCacheDataModel>{};

  void initialize({
    required OrderSessionEntity orderSessionEntity,
    required List<EmiPackageEntity> emiPackages,
    required VoucherEntity? selectedVoucher,
  }) {
    initialOrderSessionEntity = orderSessionEntity;
    final EmiPackageEntity? selectedPackage = paymentWithEMIUtils.getRecommendedEmiPackageFromList(
      emiPackages,
    );
    initialEmiPackages = emiPackages.toList();
    processingEmiPackages = initialEmiPackages.toList();

    final EmiOptionUiModel model = EmiOptionUiModel(
      orderSessionEntity: orderSessionEntity,
      emiPackages: processingEmiPackages,
      selectedEmiPackage: selectedPackage,
      selectedVoucher: selectedVoucher,
    );

    setCacheData(
      orderSession: orderSessionEntity,
      emiPackages: emiPackages,
      selectedVoucher: selectedVoucher,
    );

    emit(EmiOrderInfoLoadedState(orderUiOptionModel: model));
  }

  void setCacheData({
    required OrderSessionEntity? orderSession,
    required List<EmiPackageEntity> emiPackages,
    VoucherEntity? selectedVoucher,
  }) {
    if (selectedVoucher != null) {
      return;
    }

    emiOptionCacheDataMap.clear();
    for (final EmiPackageEntity emiPackage in emiPackages) {
      handleCachingData(
        orderSession: orderSession,
        emiPackage: emiPackage,
      );
    }
  }

  EmiOptionCacheDataModel? getCachedData(EmiPackageEntity? selectedEmiPackage) {
    final String? offerId = selectedEmiPackage?.offer?.id;
    final EmiOptionCacheDataModel? cachedData = emiOptionCacheDataMap[offerId];
    return cachedData;
  }

  Future<void> updateEmiPackageFromCacheIfNeeded({
    OrderSessionEntity? orderSession,
    EmiPackageEntity? selectedEmiPackage,
    VoucherEntity? selectedVoucher,
  }) async {
    final EmiOptionCacheDataModel? cachedDataModel = getCachedData(selectedEmiPackage);

    if (cachedDataModel != null) {
      emit(GetEmiOrderFromCacheSuccessState(
        orderSession: orderSession,
        emiPackage: selectedEmiPackage,
        selectedVoucher: selectedVoucher,
      ));
      return;
    }

    emit(NeededUpdateEmiOrderState(
      orderSession: orderSession,
      emiPackage: selectedEmiPackage,
      selectedVoucher: selectedVoucher,
    ));
  }

  void handleCachingData({
    required OrderSessionEntity? orderSession,
    required EmiPackageEntity? emiPackage,
  }) {
    final String? offerId = emiPackage?.offer?.id;
    if (offerId != null) {
      emiOptionCacheDataMap[offerId] = EmiOptionCacheDataModel(
        orderSession: orderSession,
        selectedEmiPackage: emiPackage,
      );
    }
  }

  void updateSelectedEmiPackage({
    OrderSessionEntity? orderSessionEntity,
    EmiPackageEntity? selectedEmiPackage,
    VoucherEntity? selectedVoucher,
  }) {
    if (selectedEmiPackage == null) {
      return;
    }
    updateProcessingEmiPackages(selectedEmiPackage);

    emit(UpdateSelectedTenorState(
      orderSessionEntity: orderSessionEntity,
      selectedEmiPackage: selectedEmiPackage,
      selectedVoucher: selectedVoucher,
    ));
  }

  @visibleForTesting
  void updateProcessingEmiPackages(EmiPackageEntity selectedEmiPackage) {
    final int updatedIndex = processingEmiPackages.indexWhere(
        (EmiPackageEntity emiPackage) => emiPackage.offer?.id == selectedEmiPackage.offer?.id);
    if (updatedIndex >= 0) {
      processingEmiPackages[updatedIndex] = selectedEmiPackage;
    }
  }

  void removeSelectedVoucher({
    required EmiPackageEntity? selectedPackage,
  }) {
    if (selectedPackage == null) {
      return;
    }

    processingEmiPackages = initialEmiPackages.toList();

    final EmiOptionUiModel model = EmiOptionUiModel(
      orderSessionEntity: initialOrderSessionEntity,
      emiPackages: processingEmiPackages,
      selectedEmiPackage: selectedPackage,
    );

    setCacheData(
      orderSession: initialOrderSessionEntity,
      emiPackages: processingEmiPackages,
    );

    emit(EmiOrderInfoLoadedState(orderUiOptionModel: model));
  }
}
