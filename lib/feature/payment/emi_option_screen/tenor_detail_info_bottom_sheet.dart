import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../base/evo_base_dialog_widget.dart';
import '../../../model/evo_dialog_id.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../widget/markdown_bullet_text/revamp_markdown_bullet_text_item.dart';

class TenorInfoBottomSheet extends EvoBaseDialogWidget {
  final List<String> infoItems;
  final String? title;

  TenorInfoBottomSheet({
    required super.dialogId,
    required this.infoItems,
    this.title,
    super.key,
  });

  static Future<bool?> show({
    required List<String> items,
    String? title,
  }) async {
    final BuildContext? context = navigatorContext;
    if (context == null) {
      return false;
    }

    final double paddingTop = kToolbarHeight + context.screenPadding.top;

    return await showModalBottomSheet<bool>(
      context: context,
      backgroundColor: evoColors.background,
      isScrollControlled: true,
      enableDrag: false,
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(24))),
      constraints: BoxConstraints(maxHeight: context.screenHeight - paddingTop),
      builder: (_) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: TenorInfoBottomSheet(
            infoItems: items,
            title: title,
            dialogId: EvoDialogId.emiTenorInfoBottomSheet.id,
          ),
        );
      },
    );
  }

  @override
  State<TenorInfoBottomSheet> createState() => _TenorInfoBottomSheetState();
}

class _TenorInfoBottomSheetState extends EvoBaseDialogWidgetState<TenorInfoBottomSheet> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 8,
        right: 8,
        top: 8,
        bottom: 8 + context.screenPadding.bottom,
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            _buildHeaderBottomSheet(),
            _generateMarkdownBulletItem(),
            _buildAgreeButton(),
          ],
        ),
      ),
    );
  }

  Widget _generateMarkdownBulletItem() {
    final List<Widget> widgets = <Widget>[];
    for (int i = 0; i < widget.infoItems.length; i++) {
      widgets.add(RevampMarkDownBulletTextItem(
        text: widget.infoItems[i],
        isShowDashLine: i != 0,
      ));
    }
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        ...widgets,
      ],
    );
  }

  Widget _buildHeaderBottomSheet() {
    return _buildHeaderMwgBottomSheet();
  }

  Widget _buildHeaderMwgBottomSheet() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 18.0),
      child: Text(
        widget.title ?? EvoStrings.emiConversionFee,
        style: evoTextStyles.h400(),
      ),
    );
  }

  Widget _buildAgreeButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20.0),
      child: CommonButton(
        isWrapContent: false,
        onPressed: () {
          navigatorContext?.pop();
        },
        style: getIt.get<CommonButtonStyles>().primary(ButtonSize.large),
        child: Text(EvoStrings.understand),
      ),
    );
  }
}
