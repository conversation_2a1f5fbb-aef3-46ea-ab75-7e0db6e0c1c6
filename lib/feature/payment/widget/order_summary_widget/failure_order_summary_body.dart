import 'package:flutter/material.dart';

import '../../../../data/response/payment_result_transaction_entity.dart';
import '../../utils/summary_body_utils.dart';

class FailureOrderSummaryBody extends StatelessWidget with SummaryBodyUtils {
  final PaymentResultTransactionEntity? transactionEntity;

  const FailureOrderSummaryBody({super.key, this.transactionEntity});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: _buildBody(),
    );
  }

  List<Widget> _buildBody() {
    if (isEmiTransaction(transactionEntity)) {
      return _buildBodyWithEmi();
    }
    return _buildBodyWithOutrightPurchase();
  }

  List<Widget> _buildBodyWithEmi() {
    return <Widget>[
      buildItemOrderNumber(transactionEntity?.orderInfo?.orderNumber),
      buildItemPartnerReferenceLabel(transactionEntity?.orderInfo?.partnerReferenceLabel),
      buildItemPartnerOrderId(transactionEntity?.orderInfo?.partnerOrderId),
      buildItemCreateAtWithPaymentResult(transactionEntity?.createdAtDateTime),
      buildItemPaymentMethodSource(transactionEntity?.paymentMethod?.sourceName),
      buildItemSupport(),
    ];
  }

  List<Widget> _buildBodyWithOutrightPurchase() {
    return <Widget>[
      buildItemOrderNumber(transactionEntity?.orderInfo?.orderNumber),
      buildItemPartnerReferenceLabel(transactionEntity?.orderInfo?.partnerReferenceLabel),
      buildItemPartnerOrderId(transactionEntity?.orderInfo?.partnerOrderId),
      buildItemCreateAtWithPaymentResult(transactionEntity?.createdAtDateTime),
      buildItemPaymentMethodSource(transactionEntity?.paymentMethod?.sourceName),
      buildItemSupport(),
    ];
  }
}
