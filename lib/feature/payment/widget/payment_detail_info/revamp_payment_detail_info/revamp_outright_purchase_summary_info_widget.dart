import 'package:flutter/material.dart';

import '../../../../../resources/resources.dart';
import '../../../utils/emi_format_content_utils.dart';
import '../../../utils/summary_body_utils.dart';
import '../../payment_method_source_widget.dart';

class RevampOutrightPurchaseSummaryInfoWidget extends StatelessWidget
    with EmiUIFormatContentUtils, SummaryBodyUtils {
  const RevampOutrightPurchaseSummaryInfoWidget({
    super.key,
    this.orderNumber,
    this.partnerReferenceLabel,
    this.partnerOrderId,
    this.sourceName,
    this.onManualLinkCard,
  });

  final String? orderNumber;
  final String? partnerReferenceLabel;
  final String? partnerOrderId;
  final String? sourceName;
  final VoidCallback? onManualLinkCard;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: evoColors.foreground.withOpacity(0.04),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: <Widget>[
          orderNumberCheckoutWidget(orderNumber),
          partnerReferenceLabelWidget(partnerReferenceLabel),
          partnerOrderIdCheckoutWidget(partnerOrderId),
          PaymentMethodSource(
            sourceName: sourceName,
            onManualLinkCard: onManualLinkCard,
            hasDashSeparator: false,
          ),
        ],
      ),
    );
  }
}
