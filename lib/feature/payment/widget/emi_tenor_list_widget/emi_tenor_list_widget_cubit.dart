import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../data/response/emi_package_entity.dart';
import '../../../../util/mapper.dart';
import '../../models/emi_tenor_ui_model.dart';
import 'emi_tenor_list_widget_state.dart';

class EmiTenorListWidgetCubit extends CommonCubit<EmiTenorListWidgetState> {
  EmiTenorListWidgetCubit() : super(EmiTenorListWidgetState());

  @visibleForTesting
  int? tenorSelectedIndex;

  @visibleForTesting
  final List<EmiTenorUIModel?> listTenorModel = <EmiTenorUIModel>[];

  void initialize(List<EmiPackageEntity> listEmiPackageEntity, String selectedTenorIdDefault) {
    /// Do nothing if list tenor entity is empty
    if (listEmiPackageEntity.isEmpty) {
      return;
    }

    int? selectedIndex;

    for (int index = 0; index < listEmiPackageEntity.length; index++) {
      final EmiPackageEntity entity = listEmiPackageEntity[index];
      final EmiTenorUIModel? model = entity.offer?.toEmiTenorUIModel();
      listTenorModel.add(model);
      if (model?.id == selectedTenorIdDefault) {
        selectedIndex = index;
      }
    }

    /// update [tenorSelectedIndex] is index of item has id == [selectedTenorIdDefault]
    updateTenorSelectedIndex(selectedIndex);
  }

  void updateTenorSelectedIndex(int? selectedIndex) {
    if (selectedIndex != null) {
      if (tenorSelectedIndex == selectedIndex) {
        return;
      }

      /// update previous selected item is not selected
      tenorSelectedIndex?.let((int index) {
        listTenorModel[index]?.isSelected = false;
      });

      tenorSelectedIndex = selectedIndex;

      /// update new selected item is selected
      final EmiTenorUIModel? modelSelected = listTenorModel[selectedIndex];
      modelSelected?.isSelected = true;
    }
    emit(UpdateEmiTenorUIModelDoneState(listTenorModel, selectedIndex));
  }
}
