import 'package:flutter_common_package/base/bloc_state.dart';

import '../../models/emi_tenor_ui_model.dart';

class EmiTenorListWidgetState extends BlocState {}

class UpdateEmiTenorUIModelDoneState extends EmiTenorListWidgetState {
  final List<EmiTenorUIModel?> listTenorModel;
  final int? indexSelectedTenorModel;

  UpdateEmiTenorUIModelDoneState(this.listTenorModel, this.indexSelectedTenorModel);
}
