import 'package:flutter/material.dart';

import '../../../model/transaction_status_model.dart';
import '../../../resources/resources.dart';
import '../../../util/functions.dart';
import 'payment_result_title_widget.dart';

class TransactionDetailTitleAndAmountWidget extends StatelessWidget {
  final TransactionStatusModel status;
  final String? merchantName;
  final String? storeAddress;
  final int amount;
  final bool isEmiTransaction;

  const TransactionDetailTitleAndAmountWidget({
    required this.status,
    required this.merchantName,
    required this.amount,
    super.key,
    this.storeAddress,
    this.isEmiTransaction = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: status.getTransactionStatusBgColor(),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          _itemIconByType(status),
          const SizedBox(height: 16),
          _itemTitleByType(status, merchantName),
          _itemAmount(),
          const SizedBox(height: 16),
          _itemStoreAddress(),
        ],
      ),
    );
  }

  Widget _itemIconByType(TransactionStatusModel status) {
    return evoImageProvider.asset(
      status.getTransactionStatusIconPath(),
      height: 56,
      width: 56,
    );
  }

  Widget _itemTitleByType(TransactionStatusModel status, String? merchantName) {
    final String textByType =
        status.getTransactionDetailStatusTitle(merchantName, isEmiTransaction: isEmiTransaction);
    return PaymentResultTitleWidget(title: textByType);
  }

  Widget _itemAmount() {
    final String textAmount =
        evoUtilFunction.evoFormatCurrency(amount, currencySymbol: vietNamCurrencySymbol);
    return Text(
      textAmount,
      style: evoTextStyles.h600(evoColors.emiTenorBackground),
    );
  }

  Widget _itemStoreAddress() {
    final String? address = storeAddress;
    return address == null
        ? const SizedBox.shrink()
        : Text(
            address,
            style: evoTextStyles.bodySmall(color: evoColors.textActive),
          );
  }
}
