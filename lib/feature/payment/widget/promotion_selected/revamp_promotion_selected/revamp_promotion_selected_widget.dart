import 'package:flutter/material.dart';

import '../../../../../resources/resources.dart';
import '../../../confirm_payment/model/order_info_ui_model.dart';
import '../../../promotion/dash_separator_horizontal.dart';

class RevampPromotionSelectedWidget extends StatefulWidget {
  final VoucherSelectionState voucherSelectionState;
  final String title;
  final String? iconPath;
  final Color? borderColor;
  final Color? backgroundColor;
  final String? discountText;
  final Color? discountColor;

  const RevampPromotionSelectedWidget({
    required this.title,
    required this.voucherSelectionState,
    super.key,
    this.iconPath,
    this.borderColor,
    this.backgroundColor,
    this.discountText,
    this.discountColor,
  });

  @override
  State<RevampPromotionSelectedWidget> createState() => _RevampPromotionSelectedWidgetState();
}

class _RevampPromotionSelectedWidgetState extends State<RevampPromotionSelectedWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 64,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? evoColors.background,
        borderRadius: BorderRadius.circular(12),
        border: widget.borderColor != null
            ? Border.all(
                color: widget.borderColor ?? Colors.transparent,
              )
            : null,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          evoImageProvider.asset(widget.iconPath ?? EvoImages.icMWGPaymentNoPromotion),
          const SizedBox(width: 8),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 12.0),
              child: _buildTitleWidget(),
            ),
          ),
          widget.voucherSelectionState != VoucherSelectionState.noSelect
              ? Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: DashSeparatorHorizontal(
                    heightBox: 64,
                    color: Colors.white,
                  ),
                )
              : const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.only(top: 12.0, left: 8.0, bottom: 12.0),
            constraints: const BoxConstraints(maxWidth: 120),
            child: _buildSuffixWidget(),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleWidget() {
    if (widget.voucherSelectionState == VoucherSelectionState.invalidVoucher) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            EvoStrings.paymentPromotionApplyErrorTitle,
            maxLines: 1,
            style: evoTextStyles.bodySmall(
                color: widget.discountColor ?? evoColors.mwgEmiInvalidVoucherSelectedBorder),
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            widget.title,
            maxLines: 1,
            style: evoTextStyles.h200(color: evoColors.textActive),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      );
    }

    return Text(
      widget.title,
      maxLines: 2,
      style: evoTextStyles.h200(color: evoColors.textActive),
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildSuffixWidget() {
    if (widget.voucherSelectionState == VoucherSelectionState.noSelect) {
      return Text(
        EvoStrings.mwgPromotionSuffixUnSelectedTitle,
        style: evoTextStyles.bodySmall(color: widget.borderColor).copyWith(
              decoration: TextDecoration.underline,
              fontWeight: FontWeight.w700,
            ),
        textAlign: TextAlign.end,
      );
    }

    if (widget.voucherSelectionState == VoucherSelectionState.invalidVoucher) {
      return Text(
        EvoStrings.mwgPromotionSuffixSelectedTitle,
        style: evoTextStyles.bodySmall(color: widget.borderColor).copyWith(
              decoration: TextDecoration.underline,
              fontWeight: FontWeight.w700,
            ),
        textAlign: TextAlign.end,
      );
    }

    final Color? color = widget.discountColor;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: <Widget>[
        Expanded(
          child: Text(
            widget.discountText ?? '',
            style: evoTextStyles.h200(color: color),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          EvoStrings.mwgPromotionSuffixSelectedTitle,
          style: evoTextStyles.bodySmall(color: evoColors.textActive).copyWith(
                decoration: TextDecoration.underline,
                fontWeight: FontWeight.w700,
              ),
        ),
      ],
    );
  }
}
