import 'package:flutter/material.dart';

import '../../../model/transaction_status_model.dart';
import '../../../resources/resources.dart';
import '../../../util/functions.dart';
import 'payment_result_title_widget.dart';

class PaymentResultTitleAndAmountWidget extends StatelessWidget {
  final TransactionStatusModel status;
  final String merchantName;
  final int amount;
  final VoidCallback? onMoreDetail;
  final bool isEmiTransaction;

  const PaymentResultTitleAndAmountWidget({
    required this.status,
    required this.merchantName,
    required this.amount,
    super.key,
    this.onMoreDetail,
    this.isEmiTransaction = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: status.getTransactionStatusBgColor(),
      padding: const EdgeInsets.all(20),
      child: SafeArea(
        bottom: false,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _itemIconAndMoreDetail(),
            const SizedBox(height: 16),
            _itemTitleByType(status, merchantName),
            _itemAmount(),
          ],
        ),
      ),
    );
  }

  Widget _itemIconAndMoreDetail() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        _itemIconByType(status),
        _itemMoreDetail(),
      ],
    );
  }

  Widget _itemIconByType(TransactionStatusModel status) {
    return evoImageProvider.asset(
      status.getTransactionStatusIconPath(),
      height: 56,
      width: 56,
    );
  }

  Widget _itemTitleByType(TransactionStatusModel type, String? merchantName) {
    final String textByType =
        type.getPaymentResultStatusTitle(merchantName, isEmiTransaction: isEmiTransaction);
    return PaymentResultTitleWidget(title: textByType);
  }

  Widget _itemAmount() {
    return Text(
      evoUtilFunction.evoFormatCurrency(
        amount,
        currencySymbol: vietNamCurrencySymbol,
      ),
      style: evoTextStyles.h600(evoColors.emiTenorBackground),
    );
  }

  Widget _itemMoreDetail() {
    if (onMoreDetail == null) {
      return const SizedBox.shrink();
    }

    return InkWell(
      onTap: () => onMoreDetail?.call(),
      child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(60),
              color: evoColors.paymentResultEmiMoreDetailBackground),
          child: Row(
            children: <Widget>[
              Text(
                EvoStrings.paymentResultEmiDetail,
                style: evoTextStyles.bodyMedium(evoColors.emiTenorBackground),
              ),
              evoImageProvider.asset(EvoImages.icSmallRightEmi, height: 16, width: 16),
            ],
          )),
    );
  }
}
