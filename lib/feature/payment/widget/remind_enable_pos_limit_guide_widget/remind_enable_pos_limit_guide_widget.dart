import 'package:flutter/material.dart';

import '../../../../resources/resources.dart';
import '../../../../util/ui_utils/evo_ui_utils.dart';
import '../indexed_indicator_widget.dart';
import 'remind_enable_pos_limit_guide_model.dart';
import 'remind_enable_pos_limit_title_and_description.dart';

class RemindEnablePosLimitGuideWidgetConfigs {
  static const double imageRatio = 320 / 260;
  static const double imageWidthPercentage = 320 / 375;
  static const double imageHeightPercentage = 260 / 812;
  static const double minContentHeightPercentage = 158 / 812;
  static const Duration animatedDurationTime = Duration(milliseconds: 300);
}

class RemindEnablePosLimitGuideWidgetController {
  VoidCallback? onNextTap;
}

class RemindEnablePosLimitGuideWidget extends StatefulWidget {
  final List<RemindEnablePosLimitGuideModel> models;
  final RemindEnablePosLimitGuideWidgetController? controller;
  final void Function(int index)? onPageChanged;

  const RemindEnablePosLimitGuideWidget({
    required this.models,
    this.controller,
    this.onPageChanged,
    super.key,
  });

  @override
  State<RemindEnablePosLimitGuideWidget> createState() => _RemindEnablePosLimitGuideWidgetState();
}

class _RemindEnablePosLimitGuideWidgetState extends State<RemindEnablePosLimitGuideWidget> {
  final PageController _pageController = PageController(viewportFraction: 0.9);

  int _currentIndex = 0;

  @override
  void initState() {
    widget.controller?.onNextTap = _onNextTap;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.models.isEmpty) {
      return const SizedBox.shrink();
    }

    const SizedBox space20 = SizedBox(height: 20);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        _buildRemindPosLimitGuideSlider(),
        space20,
        _buildTitleAndDescription(),
        space20,
        _buildIndicator(),
      ],
    );
  }

  Widget _buildRemindPosLimitGuideSlider() {
    final List<RemindEnablePosLimitGuideModel> models = widget.models;
    final int itemCount = models.length;
    final bool isLast = _currentIndex == itemCount - 1;

    final double imageHeight = EvoUiUtils().calculateVerticalSpace(
      context: context,
      heightPercentage: RemindEnablePosLimitGuideWidgetConfigs.imageHeightPercentage,
    );

    double imageWidth = EvoUiUtils().calculateHorizontalSpace(
      context: context,
      widthPercentage: RemindEnablePosLimitGuideWidgetConfigs.imageWidthPercentage,
    );

    /// The PageView will scale down content pageController.viewportFraction to see the next page
    /// So we need to increase the imageWidth to 100% to keep the width value in design after the scaling down.
    imageWidth += imageWidth * (1 - _pageController.viewportFraction);

    return Container(
      padding: EdgeInsets.only(left: isLast ? 0 : 4, right: isLast ? 4 : 0),
      height: imageHeight,
      child: PageView.builder(
        controller: _pageController,
        itemBuilder: (BuildContext context, int index) {
          return Padding(
            padding: EdgeInsets.only(left: isLast ? 0 : 16, right: isLast ? 16 : 0),
            child: AspectRatio(
              aspectRatio: RemindEnablePosLimitGuideWidgetConfigs.imageRatio,
              child: evoImageProvider.asset(
                models[index].image,
                fit: BoxFit.contain,
                width: imageWidth,
              ),
            ),
          );
        },
        itemCount: itemCount,
        onPageChanged: (int index) {
          widget.onPageChanged?.call(index);
          setState(() {
            _currentIndex = index;
          });
        },
        padEnds: false,
      ),
    );
  }

  Widget _buildTitleAndDescription() {
    final RemindEnablePosLimitGuideModel curModel = widget.models[_currentIndex];

    return RemindEnablePosLimitTitleAndDescription(
      model: curModel,
      constraints: BoxConstraints(
        minHeight: EvoUiUtils().calculateVerticalSpace(
          context: context,
          heightPercentage: RemindEnablePosLimitGuideWidgetConfigs.minContentHeightPercentage,
        ),
      ),
    );
  }

  Widget _buildIndicator() {
    final bool isFirst = _currentIndex == 0;
    final bool isLast = _currentIndex == (widget.models.length - 1);

    return IndexedIndicatorWidget(
      decoration: BoxDecoration(
        color: evoColors.remindPosLimitIndicatorBackground,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: evoColors.remindPosLimitIndicatorBorder),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: evoColors.remindPosLimitIndicatorShadow,
            offset: const Offset(0, 24),
            spreadRadius: -8,
            blurRadius: 24,
          ),
          BoxShadow(
            color: evoColors.remindPosLimitIndicatorShadow,
            blurRadius: 16,
          ),
          BoxShadow(
            color: evoColors.remindPosLimitIndicatorShadow,
            blurRadius: 4,
          ),
        ],
      ),
      text: '${_currentIndex + 1}/${widget.models.length}',
      textStyle: evoTextStyles.bodySmall(color: evoColors.remindPosLimitIndicatorText),
      iconEnableColor: evoColors.remindPosLimitIndicator,
      iconDisableColor: evoColors.remindPosLimitIndicator,
      onPreviousTap: isFirst ? null : _onPreviousTap,
      onNextTap: isLast ? null : _onNextTap,
    );
  }

  void _onPreviousTap() {
    _pageController.previousPage(
      duration: RemindEnablePosLimitGuideWidgetConfigs.animatedDurationTime,
      curve: Curves.ease,
    );
  }

  void _onNextTap() {
    _pageController.nextPage(
      duration: RemindEnablePosLimitGuideWidgetConfigs.animatedDurationTime,
      curve: Curves.ease,
    );
  }
}
