import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../resources/resources.dart';

class PaymentPollingTimeOutWidget extends StatelessWidget {
  final VoidCallback? onRePayment;

  const PaymentPollingTimeOutWidget({
    super.key,
    this.onRePayment,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.all(Radius.circular(16)),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: evoColors.inActiveStepIcon,
            offset: const Offset(0, 15),
            spreadRadius: 8,
            blurRadius: 24,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            EvoStrings.paymentPollingTimeoutTitle,
            style: evoTextStyles.h400(),
          ),
          const SizedBox(height: 10),
          Text(
            EvoStrings.paymentPollingTimeoutDesc,
            style: evoTextStyles.bodyLarge(evoColors.textPassive),
          ),
          const SizedBox(height: 20),
          CommonButton(
            isWrapContent: false,
            style: evoButtonStyles.primary(ButtonSize.xLarge),
            onPressed: () {
              onRePayment?.call();
            },
            child: const Text(EvoStrings.paymentRePayment),
          )
        ],
      ),
    );
  }
}
