import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../resources/resources.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../../widget/empty_data_container.dart';

class EmiNotSupportBodyWidget extends StatelessWidget {
  /// reference: https://www.figma.com/file/bFht7WW9fsBDkPexbH5QSf/EVO-App-Hand-Off?node-id=29140%3A22369&mode=dev
  static double defaultPaddingTopPercentage = 88 / 812;

  final VoidCallback onClickPayInAll;

  const EmiNotSupportBodyWidget({required this.onClickPayInAll, super.key});

  @override
  Widget build(BuildContext context) {
    return EmptyDataContainer(
      assetName: EvoImages.imageEmiNotSupport,
      text: EvoStrings.emiNotSupportTitle,
      spacingBetweenTextAndImage: 44,
      bottomWidgets: <Widget>[
        const SizedBox(height: 8),
        Text(
          EvoStrings.emiGuideToOutrightPaymentDescription,
          style: evoTextStyles.bodyMedium(evoColors.textPassive),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        CommonButton(
          isWrapContent: false,
          style: evoButtonStyles.primary(ButtonSize.xLarge),
          onPressed: onClickPayInAll,
          child: const Text(EvoStrings.titlePayInAll),
        ),
      ],
      paddingTop: EvoUiUtils().calculateVerticalSpace(
        heightPercentage: defaultPaddingTopPercentage,
        context: context,
      ),
    );
  }
}
