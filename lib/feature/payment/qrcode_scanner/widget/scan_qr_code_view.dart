import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

import 'evo_qr_code_controller.dart';

enum QrErrorCode {
  genericError,
  permissionDenied;
}

abstract class QRCodeScannerCallback {
  void onDetectQRCode(String? rawData);

  void onScanQrError(QrErrorCode errorCode, String? errMsg);
}

class QRCodeScannerView extends StatelessWidget {
  final QRCodeScannerCallback? callback;
  final EvoMobileScannerController evoMobileScannerController;

  const QRCodeScannerView({required this.evoMobileScannerController, super.key, this.callback});

  @override
  Widget build(BuildContext context) {
    return MobileScanner(
      controller: evoMobileScannerController.scannerController,
      onDetect: _onDetect,
      errorBuilder: (BuildContext context, MobileScannerException exp, Widget? widget) {
        _onException(exp);
        return const SizedBox.shrink();
      },
    );
  }

  void _onDetect(BarcodeCapture barcodeCapture) {
    final String? rawData = barcodeCapture.barcodes.first.rawValue;
    callback?.onDetectQRCode(rawData);
  }

  void _onException(MobileScannerException exp) {
    switch (exp.errorCode) {
      case MobileScannerErrorCode.permissionDenied:
        callback?.onScanQrError(QrErrorCode.permissionDenied, exp.errorDetails?.message);
        break;
      case MobileScannerErrorCode.controllerUninitialized:
      case MobileScannerErrorCode.genericError:
      default:
        callback?.onScanQrError(QrErrorCode.genericError, exp.errorDetails?.message);
        break;
    }
  }
}
