import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../data/response/linked_card_status_checking_entity.dart';
import '../../ekyc/ekyc_flow_callback.dart';
import '../../ekyc/ekyc_flow_failed_reason.dart';
import '../../ekyc/model/ekyc_for_flow_type.dart';
import '../../ekyc_v2/ekyc_v2_flow_callback.dart';
import '../../ekyc_v2/ekyc_v2_flow_type.dart';
import '../../feature_flow/feature_flow_failed_reason.dart';
import '../../manual_link_card/manual_link_card_flow_callback.dart';
import '../../manual_link_card/manual_link_card_mixin.dart';
import 'qrcode_scanner_screen.dart';

mixin QrCodeScannerManualLinkFlow on ManualLinkCardMixin {
  ManualLinkCardFlowCallback createManualLinkCardCallbackForQRCodeEntryPoint() {
    return ManualLinkCardFlowCallback(
      onSuccess: (ManualLinkCardFlowPayload? payload) {
        moveToEntryPoint();
      },
      onFailed: (FlowFailedReason? reason, String? userMessage) {
        commonLog('reason: >>>>>> $reason');

        moveToEntryPoint();
      },
    );
  }

  EkycV2FlowCallback? createFaceAuthCallbackForQRCodeEntryPointIfNeed(
    LinkedCardStatusCheckingEntity entity,
  ) {
    if (entity.verdict != LinkedCardStatusCheckingEntity.verdictSuccess ||
        entity.challengeType != LinkCardStatusChallengeType.faceAuth.value) {
      return null;
    }

    return EkycV2FlowCallback(
      flowType: EkycV2FlowType.linkCard,
      onSuccess: (EkycV2FlowPayload? payload) {
        faceAuthMoveToLinkCardPollingScreen(payload: payload);
      },
      onError: (EkycV2FlowErrorReason errorReason) {
        clearManualLinkCardDataInAppState();
        moveToEntryPoint();
      },
    );
  }

  EkycFlowCallback? createFaceOtpCallbackForQRCodeEntryPointIfNeed(
    LinkedCardStatusCheckingEntity entity,
  ) {
    if (entity.verdict != LinkedCardStatusCheckingEntity.verdictSuccess ||
        entity.challengeType != LinkCardStatusChallengeType.faceOtp.value) {
      return null;
    }

    return EkycFlowCallback(
      flow: EkycFlowType.linkCard,
      onSuccess: (BuildContext context, EkycFlowPayload? payload) {
        faceOtpMoveToLinkCardPollingScreen(payload: payload);
      },
      onFailed: (BuildContext context, EkycFlowFailedReason reason, String? userMessage) {
        commonLog('reason: >>>>>> $reason');

        clearManualLinkCardDataInAppState();

        moveToEntryPoint();
      },
    );
  }

  void moveToEntryPoint() {
    QrCodeScannerScreen.openSingleInstance();
  }
}
