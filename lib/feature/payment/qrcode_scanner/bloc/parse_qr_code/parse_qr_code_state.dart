import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../data/response/qr_code_parse_entity.dart';

abstract class ParseQRCodeState extends BlocState {}

class ParseQRCodeInitialState extends ParseQRCodeState {}

class ParseQRCodeLoading extends ParseQRCodeState {}

class ParseQRCodeSuccess extends ParseQRCodeState {
  final QrCodeParseEntity entity;

  ParseQRCodeSuccess(this.entity);
}

class ParseQRCodeDetectGoEvoUrl extends ParseQRCodeState {
  final String url;

  ParseQRCodeDetectGoEvoUrl(this.url);
}

class ParseQRCodeError extends ParseQRCodeState {
  final ErrorUIModel? error;

  ParseQRCodeError({this.error});
}
