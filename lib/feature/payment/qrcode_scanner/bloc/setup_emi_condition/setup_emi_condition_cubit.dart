import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../../data/repository/emi_repo.dart';
import '../../../../../data/response/emi_rules_entity.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../conditions/emi_conditions_utils.dart';
import '../../../emi_shared_data.dart';

part 'setup_emi_condition_state.dart';

class SetupEmiConditionCubit extends CommonCubit<SetupEmiConditionState>
    with EmiConditionsUtilsMixin {
  SetupEmiConditionCubit() : super(SetupEmiConditionInitialState());
  final EmiRepo emiRepo = getIt<EmiRepo>();

  /// loading emi rule from server
  /// if success, save to [emiConditionInfo] in [AppState.paymentSharedData.emiSharedData]
  /// otherwise, we will use default value for checking rules
  /// default value are [EmiConstants.defaultMinUserChargeAmount] && [EmiConstants.defaultAllowProductCodes]
  Future<void> getEmiRules() async {
    final EmiRulesEntity emiEntity = await emiRepo.getEmiRules(
      mockConfig: const MockConfig(
        enable: false,
        fileName: 'emi_rules_success.json',
      ),
    );

    if (emiEntity.statusCode == CommonHttpClient.SUCCESS) {
      saveEmiRuleToAppState(emiEntity);
    }

    /// Currently, we don't handle the case when the server returns an error.
    emit(SetupEmiConditionCompleteState());
  }

  void saveEmiRuleToAppState(EmiRulesEntity emiEntity) {
    final EmiConditionInfo emiConditionInfo = sharedEmiConditionInfo();
    emiConditionInfo.allowProductCodes = emiEntity.rules?.allowedProductCodes;
    emiConditionInfo.minUserChargeAmount = emiEntity.rules?.minUserChargeAmount;
    emiConditionInfo.productCodeToBlockedMerchants = emiEntity.rules?.productCodeToBlockedMerchants;

    commonLog(
      'allowProductCodes: ${emiConditionInfo.allowProductCodes}'
      ', minUserChargeAmount: ${emiConditionInfo.minUserChargeAmount}'
      ', productCodeToBlockedMerchants ${emiConditionInfo.productCodeToBlockedMerchants}',
      methodName: 'saveEmiRuleToAppState',
    );
  }
}
