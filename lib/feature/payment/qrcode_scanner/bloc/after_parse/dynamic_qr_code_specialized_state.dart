import '../../../../../data/response/create_order_entity.dart';
import 'qr_scanner_after_parse_state.dart';

abstract class DynamicQrCodeSpecializedState extends QrScannerAfterParseState {}

class CreateDynamicOrderSuccess extends DynamicQrCodeSpecializedState {
  final CreateOrderEntity? entity;

  CreateDynamicOrderSuccess(this.entity);
}

class CreateDynamicOrderWithActivatePosLimitErrorState extends DynamicQrCodeSpecializedState {
  final CreateOrderEntity? entity;

  CreateDynamicOrderWithActivatePosLimitErrorState(this.entity);
}
