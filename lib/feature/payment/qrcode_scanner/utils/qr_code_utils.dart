import '../../../../data/response/qr_code_parse_entity.dart';
import '../../../../resources/ui_strings.dart';

class QrCodeUiUtils {
  static String getTitlePopUpFromVerdict({String? verdict}) {
    switch (verdict) {
      case QrCodeParseEntity.verdictInvalidQRFormat:
        return EvoStrings.titleErrorPopupInvalidFormat;

      case QrCodeParseEntity.verdictOrderExpired:
        return EvoStrings.titleErrorPopupOrderExpired;

      case QrCodeParseEntity.verdictOrderInPayment:
        return EvoStrings.titleErrorPopupOrderInPayment;

      case QrCodeParseEntity.verdictPayTimesExceeded:
        return EvoStrings.titleErrorPopupPayTimesExceeded;

      case QrCodeParseEntity.verdictInvalidOrderStatus:
        return EvoStrings.titleErrorPopupInvalidOrderStatus;

      case QrCodeParseEntity.verdictOrderNotFound:
      case QrCodeParseEntity.verdictInvalidProductCode:
      case QrCodeParseEntity.verdictStoreInactive:
      case QrCodeParseEntity.verdictMerchantInactive:
      case QrCodeParseEntity.verdictStoreNotFound:
        return EvoStrings.titleErrorPopupOrderInvalid;

      case QrCodeParseEntity.verdictOrderSucceeded:
        return EvoStrings.titleErrorPopupOrderSucceeded;

      case QrCodeParseEntity.verdictOrderFailed:
        return EvoStrings.titleErrorPopupOrderFailed;

      case QrCodeParseEntity.verdictOrderCancelled:
        return EvoStrings.titleErrorPopupOrderCancelled;

      case QrCodeParseEntity.verdictOrderPending:
        return EvoStrings.titleErrorPopupOrderPending;

      default:
        return EvoStrings.titleErrorPopupInvalidFormat;
    }
  }

  static String getContentPopUpFromVerdict({String? message}) {
    return message ?? EvoStrings.errorQrCodeDefaultErrorDescription;
  }
}
