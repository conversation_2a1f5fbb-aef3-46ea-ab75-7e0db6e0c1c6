import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../data/repository/checkout_repo.dart';
import '../../../data/response/create_order_entity.dart';
import '../../../data/response/order_session_entity.dart';
import '../../../data/response/qr_code_parse_entity.dart';
import '../../../data/response/store_info_entity.dart';
import '../../../data/response/vn_pay_qr_info_entity.dart';
import '../../../model/evo_dialog_id.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/ui_utils/evo_dialog_helper.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../main_screen/main_screen.dart';
import '../../manual_link_card/dop_status/dop_card_status_screen.dart';
import '../../manual_link_card/manual_link_card_cubit.dart';
import '../../manual_link_card/manual_link_card_mixin.dart';
import '../../manual_link_card/model/linked_card_status_model.dart';
import '../../qrcode_scanner/base_qrcode_scanner_screen.dart';
import '../../scanner_permission/permission_grant_guide_widget.dart';
import '../../webview/models/evo_webview_arg.dart';
import '../confirm_payment/update_confirm_payment_screen/update_confirm_payment_screen.dart';
import '../input_amount/emi_input_amount/emi_input_amount_screen.dart';
import '../input_amount/full_payment_input_amount/full_payment_input_amount_screen.dart';
import '../utils/active_pos_limit_handler/active_pos_limit_handler.dart';
import '../utils/payment_with_emi_utils.dart';
import 'bloc/after_parse/dynamic_qr_code_specialized_state.dart';
import 'bloc/after_parse/qr_scanner_after_parse_cubit.dart';
import 'bloc/after_parse/qr_scanner_after_parse_state.dart';
import 'bloc/parse_qr_code/parse_qr_code_cubit.dart';
import 'bloc/parse_qr_code/parse_qr_code_state.dart';
import 'bloc/setup_emi_condition/setup_emi_condition_cubit.dart';
import 'qrcode_scanner_manual_link_card_flow.dart';
import 'utils/qr_code_utils.dart';
import 'widget/scan_qr_code_view.dart';

class QrCodeScannerScreen extends BaseQrCodeScannerScreen {
  /// This function ensures only one [QrCodeScannerScreen] instance in navigator stack,
  /// to prevent the issue in https://github.com/juliansteenbakker/mobile_scanner/issues/539
  static void openSingleInstance() {
    final BuildContext? ctx = navigatorContext;
    return ctx?.popUntilNamed(
      Screen.qrCodeScannerScreen.name,
      onPageNotFound: () {
        return ctx.pushNamed(Screen.qrCodeScannerScreen.name);
      },
    );
  }

  const QrCodeScannerScreen({super.key});

  @override
  State<QrCodeScannerScreen> createState() => QrCodeScannerScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.qrCodeScannerScreen.routeName);
}

class QrCodeScannerScreenState extends BaseQrCodeScannerScreenState<QrCodeScannerScreen>
    with ManualLinkCardMixin, QrCodeScannerManualLinkFlow {
  final ParseQRCodeCubit _parseQrCodeCubit = getIt<ParseQRCodeCubit>();

  final SetupEmiConditionCubit _setupEmiConditionCubit = getIt<SetupEmiConditionCubit>();

  final QrScannerAfterParseCubit _qrScreenCommonCubit = getIt<QrScannerAfterParseCubit>();

  final ManualLinkCardCubit _manualLinkCardCubit = getIt<ManualLinkCardCubit>();

  final ActivePosLimitHandler _activePosLimitHandler = getIt<ActivePosLimitHandler>();

  final PaymentWithEMIUtils _paymentWithEMIUtils = getIt<PaymentWithEMIUtils>();

  /// [isLinkingCardStatusChecking] is used to check the app is linking card
  /// When the app is linking card, we should not handle result from scanning QR Code
  @visibleForTesting
  bool? isLinkingCardChecking = false;

  /// clear state to ready scan new QR code when user back from another page to this page
  /// or close popup bottom-sheet, dialog
  @override
  Future<void> didPopNext() async {
    await super.didPopNext();

    /// Reset [orderCreationScreen] when the user back to this screen
    /// So it will set when the user create order
    appState.paymentSharedData.orderCreationScreen = null;

    if (!enableMockTestFlow()) {
      _parseQrCodeCubit.unlockQrCodeScanning();
    }
  }

  @override
  void dispose() {
    /// QRScanner still process during user swipe to back previous screen
    /// and we can not capture this event to close camera, pls refer https://github.com/flutter/flutter/issues/28237
    /// so some case showHubLoading still call and can not close when user back to previous screen
    EvoUiUtils().hideHudLoading();
    super.dispose();
  }

  @override
  void onDetectQRCode(String? rawData) {
    super.onDetectQRCode(rawData);
    _parseQrCodeCubit.parseQRCode(rawData);
  }

  @override
  void onScanQrError(QrErrorCode errorCode, String? errMsg) {
    showSnackBarError(errMsg ?? CommonStrings.otherGenericErrorMessage);
  }

  @override
  void onInitializeCompleted() {
    getPaymentInfo();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return buildBody(super.getContentWidget(context));
  }

  @override
  Widget buildPermissionGuideWidget() {
    /// this UI will be shown when user deny camera permission
    /// it is placed at the center of the scan QR screen
    return const PermissionGrantGuideWidget();
  }

  @visibleForTesting
  Widget buildBody(Widget child) {
    return MultiBlocProvider(
      providers: <BlocProvider<dynamic>>[
        BlocProvider<QrScannerAfterParseCubit>(
          create: (_) => _qrScreenCommonCubit,
        ),
        BlocProvider<ParseQRCodeCubit>(
          create: (_) => _parseQrCodeCubit,
        ),
        BlocProvider<ManualLinkCardCubit>(
          create: (_) => _manualLinkCardCubit,
        ),
        BlocProvider<SetupEmiConditionCubit>(
          create: (_) => _setupEmiConditionCubit,
        ),
      ],
      child: MultiBlocListener(
        listeners: <BlocListener<dynamic, dynamic>>[
          BlocListener<QrScannerAfterParseCubit, QrScannerAfterParseState>(
            listener: listenQrScannerAfterParseState,
          ),
          BlocListener<ParseQRCodeCubit, ParseQRCodeState>(
            listener: listenQRCodeParseState,
          ),
          BlocListener<ManualLinkCardCubit, ManualLinkCardState>(
            listener: listenManualLinkCardState,
          ),
          BlocListener<SetupEmiConditionCubit, SetupEmiConditionState>(
            listener: listenerEmiConditionState,
          ),
        ],
        child: child,
      ),
    );
  }

  @visibleForTesting
  void listenQRCodeParseState(BuildContext context, ParseQRCodeState state) {
    if (state is ParseQRCodeDetectGoEvoUrl) {
      CommonWebView.pushNamed(
        arg: EvoWebViewArg(
          title: EvoStrings.scanQRGoEvoUrlWebViewTitle,
          url: state.url,
        ),
      );
      return;
    }

    if (state is ParseQRCodeLoading) {
      EvoUiUtils().showHudLoading(loadingText: EvoStrings.footerLoadingText);
      return;
    }

    if (state is ParseQRCodeSuccess) {
      handleQRCodeResponse(state.entity);
      return;
    }

    EvoUiUtils().hideHudLoading();
    if (state is ParseQRCodeError) {
      handleApiError(
        state.error,
        onShowUIError: (ErrorUIModel? error) {
          handleShowInvalidQrCodePopup(
            title: QrCodeUiUtils.getTitlePopUpFromVerdict(verdict: error?.verdict),
            message: QrCodeUiUtils.getContentPopUpFromVerdict(message: error?.userMessage),
          );
        },
      );
      return;
    }
  }

  @visibleForTesting
  void getEmiConditionIfNeed() {
    if (_paymentWithEMIUtils.isEmiFeatureEnabled()) {
      _setupEmiConditionCubit.getEmiRules();
    }
  }

  @visibleForTesting
  void getPaymentMethodIfNeed() {
    if (!_manualLinkCardCubit.isPaymentMethodLoaded) {
      _parseQrCodeCubit.lockQrCodeScanning();
      _manualLinkCardCubit.getPaymentMethod(showLoading: false);
    } else {
      /// start mock test
      parseMockQRCodeIfNeed();
    }
  }

  @visibleForTesting
  void listenManualLinkCardState(BuildContext context, ManualLinkCardState state) {
    if (state is ManualLinkCardLoadingState) {
      EvoUiUtils().showHudLoading();
      return;
    }

    EvoUiUtils().hideHudLoading();

    unlockCameraForScanning(state);

    if (state is PaymentMethodIsEmptyState) {
      showManualLinkCardEncourageBottomSheet(
        ctx: context,
        title: EvoStrings.titleManualLinkCardScanQr,
        content: EvoStrings.descriptionManualLinkCardScanQr,
        buttonTitle: EvoStrings.manualLinkCardButtonTitleScanQr,
        onLinkCardClick: () {
          isLinkingCardChecking = true;
          navigatorContext?.pop();
          _manualLinkCardCubit.checkDopCardStatus();
        },
        onDismiss: () async {
          /// If user dismiss the bottom sheet & the manual link card is still getting data
          /// then we should block the QR code parser, otherwise we should release it
          if (_manualLinkCardCubit.isGettingData()) {
            _parseQrCodeCubit.lockQrCodeScanning();
          } else {
            _parseQrCodeCubit.unlockQrCodeScanning();

            /// Start parse local image if mode is mock test.
            parseMockQRCodeIfNeed();
          }
        },
      );
    } else if (state is LinkedCardStatusCheckingLoadedState) {
      saveEntryPointScreenAndFlowCallback(
        entryPointScreen: Screen.qrCodeScannerScreen,
        manualLinkFlowCallback: createManualLinkCardCallbackForQRCodeEntryPoint(),
        faceOtpCallback: createFaceOtpCallbackForQRCodeEntryPointIfNeed(
          state.linkedCardStatusCheckingEntity,
        ),
        faceAuthCallback: createFaceAuthCallbackForQRCodeEntryPointIfNeed(
          state.linkedCardStatusCheckingEntity,
        ),
      );

      handleLinkingCardStatus(
        context: context,
        cardStatusEntity: state.linkedCardStatusCheckingEntity,
      );
      setLinkingCardChecking();
    } else if (state is LinkedCardStatusCheckingErrorState) {
      // PREPARE LINK: https://trustingsocial1.atlassian.net/browse/EMA-1052
      // With Response code = "failure"
      final ErrorUIModel errorUIModel = state.errorUIModel;
      final String verdictFailureAll = LinkedCardStatusVerdict.verdictFailureAll.value;
      if (errorUIModel.statusCode == CommonHttpClient.INTERNAL_SERVER_ERROR &&
          errorUIModel.verdict == verdictFailureAll) {
        DOPCardStatusScreen.pushReplacementNamed(
          linkedCardStatusModel: LinkedCardStatusModel(verdict: verdictFailureAll),
        );
        return;
      }

      handleApiError(state.errorUIModel);
      setLinkingCardChecking();
    } else if (state is ManualLinkCardErrorState) {
      handleApiError(
        state.errorUIModel,
        onShowUIError: (_) {
          showManualLinkCardFailedBottomSheet(ctx: context);
        },
      );
    }
  }

  @visibleForTesting
  void setLinkingCardChecking() {
    /// This is a Workaround for the issue https://trustingsocial1.atlassian.net/browse/EMA-6218
    /// We need to delay Xms to prevent the result from scanning QR
    /// When the app is linking card, we should not handle result from scanning QR Code
    Future<void>.delayed(const Duration(milliseconds: 800), () {
      isLinkingCardChecking = false;
    });
  }

  @visibleForTesting
  void listenerEmiConditionState(BuildContext context, SetupEmiConditionState state) {
    /// emi condition loaded
    if (state is SetupEmiConditionCompleteState) {
      _qrScreenCommonCubit.onEmiRulesLoaded();
    }
  }

  @visibleForTesting
  void unlockCameraForScanning(ManualLinkCardState state) {
    if (state is PaymentMethodLoadedState || state is LinkedCardStatusCheckingErrorState) {
      _parseQrCodeCubit.unlockQrCodeScanning();

      /// Start parse local image if mode is mock test.
      parseMockQRCodeIfNeed();
    }
  }

  void handleShowInvalidQrCodePopup({
    String title = EvoStrings.titleErrorPopupInvalidFormat,
    String message = EvoStrings.errorQrCodeDefaultErrorDescription,
    String textPositive = EvoStrings.scanFailBackToHome,
  }) {
    EvoDialogHelper().showDialogBottomSheet(
      dialogId: EvoDialogId.paymentInvalidQrCodeBottomSheet,
      title: title,
      content: message,
      isShowButtonClose: true,
      onClickClose: () {
        _parseQrCodeCubit.unlockQrCodeScanning();
      },
      header: evoImageProvider.asset(
        EvoImages.icScanError,
        width: context.screenWidth,
        fit: BoxFit.fitWidth,
      ),
      textPositive: textPositive,
      onClickPositive: () {
        MainScreen.removeUntilAndPushReplacementNamed(isLoggedIn: true);
      },
    );
  }

  @override
  Future<void> handleApiError(
    ErrorUIModel? error, {
    void Function(ErrorUIModel? error)? onShowUIError,
  }) async {
    switch (error?.statusCode) {
      case CommonHttpClient.NO_INTERNET:
      case CommonHttpClient.SOCKET_ERRORS:
      case CommonHttpClient.UNKNOWN_ERRORS:
      case CommonHttpClient.CONNECTION_TIMEOUT:
      case CommonHttpClient.INTERNAL_SERVER_ERROR:
        _parseQrCodeCubit.unlockQrCodeScanning();
        break;
    }
    super.handleApiError(error, onShowUIError: onShowUIError);
  }

  @visibleForTesting
  void getPaymentInfo() {
    getPaymentMethodIfNeed();

    getEmiConditionIfNeed();
  }

  Future<void> handleQRCodeResponse(QrCodeParseEntity entity) async {
    _qrScreenCommonCubit.onParseQrCodeSuccess(entity);
  }

  @visibleForTesting
  Future<void> listenQrScannerAfterParseState(
      BuildContext context, QrScannerAfterParseState state) async {
    if (state is QrScannerAfterParseApiLoading) {
      EvoUiUtils().showHudLoading();
      return;
    }

    if (isLinkingCardChecking == true) {
      return;
    }

    /// To enhance UX, we hide [HubLoading] here (at the end of the screen flow) rather than in previous states.
    /// This ensures that the loading is displayed and hidden only once during
    /// the entire process of this screen.
    EvoUiUtils().hideHudLoading();

    if (state is QrScannerAfterParseCommonError) {
      handleApiError(
        state.errorUIModel,
        onShowUIError: (ErrorUIModel? error) {
          handleShowInvalidQrCodePopup(
            title: QrCodeUiUtils.getTitlePopUpFromVerdict(verdict: error?.verdict),
            message: QrCodeUiUtils.getContentPopUpFromVerdict(message: error?.userMessage),
          );
        },
      );
    } else if (state is StoreNotFoundOrDeactivatedState) {
      /// StoreNotFoundOrDeactivatedState is a special case of error that we need to handle it separately
      handleShowInvalidQrCodePopup(
        title: QrCodeUiUtils.getTitlePopUpFromVerdict(verdict: state.errorUIModel?.verdict),
        message: QrCodeUiUtils.getContentPopUpFromVerdict(message: state.errorUIModel?.userMessage),
      );
    } else if (state is StoreInfoLoaded) {
      openInputAmountScreen(state);
    } else if (state is DynamicQrCodeSpecializedState) {
      handleDynamicQrSpecializedStates(state);
    }
  }

  @visibleForTesting
  void openInputAmountScreen(StoreInfoLoaded state) {
    final StoreInfoEntity? store = state.store;
    final String? productCode = state.productCode;
    final VNPayQrInfoEntity? vnPayQRInfo = state.vnPayQrInfo;
    final bool isProductCodeAndMerchantValidForEMI =
        _paymentWithEMIUtils.checkIfProductCodeValid(productCode) &&
            _paymentWithEMIUtils.checkIfMerchantIsValid(productCode, store?.merchantId);
    if (_paymentWithEMIUtils.isUserWantPayWithEmi() && isProductCodeAndMerchantValidForEMI) {
      EmiInputAmountScreen.pushNamed(
        store: store,
        productCode: productCode,
        vnPayQRInfo: vnPayQRInfo,
      );
    } else {
      FullPaymentInputAmountScreen.pushNamed(
        store: store,
        productCode: productCode,
        vnPayQRInfo: vnPayQRInfo,
        shouldShowEMIUnqualifiedBottomSheet:
            //still allow user to go to input amount screen, but show EMI unqualified bottom sheet
            //https://trustingsocial1.atlassian.net/browse/EMA-6401
            _paymentWithEMIUtils.isUserWantPayWithEmi() && !isProductCodeAndMerchantValidForEMI,
      );
    }
  }

  /// Handle Dynamic Order states
  void handleDynamicQrSpecializedStates(DynamicQrCodeSpecializedState state) {
    if (state is CreateDynamicOrderSuccess) {
      handleCreatePaymentSuccessState(state);
      return;
    }

    if (state is CreateDynamicOrderWithActivatePosLimitErrorState) {
      final OrderActivatePosLimitEntity orderActivatePosLimitEntity = OrderActivatePosLimitEntity(
        orderSession: state.entity?.session,
        emiPackages: state.entity?.emiPackages,
        // With Dynamic QR, PaymentService is outrightPurchase (have not emi)
        paymentService: PaymentService.outrightPurchase,
        prerequisitesAction: state.entity?.prerequisitesAction,
      );

      _activePosLimitHandler.handleActivatedPOSLimitErrorState(
        orderActivatePosLimitEntity: orderActivatePosLimitEntity,
        entity: state.entity,
        entryPoint: Screen.qrCodeScannerScreen,
        onActivatedPOSLimitFlowSucceed: (OrderActivatePosLimitEntity orderActivatePosLimitEntity) {
          UpdateConfirmPaymentScreen.pushNamed(
            orderSession: orderActivatePosLimitEntity.orderSession,
            selectedVoucher: appState.paymentSharedData.selectedVoucher,
            emiPackage: state.entity?.emiPackages?.firstOrNull,
          );
        },
      );
      return;
    }
  }

  @visibleForTesting
  Future<void> handleCreatePaymentSuccessState(CreateDynamicOrderSuccess state) async {
    final OrderSessionEntity? orderSession = state.entity?.session;
    final bool isActivateCardOrPosLimit =
        await _activePosLimitHandler.handleActivateCardOrPosLimitIfNeed(
      orderActivatePosLimitEntity: OrderActivatePosLimitEntity(
        orderSession: orderSession,
        // Note: Dynamic QR with EMI payment is not supported
        paymentService: PaymentService.outrightPurchase,
        prerequisitesAction: state.entity?.prerequisitesAction,
      ),
      entryPoint: Screen.qrCodeScannerScreen,
      onActivatedPOSLimitFlowSucceed: (OrderActivatePosLimitEntity entity) {
        final CreateDynamicOrderSuccess newState = CreateDynamicOrderSuccess(
          CreateOrderEntity(
            session: entity.orderSession,
            emiPackages: entity.emiPackages,
          ),
        );
        handleCreatePaymentSuccessState(newState);
      },
    );

    if (isActivateCardOrPosLimit) {
      return;
    }

    UpdateConfirmPaymentScreen.pushNamed(
      orderSession: orderSession,
      selectedVoucher: appState.paymentSharedData.selectedVoucher,
      emiPackage: state.entity?.emiPackages?.firstOrNull,
    );
  }
}
