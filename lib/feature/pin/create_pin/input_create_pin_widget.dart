import 'package:flutter/material.dart';

import '../../../resources/resources.dart';
import '../../../widget/evo_pin_code/evo_pin_code_config.dart';
import 'obscuring_text_editing_controller.dart';

class PinTextFieldWidget extends StatefulWidget {
  final ObscuringTextEditingController? controller;
  final FocusNode? focusNode;
  final String? errorText;
  final String? labelText;
  final TextStyle? labelStyle;
  final TextStyle? textStyle;
  final TextStyle? errorStyle;
  final Color? background;
  final TextInputType? keyboardType;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final Widget? errorIcon;
  final bool obscureText;
  final int maxLength;
  final int? maxLines;
  final double? heightSuffixIcon;
  final double? widthSuffixIcon;
  final void Function(String)? onChange;
  final bool autoUnFocus;

  const PinTextFieldWidget(
      {super.key,
      this.background,
      this.controller,
      this.focusNode,
      this.textStyle,
      this.labelText,
      this.keyboardType,
      this.labelStyle,
      this.prefixIcon,
      this.suffixIcon,
      this.errorText,
      this.errorStyle,
      this.errorIcon,
      this.maxLines = 1,
      this.maxLength = EvoPinCodeConfig.maxPinCodeLength,
      this.obscureText = false,
      this.heightSuffixIcon,
      this.onChange,
      this.autoUnFocus = true,
      this.widthSuffixIcon});

  @override
  State<PinTextFieldWidget> createState() => _PinTextFieldWidgetState();
}

class _PinTextFieldWidgetState extends State<PinTextFieldWidget> {
  bool showObscureText = true;

  @override
  void initState() {
    super.initState();
    widget.focusNode?.addListener(clearText);
  }

  @override
  void dispose() {
    widget.focusNode?.removeListener(clearText);
    widget.focusNode?.dispose();
    super.dispose();
  }

  void clearText() {
    if (widget.focusNode?.hasFocus == true) {
      setState(() {
        widget.controller?.clear();
        widget.onChange?.call('');
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: <Widget>[
      TextField(
          controller: widget.controller,
          focusNode: widget.focusNode,
          enableInteractiveSelection: false,
          keyboardType: widget.keyboardType ?? TextInputType.number,
          style: widget.textStyle ??
              evoTextStyles.h600(evoColors.textActive).copyWith(letterSpacing: 8),
          obscureText: widget.obscureText,
          maxLength: widget.maxLength,
          maxLines: widget.maxLines,
          onChanged: (String value) {
            ///auto unFocus when input maxLength and has focus
            if (value.length == widget.maxLength &&
                widget.focusNode?.hasPrimaryFocus == true &&
                widget.autoUnFocus) {
              widget.focusNode?.unfocus();
            }
            widget.onChange?.call(value);
          },
          decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
              isCollapsed: true,
              labelText: widget.labelText,
              labelStyle: widget.labelStyle ??
                  evoTextStyles
                      .bodyLarge(
                          widget.errorText == null ? evoColors.textPassive2 : evoColors.error)
                      .copyWith(letterSpacing: 0),
              counterText: '',
              errorText:
                  widget.errorText == null || widget.controller?.text.isEmpty == true ? null : '',
              errorStyle: const TextStyle(fontSize: 0),
              focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: evoColors.primary),
                  borderRadius: BorderRadius.circular(8)),
              enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: evoColors.textHint),
                  borderRadius: BorderRadius.circular(8)),
              errorBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: evoColors.error),
                  borderRadius: BorderRadius.circular(8)),
              focusedErrorBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: evoColors.error),
                  borderRadius: BorderRadius.circular(8)),
              suffixIcon: _itemObscuring(),
              suffixIconConstraints: BoxConstraints(
                maxHeight: widget.heightSuffixIcon ?? 48,
                maxWidth: widget.widthSuffixIcon ?? 48,
              ))),
      if (widget.errorText != null && widget.controller?.text.isNotEmpty == true)
        Row(children: <Widget>[
          widget.errorIcon ??
              evoImageProvider.asset(EvoImages.icPinCodeError, width: 16, fit: BoxFit.fitWidth),
          Expanded(
              child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Text(widget.errorText ?? '',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: widget.errorStyle ?? evoTextStyles.bodySmall(color: evoColors.error)),
          ))
        ])
    ]);
  }

  Widget _itemObscuring() {
    return GestureDetector(
        onTap: () {
          setState(() {
            showObscureText = !showObscureText;
            widget.controller?.updateShowObscureText = showObscureText;
          });
        },
        child: Container(
          padding: const EdgeInsets.only(right: 16, top: 8, bottom: 8, left: 8),
          child: widget.suffixIcon ??
              evoImageProvider.asset(
                  showObscureText ? EvoImages.icShowOffPin : EvoImages.icShowOnPin,
                  color: evoColors.textPassive2),
        ));
  }
}
