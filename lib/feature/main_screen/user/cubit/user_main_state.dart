import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/response/private_policy_entity.dart';

abstract class UserMainState {}

class UserMainInitialState extends UserMainState {}

class BiometricChangedState extends UserMainState {}

class BiometricUnusableState extends UserMainState {}

class BiometricValidState extends UserMainState {}

class DecreeConsentedState extends UserMainState {}

class DecreeNotYetConsentState extends UserMainState {
  final PrivacyPolicyEntity? entity;

  DecreeNotYetConsentState(this.entity);
}

class DecreeConsentErrorState extends UserMainState {
  final ErrorUIModel errorUIModel;

  DecreeConsentErrorState(this.errorUIModel);
}

class RequestUserActivateBiometricHandledState extends UserMainState {}
