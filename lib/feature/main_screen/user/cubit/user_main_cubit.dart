import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../data/repository/decree_consent_repo.dart';
import '../../../../../data/response/private_policy_entity.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../../../util/mock_file_name_utils/mock_common_file_name.dart';
import '../../../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../biometric/model/biometric_status_change_notifier.dart';
import '../../../biometric/request_user_active_biometric/request_user_active_biometric_handler.dart';
import '../../../biometric/request_user_active_biometric/request_user_active_biometric_handler_impl.dart';
import '../../../feature_toggle.dart';
import '../../../payment/models/payment_entry_point.dart';
import 'user_main_state.dart';

class UserMainCubit extends CommonCubit<UserMainState> {
  @visibleForTesting
  final AppState appState;
  @visibleForTesting
  final DecreeConsentRepo decreeConsentRepo;
  @visibleForTesting
  final FeatureToggle featureToggle;

  @visibleForTesting
  final RequestUserActivateBiometricHandler requestUserActiveBiometricHandler =
      getIt.get<RequestUserActivateBiometricHandler>();

  @visibleForTesting
  final EvoLocalStorageHelper evoLocalStorageHelper = getIt.get<EvoLocalStorageHelper>();

  UserMainCubit({
    required this.appState,
    required this.decreeConsentRepo,
    required this.featureToggle,
  }) : super(UserMainInitialState());

  Future<void> checkDecreeConsent() async {
    final PrivacyPolicyEntity entity = await decreeConsentRepo.checkDecreeConsent(
      mockConfig: MockConfig(
        enable: false,
        fileName: checkPrivatePolicyFileName(
          PrivacyPolicyEntity.verdictSuccess,
        ),
      ),
    );
    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      if (entity.consented == true) {
        emit(DecreeConsentedState());
      } else {
        emit(DecreeNotYetConsentState(entity));
      }
    } else {
      emit(DecreeConsentErrorState(ErrorUIModel.fromEntity(entity)));
    }
  }

  Future<void> handleBiometricFeature({
    ActivateBiometricUseCase? activateBiometricUseCase,
  }) async {
    final RequestUserActivateBiometricStatus status = await requestUserActiveBiometricHandler.start(
      useCase: activateBiometricUseCase,
    );

    handleRequestUserActivateBiometricStatus(status);
    checkBiometricStatus();
    setNewDevice(false);
  }

  void clearUserStatus() {
    appState.loginSharedData.clear();
  }

  void preparePaymentDataIfNeed() {
    if (featureToggle.enableEmiFeatureVersion == EmiFeatureVersion.version_1) {
      appState.paymentSharedData.paymentEntryPoint = PaymentEntryPoint.bottomNavigationBar;
    }
  }

  @visibleForTesting
  Future<void> checkBiometricStatus() async {
    switch (appState.biometricStatusChangeNotifier.value) {
      case BiometricStatus.deviceSettingChanged:
        emit(BiometricChangedState());
        break;

      case BiometricStatus.biometricTokenUnusable:
        emit(BiometricUnusableState());
        break;

      case BiometricStatus.notSetup:
      case BiometricStatus.usable:
      default:
        emit(BiometricValidState());
        break;
    }
  }

  @visibleForTesting
  Future<void> setNewDevice(bool isNewDevice) async {
    await evoLocalStorageHelper.setNewDevice(isNewDevice);
  }

  @visibleForTesting
  void handleRequestUserActivateBiometricStatus(RequestUserActivateBiometricStatus status) {
    if (featureToggle.enableDeleteAccountFeatureVersion == DeleteAccountFeatureVersion.version_2) {
      if (status == RequestUserActivateBiometricStatus.success ||
          status == RequestUserActivateBiometricStatus.fail ||
          status == RequestUserActivateBiometricStatus.unQualified) {
        emit(RequestUserActivateBiometricHandledState());
      }
    }
  }
}
