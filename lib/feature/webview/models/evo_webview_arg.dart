import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/webview/common_webview_controller.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/flutter_downloader/models/download_info.dart';
import 'package:flutter_common_package/util/flutter_downloader/models/download_result.dart';

import '../../../prepare_for_app_initiation.dart';
import '../../../resources/ui_strings.dart';
import '../../../util/evo_snackbar.dart';
import '../../../util/extension.dart';
import '../../../util/web_link_utils.dart';
import '../../../widget/evo_appbar.dart';
import '../../deep_link/deep_link_handler.dart';
import '../../deep_link/deep_link_utils.dart';
import '../../deep_link/model/deep_link_model.dart';
import '../../dop_native/features/ekyc_ui_only/nfc_reader_introduction/constants/dop_native_nfc_entry_point.dart';
import '../../dop_native/features/ekyc_ui_only/nfc_reader_introduction/fpt/dop_native_fpt_nfc_reader_introduction_screen.dart';
import '../../dop_native/features/ekyc_ui_only/nfc_reader_introduction/models/dop_native_nfc_shared_model.dart';
import '../../dop_native/features/ekyc_ui_only/nfc_reader_introduction/tv_ekyc/dop_native_tv_nfc_reader_introduction_screen.dart';
import '../../dop_native/models/dop_native_state.dart';
import '../../feature_toggle.dart';
import '../../logging/evo_logging_event.dart';
import '../../logging/metadata_define/evo_event_metadata.dart';
import '../../server_logging/evo_analytic_logger.dart';
import '../../user_journey/user_journey_handler.dart';
import '../utils/webview_util.dart';

/// Callback if special link detected by web view is not deeplink/onelink
typedef AdditionalSpecialLinkCallback = void Function(String? url);

class EvoWebViewArg extends CommonWebViewArg {
  /// The webpage will call `function shareLogEventWithApp(eventName, params)` to share log event with app
  /// Where:
  /// - `eventName` is String
  /// - `params` is Map<String, Object?>?
  ///
  /// The app will handle the log event and send it to Firebase Analytics and Appsflyer for Marketing purposes
  static const String webViewSourceLogEventHandler = 'shareLogEventWithApp';

  /// The webpage will call `function startDopNfc(accessToken)` to start the NFC feature in the Evo app
  /// Where:
  /// - `accessToken` is String. Used to authenticate with the DOP server when sending NFC data to the server.
  static const String webViewSourceNFCHandler = 'startDopNfc';

  /// when load an web-view, will set an flag to identify EVO App web-view
  /// ref: https://trustingsocial1.atlassian.net/browse/EMA-6720
  static const String dopWebViewIdentifyFlagJavascriptSource = 'window.is_wv_evoapp = true;';

  EvoWebViewArg({
    required super.url,
    required super.title,
    AdditionalSpecialLinkCallback? additionalSpecialLinkCallback,
    RegExp? formatAdditionalSpecialLink,
    super.defaultHtmlFileName,
    super.isFullScreen,
    super.redirectType = WebViewRedirectType.inApp,
    CommonWebViewController? controller,
    PreferredSizeWidget? appBar,
    super.useShouldOverrideUrlLoading,
    super.safeAreaTop,
    super.safeAreaBottom,
    super.safeAreaLeft,
    super.safeAreaRight,
    super.nextActionWidget,
    super.errorWidget,
    super.onHandlePopScope,
    super.onDidPopWebView,
    super.eventTrackingScreenId,
    super.resizeToAvoidBottomInset,
  }) : super(
          controller: createController(controller),
          appBar: appBar ?? EvoAppBar(title: title),
          formatSpecialLinkHandledByHostApp: WebViewUtil()
              .getRegExpOfSpecialLinkHandledByEvoApp(additionalFormat: formatAdditionalSpecialLink),
          onHandleSpecialLinkDetected: (String specialLink) {
            handleSpecialLinkDetected(
              specialLink,
              additionalSpecialLinkCallback: additionalSpecialLinkCallback,
            );
          },
          eventTrackingMetaData: buildEventLoggingMetaData,
          isEnableEventTracking: getIt<FeatureToggle>().enableEventTrackingFeature,
          onAppStateChange: handleAppStateChange,
          onDispose: handleOnDispose,
        );

  EvoWebViewArg copyWith({
    String? url,
    String? title,
    String? defaultHtmlFileName,
    bool? isFullScreen,
    WebViewRedirectType? redirectType,
    CommonWebViewController? controller,
    PreferredSizeWidget? appBar,
    bool? useShouldOverrideUrlLoading,
    bool? safeAreaTop,
    bool? safeAreaBottom,
    bool? safeAreaLeft,
    bool? safeAreaRight,
    Widget? nextActionWidget,
    Widget Function(String, VoidCallback)? errorWidget,
    VoidCallback? onHandlePopScope,
    VoidCallback? onDidPopWebView,
    RegExp? additionalFormatSpecialLink,
    AdditionalSpecialLinkCallback? specialLinkCallback,
    bool? resizeToAvoidBottomInset,
  }) =>
      EvoWebViewArg(
        url: url ?? this.url,
        title: title ?? this.title,
        defaultHtmlFileName: defaultHtmlFileName ?? this.defaultHtmlFileName,
        isFullScreen: isFullScreen ?? this.isFullScreen,
        redirectType: redirectType ?? this.redirectType,
        controller: controller ?? this.controller,
        appBar: appBar ?? this.appBar,
        useShouldOverrideUrlLoading:
            useShouldOverrideUrlLoading ?? this.useShouldOverrideUrlLoading,
        safeAreaTop: safeAreaTop ?? this.safeAreaTop,
        safeAreaBottom: safeAreaBottom ?? this.safeAreaBottom,
        safeAreaLeft: safeAreaLeft ?? this.safeAreaLeft,
        safeAreaRight: safeAreaRight ?? this.safeAreaRight,
        nextActionWidget: nextActionWidget ?? this.nextActionWidget,
        errorWidget: errorWidget ?? this.errorWidget,
        onHandlePopScope: onHandlePopScope ?? this.onHandlePopScope,
        onDidPopWebView: onDidPopWebView ?? this.onDidPopWebView,
        resizeToAvoidBottomInset: resizeToAvoidBottomInset ?? this.resizeToAvoidBottomInset,
      );

  @visibleForTesting
  static CommonWebViewController createController(CommonWebViewController? controller) {
    controller ??= CommonWebViewController();

    /// Handle download file from WebView
    controller.onDownloadCallback ??= onDownloadCallback;

    /// Handle calls from webpage's javascript to app
    controller.javaScriptHandlers ??= <JavaScriptHandler>[];
    // Handle log event from WebView
    handleLogEventFromWebView(controller);

    // Handle NFC feature from WebView
    handleNFCFeatureFromWebView(controller);

    /// set up javascript evaluators inside onLoadStop
    handleJavascriptEvaluators(controller);

    return controller;
  }

  @visibleForTesting
  static Future<void> handleAppStateChange(
    CommonWebViewController? controller,
    bool isTopVisible,
    AppLifecycleState state,
  ) async {
    switch (state) {
      case AppLifecycleState.inactive:
        // check and save info DOP Web Journey when app is inactive instead of paused, because:
        // - app is killed quickly by user: inactive is called before paused and paused is not long enough to save data
        // - app enter background: inactive is called before paused
        final Uri? uri = await controller?.getCurrentUrl?.call();
        final String? url = uri?.toString();
        if (url != null && webLinkUtils.isDOPLink(url)) {
          UserJourneyHandler().saveInfoDOPWebJourney(url: url);
        }
        break;
      case AppLifecycleState.resumed:
        // should clear info DOP Web Journey when app is resumed to make sure the data is cleared when user back to app
        UserJourneyHandler().clearInfoDOPWebJourneyIfSaved();
        break;
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
      case AppLifecycleState.paused:
        break;
    }
  }

  @visibleForTesting
  static void handleOnDispose() {
    // clear info DOP Web Journey when user exist WebView
    UserJourneyHandler().clearInfoDOPWebJourneyIfSaved();
  }

  @visibleForTesting
  static void onDownloadCallback(DownloadResult result) {
    final EvoSnackBar snackBar = getIt.get<EvoSnackBar>();

    final DownloadInfo? info = result.info;
    if (result.errorType != null || info == null) {
      snackBar.show(
        CommonStrings.genericWebViewError,
        typeSnackBar: SnackBarType.error,
        durationInSec: SnackBarDuration.short.value,
      );
      return;
    }

    final CommonDownloadTaskStatus status = info.status;

    if (status == CommonDownloadTaskStatus.complete) {
      snackBar.show(
        EvoStrings.downloadLinkFileSuccess,
        durationInSec: SnackBarDuration.short.value,
      );
    } else if (status == CommonDownloadTaskStatus.failed) {
      snackBar.show(
        EvoStrings.downloadLinkFileFail,
        typeSnackBar: SnackBarType.error,
        durationInSec: SnackBarDuration.short.value,
      );
    } else if (status == CommonDownloadTaskStatus.running && info.progress == 0) {
      snackBar.show(
        EvoStrings.startDownloadLinkFile,
        typeSnackBar: SnackBarType.neutral,
        durationInSec: SnackBarDuration.short.value,
      );
    } else {
      return;
    }
  }

  /// The webpage will call `function shareLogEventWithApp(eventName, params)` to share log event with app
  /// Where:
  /// - `eventName` is String
  /// - `params` is Map<String, Object?>?
  ///
  /// The app will handle the log event and send it to Firebase Analytics and Appsflyer for Marketing purposes
  @visibleForTesting
  static void handleLogEventFromWebView(CommonWebViewController controller) {
    controller.javaScriptHandlers?.add(JavaScriptHandler(
        handlerName: webViewSourceLogEventHandler,
        callback: (List<dynamic> arguments) async {
          if (arguments.length < 2) {
            if (kDebugMode) {
              print(
                'EvoWebViewArg #createController Invalid arguments for $webViewSourceLogEventHandler',
              );
            }
            return;
          }
          final String name = arguments[0] as String;
          final Map<String, Object?>? parameters = arguments[1] as Map<String, Object?>?;
          if (kDebugMode) {
            print(
              'EvoWebViewArg #createController WebView calls: shareLogEventWithApp(eventName = $name, parameters = $parameters)',
            );
          }

          /// To analyze advertising campaigns's performance for EVO APP
          await EvoAnalyticLogger.logEvent(name: name, params: parameters?.toNonNullable());
        }));
  }

  /// https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3612672239/DOP-EVO+Technical+Alignment+-+Meeting+notes#Step-13%3A-DOP-WebView-request-Evo-app-to-start-NFC-feature
  /// The webpage will call `function startNFC(accessToken)` to start the NFC feature in the Evo app
  /// Where:
  /// - `accessToken` is String. Used to authenticate with the DOP server when sending NFC data to the server.
  static void handleNFCFeatureFromWebView(CommonWebViewController controller) {
    controller.javaScriptHandlers?.add(
      JavaScriptHandler(
        handlerName: webViewSourceNFCHandler,
        callback: (List<dynamic> arguments) async {
          final LoggingRepo loggingRepo = getIt.get<LoggingRepo>();
          final dynamic params = arguments.elementAtOrNull(0);
          if (params is! Map<String, dynamic>) {
            if (kDebugMode) {
              print(
                'EvoWebViewArg #createController Invalid arguments for $webViewSourceNFCHandler',
              );
            }

            /// log error for DOP NFC
            loggingRepo.logErrorEvent(
              errorType: EvoEventType.dopNFC.name,
              args: <String, dynamic>{
                'step': 'triggered_by_webview',
                'error': 'Invalid arguments for $webViewSourceNFCHandler',
                'errorDetail': 'arguments[0] is not json, but ${params.runtimeType}',
              },
            );
            return;
          }

          final String? accessToken = params['accessToken'] as String?;
          final String? nationalIdNumber = params['nationalIdNumber'] as String?;
          final String? dateOfBirth = params['dateOfBirth'] as String?;
          final String? dateOfExpiryIdCard = params['dateOfExpiryIdCard'] as String?;

          final Uri? currentDOPUri = await controller.getCurrentUrl?.call();
          final String? currentDOPUrl = currentDOPUri?.toString();

          if (kDebugMode) {
            print(
              'EvoWebViewArg #createController WebView calls: '
              'startDopNfc(accessToken = $accessToken, '
              'nationalIdNumber = $nationalIdNumber, '
              'dateOfBirth = $dateOfBirth, '
              'dateOfExpiryIdCard = $dateOfExpiryIdCard'
              'currentDOPUrl = $currentDOPUrl',
            );
          }

          /// log event for DOP NFC
          loggingRepo.logEvent(
            eventType: EvoEventType.dopNFC,
            data: <String, dynamic>{
              'step': 'triggered_by_webview',
              'accessToken': accessToken,
              'nationalIdNumber': nationalIdNumber,
              'dateOfBirth': dateOfBirth,
              'dateOfExpiryIdCard': dateOfExpiryIdCard,
              'currentDOPUrl': currentDOPUrl,
            },
          );

          final DOPNativeState dopNativeState = getIt.get<AppState>().dopNativeState;

          /// Save the NFC data to the app state, to use later when the NFC feature is triggered
          dopNativeState.nfcSharedModel = DOPNativeNFCSharedModel(
            accessToken: accessToken,
            nationalIdNumber: nationalIdNumber,
            dateOfBirth: dateOfBirth,
            dateOfExpiryIdCard: dateOfExpiryIdCard,
            dopWebViewURL: currentDOPUrl,
          );

          /// set entry point to WebView
          /// refer: AC https://trustingsocial1.atlassian.net/browse/EMA-2913
          dopNativeState.entryPoint = CommonScreen.webViewPage.name;

          /// Redirect to the NFC Reader screen by Feature Toggle
          final NfcSdkProvider nfcSdkProvider = getIt<FeatureToggle>().nfcSdkProvider;
          switch (nfcSdkProvider) {
            case NfcSdkProvider.trustVision:
              DOPNativeTvNFCReaderIntroductionScreen.pushNamed(
                DOPNativeTvNFCReaderIntroductionScreenArg(
                  controller: DOPNativeTvNFCReaderIntroductionController(
                    // Reload the WebView after the NFC feature is finished, to update DOP app state
                    onFinished: () {
                      // clear shared model when finish
                      dopNativeState.clearNFCSharedModel();
                      controller.reload?.call();
                    },
                  ),
                  entryPoint: NFCEntryPoint.dopWebView,
                ),
              );
              break;
            case NfcSdkProvider.fpt:
              DOPNativeFptNFCReaderIntroductionScreen.pushNamed(
                DOPNativeFptNFCReaderIntroductionScreenArg(
                  controller: DOPNativeFptNFCReaderIntroductionController(
                    // Reload the WebView after the NFC feature is finished, to update DOP app state
                    onFinished: () {
                      // clear shared model when finish
                      dopNativeState.clearNFCSharedModel();
                      controller.reload?.call();
                    },
                  ),
                  entryPoint: NFCEntryPoint.dopWebView,
                ),
              );
              break;
          }
        },
      ),
    );
  }

  /// Handle special link detected by web view, with format [formatSpecialLinkHandledByHostApp]
  /// Refer: https://trustingsocial1.atlassian.net/browse/EMA-3983
  ///        https://trustingsocial1.atlassian.net/browse/EMA-4087
  @visibleForTesting
  static void handleSpecialLinkDetected(
    String specialLink, {
    AdditionalSpecialLinkCallback? additionalSpecialLinkCallback,
  }) {
    final String? evoDeepLink = deepLinkUtils.extractEvoDeepLinkFromUrl(specialLink);

    if (evoDeepLink == null) {
      additionalSpecialLinkCallback?.call(specialLink);
      return;
    }

    handleDeeplinkOneLink(evoDeepLink);
  }

  /// Handle OneLink/deeplink
  @visibleForTesting
  static void handleDeeplinkOneLink(String evoDeepLink) {
    final DeepLinkModel deepLinkModel = deepLinkUtils.generateDeepLinkModel(
      deepLinkValue: evoDeepLink,
    );

    getIt.get<DeepLinkHandler>().executeDeepLink(
          deepLink: deepLinkModel,
          isLoggedIn: getIt.get<AppState>().isUserLogIn,
        );
  }

  /// inject unique_token for WebView event log if needed
  static Map<String, dynamic> buildEventLoggingMetaData(Uri? uri) {
    final Map<String, dynamic> metaData = <String, dynamic>{};
    final String? uniqueToken = webLinkUtils.getUniqueTokenFromDOPWebUrl(uri.toString());
    if (uniqueToken != null) {
      metaData[EvoEventMetadataKey.uniqueToken] = uniqueToken;
    }
    return metaData;
  }

  @visibleForTesting
  static void handleJavascriptEvaluators(CommonWebViewController controller) {
    controller.javaScriptEvaluators ??= <JavaScriptEvaluator>[];

    // ref: https://trustingsocial1.atlassian.net/browse/EMA-6720
    // setup flag to identify EVO App web-view
    controller.javaScriptEvaluators?.add(
      JavaScriptEvaluator(source: dopWebViewIdentifyFlagJavascriptSource),
    );
  }
}
