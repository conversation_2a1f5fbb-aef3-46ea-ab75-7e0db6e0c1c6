import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../util/extension.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import 'evo_alice_chatwoot_config_values.dart';

part 'evo_alice_chatwoot_state.dart';

class EvoAliceChatWootCubit extends CommonCubit<EvoAliceChatWootState> {
  final EvoLocalStorageHelper evoLocalStorageHelper;

  EvoAliceChatWootCubit({required this.evoLocalStorageHelper}) : super(ChatwootInitState());

  Future<void> initialize() async {
    emit(EvoAliceChatWootLoadingState());
    final String? nonEvoUserId = await evoLocalStorageHelper.getChatwootIdForNonEvoUser();
    emit(NonUserEvoIdLoadedState(
      nonEvoUserId: nonEvoUserId,
      nonEvoUserChatwootName: getNonEvoUserChatWootName(nonEvoUserId),
    ));
  }

  /// Refer thread: https://trustingsocial.slack.com/archives/C067KK41EEN/p1705476273699199?thread_ts=1705475922.897469&cid=C067KK41EEN
  @visibleForTesting
  String getNonEvoUserChatWootName(String? nonEvoUserId) =>
      '${EvoAliceChatwootConfigValues.nonEvoUserChatWootNamePrefix} ${nonEvoUserId?.getLastCharacters(10)}';
}
