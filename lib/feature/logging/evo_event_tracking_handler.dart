import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/util/network_manager.dart';

import '../../prepare_for_app_initiation.dart';
import 'evo_event_tracking_utils/evo_event_tracking_utils_impl.dart';
import 'screen_action_define/special_action_event.dart';

EvoEventTrackingHandler get evoEventTrackingHandler => getIt.get<EvoEventTrackingHandler>();

class EvoEventTrackingHandler {
  @visibleForTesting
  bool isLoggedInitSessionEvent = false;

  // handle logging for init session
  // if there is no internet when prepare to logging -> observe the network status stream to log again after having internet
  void prepareInitialSession() {
    final NetworkManager networkManager = getIt.get<NetworkManager>();
    if (networkManager.hasInternet == false) {
      // observe to network stream to re-send initial event
      networkManager.myStreamNetwork.listen((bool hasInternet) {
        if (hasInternet == true && isLoggedInitSessionEvent == false) {
          logInitialSession();
        }
      });
      return;
    }
    if (isLoggedInitSessionEvent) {
      return;
    }
    logInitialSession();
  }

  @visibleForTesting
  void logInitialSession() {
    evoEventTrackingUtils.sendEvoSpecialEvent(
      eventActionId: SpecialActionEvent.initSession,
    );
    isLoggedInitSessionEvent = true;
  }
}
