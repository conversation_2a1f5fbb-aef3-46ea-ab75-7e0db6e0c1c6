const String maleInString = 'M';
const String femaleInString = 'F';

enum Gender {
  male,
  female;

  String get code {
    switch (this) {
      case male:
        return maleInString;
      case female:
        return femaleInString;
    }
  }

  static Gender? formatGenderString(String? gender) {
    switch (gender) {
      case femaleInString:
        return Gender.female;
      case maleInString:
        return Gender.male;
      default:
        return null;
    }
  }
}
