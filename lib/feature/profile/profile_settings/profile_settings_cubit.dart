import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../biometric/biometric_token_module/biometric_token_usability_mixin.dart';
import '../../biometric/utils/bio_auth_result.dart';
import '../../biometric/utils/biometrics_authenticate.dart';

part 'profile_settings_state.dart';

class ProfileSettingCubit extends CommonCubit<ProfileSettingsState>
    with BiometricTokenUsabilityMixin {
  final BiometricsAuthenticate bioAuth;
  final EvoLocalStorageHelper secureStorageHelper;

  ProfileSettingCubit({required this.bioAuth, required this.secureStorageHelper})
      : super(ProfileSettingsBioState.init());

  Future<void> getBioAuthInfo() async {
    /// check phone if support biometrics
    final bool isDeviceSupportBioMetrics = await bioAuth.isDeviceSupportBiometrics();

    if (isDeviceSupportBioMetrics) {
      /// Check biometric token status
      final bool isBiometricTokenUnUsable = await checkAndHandleBiometricTokenUnUsable();

      final bool isEnableAuthByBiometrics =
          await secureStorageHelper.isEnableBiometricAuthenticator();

      emit(ProfileSettingsBioState(
        isEnableAuthByBiometrics: isEnableAuthByBiometrics,
        isDeviceSupportBiometrics: true,
        isBiometricTokenUnUsable: isBiometricTokenUnUsable,
      ));
    } else {
      emit(ProfileSettingsBioState(
        isEnableAuthByBiometrics: false,
        isDeviceSupportBiometrics: false,
        isBiometricTokenUnUsable: false,
      ));
    }
  }

  Future<void> reloadBiometricInfo(bool isEnableBioAuthenticator) async {
    getBioAuthInfo();
    showBiometricAuthAlertMessage(isEnableSuccess: isEnableBioAuthenticator);
  }

  void showBiometricAuthAlertMessage({required bool isEnableSuccess}) {
    emit(EnableAuthBioSuccess(isEnable: isEnableSuccess));
  }
}
