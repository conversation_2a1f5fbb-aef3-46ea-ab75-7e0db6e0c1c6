import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/user_repo.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/app_setting_util.dart';
import '../../../util/evo_authentication_helper.dart';
import '../../../util/functions.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../widget/evo_appbar.dart';
import '../../../widget/evo_divider_widget.dart';
import '../../biometric/biometric_token_module/biometrics_token_module.dart';
import '../../biometric/model/biometric_status_change_notifier.dart';
import '../../biometric/model/biometric_ui_model.dart';
import '../../biometric/utils/bio_auth_result.dart';
import '../../biometric/utils/biometrics_authenticate.dart';
import '../../delete_account/delete_account_handler.dart';
import '../../delete_account/delete_account_handler_impl.dart';
import '../../feature_toggle.dart';
import '../../pin/reset_pin/reset_pin_callback.dart';
import '../../pin/reset_pin/reset_pin_handler.dart';
import '../../verify_otp/verify_otp_cubit.dart';
import '../../verify_otp/verify_otp_page.dart';
import 'profile_settings_cubit.dart';
import 'widget/evo_list_tile_widget.dart';
import 'widget/evo_switch_widget.dart';

class ProfileSettingPage extends PageBase {
  static void pushNamed() {
    return navigatorContext?.pushNamed(Screen.profileSettingScreen.name);
  }

  const ProfileSettingPage({super.key});

  @override
  State<ProfileSettingPage> createState() => _ProfileSettingPageState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.profileSettingScreen.routeName);
}

class _ProfileSettingPageState extends EvoPageStateBase<ProfileSettingPage>
    with AppSettingUtil
    implements BiometricTokenModuleCallback {
  final ProfileSettingCubit _cubit = ProfileSettingCubit(
    bioAuth: getIt.get<BiometricsAuthenticate>(),
    secureStorageHelper: getIt.get<EvoLocalStorageHelper>(),
  );

  final DeleteAccountHandler _deleteAccountHandler = DeleteAccountHandlerImpl(
    userRepo: getIt.get<UserRepo>(),
    appState: getIt.get<AppState>(),
  );

  final BiometricsTokenModule _biometricsTokenModule = getIt.get<BiometricsTokenModule>();

  final ResetPinHandler _resetPinHandler = getIt.get<ResetPinHandler>();

  final ResetPinCallback _resetPinCallback = ResetPinCallback();

  String? phoneNumber = getIt.get<AppState>().userInfo.value?.phoneNumber;

  final FeatureToggle featureToggle = getIt.get<FeatureToggle>();

  RequestDeleteAccountCallback? _requestDeleteAccountCallback;

  RequestDeleteAccountCallback get requestDeleteAccountCallback {
    return _requestDeleteAccountCallback ??= _createRequestDeleteCallback();
  }

  @override
  void initState() {
    super.initState();
    _cubit.getBioAuthInfo();
    appState.biometricStatusChangeNotifier.addListener(_reInitCubit);
    _createResetPinCallback();
  }

  @override
  void dispose() {
    appState.biometricStatusChangeNotifier.removeListener(_reInitCubit);
    super.dispose();
  }

  void _reInitCubit() {
    _cubit.getBioAuthInfo();
    switch (appState.biometricStatusChangeNotifier.value) {
      case BiometricStatus.deviceSettingChanged:
      case BiometricStatus.biometricTokenUnusable:
        _cubit.showBiometricAuthAlertMessage(isEnableSuccess: false);
        break;
      case BiometricStatus.notSetup:
      case BiometricStatus.usable:
      default:

        /// do nothing
        break;
    }
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return MultiBlocProvider(
      providers: <BlocProvider<dynamic>>[
        BlocProvider<ProfileSettingCubit>(
          create: (_) => _cubit,
        ),
      ],
      child: Scaffold(
        backgroundColor: _getBackgroundColorByDeleteAccountFeatureVersion(),
        appBar: EvoAppBar(title: EvoStrings.settingTitle),
        body: SafeArea(
          child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: _buildBodyByDeleteAccountFeatureVersion()),
        ),
      ),
    );
  }

  Color _getBackgroundColorByDeleteAccountFeatureVersion() {
    switch (featureToggle.enableDeleteAccountFeatureVersion) {
      case DeleteAccountFeatureVersion.version_1:
        return evoColors.background;
      case DeleteAccountFeatureVersion.version_2:
        return evoColors.secondaryBackground;
    }
  }

  Widget _buildBodyByDeleteAccountFeatureVersion() {
    switch (featureToggle.enableDeleteAccountFeatureVersion) {
      case DeleteAccountFeatureVersion.version_1:
        return _bodyContentForDeleteAccountFlowVersion1();
      case DeleteAccountFeatureVersion.version_2:
        return _bodyContentForDeleteAccountFlowVersion2();
    }
  }

  Widget _bodyContentForDeleteAccountFlowVersion1() {
    return Column(
      children: <Widget>[
        _biometricSection(),
        const SizedBox(
          height: 8,
        ),
        InkWell(
          onTap: () {
            _resetPinHandler.requestResetPin(_resetPinCallback, phoneNumber ?? '');
          },
          child: EvoListTile(
            borderRadius: BorderRadius.circular(12),
            backgroundColor: evoColors.secondaryBackground,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            leadingWidget: imageProvider.asset(EvoImages.icResetPin, width: 48, height: 48),
            title: EvoStrings.resetPin,
          ),
        ),
      ],
    );
  }

  Widget _bodyContentForDeleteAccountFlowVersion2() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Container(
          clipBehavior: Clip.hardEdge,
          decoration: ShapeDecoration(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            color: evoColors.background,
            shadows: <BoxShadow>[
              BoxShadow(
                color: evoColors.settingsCardShadow,
                offset: const Offset(0, 15),
                blurRadius: 24,
                spreadRadius: -8,
              )
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              _biometricSection(),
              _dividerBetweenBiometricAndResetPinForDeleteAccountFlowV2(),
              _resetPinSectionForDeleteAccountFlowV2(),
            ],
          ),
        ),
        const SizedBox(height: 10),
        _deleteTextWithForDeleteAccountFlowV2(),
      ],
    );
  }

  Widget _biometricSection() {
    return BlocConsumer<ProfileSettingCubit, ProfileSettingsState>(
      listener: (BuildContext context, ProfileSettingsState state) {
        _handleStateFromListener(state);
      },
      buildWhen: (ProfileSettingsState prevState, ProfileSettingsState currState) =>
          currState is ProfileSettingsBioState,
      builder: (_, ProfileSettingsState state) {
        bool isEnableAuthenticateByBio = false;
        bool isDeviceSupportBiometrics = false;
        bool isBiometricTokenUnUsable = true;

        if (state is ProfileSettingsBioState) {
          isEnableAuthenticateByBio = state.isEnableAuthByBiometrics;
          isDeviceSupportBiometrics = state.isDeviceSupportBiometrics;
          isBiometricTokenUnUsable = state.isBiometricTokenUnUsable;
        }

        return Visibility(
            visible: isDeviceSupportBiometrics,
            child: _buildBiometricListTileByDeleteAccountVersion(
              isEnableAuthenticateByBio: isEnableAuthenticateByBio,
              isBiometricTokenUnUsable: isBiometricTokenUnUsable,
            ));
      },
    );
  }

  Widget _buildBiometricListTileByDeleteAccountVersion({
    required bool isEnableAuthenticateByBio,
    required bool isBiometricTokenUnUsable,
  }) {
    final BiometricTypeUIModel bioTypeInfo = getIt.get<AppState>().bioTypeInfo;
    final String title = '${EvoStrings.enableText} ${bioTypeInfo.biometricTypeName}';

    Color? backgroundColor;
    BorderRadius? borderRadius;
    EdgeInsets? padding;
    switch (featureToggle.enableDeleteAccountFeatureVersion) {
      case DeleteAccountFeatureVersion.version_1:
        backgroundColor = evoColors.secondaryBackground;
        borderRadius = BorderRadius.circular(12);
        padding = const EdgeInsets.symmetric(vertical: 16, horizontal: 20);
        break;
      case DeleteAccountFeatureVersion.version_2:
        padding = const EdgeInsets.symmetric(horizontal: 20, vertical: 16);
        break;
    }

    return EvoListTile(
      backgroundColor: backgroundColor,
      padding: padding,
      borderRadius: borderRadius,
      leadingWidget: imageProvider.asset(bioTypeInfo.iconSettingPath, width: 48, height: 48),
      title: title,
      trailingWidget: _buildBiometricEvoSwitch(
        isEnableAuthenticateByBio: isEnableAuthenticateByBio,
        isBiometricTokenUnUsable: isBiometricTokenUnUsable,
      ),
    );
  }

  Widget _buildBiometricEvoSwitch(
      {required bool isEnableAuthenticateByBio, required bool isBiometricTokenUnUsable}) {
    return EvoSwitch(
      value: isEnableAuthenticateByBio && !isBiometricTokenUnUsable,
      onToggle: (bool value) async {
        final bool hasEnrolledBiometrics = await _biometricsTokenModule.hasEnrolledBiometrics();
        if (hasEnrolledBiometrics) {
          /// enable using Authenticate Biometrics, it is request enter pin.
          /// if pin is verified success, it will allow to enable this feature.
          /// in case that disable feature, it doesn't request enter pin
          if (value) {
            _biometricsTokenModule.enable(callback: this);
          } else {
            await _biometricsTokenModule.disableBiometricAuthenticatorFeature();
            _cubit.reloadBiometricInfo(false);
          }
        } else {
          _showPopupAskGoToDeviceSecuritySetting();
        }
      },
    );
  }

  Widget _resetPinSectionForDeleteAccountFlowV2() {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          _resetPinHandler.requestResetPin(_resetPinCallback, phoneNumber ?? '');
        },
        child: EvoListTile(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          leadingWidget: imageProvider.asset(EvoImages.icResetPin, width: 48, height: 48),
          title: EvoStrings.resetPin,
        ),
      ),
    );
  }

  Widget _dividerBetweenBiometricAndResetPinForDeleteAccountFlowV2() {
    return BlocBuilder<ProfileSettingCubit, ProfileSettingsState>(
      buildWhen: (ProfileSettingsState prevState, ProfileSettingsState currState) =>
          currState is ProfileSettingsBioState,
      builder: (_, ProfileSettingsState state) {
        bool isDeviceSupportBiometrics = false;
        if (state is ProfileSettingsBioState) {
          isDeviceSupportBiometrics = state.isDeviceSupportBiometrics;
        }
        return Visibility(
          visible: isDeviceSupportBiometrics,
          child: EvoDividerWidget(
            height: 1,
            color: evoColors.secondaryBackground,
          ),
        );
      },
    );
  }

  Widget _deleteTextWithForDeleteAccountFlowV2() {
    return InkWell(
      onTap: () {
        _deleteAccountHandler.requestDeleteAccount(requestDeleteAccountCallback);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: RichText(
          text: TextSpan(
            style: evoTextStyles.bodySmall(color: evoColors.textPassive),
            children: <TextSpan>[
              const TextSpan(text: EvoStrings.deleteAccountPrompt),
              const TextSpan(text: ' '), //space
              TextSpan(
                  text: EvoStrings.deleteAccountActionText,
                  style: evoTextStyles.bodySmall(color: evoColors.primary)),
            ],
          ),
        ),
      ),
    );
  }

  void _handleStateFromListener(ProfileSettingsState state) {
    if (state is VerifyByUsingBioError) {
      final String msg = BioAuthError.getErrMsg(state.errCode);
      showSnackBarError(msg);
      return;
    }

    if (state is EnableAuthBioSuccess) {
      final BiometricTypeUIModel bioTypeInfo = getIt.get<AppState>().bioTypeInfo;
      final String alertMsg;
      if (state.isEnable) {
        alertMsg =
            '${EvoStrings.enableText} ${EvoStrings.authenticateText.toLowerCase()} ${bioTypeInfo.biometricTypeName} ${EvoStrings.statusSuccess.toLowerCase()}';
      } else {
        alertMsg =
            '${EvoStrings.prefixDisableBioAuthenticatorText} ${bioTypeInfo.biometricTypeName}';
      }
      showSnackBar(alertMsg);
      return;
    }
  }

  void _showPopupAskGoToDeviceSecuritySetting() {
    showPopupAskGoToDeviceSecuritySetting(
      footer: getDefaultFooterSecuritySettingPopup(),
    );
  }

  @override
  void onError(
      {required BiometricTokenModuleErrorType type,
      String? userMessage,
      ErrorUIModel? error,
      BioAuthError? bioError}) {
    switch (type) {
      case BiometricTokenModuleErrorType.biometrics:
        evoUtilFunction.handleBioError(bioError);
        break;
      case BiometricTokenModuleErrorType.apiError:
        handleEvoApiError(error);
        break;
      default:
        showSnackBarError(userMessage ?? CommonStrings.otherGenericErrorMessage);
        break;
    }
  }

  @override
  void onSuccess() {
    _cubit.reloadBiometricInfo(true);
  }

  void _createResetPinCallback() {
    _resetPinCallback.handleResetPinError = (ErrorUIModel? errorUIModel) {
      handleEvoApiError(errorUIModel);
    };
    _resetPinCallback.handleXSessionExpired = () {
      EvoAuthenticationHelper().showDialogSessionTokenExpired(
        type: VerifyOtpType.resetPin,
        onClickPositive: () {
          navigatorContext?.pop();
        },
      );
    };
    _resetPinCallback.handleVerifyOtpSuccess = (VerifyOtpState state) {
      _resetPinHandler.processResetPin(
        phoneNumber: phoneNumber ?? '',
        state: state,
      );
    };
  }

  RequestDeleteAccountCallback _createRequestDeleteCallback() {
    final RequestDeleteAccountCallback callback = RequestDeleteAccountCallback();

    callback.onRequestDeleteError = (ErrorUIModel? errorUIModel) {
      handleEvoApiError(errorUIModel);
    };
    return callback;
  }
}
