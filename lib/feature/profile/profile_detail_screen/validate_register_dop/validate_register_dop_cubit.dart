import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

part 'validate_register_dop_state.dart';

class ValidateRegisterDOPCubit extends CommonCubit<ValidateRegisterDOPState> {
  ValidateRegisterDOPCubit() : super(FinishRegisterDOP(isFinish: false));

  // TODO: when event notify for Register DOP
  Future<void> checkFinishRegisterDOP({bool isFinish = true}) async {
    emit(FinishRegisterDOP(isFinish: isFinish));
  }
}
