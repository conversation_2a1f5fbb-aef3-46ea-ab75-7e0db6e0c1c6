import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/user_repo.dart';
import '../../../../util/mock_file_name_utils/mock_user_file_name.dart';
import '../../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../../util/evo_authentication_helper.dart';

part 'deactivate_account_state.dart';

class DeactivateAccountCubit extends CommonCubit<DeactivateAccountState> {
  final UserRepo _userRepo;
  final EvoLocalStorageHelper _localStorageHelper;

  DeactivateAccountCubit(
    this._userRepo,
    this._localStorageHelper,
  ) : super(IdleState());

  void confirmDeactivate() {
    emit(ConfirmDeactivateState());
  }

  Future<void> deactivateAccount() async {
    emit(LoadingState());
    final BaseEntity deactivateStatus = await _userRepo.deactivateAccount(
      mockConfig: MockConfig(
        enable: false,
        fileName: getDeactivateAccountMockFileName(),
        statusCode: CommonHttpClient.BAD_REQUEST,
      ),
    );

    if (deactivateStatus.statusCode == CommonHttpClient.SUCCESS) {
      // delete all data on local storage and memory
      await _localStorageHelper.deleteAllSecureStorageData();
      // Clear all unread notification when user chooses to logout manually
      // Refer: https://trustingsocial1.atlassian.net/browse/EMA-1171
      await EvoAuthenticationHelper().clearAllUserData();

      emit(DeactivateSuccessState(deactivateStatus.userMessage));
    } else {
      emit(DeactivateFailState(error: ErrorUIModel.fromEntity(deactivateStatus)));
    }
  }
}
