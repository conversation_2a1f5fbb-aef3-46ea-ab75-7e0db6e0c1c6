import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

part 'show_phone_number_state.dart';

class ShowPhoneNumberCubit extends CommonCubit<ShowPhoneNumberState> {
  ShowPhoneNumberCubit() : super(ShowPhoneNumber(hasShow: false));

  Future<void> checkShowPhoneNumber({bool hasShow = false}) async {
    emit(ShowPhoneNumber(hasShow: hasShow));
  }
}
