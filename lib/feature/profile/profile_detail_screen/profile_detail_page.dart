import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/common_radio.dart';
import 'package:flutter_common_package/widget/refreshable_view.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/user_repo.dart';
import '../../../data/response/user_information_entity.dart';
import '../../../model/evo_dialog_id.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/ui_utils/evo_dialog_helper.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../../widget/evo_appbar.dart';
import '../../../widget/evo_image_provider_widget.dart';
import '../../../widget/evo_radio_widget.dart';
import '../../feature_toggle.dart';
import '../../main_screen/main_screen.dart';
import '../../main_screen/main_screen_initial_action/show_bottom_sheet_action.dart';
import '../model/gender.dart';
import 'deactivate_account/deactivate_account_cubit.dart';
import 'other_widgets/default_avatar_user_widget.dart';
import 'other_widgets/list_action_bottom_sheet.dart';
import 'profile_detail_page_cubit.dart';
import 'show_identity_card_number/show_identity_card_number_cubit.dart';
import 'show_phone_number/show_phone_number_cubit.dart';
import 'validate_register_dop/validate_register_dop_cubit.dart';
import 'widget/profile_detail_text_field.dart';

class ProfileDetailPageArg extends PageBaseArg {
  final bool isFinishRegisterDOP;

  ProfileDetailPageArg({required this.isFinishRegisterDOP});
}

class ProfileDetailPage extends PageBase {
  final bool isFinishRegisterDOP;

  const ProfileDetailPage({required this.isFinishRegisterDOP, super.key});

  static void pushNamed({required bool isFinishRegisterDOP}) {
    return navigatorContext?.pushNamed(
      Screen.profileDetailScreen.name,
      extra: ProfileDetailPageArg(isFinishRegisterDOP: isFinishRegisterDOP),
    );
  }

  @override
  State<ProfileDetailPage> createState() => ProfileDetailPageScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.profileDetailScreen.routeName);
}

@visibleForTesting
class ProfileDetailPageScreenState extends EvoPageStateBase<ProfileDetailPage> {
  static const double cornerRadius = 60;
  static const double sizeAvatar = 96;

  final AppState _appState = getIt.get<AppState>();

  final ProfileDetailPageCubit _profileDetailPageCubit =
      ProfileDetailPageCubit(getIt.get<UserRepo>());
  final ValidateRegisterDOPCubit _validateRegisterDOPCubit = ValidateRegisterDOPCubit();
  final ShowPhoneNumberCubit _showPhoneNumberCubit = ShowPhoneNumberCubit();
  final ShowIdentityCardNumberCubit _showIdentityCardNumberCubit = ShowIdentityCardNumberCubit();
  final DeactivateAccountCubit _deactivateAccountCubit = DeactivateAccountCubit(
    getIt.get<UserRepo>(),
    getIt.get<EvoLocalStorageHelper>(),
  );

  final TextEditingController _fullNameController = TextEditingController();
  final TextEditingController _phoneNumberController = TextEditingController();
  final TextEditingController _identityCardNumberController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _birthdayController = TextEditingController();
  final RefreshController _refreshController = RefreshController();

  final FeatureToggle featureToggle = getIt.get<FeatureToggle>();

  UserInformationEntity? _userInfo;

  @override
  void initState() {
    super.initState();
    _userInfo = _appState.userInfo.value;
    _validateRegisterDOPCubit.checkFinishRegisterDOP(isFinish: widget.isFinishRegisterDOP);
  }

  @override
  void dispose() {
    _profileDetailPageCubit.close();
    _validateRegisterDOPCubit.close();
    _showPhoneNumberCubit.close();
    _showIdentityCardNumberCubit.close();
    _fullNameController.dispose();
    _phoneNumberController.dispose();
    _identityCardNumberController.dispose();
    _emailController.dispose();
    _birthdayController.dispose();
    _refreshController.dispose();
    _userInfo = null;
    super.dispose();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return MultiBlocProvider(
      providers: <BlocProvider<dynamic>>[
        BlocProvider<ProfileDetailPageCubit>(create: (_) => _profileDetailPageCubit),
        BlocProvider<DeactivateAccountCubit>(create: (_) => _deactivateAccountCubit),
      ],
      child: BlocListener<DeactivateAccountCubit, DeactivateAccountState>(
        listenWhen: (DeactivateAccountState previous, DeactivateAccountState current) {
          return current is LoadingState ||
              current is ConfirmDeactivateState ||
              current is DeactivateSuccessState ||
              current is DeactivateFailState;
        },
        listener: (BuildContext context, DeactivateAccountState state) {
          handleDeactivateAccountState(state);
        },
        child: Scaffold(
          backgroundColor: evoColors.background,
          appBar: appBar(context),
          body: SafeArea(
            child: Column(
              children: <Widget>[Expanded(child: bodyContent(context)), editButtonWidget(context)],
            ),
          ),
        ),
      ),
    );
  }

  @visibleForTesting
  void onRefreshData() {
    Future.wait<void>(<Future<void>>[
      _profileDetailPageCubit.getUserInfo(),
      _validateRegisterDOPCubit.checkFinishRegisterDOP()
    ]);
  }

  @visibleForTesting
  PreferredSizeWidget appBar(BuildContext context) {
    switch (featureToggle.enableDeleteAccountFeatureVersion) {
      case DeleteAccountFeatureVersion.version_1:
        return EvoAppBar(title: EvoStrings.userInfo, actions: <Widget>[
          InkWell(onTap: onMoreMenuClick, child: evoImageProvider.asset(EvoImages.icMore))
        ]);
      case DeleteAccountFeatureVersion.version_2:
        return EvoAppBar(title: EvoStrings.userInfo);
    }
  }

  @visibleForTesting
  Widget bodyContent(BuildContext context) =>
      BlocConsumer<ProfileDetailPageCubit, ProfileDetailPageState>(
          listener: (BuildContext context, ProfileDetailPageState state) {
            onStateChanged(state);
          },
          buildWhen: (ProfileDetailPageState prevState, ProfileDetailPageState currentState) =>
              currentState is ProfileDetailPageInfoLoaded,
          builder: (BuildContext context, ProfileDetailPageState state) {
            return RefreshableView(
                onRefresh: () {
                  onRefreshData();
                },
                controller: _refreshController,
                child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child:
                        SingleChildScrollView(child: Column(children: getBodyContent(context)))));
          });

  @visibleForTesting
  void onStateChanged(ProfileDetailPageState state) {
    if (state is ProfileDetailPageLoading) {
      return;
    }

    if (_refreshController.isRefresh) {
      _refreshController.refreshCompleted(resetFooterState: true);
    }

    if (state is ProfileDetailPageFail) {
      handleEvoApiError(state.error);
      return;
    }

    if (state is ProfileDetailPageInfoLoaded) {
      _userInfo = state.user;
      updateUserInfo(state.user);
      return;
    }
  }

  @visibleForTesting
  List<Widget> getBodyContent(BuildContext context) {
    final List<Widget> content = <Widget>[];

    const Widget spacing = SizedBox(height: 16);

    final Gender? gender = Gender.formatGenderString(_userInfo?.gender);
    content.add(avatarWidget(context, gender: gender));

    if (_userInfo?.hasFullName() == true) {
      content.addAll(<Widget>[fullNameWidget(), spacing]);
    }

    content.addAll(<Widget>[phoneNumberWidget(), spacing]);

    if (gender != null) {
      content.addAll(<Widget>[genderGroup(gender), spacing]);
    }

    content.addAll(<Widget>[birthdayWidget(), spacing]);

    return content;
  }

  @visibleForTesting
  Widget avatarWidget(BuildContext context, {Gender? gender}) {
    final Widget defaultAvatar = DefaultAvatarUserWidget(gender: gender);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20),
      child: _userInfo?.avatarUrl?.isNotEmpty == true
          ? ClipOval(
              child: EvoNetworkImageProviderWidget(
                _userInfo?.avatarUrl,
                cornerRadius: cornerRadius,
                width: sizeAvatar,
                height: sizeAvatar,
                errorWidget: defaultAvatar,
              ),
            )
          : defaultAvatar,
    );
  }

  @visibleForTesting
  Widget fullNameWidget() {
    _fullNameController.text = _userInfo?.getDisplayName() ?? EvoStrings.unKnowUser;
    return textFieldWidget(EvoStrings.fullName, controller: _fullNameController);
  }

  @visibleForTesting
  Widget phoneNumberWidget() {
    return BlocProvider<ShowPhoneNumberCubit>(
        create: (_) => _showPhoneNumberCubit,
        child: BlocBuilder<ShowPhoneNumberCubit, ShowPhoneNumberState>(
            builder: (_, ShowPhoneNumberState state) {
          final bool hasShowPhoneNumber = (state as ShowPhoneNumber).hasShow;
          final String? userPhoneNumber = _userInfo?.phoneNumber;
          final String? phoneNumber = hasShowPhoneNumber
              ? userPhoneNumber
              : userPhoneNumber?.hiddenByFormat(
                  (_userInfo?.phoneNumber?.length ?? 0) - phoneNumberFormatNumOfLastShow);
          _phoneNumberController.text = phoneNumber ?? EvoStrings.notHaveInfo;
          return textFieldWidget(EvoStrings.phoneNumber,
              controller: _phoneNumberController,
              keyboardType: TextInputType.phone,
              suffixIcon: InkWell(
                  onTap: () {
                    _showPhoneNumberCubit.checkShowPhoneNumber(hasShow: !hasShowPhoneNumber);
                  },
                  child: evoImageProvider.asset(
                      hasShowPhoneNumber
                          ? EvoImages.icEyeVisibilityOn
                          : EvoImages.icEyeVisibilityOff,
                      fit: BoxFit.none)));
        }));
  }

  @visibleForTesting
  Widget birthdayWidget() {
    final String? birthday = _userInfo?.getFormattedBirthday();
    _birthdayController.text = birthday ?? EvoStrings.notHaveInfo;

    return textFieldWidget(EvoStrings.birthday,
        controller: _birthdayController,
        suffixIcon: evoImageProvider.asset(EvoImages.icCalendar, fit: BoxFit.none));
  }

  @visibleForTesting
  Widget textFieldWidget(String labelText,
      {TextEditingController? controller, TextInputType? keyboardType, Widget? suffixIcon}) {
    return ProfileDetailTextField(
        controller: controller,
        labelText: labelText,
        keyboardType: keyboardType,
        suffixIcon: suffixIcon);
  }

  @visibleForTesting
  Widget genderGroup(Gender selectedGender) {
    if (getIt.get<FeatureToggle>().enableRevampUiFeature) {
      return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Row(children: <Widget>[
            EvoRadioWidget<Gender>(
                title: EvoStrings.male,
                isSelected: selectedGender == Gender.male,
                value: Gender.male),
            const SizedBox(width: 32),
            EvoRadioWidget<Gender>(
                title: EvoStrings.female,
                isSelected: selectedGender == Gender.female,
                value: Gender.female)
          ]));
    }
    return Padding(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Row(children: <Widget>[
          CommonRadio<Gender>(
              title: EvoStrings.male,
              isSelected: selectedGender == Gender.male,
              enable: false,
              value: Gender.male),
          const SizedBox(width: 32),
          CommonRadio<Gender>(
              title: EvoStrings.female,
              isSelected: selectedGender == Gender.female,
              enable: false,
              value: Gender.female)
        ]));
  }

  @visibleForTesting
  Widget editButtonWidget(BuildContext context) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8.0),
        child: BlocProvider<ValidateRegisterDOPCubit>(
            create: (_) => _validateRegisterDOPCubit,
            child: BlocBuilder<ValidateRegisterDOPCubit, ValidateRegisterDOPState>(
                builder: (_, ValidateRegisterDOPState state) {
              final bool isFinishRegister = (state as FinishRegisterDOP).isFinish;
              return CommonButton(
                  onPressed: isFinishRegister
                      ? null
                      : () {
                          commonLog('Sửa thông tin');
                          // TODO: Navigate to DOP for editing info
                          // context.go(const CommonWebView(
                          //     title: EvoStrings.termsAndConditions, url: WebsiteUrl.termsAndConditionsUrl));
                        },
                  isWrapContent: false,
                  style: evoButtonStyles.primary(ButtonSize.xLarge),
                  child: const Text(EvoStrings.editInfo));
            })));
  }

  @visibleForTesting
  void onMoreMenuClick() {
    final List<ActionBottomSheet> actions = <ActionBottomSheet>[
      ActionBottomSheet(
          title: EvoStrings.deactivateAccount,
          icon: evoImageProvider.asset(EvoImages.icDelete),
          color: evoColors.error)
    ];
    EvoDialogHelper().showMultiActionBottomSheet(context, actions,
        onSelectedItem: (ActionBottomSheet item) {
      _deactivateAccountCubit.confirmDeactivate();
    });
  }

  @visibleForTesting
  void handleDeactivateAccountState(DeactivateAccountState state) {
    if (state is LoadingState) {
      EvoUiUtils().showHudLoading(loadingText: EvoStrings.hubLoadingText);
      return;
    }

    EvoUiUtils().hideHudLoading();

    if (state is DeactivateSuccessState) {
      handleDeactivateAccountSuccess();
      return;
    }

    if (state is ConfirmDeactivateState) {
      handleConfirmDeactivateAccount();
      return;
    }

    if (state is DeactivateFailState) {
      handleDeactivateAccountFail();
      return;
    }
  }

  @visibleForTesting
  void handleConfirmDeactivateAccount() {
    EvoDialogHelper().showDialogBottomSheet(
      dialogId: EvoDialogId.confirmDeactivateAccountBottomSheet,
      isShowButtonClose: true,
      header: evoImageProvider.asset(
        EvoImages.imgConfirmDeactivateAccount,
        width: context.screenWidth,
        fit: BoxFit.fitWidth,
      ),
      title: EvoStrings.confirmDeactivateTitle,
      content: EvoStrings.confirmDeactivateNotice,
      textPositive: EvoStrings.confirmDeactivatePositiveButton,
      textNegative: EvoStrings.confirmDeactivateNegativeButton,
      positiveButtonStyle: evoButtonStyles.primary(ButtonSize.xLarge),
      negativeButtonStyle: evoButtonStyles.tertiary(ButtonSize.xLarge, isHasShadow: false),
      titleTextStyle: evoTextStyles.h400(),
      contentTextStyle: evoTextStyles.bodyLarge(evoColors.textPassive),
      onClickPositive: () {
        /// TODO: Call API check deactivate feature status
        /// dismiss popup
        navigatorContext?.pop();

        _deactivateAccountCubit.deactivateAccount();
      },
    );
  }

  @visibleForTesting
  void handleDeactivateAccountSuccess() {
    MainScreen.removeUntilAndPushReplacementNamed(
      isLoggedIn: false,
      initialAction: ShowBottomSheetAction(
        title: EvoStrings.deactivateAccountSuccessTitle,
        content: EvoStrings.deactivateAccountSuccessNotice,
        evoDialogId: EvoDialogId.deactivateAccountBottomSheet,
      ),
    );
  }

  @visibleForTesting
  void handleDeactivateAccountFail() {
    EvoDialogHelper().showDialogBottomSheet(
      dialogId: EvoDialogId.deactivateAccountFailBottomSheet,
      title: EvoStrings.deactivateAccountFailTitle,
      content: EvoStrings.deactivateAccountFailNotice,
      textPositive: EvoStrings.close,
      positiveButtonStyle: evoButtonStyles.primary(ButtonSize.xLarge),
      titleTextStyle: evoTextStyles.h400(),
      contentTextStyle: evoTextStyles.bodyMedium(evoColors.textPassive),
      onClickPositive: () {
        navigatorContext?.pop();
      },
    );
  }
}
