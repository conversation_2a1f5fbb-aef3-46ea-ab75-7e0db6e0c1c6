import 'package:flutter/material.dart';

import '../../../../../resources/resources.dart';

typedef OnMaskedChanged = void Function(bool isMasked);

class ToggleableMaskedTextUIConfigs {
  final TextStyle? textStyle;
  final Color? iconColor;
  final double? iconPadding;

  const ToggleableMaskedTextUIConfigs({
    this.textStyle,
    this.iconColor,
    this.iconPadding,
  });
}

class ToggleableMaskedTextWidget extends StatefulWidget {
  final String? unmaskedText;
  final String? maskedText;
  final bool defaultMasked;
  final ToggleableMaskedTextUIConfigs? uiConfigs;
  final OnMaskedChanged? onMaskedChanged;

  const ToggleableMaskedTextWidget({
    super.key,
    this.unmaskedText,
    this.maskedText,
    this.uiConfigs,
    this.defaultMasked = true,
    this.onMaskedChanged,
  });

  @override
  State<ToggleableMaskedTextWidget> createState() => _ToggleableMaskedTextWidgetState();
}

class _ToggleableMaskedTextWidgetState extends State<ToggleableMaskedTextWidget> {
  late bool isMasked;

  @override
  void initState() {
    super.initState();
    isMasked = widget.defaultMasked;
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: Padding(
        padding: const EdgeInsets.all(2),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Text(
              isMasked ? (widget.maskedText ?? '') : (widget.unmaskedText ?? ''),
              style:
                  widget.uiConfigs?.textStyle ?? evoTextStyles.h200(color: evoColors.textPassive2),
            ),
            SizedBox(width: widget.uiConfigs?.iconPadding ?? 6),
            evoImageProvider.asset(
              isMasked ? EvoImages.icEyeHide : EvoImages.icEyeShow,
              color: widget.uiConfigs?.iconColor ?? evoColors.textPassive2,
            ),
          ],
        ),
      ),
      onTap: () {
        setState(() {
          isMasked = !isMasked;
          widget.onMaskedChanged?.call(isMasked);
        });
      },
    );
  }

  @override
  void didUpdateWidget(covariant ToggleableMaskedTextWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.defaultMasked != widget.defaultMasked) {
      setState(() {
        isMasked = widget.defaultMasked;
      });
    }
  }
}
