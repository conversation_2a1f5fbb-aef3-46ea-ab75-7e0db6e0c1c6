import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/refreshable_view.dart';
import 'package:flutter_common_package/widget/shimmer/shimmer_animation.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../data/repository/campaign_repo.dart';
import '../../../data/repository/user_repo.dart';
import '../../../data/response/action_entity.dart';
import '../../../data/response/campaign_entity.dart';
import '../../../data/response/campaign_list_entity.dart';
import '../../../data/response/linked_card_entity.dart';
import '../../../data/response/user_information_entity.dart';
import '../../../model/evo_action_model.dart';
import '../../../model/evo_dialog_id.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/evo_snackbar.dart';
import '../../../util/mapper.dart';
import '../../../util/ui_utils/evo_dialog_helper.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../../widget/animation/lottie_animation_widget.dart';
import '../../../widget/evo_divider_widget.dart';
import '../../../widget/evo_next_action/evo_next_action_widget.dart';
import '../../feature_toggle.dart';
import '../../linked_card_detail_screen/linked_card_detail_screen.dart';
import '../../main_screen/main_screen.dart';
import '../../main_screen/main_screen_controller.dart';
import '../../manual_link_card/manual_link_card_mixin.dart';
import '../../webview/models/evo_webview_arg.dart';
import '../profile_detail_screen/profile_detail_page.dart';
import 'card_status/card_status_widget.dart';
import 'linked_card_list/linked_card_list_widget.dart';
import 'profile_appbar_widget.dart';
import 'profile_menu_data_model.dart';
import 'profile_menu_item_widget.dart';
import 'profile_page_cubit.dart';
import 'profile_page_manual_link_card_flow.dart';
import 'referral_campaign/referral_campaign_cubit.dart';
import 'sign_out/sign_out_cubit.dart';
import 'user/user_profile_cubit.dart';
import 'widget/evo_inkwell_container_widget.dart';

class ProfileScreen extends PageBase {
  final MainScreenController? mainScreenController;

  const ProfileScreen({super.key, this.mainScreenController});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.profileScreen.routeName);
}

class _ProfileScreenState extends EvoPageStateBase<ProfileScreen>
    with AutomaticKeepAliveClientMixin, ManualLinkCardMixin, ProfilePageManualLinkCardFlow {
  final AppState _appState = getIt.get<AppState>();

  final UserProfileCubit _userCubit = UserProfileCubit(getIt.get<UserRepo>());
  final SignOutCubit _signOutCubit = SignOutCubit(getIt.get<AuthenticationRepo>());
  final ReferralCampaignCubit _referralCampaignCubit =
      ReferralCampaignCubit(campaignRepo: getIt.get<CampaignRepo>());

  final ProfilePageCardCubit _cardCubit = ProfilePageCardCubit(getIt.get<UserRepo>());

  final List<ProfileMenuDataModel> _menuDataTop = <ProfileMenuDataModel>[];
  final List<ProfileMenuDataModel> _menuDataBottom = <ProfileMenuDataModel>[];

  final RefreshController _refreshController = RefreshController();
  final CardStatusWidgetController _cardStatusWidgetController = CardStatusWidgetController();

  String? versionApp;

  final FeatureToggle _featureToggle = getIt.get<FeatureToggle>();

  static const double _heightPercentageOfReferral = 0.1;

  @override
  void initState() {
    versionApp = _appState.appVersion ?? EvoStrings.defaultVersion;

    super.initState();

    _initMenuData();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return buildVisibilityDetectorPage(context);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: evoColors.background,
      body: ShimmerAnimation(
        child: _contentWidget(),
      ),
    );
  }

  Widget _contentWidget() {
    return MultiBlocProvider(
      providers: <BlocProvider<dynamic>>[
        BlocProvider<UserProfileCubit>(
          create: (_) => _userCubit,
        ),
        BlocProvider<SignOutCubit>(
          create: (_) => _signOutCubit,
        ),
        BlocProvider<ReferralCampaignCubit>(
          create: (_) => _referralCampaignCubit,
        ),
        if (_featureToggle.enableCardStatusFeatureVersion == CardStatusFeatureVersion.version_2)
          BlocProvider<ProfilePageCardCubit>(
            create: (_) => _cardCubit,
          ),
      ],
      child: MultiBlocListener(
        listeners: <BlocListener<dynamic, dynamic>>[
          BlocListener<UserProfileCubit, UserProfileState>(
            listener: (_, UserProfileState state) {
              if (state is! UserProfileLoading) {
                if (_refreshController.isRefresh) {
                  _refreshController.refreshCompleted(resetFooterState: true);
                }
              }

              if (state is UserProfileLoadedSuccess) {
                updateUserInfo(state.user);
              }
            },
          ),
          BlocListener<SignOutCubit, SignOutState>(
            listener: (_, SignOutState state) {
              _listenSignOutUser(context, state);
            },
          ),
        ],
        child: Scaffold(
            appBar: ProfileAppbarWidget(child: _buildAppBarWidget(context)),
            body: RefreshableView(
              controller: _refreshController,
              onRefresh: _reloadData,
              child: Scrollbar(
                  radius: const Radius.circular(20),
                  child: Container(
                      color: evoColors.background,
                      child: SingleChildScrollView(
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                            _buildCardStatusWidget(),
                            ..._getProfileMenuTopWidgets(context),
                            const EvoDividerWidget(height: 8),
                            _buildReferralWidget(),
                            ..._getProfileMenuBottomWidgets(context),
                            const EvoDividerWidget(height: 8),
                            _buildLogOutWidget(context),
                            _buildAppVersionWidget(),
                            const SizedBox(height: 100)
                          ])))),
            )),
      ),
    );
  }

  void _loadData() {
    _userCubit.getUserInfo();
    _referralCampaignCubit.getReferralCampaign();
    _getCardInfo();
  }

  void _initMenuData() {
    _menuDataTop.addAll(
      <ProfileMenuDataModel>[
        ProfileMenuDataModel(
          type: ProfileMenuDataType.promotion,
          icon: EvoImages.icGift,
          title: EvoStrings.myVoucher,
        ),
        ProfileMenuDataModel(
          type: ProfileMenuDataType.transactionHistory,
          icon: EvoImages.icHistory,
          title: EvoStrings.transactionHistory,
        ),
        if (_featureToggle.enableEmiManagementFeature)
          ProfileMenuDataModel(
            type: ProfileMenuDataType.emiManagement,
            icon: EvoImages.icEmiManagement,
            title: EvoStrings.emiManagement,
          ),
        ProfileMenuDataModel(
          type: ProfileMenuDataType.setting,
          icon: EvoImages.icSetting,
          title: EvoStrings.setting,
        )
      ],
    );

    _menuDataBottom.addAll(
      <ProfileMenuDataModel>[
        ProfileMenuDataModel(
          type: ProfileMenuDataType.termAndCondition,
          title: EvoStrings.termsAndConditions,
        ),
        ProfileMenuDataModel(
          type: ProfileMenuDataType.privatePolicy,
          title: EvoStrings.privatePolicy,
        ),
        ProfileMenuDataModel(
          type: ProfileMenuDataType.faq,
          title: EvoStrings.frequentlyQuestions,
        ),
        ProfileMenuDataModel(
          type: ProfileMenuDataType.contact,
          title: EvoStrings.feedbackAndContact,
        ),
        ProfileMenuDataModel(
          type: ProfileMenuDataType.about,
          title: EvoStrings.aboutUs,
        )
      ],
    );
  }

  Widget _buildAppBarWidget(BuildContext context) {
    return BlocBuilder<UserProfileCubit, UserProfileState>(
      builder: (_, UserProfileState state) {
        return ValueListenableBuilder<UserInformationEntity?>(
          valueListenable: _appState.userInfo,
          builder: (BuildContext context, UserInformationEntity? value, Widget? child) {
            return ProfileAppbarChildWidget(
              user: value,
              onViewProfileDetailClick: () {
                ProfileDetailPage.pushNamed(isFinishRegisterDOP: true);
              },
            );
          },
        );
      },
    );
  }

  Widget _buildReferralWidget() {
    final double heightReferralImage = EvoUiUtils()
        .calculateVerticalSpace(context: context, heightPercentage: _heightPercentageOfReferral);
    const double cornerRadius = 12;

    return BlocBuilder<ReferralCampaignCubit, ReferralCampaignState>(
      buildWhen: (ReferralCampaignState prev, ReferralCampaignState current) {
        return current is ReferralCampaignLoadedSuccess;
      },
      builder: (_, ReferralCampaignState state) {
        CampaignListEntity? campaignListEntity;

        if (state is ReferralCampaignLoadedSuccess) {
          campaignListEntity = state.campaigns;
        }

        final List<CampaignEntity>? campaigns = campaignListEntity?.campaigns;
        return campaigns?.isNotEmpty == true
            ? Container(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                color: evoColors.secondaryBackground,
                child: EvoInkWellContainer(
                  height: heightReferralImage,
                  cornerRadius: cornerRadius,
                  onTap: () {
                    _gotoReferralIntroduction(
                      context: context,
                      action: campaigns?.first.action,
                    );
                  },
                  child: Column(
                    children: <Widget>[
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        decoration: BoxDecoration(
                          color: evoColors.primary,
                          borderRadius: BorderRadius.circular(cornerRadius),
                        ),
                        child: Row(
                          children: <Widget>[
                            Expanded(
                              child: Text(
                                EvoStrings.referralNewMemberProfile,
                                style: evoTextStyles.h300(color: evoColors.referralNewMemberText),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Image.asset(EvoImages.icReferralProfile, height: heightReferralImage)
                          ],
                        ),
                      ),
                      const EvoDividerWidget(height: 8),
                    ],
                  ),
                ),
              )
            : const SizedBox.shrink();
      },
    );
  }

  Widget _buildCardStatusWidget() {
    switch (_featureToggle.enableCardStatusFeatureVersion) {
      case CardStatusFeatureVersion.version_1:
        // no longer maintain
        return const SizedBox.shrink();
      case CardStatusFeatureVersion.version_2:
        return _buildCardStatusWidgetVersion2();
      case CardStatusFeatureVersion.version_3:
        return _buildCardStatusWidgetVersion3();
      case CardStatusFeatureVersion.version_4:
        return const SizedBox.shrink();
      case CardStatusFeatureVersion.version_5:
        return _buildCardStatusWidgetVersion5();
    }
  }

  List<Widget> _getProfileMenuTopWidgets(BuildContext context) {
    final List<Widget> contents = <Widget>[];

    for (int index = 0; index < _menuDataTop.length; index++) {
      final ProfileMenuDataModel item = _menuDataTop[index];
      contents.add(
        ProfileMenuItemWidget.withIcon(
          icon: item.icon,
          title: item.title,
          onPress: () {
            _onMenuItemClick(item);
          },
        ),
      );
      if (index < _menuDataTop.length - 1) {
        contents.add(
          const Padding(
            padding: EdgeInsets.only(right: EvoDimension.profilePageHorizontalMargin),
            child: Divider(height: 1, indent: 68),
          ),
        );
      }
    }

    return contents;
  }

  List<Widget> _getProfileMenuBottomWidgets(BuildContext context) {
    final List<Widget> contents = <Widget>[];

    for (int index = 0; index < _menuDataBottom.length; index++) {
      final ProfileMenuDataModel item = _menuDataBottom[index];
      contents.add(ProfileMenuItemWidget(
          title: item.title,
          onPress: () {
            _onMenuItemClick(item);
          }));
      if (index < _menuDataBottom.length - 1) {
        contents.add(const Padding(
            padding: EdgeInsets.symmetric(horizontal: EvoDimension.profilePageHorizontalMargin),
            child: Divider(height: 1)));
      }
    }

    return contents;
  }

  Widget _buildLogOutWidget(BuildContext context) {
    return ProfileMenuItemWidget(
        title: EvoStrings.signOut, showTrailingIcon: false, onPress: () => _handleLogOut());
  }

  void _listenSignOutUser(BuildContext context, SignOutState state) {
    if (state is SignOutLoading) {
      EvoUiUtils().showHudLoading();
      return;
    }

    EvoUiUtils().hideHudLoading();

    if (state is SignOutInitial) {
      return;
    }

    if (state is SignOutFail) {
      getIt.get<EvoSnackBar>().show(state.error?.userMessage ?? EvoStrings.signOutFail,
          typeSnackBar: SnackBarType.error, durationInSec: SnackBarDuration.short.value);
      return;
    }

    if (state is SignOutSuccess) {
      final String? message = state.baseEntity?.userMessage;
      final String msgSignOut =
          (message?.isNotEmpty == true) ? message! : EvoStrings.signOutSuccess;
      getIt.get<EvoSnackBar>().show(msgSignOut, durationInSec: SnackBarDuration.short.value);

      MainScreen.pushReplacementNamed(isLoggedIn: false);
      return;
    }
  }

  Widget _buildAppVersionWidget() {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: EvoDimension.profilePageHorizontalMargin),
        child: Text('${EvoStrings.version}: $versionApp',
            style: evoTextStyles.bodyLarge(evoColors.textPassive2)));
  }

  void _onMenuItemClick(ProfileMenuDataModel item) {
    item.onClick(mainScreenController: widget.mainScreenController);
  }

  void _handleLogOut() {
    _showConfirmSignOutAccount();
  }

  void _showConfirmSignOutAccount() {
    EvoDialogHelper().showDialogBottomSheet(
        dialogId: EvoDialogId.confirmLogOutBottomSheet,
        title: EvoStrings.signOut,
        content: EvoStrings.signOutConfirm,
        textPositive: EvoStrings.confirm,
        textNegative: EvoStrings.cancel,
        positiveButtonStyle: evoButtonStyles.primary(ButtonSize.xLarge),
        negativeButtonStyle: evoButtonStyles.tertiary(ButtonSize.xLarge, isHasShadow: false),
        titleTextStyle: evoTextStyles.h500(),
        contentTextStyle:
            evoTextStyles.h300(color: evoColors.textPassive).copyWith(fontWeight: FontWeight.w400),
        onClickPositive: () {
          ///dismiss popup
          navigatorContext?.pop();

          _signOutCubit.signOut();
        });
  }

  void _reloadData() {
    _userCubit.getUserInfo();
    _referralCampaignCubit.getReferralCampaign();
    _cardStatusWidgetController.onPullToRefresh?.call();
    _getCardInfo();
  }

  /// Card status version 2 : without manual link card, only show the card
  /// https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/3254943850/View+User+s+EVO+Card+Image+View+Card+Status+version+2
  Widget _buildCardStatusWidgetVersion2() {
    return BlocBuilder<ProfilePageCardCubit, ProfilePageCardState>(
        builder: (_, ProfilePageCardState state) {
      if (state is ProfilePageCardInfoLoading) {
        return _buildLinkedCardLoadingWidget();
      }

      if (state is ProfilePageCardInfoFail) {
        return const SizedBox.shrink();
      }

      if (state is ProfilePageCardInfoSuccess) {
        final List<LinkedCardEntity>? linkedCards = state.linkedCardListEntity?.linkedCards;
        if (linkedCards?.isNotEmpty == true) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const SizedBox(height: 16),
              LinkedCardListWidget(linkedCards: linkedCards, onTap: _openLinkedCardDetail),
            ],
          );
        } else {
          return const SizedBox.shrink();
        }
      }

      return const SizedBox.shrink();
    });
  }

  /// Card status version 3: with manual link card. Does not show the card, but show Credit Limit Amount, and CTA to link & activate card
  /// https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/3248422913/Card+Status+Tracking+-+General+rules+to+display+on+EVO+app
  Widget _buildCardStatusWidgetVersion3() {
    return CardStatusWidget(
      controller: _cardStatusWidgetController,
      entryPointScreen: Screen.profileScreen,
      createFaceOtpCallbackFn: createFaceOtpCallbackForProfilePageEntryPointIfNeed,
      createManualLinkCardCallbackFn: createManualLinkCardCallbackForProfilePageEntryPoint,
      createFaceAuthCallbackFn: createFaceAuthCallbackForProfilePageEntryPointIfNeed,
    );
  }

  /// version 5 : Does not show the card, but show Credit Limit Amount, and hide CTA to link & activate card
  /// Refer ticket: https://trustingsocial1.atlassian.net/browse/EMA-5401
  Widget _buildCardStatusWidgetVersion5() {
    return CardStatusWidget(
      controller: _cardStatusWidgetController,
      entryPointScreen: Screen.profileScreen,
      createFaceOtpCallbackFn: createFaceOtpCallbackForProfilePageEntryPointIfNeed,
      createFaceAuthCallbackFn: createFaceAuthCallbackForProfilePageEntryPointIfNeed,
      createManualLinkCardCallbackFn: createManualLinkCardCallbackForProfilePageEntryPoint,
      forceHideCTAs: true,
    );
  }

  Widget _buildLinkedCardLoadingWidget() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.only(top: 16),
        child: LottieAnimationWidget(EvoAnimation.animationCircleLoading, size: 40),
      ),
    );
  }

  void _openLinkedCardDetail(LinkedCardEntity? linkedCard) {
    LinkedCardDetailScreen.pushNamed(linkedCard: linkedCard);
  }

  void _getCardInfo() {
    if (_featureToggle.enableCardStatusFeatureVersion == CardStatusFeatureVersion.version_2) {
      _cardCubit.initData();
    }
  }

  void _gotoReferralIntroduction({required BuildContext context, ActionEntity? action}) {
    action?.toEvoActionModel().let((EvoActionModel it) async {
      final EvoWebViewArg arg = EvoWebViewArg(
        title: EvoStrings.referralIntroductionTitle,
        url: action.args?.link,
        nextActionWidget: EvoNextActionWidget(
          nextAction: action.args?.nextAction?.toEvoActionModel(),
        ),
      );

      CommonWebView.pushNamed(arg: arg);
    });
  }
}
