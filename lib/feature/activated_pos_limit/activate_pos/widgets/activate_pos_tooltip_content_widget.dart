import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../resources/resources.dart';
import '../../../../util/functions.dart';

class ActivatePosTooltipContentWidget extends StatelessWidget {
  final int posLimit;

  const ActivatePosTooltipContentWidget({required this.posLimit, super.key});

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double tooltipWidthPercentage =
        228 / 375; // base on design: 228 is tooltip design width, 375 is screen design width
    final double widthToolTip = screenWidth * tooltipWidthPercentage;

    return Container(
      width: widthToolTip,
      padding: const EdgeInsets.all(15),
      child: Text(
        EvoStrings.setupPosLimitTooltipContent.replaceVariableByValue(
          <String>[
            evoUtilFunction.evoFormatCurrency(
              posLimit,
              currencySymbol: vietNamCurrencySymbol,
            ),
          ],
        ),
        style: evoTextStyles.bodyMedium(evoColors.background),
      ),
    );
  }
}
