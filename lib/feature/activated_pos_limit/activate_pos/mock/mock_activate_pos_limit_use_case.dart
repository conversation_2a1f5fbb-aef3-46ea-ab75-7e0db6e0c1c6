enum MockActivatePosLimitUseCase {
  activateCardSuccess('activate_card_success.json'),
  activateCardInvalidState('activate_card_invalid_state.json'),
  activatePosSuccess('set_pos_limit_success.json'),
  activatePosVerdictRedirectToTPB('activate_card_and_set_pos_limit_verdict_redirect_to_tpb.json');

  final String value;

  const MockActivatePosLimitUseCase(this.value);
}

String getMockActivatePosLimitUseCase(MockActivatePosLimitUseCase mockCase) {
  return mockCase.value;
}
