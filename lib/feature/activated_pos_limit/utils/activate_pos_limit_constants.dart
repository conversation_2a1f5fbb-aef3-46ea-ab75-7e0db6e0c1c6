class ActivatePosLimitConstants {
  /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3787620432/ES+CO+Dive-in+Card+activation+within+payment+flow#TPB-redirects-to-the-EVO-app.
  static String threeDSecureCallbackUrl = 'evo://evo-card-activating';

  /// Defined in PRD: https://trustingsocial1.atlassian.net/wiki/spaces/EVO/pages/3773464803/Enable+POS+limit+at+the+end+of+DOP+flow#iv.-Checkpoint-%233.1%3A-60-minutes-time-limit-check-(card-not-activated)
  static final int maxSuggestionItem = 5;
  static final int suggestionSpacing = 5000000;
  static final int maxSuggestionAmount = 50000000;
  static final int minSuggestionAmount = 1000000;
  static final int minSuggestionAmountWithOutInput = 5000000;

  /// Refer ticket: https://trustingsocial1.atlassian.net/browse/EMA-5294
  static final int needToWaitAfterCardActivationInSecond = 60;
}
