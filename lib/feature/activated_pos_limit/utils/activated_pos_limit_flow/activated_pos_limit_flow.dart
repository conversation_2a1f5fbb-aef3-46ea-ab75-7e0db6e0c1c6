import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/global.dart';
import '../../models/activated_pos_limit_flow_failed_reason.dart';
import '../activated_pos_limit_flow_callback.dart';
import '../activated_pos_limit_state.dart';

part 'activated_pos_limit_flow_impl.dart';

abstract class ActivatedPOSLimitFlow {
  /// Prepare flow to activate POS limit
  /// We need to init some data before starting the flow
  void prepareActivatePOSLimitFlow({
    required Screen? screenName,
    required ActivatedPOSLimitFlowCallback callback,
    required int? orderAmount,
  });

  /// Handle success flow
  /// Return callback
  void onSuccess({
    required BuildContext context,
    ActivatedPOSLimitFlowPayload? payload,
  });

  /// Handle failed flow
  /// Return callback
  void onFailed({
    required BuildContext context,
    required ActivatedPOSLimitFlowFailedReason reason,
    String? userMessage,
  });
}
