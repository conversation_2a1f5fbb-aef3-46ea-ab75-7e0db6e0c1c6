import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../data/repository/user_repo.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../activate_card_base/activate_card_state_base.dart';
import '../activate_pos/activate_pos_limit_screen.dart';
import '../card_confirm_activation_cubit/card_confirm_activation_cubit.dart';
import '../models/activated_pos_limit_flow_failed_reason.dart';
import '../models/card_confirm_activation_succeed_payload.dart';
import '../models/platform_active_card.dart';
import '../widgets/active_pos_limit_introduction_widget.dart';

class ActivateCardIntroductionScreenArg extends PageBaseArg {
  final int creditLimit;
  final int posLimit;

  ActivateCardIntroductionScreenArg({
    required this.creditLimit,
    required this.posLimit,
  });
}

class ActivateCardIntroductionScreen extends PageBase {
  final ActivateCardIntroductionScreenArg arg;

  const ActivateCardIntroductionScreen({
    required this.arg,
    super.key,
  });

  static void pushNamed({
    required int creditLimit,
    required int posLimit,
  }) {
    return navigatorContext?.pushNamed(
      Screen.activateCardScreen.name,
      extra: ActivateCardIntroductionScreenArg(
        creditLimit: creditLimit,
        posLimit: posLimit,
      ),
    );
  }

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.activateCardScreen.routeName);

  @override
  State<ActivateCardIntroductionScreen> createState() => ActivateCardIntroductionScreenState();
}

@visibleForTesting
class ActivateCardIntroductionScreenState
    extends ActivateCardStateBase<ActivateCardIntroductionScreen> {
  @visibleForTesting
  final CardConfirmActivationCubit cubit = CardConfirmActivationCubit(
    userRepo: getIt.get<UserRepo>(),
  );

  @override
  void handleBackButton() {
    activatedPOSLimitFlow.onFailed(
      context: context,
      reason: ActivatedPOSLimitFlowFailedReason.userNotActivatedCard,
    );
  }

  @override
  Widget buildBody(BuildContext context) {
    return SafeArea(
      child: BlocProvider<CardConfirmActivationCubit>(
        create: (_) => cubit,
        child: SafeArea(
          child: BlocListener<CardConfirmActivationCubit, CardConfirmActivationState>(
            listener: (_, CardConfirmActivationState state) {
              _onStateChanged(state);
            },
            child: ActivePosLimitIntroductionWidget(
              title: EvoStrings.activatedCardIntroductionTitle,
              description: EvoStrings.activatedCardIntroductionDescription,
              ctaText: EvoStrings.activatedCardIntroductionCTA,
              imageUrl: EvoImages.imgActivatedCardGuide,
              titleAcceptedActivatedPOSLimit: EvoStrings.activatedCardAcceptedDescription,
              onAcceptedActivatedPOSLimit: () {
                cubit.confirmActivationCard(platform: PlatformActiveCard.userBypassed);
              },
              onCTAPressed: () {
                ActivatePosLimitScreen.pushNamed(
                  creditLimit: widget.arg.creditLimit,
                  posLimit: widget.arg.posLimit,
                  entryPoint: Screen.activateCardScreen,
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void _onStateChanged(CardConfirmActivationState state) {
    if (state is CardConfirmActivationLoadingState) {
      EvoUiUtils().showHudLoading();
      return;
    }

    EvoUiUtils().hideHudLoading();

    if (state is CardConfirmActivationSucceedState) {
      activatedPOSLimitFlow.onSuccess(
        context: context,
        payload: AcceptedActivationCardAndPOSLimitSucceedPayload(),
      );
      return;
    }

    if (state is CardConfirmActivationFailureState) {
      handleEvoApiError(state.errorUIModel);
      return;
    }
  }
}
