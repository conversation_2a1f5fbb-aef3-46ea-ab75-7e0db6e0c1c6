import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../../resources/resources.dart';
import '../../dop_native/util/dop_functions.dart';
import 'active_pos_limit_slider_widget.dart';
import 'activated_pos_limit_second_button_widget.dart';

class ActiveCardOrPosLimitGuideWidget extends StatelessWidget {
  final List<String> images;
  final String title;
  final String description;
  final VoidCallback onPayAgain;

  const ActiveCardOrPosLimitGuideWidget({
    required this.images,
    required this.title,
    required this.description,
    required this.onPayAgain,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        _buildTopWidget(),
        const SizedBox(height: 20),
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              children: <Widget>[
                ActivePosLimitSliderWidget(
                  images: images,
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 5),
        _buildCTAs(),
      ],
    );
  }

  Widget _buildTopWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: <Widget>[
          Text(
            title,
            style: evoTextStyles.h500(),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: evoTextStyles.bodyLarge(evoColors.textPassive),
          ),
        ],
      ),
    );
  }

  Widget _buildCTAs() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          _paymentAgainWidget(),
          const SizedBox(height: 8),
          _openTpBankApp(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _paymentAgainWidget() {
    return ActivatedPOSLimitSecondButtonWidget(
      title: EvoStrings.activePosLimitPayAgainCTA,
      onPressed: onPayAgain,
    );
  }

  Widget _openTpBankApp() {
    return CommonButton(
      onPressed: () {
        dopUtilFunction.openTPBAppLink();
      },
      isWrapContent: false,
      style: evoButtonStyles.primary(ButtonSize.large),
      child: Text(EvoStrings.activePosLimitSetupTpBankCTA),
    );
  }
}
