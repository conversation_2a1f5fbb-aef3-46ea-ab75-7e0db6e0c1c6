import 'package:flutter/material.dart';

import '../../../resources/resources.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';

class ActivePosLimitSliderWidget extends StatefulWidget {
  final List<String> images;
  final void Function(int index)? onPageChanged;

  const ActivePosLimitSliderWidget({
    required this.images,
    this.onPageChanged,
    super.key,
  });

  @override
  State<ActivePosLimitSliderWidget> createState() => _ActivePosLimitSliderWidgetState();
}

class _ActivePosLimitSliderWidgetState extends State<ActivePosLimitSliderWidget> {
  final PageController _pageController = PageController(viewportFraction: 0.82);

  int _currentIndex = 0;
  static const double _imageHeightPercentage = 384 / 812;
  static const double _imageWidthPercentage = 271 / 375;

  @override
  Widget build(BuildContext context) {
    if (widget.images.isEmpty) {
      return const SizedBox.shrink();
    }

    return _buildGuideSlider();
  }

  Widget _buildGuideSlider() {
    final List<String> images = widget.images;
    final int itemCount = images.length;
    final bool isLast = _currentIndex == itemCount - 1;

    final double imageHeight = EvoUiUtils().calculateVerticalSpace(
      context: context,
      heightPercentage: _imageHeightPercentage,
    );

    double imageWidth = EvoUiUtils().calculateHorizontalSpace(
      context: context,
      widthPercentage: _imageWidthPercentage,
    );

    /// The PageView will scale down content pageController.viewportFraction to see the next page
    /// So we need to increase the imageWidth to 100% to keep the width value in design after the scaling down.
    imageWidth += imageWidth * (1 - _pageController.viewportFraction);

    return Container(
      padding: EdgeInsets.only(left: isLast ? 0 : 10, right: isLast ? 10 : 0),
      height: imageHeight,
      child: PageView.builder(
        padEnds: false,
        controller: _pageController,
        itemBuilder: (BuildContext context, int index) {
          return evoImageProvider.asset(
            images[index],
            fit: BoxFit.fitHeight,
            width: imageWidth,
          );
        },
        itemCount: itemCount,
        onPageChanged: (int index) {
          widget.onPageChanged?.call(index);
          setState(() {
            _currentIndex = index;
          });
        },
      ),
    );
  }
}
