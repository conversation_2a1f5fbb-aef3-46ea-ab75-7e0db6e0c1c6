import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../data/response/card_activation_status_entity.dart';
import '../../../resources/global.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import 'card_activation_status_cubit.dart';

class CardActivationStatusHandleUtils {
  void handleCardStatusStateChange({
    required CardActivationStatusState state,
    required VoidCallback onCardStatusActivated,
    required void Function(ErrorUIModel error) onGetCardStatusError,
  }) {
    if (state is CardActivationStatusLoadingState) {
      EvoUiUtils().showHudLoading();
      return;
    }

    EvoUiUtils().hideHudLoading();

    if (state is GetCardActivationStatusSucceedState) {
      final String? cardStatus = state.entity.cardStatus;
      final bool? allowActivateCard = state.entity.allowActivateCard;

      if (cardStatus == CardStatus.activated.value) {
        onCardStatusActivated.call();
      } else if (allowActivateCard == true) {
        navigatorContext?.popUntilNamed(Screen.activateCardScreen.name);
      } else {
        navigatorContext?.pushNamed(Screen.activateCardGuidanceScreen.name);
      }
    }

    if (state is GetCardActivationStatusFailedState) {
      onGetCardStatusError.call(state.errorUIModel);
      return;
    }
  }
}
