import 'package:flutter_common_package/flavors/flavor_config.dart';

import '../../../flavors/flavors_type.dart';
import '../../../util/evo_flutter_wrapper.dart';

class DOPNativeVideoUrl {
  static const String proURL = 'https://dop-vnetwork-cdn.goevo.vn/documents/';
  static const String stagUATURL = 'https://dop-release-vnetwork-cdn.goevo.vn/documents/';

  // https://trustingsocial1.atlassian.net/browse/CO-10626
  static const String androidNfcIntroductionFileName = 'scan-nfc-android.mp4';
  static const String iosNfcIntroductionFileName = 'scan-nfc-ios.mp4';
}

String getDOPNativeNfcVideoIntroductionUrlByFlavor() {
  final String fileName = evoFlutterWrapper.isAndroid()
      ? DOPNativeVideoUrl.androidNfcIntroductionFileName
      : DOPNativeVideoUrl.iosNfcIntroductionFileName;
  if (FlavorConfig.instance.flavor == FlavorType.prod.name) {
    return '${DOPNativeVideoUrl.proURL}$fileName';
  }
  return '${DOPNativeVideoUrl.stagUATURL}$fileName';
}
