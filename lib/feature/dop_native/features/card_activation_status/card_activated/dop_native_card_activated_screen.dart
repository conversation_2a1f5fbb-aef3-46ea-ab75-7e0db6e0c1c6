import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../../../base/evo_page_state_base.dart';
import '../../../../../../resources/resources.dart';
import '../../../../in_app_review/in_app_review_handler_mixin.dart';
import '../../../base/cubit/dop_native_application_state.dart';
import '../../../base/dop_native_page_state_base.dart';
import '../../../dop_native_constants.dart';
import '../../../resources/dop_native_resources.dart';
import '../../../resources/dop_native_ui_strings.dart';
import '../../../util/dop_functions.dart';
import '../../../util/dop_native_navigation_utils.dart';
import '../../../util/dop_native_submit_status_polling/dop_native_submit_status_polling.dart';
import '../../../util/dop_native_submit_status_polling/dop_native_submit_status_polling_impl.dart';
import '../../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../../widgets/dop_native_card_acquisition_related_info_widget.dart';
import '../../../widgets/dop_native_status_card_icon_widget.dart';

class DOPNativeCardActivatedScreen extends PageBase {
  const DOPNativeCardActivatedScreen({super.key});

  @override
  EvoPageStateBase<DOPNativeCardActivatedScreen> createState() =>
      DOPNativeCardActivatedScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.dopNativeCardActivatedScreen.name);

  static void pushNamed() {
    return navigatorContext?.pushNamed(Screen.dopNativeCardActivatedScreen.name);
  }

  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(Screen.dopNativeCardActivatedScreen.name);
  }
}

@visibleForTesting
class DOPNativeCardActivatedScreenState extends DOPNativePageStateBase<DOPNativeCardActivatedScreen>
    with InAppReviewHandlerMixin {
  @visibleForTesting
  DOPNativeSubmitStatusPolling dopNativeSubmitStatusPolling = DOPNativeSubmitStatusPollingImpl(
    intervalDuration: const Duration(
      milliseconds: DOPNativeConstants.defaultPollingIntervalTimeInMs,
    ),
  );

  @visibleForTesting
  DOPNativeSubmitStatusPolling dopNativeDelayToCallAppState = DOPNativeSubmitStatusPollingImpl(
    intervalDuration: const Duration(
      seconds: DOPNativeConstants.delayToGetAppStateCardActivatedInSecs,
    ),
  );

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      /// Request the user: Review/rating app
      /// Refer: https://trustingsocial1.atlassian.net/browse/EMA-4432
      await showRequestRatingDialogIfNeeded(
        title: EvoStrings.cardIssuedCardActivatedRatingReviewPopupTitle,
        content: EvoStrings.cardIssuedCardActivatedRatingReviewPopupContent,
      );

      _delayToStartGetApplicationState();
    });
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      appBar: DOPNativeAppBar(
        onExitDOP: dopNativeCompleteOnboardingCubit.onExitDOPFlow,
      ),
      backgroundColor: dopNativeColors.screenBackground,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: <Widget>[
                DOPNativeStatusCardIconWidget(
                  title: DOPNativeStrings.dopNativeCardActivatedSuccessTitle,
                  ctaWidget: buildCTA(),
                  description: DOPNativeStrings.dopNativeCardActivatedSuccessDesc,
                ),
                const SizedBox(height: 20),
                const DOPNativeCardAcquisitionRelatedInfoWidget(
                  enableViewEContractCTA: false,
                ),
                SizedBox(height: dopUtilFunction.getPaddingBottom(context)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget buildCTA() {
    return CommonButton(
      onPressed: () => dopNativeCompleteOnboardingCubit.onExitDOPFlow(),
      isWrapContent: false,
      style: dopNativeButtonStyles.primary(ButtonSize.medium),
      child: const Text(DOPNativeStrings.dopNativeReceiveVoucher),
    );
  }

  @override
  void handleDOPNativeApplicationStateChanged(DOPNativeApplicationState state) {
    if (state is DOPNativeApplicationStateLoading) {
      return;
    }

    if (state is DOPNativeApplicationStateLoaded) {
      final String? currentScreen = state.entity.currentStep;

      // Continue poll application state if state still card_status.information
      if (currentScreen == DOPNativeNavigationStep.cardStatusInformation.value) {
        dopNativeSubmitStatusPolling.delayToPolling(
          onDoPolling: dopNativeApplicationStateCubit.getApplicationState,
        );
        return;
      }
    }

    dopNativeSubmitStatusPolling.cancel();
    super.handleDOPNativeApplicationStateChanged(state);
  }

  void _delayToStartGetApplicationState() => dopNativeDelayToCallAppState.delayToPolling(
      onDoPolling: dopNativeApplicationStateCubit.getApplicationState);

  @override
  void didPopNext() {
    disposeShowRatingPopup();
    super.didPopNext();
  }

  @override
  void dispose() {
    disposeShowRatingPopup();
    dopNativeSubmitStatusPolling.cancel();
    dopNativeDelayToCallAppState.cancel();
    super.dispose();
  }

  @override
  void didPop() {
    dopNativeSubmitStatusPolling.cancel();
    dopNativeDelayToCallAppState.cancel();
    super.didPop();
  }
}
