import '../../../util/card_status/cubit/dop_native_card_status_state.dart';
import '../../underwriting_sub_flow/dop_native_card_cic_blocked_screen.dart';
import '../card_activated/dop_native_card_activated_screen.dart';
import '../card_activated_pos_failed/dop_native_card_activated_pos_failed_screen.dart';
import '../card_activated_retry_pos_limit/dop_native_card_activated_retry_pos_limit_screen.dart';
import '../card_active_fail/dop_native_card_active_fail_screen.dart';
import '../card_active_retry/dop_native_card_active_retry_screen.dart';

class DOPNativeCardStatusInformationUtils {
  // Refer to: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3583082497/Underwriting+sub+flow+Card+activation
  // Updated: new status by https://trustingsocial1.atlassian.net/browse/EMA-3329
  static void handleActivationStatus(DOPNativeCardStatusState state) {
    if (state is GetCardStatusBlocked) {
      DOPNativeCardCICBlockedScreen.pushReplacementNamed();
      return;
    }

    if (state is CardStatusInformationActivated) {
      DOPNativeCardActivatedScreen.pushReplacementNamed();
      return;
    }

    if (state is CardStatusInformationActivatedRetryPosLimit) {
      DOPNativeCardActivatedRetryPosLimitScreen.pushReplacementNamed();
      return;
    }

    if (state is CardStatusInformationActivatedPosFailed) {
      DOPNativeCardActivatedPosFailedScreen.pushReplacementNamed();
      return;
    }

    if (state is CardStatusInformationRetry) {
      DOPNativeCardActiveRetryScreen.pushReplacementNamed();
      return;
    }

    if (state is CardStatusInformationFail) {
      DOPNativeCardActiveFailScreen.pushReplacementNamed();
      return;
    }
  }
}
