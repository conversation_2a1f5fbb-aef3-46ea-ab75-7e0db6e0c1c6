import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../../../base/evo_page_state_base.dart';
import '../../../../../../resources/resources.dart';
import '../../../base/dop_native_page_state_base.dart';
import '../../../resources/dop_native_resources.dart';
import '../../../resources/dop_native_ui_strings.dart';
import '../../../util/dop_functions.dart';
import '../../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../../widgets/card_activate/dop_native_card_status_widget.dart';
import '../../../widgets/dop_native_card_acquisition_related_info_widget.dart';

class DOPNativeCardActiveFailScreen extends PageBase {
  const DOPNativeCardActiveFailScreen({super.key});

  @override
  EvoPageStateBase<DOPNativeCardActiveFailScreen> createState() =>
      _DOPNativeCardActiveFailScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.dopNativeCardActiveFailScreen.name);

  static void pushNamed() {
    return navigatorContext?.pushNamed(
      Screen.dopNativeCardActiveFailScreen.name,
    );
  }

  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopNativeCardActiveFailScreen.name,
    );
  }
}

class _DOPNativeCardActiveFailScreenState
    extends DOPNativePageStateBase<DOPNativeCardActiveFailScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      appBar: const DOPNativeAppBar(),
      backgroundColor: dopNativeColors.screenBackground,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 24,
            ),
            child: Column(
              children: <Widget>[
                DOPNativeCardStatusWidget(
                  title: DOPNativeStrings.dopNativeActiveCardFailTitle,
                  description: DOPNativeStrings.dopNativeActiveCardFailDesc,
                  bannerTitle: DOPNativeStrings.dopNativeDownloadTPBankToActive,
                  actionWidget: _buildCTA(),
                ),
                const SizedBox(height: 24),
                const DOPNativeCardAcquisitionRelatedInfoWidget(),
                SizedBox(height: dopUtilFunction.getPaddingBottom(context)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCTA() {
    return CommonButton(
      onPressed: dopUtilFunction.openTPBAppLink,
      style: dopNativeButtonStyles.primary(ButtonSize.medium),
      child: Text(
        DOPNativeStrings.dopNativeDownload,
        style: dopNativeTextStyles.h200(
          color: dopNativeColors.textPassive2,
        ),
      ),
    );
  }
}
