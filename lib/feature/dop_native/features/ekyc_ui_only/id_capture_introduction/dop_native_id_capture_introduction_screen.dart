import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../../model/evo_dialog_id.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../../../resources/resources.dart';
import '../../../../../util/ui_utils/evo_dialog_helper.dart';
import '../../../../../util/ui_utils/evo_ui_utils.dart';
import '../../../../camera_permission/camera_permission_cubit.dart';
import '../../../../ekyc/model/ekyc_result_model.dart';
import '../../../base/cubit/dop_native_application_state.dart';
import '../../../base/cubit/dop_native_application_state_cubit.dart';
import '../../../base/dop_native_page_state_base.dart';
import '../../../dop_native_constants.dart';
import '../../../resources/dop_native_images.dart';
import '../../../resources/dop_native_resources.dart';
import '../../../resources/dop_native_ui_strings.dart';
import '../../../util/dop_functions.dart';
import '../../../util/dop_native_navigation_utils.dart';
import '../../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../../widgets/custom_button/camera_icon_button_widget.dart';
import '../../../widgets/dop_awareness_instruction/dop_awareness_instruction_widget.dart';
import '../../../widgets/dop_awareness_instruction/dop_awareness_item.dart';
import '../../../widgets/dop_native_form_header_widget.dart';
import '../../status_screen/dop_native_status_screen.dart';
import '../dop_native_ekyc_page_state_base.dart';
import '../ekyc_limit_exceed/dop_native_ekyc_limit_exceed_screen.dart';
import '../id_card_back_side_verification/dop_native_id_card_back_side_verification_screen.dart';
import '../id_card_front_side_verification/dop_native_id_card_front_side_verification_screen.dart';
import '../id_card_qr_code_verification/dop_native_id_card_qr_code_verification_screen.dart';
import '../id_card_success/dop_native_id_success_screen.dart';
import '../sdk_bridge/tv_ekyc/ekyc_ui_only_bridge.dart';
import '../ui_model/ekyc_error_ui_model.dart';
import '../ui_model/id_card_error/dop_native_id_verification_error_content_creator.dart';
import '../utils/dop_native_error_content_ui_model.dart';
import 'cubit/dop_native_id_capture_cubit.dart';
import 'cubit/dop_native_id_capture_state.dart';

class DOPNativeIDCaptureIntroductionScreen extends PageBase {
  final DOPNativeIDCaptureCubit? idCaptureCubit;
  final CameraPermissionCubit? cameraPermissionCubit;
  final DOPNativeApplicationStateCubit? applicationStateCubit;

  const DOPNativeIDCaptureIntroductionScreen({
    super.key,
    this.idCaptureCubit,
    this.cameraPermissionCubit,
    this.applicationStateCubit,
  });

  static void pushNamed() {
    return navigatorContext?.pushNamed(
      Screen.dopNativeIdCardCaptureIntroductionScreen.name,
    );
  }

  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopNativeIdCardCaptureIntroductionScreen.name,
    );
  }

  @override
  DOPNativePageStateBase<DOPNativeIDCaptureIntroductionScreen> createState() =>
      DOPNativeIDVerificationIntroductionScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativeIdCardCaptureIntroductionScreen.routeName);
}

@visibleForTesting
class DOPNativeIDVerificationIntroductionScreenState
    extends DOPNativeEKYCPageStateBase<DOPNativeIDCaptureIntroductionScreen> {
  @visibleForTesting
  late final CameraPermissionCubit cameraPermissionCubit =
      widget.cameraPermissionCubit ?? CameraPermissionCubit();

  @visibleForTesting
  late final DOPNativeIDCaptureCubit idCaptureCubit = widget.idCaptureCubit ??
      DOPNativeIDCaptureCubit(
        ekycUiOnlyBridge: getIt<EkycUiOnlyBridge>(),
      );

  @override
  DOPNativeApplicationStateCubit get dopNativeApplicationStateCubit =>
      widget.applicationStateCubit ?? super.dopNativeApplicationStateCubit;

  @visibleForTesting
  final int currentStep = 1;
  @visibleForTesting
  final int totalStep = 5;

  @visibleForTesting
  bool isUserCapturingIdBackSide = false;

  @visibleForTesting
  final List<DOPNativeAwarenessItem> awarenessItems = <DOPNativeAwarenessItem>[
    DOPNativeAwarenessItem(
      imgAsset: DOPNativeImages.imgIdVerificationAware1,
      descriptions: <DescriptionInlineSpan>[
        const DescriptionInlineSpan(
          '${DOPNativeStrings.dopNativeIdVerificationInstructionAware1Prefix} ',
        ),
        DescriptionInlineSpan(
          '${DOPNativeStrings.dopNativeDoNot.toLowerCase()} ',
          negative: true,
        ),
        const DescriptionInlineSpan(
          DOPNativeStrings.dopNativeIdVerificationInstructionAware1Suffix,
        )
      ],
    ),
    DOPNativeAwarenessItem(
      imgAsset: DOPNativeImages.imgIdVerificationAware2,
      descriptions: <DescriptionInlineSpan>[
        const DescriptionInlineSpan(
          '${DOPNativeStrings.dopNativeDoNot} ',
          negative: true,
        ),
        const DescriptionInlineSpan(
          DOPNativeStrings.dopNativeIdVerificationInstructionAware2Suffix,
        ),
      ],
    ),
    DOPNativeAwarenessItem(
      imgAsset: DOPNativeImages.imgIdVerificationAware3,
      descriptions: <DescriptionInlineSpan>[
        const DescriptionInlineSpan(
          '${DOPNativeStrings.dopNativeDoNot} ',
          negative: true,
        ),
        const DescriptionInlineSpan(
          DOPNativeStrings.dopNativeIdVerificationInstructionAware3Suffix,
        ),
      ],
    ),
  ];

  @override
  Widget getContentWidget(BuildContext context) {
    const SizedBox spacing12 = SizedBox(height: 12);
    const SizedBox spacing18 = SizedBox(height: 18);

    return Scaffold(
      backgroundColor: dopNativeColors.screenBackground,
      appBar: const DOPNativeAppBar(),
      body: SafeArea(
        child: MultiBlocProvider(
          providers: <BlocProvider<dynamic>>[
            BlocProvider<CameraPermissionCubit>(
              create: (_) => cameraPermissionCubit,
            ),
            BlocProvider<DOPNativeIDCaptureCubit>(
              create: (_) => idCaptureCubit,
            ),
          ],
          child: MultiBlocListener(
            listeners: <BlocListener<dynamic, dynamic>>[
              BlocListener<DOPNativeIDCaptureCubit, DOPNativeIdCaptureState>(
                listener: (BuildContext context, DOPNativeIdCaptureState state) {
                  handleIdCaptureState(state);
                },
              ),
              BlocListener<CameraPermissionCubit, CameraPermissionState>(
                listener: (BuildContext context, CameraPermissionState state) {
                  handleCameraPermission(state);
                },
              )
            ],
            child: ListView(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              children: <Widget>[
                DOPNativeFormHeaderWidget(
                  currentStep: currentStep,
                  totalStep: totalStep,
                  titleStep: DOPNativeStrings.dopNativeIdVerificationInstructionDescTitle,
                  titleStyle: dopNativeTextStyles.h500(),
                ),
                spacing12,
                Text(
                  DOPNativeStrings.dopNativeIdVerificationInstructionDesc,
                  style: dopNativeTextStyles.bodyMedium(dopNativeColors.textPassive),
                ),
                spacing12,
                spacing12,
                evoImageProvider.asset(
                  DOPNativeImages.imgIdVerification,
                  height: EvoUiUtils().calculateVerticalSpace(
                    context: context,
                    heightPercentage: DOPNativeConstants.ekycInstructionIconHeightPercentage,
                  ),
                  width: double.infinity,
                ),
                const SizedBox(height: 26),
                Align(
                  child: Text(
                    DOPNativeStrings.dopNativeIdVerificationInstructionGraphicCaption,
                    style: dopNativeTextStyles.bodyMedium(dopNativeColors.textActive),
                  ),
                ),
                spacing12,
                spacing18,
                buildStartCaptureIdCardCTA(),
                spacing18,
                DOPNativeAwarenessInstruction(items: awarenessItems),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void onPagePaused() {
    super.onPagePaused();

    /// reset cameraPermission state if app move to Background,
    /// then resuming this page, user click CTA & it will check permission again to determine the next action
    /// (Eg: user goto Settings -> disable camera permission -> back to app -> click CTA)
    cameraPermissionCubit.resetState();
  }

  @override
  void onInitEKYCServerConfigsFailed(EkycErrorUIModel error) {
    onIdCardVerificationFailed(error);
  }

  /// when user click on the CTA "Retry" on the [Failure Screen]. We will check the application state to determine the next action
  /// in case of [DOPNativeNavigationStep.ekycIdCard], we will retry the id capturing
  /// otherwise, we will navigate to the next screen based on the application state.
  @override
  void handleDOPNativeApplicationStateChanged(DOPNativeApplicationState state) {
    if (state is DOPNativeApplicationStateLoaded) {
      if (state.entity.currentStep == DOPNativeNavigationStep.ekycIdCard.value) {
        hideDOPLoading();
        onRetryIdCardVerification(isBackSideIdCard: isUserCapturingIdBackSide);
        return;
      }
    }

    super.handleDOPNativeApplicationStateChanged(state);
  }

  @visibleForTesting
  Future<void> showOpenAppSettingDialog() {
    return dopUtilFunction.showOpenAppSettingCameraDialog(
      title: DOPNativeStrings.dopNativeIdVerificationOpenSettingDialogTitle,
      content: DOPNativeStrings.dopNativeIdVerificationOpenSettingDialogContent,
      textPositive: DOPNativeStrings.dopNativeSetup,
      textNegative: DOPNativeStrings.dopNativeIgnore,
    );
  }

  @visibleForTesting
  Widget buildStartCaptureIdCardCTA() {
    return CameraIconButtonWidget(
      title: DOPNativeStrings.dopNativeStartCapture,
      onPressed: () async {
        final CameraPermissionState state = cameraPermissionCubit.state;
        if (state is CameraPermissionInitialState) {
          cameraPermissionCubit.requestPermission();
        } else {
          await handleCameraPermission(state);
        }
      },
    );
  }

  @visibleForTesting
  Future<void> handleIdCaptureState(DOPNativeIdCaptureState state) async {
    // QR code
    if (state is DOPNativeIdCaptureQRCodeBypassState) {
      verifyFrontSideIdCardWithoutUpload(cardType: state.cardType);
      return;
    }

    if (state is DOPNativeIdCaptureQRCodeTimeoutState) {
      showQRCodeTimeoutDialog(cardType: state.cardType);
      return;
    }

    if (state is DOPNativeIdCaptureQRCodeSuccessState) {
      handleQRCaptureSuccessState(state);
      return;
    }

    // front side
    if (state is DOPNativeIdCaptureFrontSideSuccessState) {
      handleIDFrontSideCaptureSuccessState(state);
      return;
    }

    if (state is DOPNativeIdCaptureFrontSideErrorState) {
      handleEkycError(failReason: state.failReason);
      return;
    }

    // back side
    if (state is DOPNativeIdCaptureBackSideSuccessState) {
      handleIDBackSideCaptureSuccessState(state);
      return;
    }

    if (state is DOPNativeIdCaptureBackSideErrorState) {
      handleEkycError(failReason: state.failReason);
      return;
    }
  }

  @visibleForTesting
  Future<void> handleCameraPermission(CameraPermissionState state) async {
    switch (state) {
      case CameraPermissionGrantedState():
        // Start capture ID card. First, capture Front side. After verify front side, then capture Back side
        startIdCardCapture(isBackSide: false);
        break;
      case CameraPermissionDeniedState():
        showOpenAppSettingDialog();
        break;
      case CameraPermissionInitialState():
      default:
        break;
    }
  }

  @visibleForTesting
  Future<void> startIdCardCapture({
    required bool isBackSide,
  }) async {
    isUserCapturingIdBackSide = isBackSide;
    idCaptureCubit.startIdCardCapture(isBackSide: isBackSide);
  }

  @visibleForTesting
  void onRetryIdCardVerification({bool isBackSideIdCard = false}) {
    /// Pop the [Failure Screen] when user retry
    navigatorContext?.pop();
    startIdCardCapture(isBackSide: isBackSideIdCard);
  }

  @visibleForTesting
  void onEkycError({
    EkycErrorUIModel error = const EkycErrorUIModel(code: EkycErrorCode.otherEkycError),
    bool isReplaceCurrentScreen = false,
    bool? needVerifyIdCardAgain,
  }) {
    final DOPNativeStatusScreenArg statusScreenArg = generateIdCardCaptureErrorScreenArg(
      error,
      needVerifyIdCardAgain: needVerifyIdCardAgain,
    );

    if (isReplaceCurrentScreen) {
      DOPNativeStatusScreen.pushReplacementNamed(arg: statusScreenArg);
      return;
    }

    DOPNativeStatusScreen.pushNamed(arg: statusScreenArg);
  }

  @visibleForTesting
  DOPNativeStatusScreenArg generateIdCardCaptureErrorScreenArg(
    EkycErrorUIModel error, {
    bool? needVerifyIdCardAgain,
  }) {
    final EKYCErrorContentUIModel uiModel = DOPNativeEKYCErrorStatusCreator.create(
      errorUIModel: error,
      onRetry: () {
        if (needVerifyIdCardAgain == true) {
          // back to DOPNativeIDCaptureIntroductionScreen
          navigatorContext?.pop();
          return;
        }

        dopNativeApplicationStateCubit.getApplicationState();
      },
    );

    return DOPNativeStatusScreenArg(
      icon: uiModel.icon,
      description: uiModel.description,
      title: uiModel.title,
      iconHeight: EvoUiUtils().calculateVerticalSpace(
        context: context,
        heightPercentage: DOPNativeConstants.statusIconHeightPercentage,
      ),
      ctaWidget: uiModel.buildCTA(),
    );
  }

  @visibleForTesting
  void onIdCardVerificationFailed(
    EkycErrorUIModel error, {
    bool isReplaceCurrentScreen = false,
    bool? needVerifyIdCardAgain,
  }) {
    switch (error.code) {
      /// With [EkycErrorCode.otherEkycError] | [EkycErrorCode.cardTypeNotSupported], we will show the error screen with CTA "Retry"
      /// If the application state changed to any state (Eg: locked,...), user retry id card steps -> the upload API will return
      /// error `409 application state is changed`
      /// So to prevent this issue & improve UI/UX, when user click on the CTA, we will check Application State to determine the next action
      /// in case of [DOPNativeNavigationStep.ekycIdCard], we will retry the id capturing
      /// otherwise, we will navigate to the next screen based on the application state

      case EkycErrorCode.cardTypeNotSupported:
      case EkycErrorCode.otherEkycError:
        onEkycError(
          error: error,
          isReplaceCurrentScreen: isReplaceCurrentScreen,
          needVerifyIdCardAgain: needVerifyIdCardAgain,
        );
        break;

      case EkycErrorCode.qrCodeRequired:
        navigatorContext?.pop();
        idCaptureCubit.startQRCodeCapture();
        break;

      case EkycErrorCode.locked:
        DOPNativeEKYCLimitExceedScreen.pushReplacementNamed();
        break;
      // common DOP Error
      case EkycErrorCode.limitExceed:
      case EkycErrorCode.network:
      case EkycErrorCode.invalidToken:
      case EkycErrorCode.commonError:
      default:
        // Replace the current screen with the error screen
        handleEvoApiError(error.toErrorUIModel(), isReplaceCurrentScreen: true);
        break;
    }
  }

  @visibleForTesting
  void handleEkycError({TVSDKFailReason? failReason}) {
    if (failReason == TVSDKFailReason.userCancelled) {
      return;
    }

    onEkycError();
  }

  @visibleForTesting
  void showQRCodeTimeoutDialog({
    required String? cardType,
  }) {
    EvoDialogHelper().showDialogConfirm(
      title: DOPNativeStrings.dopNativeQRCodeTimeoutDialogTitle,
      content: DOPNativeStrings.dopNativeQRCodeTimeoutDialogContent,

      /// The QR code information is optional, continue to verify the front ID card even if user skips QR code capturing
      textNegative: DOPNativeStrings.dopNativeIgnore,
      onClickNegative: () {
        // Close the dialog
        navigatorContext?.pop();
        verifyFrontSideIdCardWithoutUpload(cardType: cardType);
      },

      /// Retry QR code capturing
      textPositive: DOPNativeStrings.dopNativeQRCodeRetryCTA,
      onClickPositive: () {
        // Close the dialog
        navigatorContext?.pop();
        idCaptureCubit.startQRCodeCapture();
      },
      dialogId: EvoDialogId.dopNativeQRCodeTimeoutDialog,
    );
  }

  @visibleForTesting
  Future<void> handleFrontIDCardVerificationSuccess() async {
    // Front side verification is done; now start capture ID card - Back side
    startIdCardCapture(isBackSide: true);

    // Pop the [front ID card verification screen] (flutter layer) after starting capture (native layer)
    // and delay time (waiting for native capturing screen is completely opened)
    // Ref: https://trustingsocial1.atlassian.net/browse/EMA-2881
    await Future<void>.delayed(const Duration(seconds: 1));
    navigatorContext?.pop();
  }

  @visibleForTesting
  void handleIDCardVerificationFailed(EkycErrorUIModel error, {bool? needVerifyIdCardAgain}) {
    /// Replace the [front/QR/back ID card verification screen] to failure screen
    onIdCardVerificationFailed(
      error,
      isReplaceCurrentScreen: true,
      needVerifyIdCardAgain: needVerifyIdCardAgain,
    );
  }

  @visibleForTesting
  void verifyFrontSideIdCardWithoutUpload({
    required String? cardType,
  }) {
    DOPNativeIdCardFrontSideVerificationScreen.pushNamed(
      arg: DOPNativeIdCardQRCodeVerificationUploadSkippedScreenArg(
        onSuccess: () {
          handleFrontIDCardVerificationSuccess();
        },
        onFailed: (EkycErrorUIModel error, _) {
          handleIDCardVerificationFailed(error);
        },
        cardType: cardType,
      ),
    );
  }

  @visibleForTesting
  void handleQRCaptureSuccessState(DOPNativeIdCaptureQRCodeSuccessState state) {
    DOPNativeIdCardQRCodeVerificationScreen.pushNamed(
      qrCodeBase64Image: state.qrCodeBase64Image,
      cardType: state.cardType,
      onSuccess: () {
        handleFrontIDCardVerificationSuccess();
      },
      onFailed: (EkycErrorUIModel errorUIModel, _) {
        handleIDCardVerificationFailed(errorUIModel);
      },
    );
  }

  @visibleForTesting
  void handleIDFrontSideCaptureSuccessState(DOPNativeIdCaptureFrontSideSuccessState state) {
    DOPNativeIdCardFrontSideVerificationScreen.pushNamed(
      arg: DOPNativeIdCardQRCodeVerificationUploadRequiredScreenArg(
        cardFrontSideBase64Image: state.frontSideBase64Image,
        cardType: state.cardType,
        onSuccess: () {
          handleFrontIDCardVerificationSuccess();
        },
        onFailed: (EkycErrorUIModel errorUIModel, _) {
          handleIDCardVerificationFailed(errorUIModel);
        },
      ),
    );
  }

  @visibleForTesting
  void handleIDBackSideCaptureSuccessState(DOPNativeIdCaptureBackSideSuccessState state) {
    DOPNativeIdCardBackSideVerificationScreen.pushNamed(
      cardBackSideBase64Image: state.backSideBase64Image,
      onSuccess: () {
        /// The ID card verification is done, navigate to the success screen
        /// Push and replace the [Back Side Verification Screen]
        DOPNativeIdCardSuccessScreen.pushReplacementNamed();
      },
      onFailed: (EkycErrorUIModel errorUIModel, bool? needToVerifyAgain) {
        handleIDCardVerificationFailed(errorUIModel, needVerifyIdCardAgain: needToVerifyAgain);
      },
    );
  }
}
