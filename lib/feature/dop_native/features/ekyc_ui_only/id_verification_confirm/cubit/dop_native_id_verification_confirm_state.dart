part of 'dop_native_id_verification_confirm_cubit.dart';

@immutable
abstract class DOPNativeIDVerificationConfirmState implements BlocState {}

class DOPNativeIDVerificationConfirmInitial implements DOPNativeIDVerificationConfirmState {}

class DOPNativeIDVerificationConfirmLoading implements DOPNativeIDVerificationConfirmState {}

class DOPNativeIDVerificationConfirmSucceed implements DOPNativeIDVerificationConfirmState {
  final DOPNativeMetadataEntity entity;

  DOPNativeIDVerificationConfirmSucceed({required this.entity});
}

class DOPNativeIDVerificationConfirmFailed implements DOPNativeIDVerificationConfirmState {
  final ErrorUIModel error;

  DOPNativeIDVerificationConfirmFailed({required this.error});
}

class DOPNativeGetProvincesSucceed implements DOPNativeIDVerificationConfirmState {
  final List<DOPNativeMetadataItemEntity> provinces;

  DOPNativeGetProvincesSucceed({required this.provinces});
}

class DOPNativeGetProvincesFailed implements DOPNativeIDVerificationConfirmState {
  final ErrorUIModel error;

  DOPNativeGetProvincesFailed({required this.error});
}

class DOPNativeGetDistrictsSucceed implements DOPNativeIDVerificationConfirmState {
  final List<DOPNativeMetadataItemEntity> districts;

  DOPNativeGetDistrictsSucceed({required this.districts});
}

class DOPNativeGetDistrictsFailed implements DOPNativeIDVerificationConfirmState {
  final ErrorUIModel error;

  DOPNativeGetDistrictsFailed({required this.error});
}

class DOPNativeGetWardsSucceed implements DOPNativeIDVerificationConfirmState {
  final List<DOPNativeMetadataItemEntity> wards;

  DOPNativeGetWardsSucceed({required this.wards});
}

class DOPNativeGetWardsFailed implements DOPNativeIDVerificationConfirmState {
  final ErrorUIModel error;

  DOPNativeGetWardsFailed({required this.error});
}

class DOPNativeGetEmploymentStatusSucceed implements DOPNativeIDVerificationConfirmState {
  final List<DOPNativeMetadataItemEntity> employmentStatus;

  DOPNativeGetEmploymentStatusSucceed({required this.employmentStatus});
}

class DOPNativeGetEmploymentStatusFailed implements DOPNativeIDVerificationConfirmState {
  final ErrorUIModel error;

  DOPNativeGetEmploymentStatusFailed({required this.error});
}

class DOPNativeGetEmploymentsSucceed implements DOPNativeIDVerificationConfirmState {
  final List<DOPNativeMetadataItemEntity> employments;

  DOPNativeGetEmploymentsSucceed({required this.employments});
}

class DOPNativeGetEmploymentsFailed implements DOPNativeIDVerificationConfirmState {
  final ErrorUIModel error;

  DOPNativeGetEmploymentsFailed({required this.error});
}

class DOPNativeGetOCRDataSucceed implements DOPNativeIDVerificationConfirmState {
  final DOPNativeOCRDataModel? ocrData;

  DOPNativeGetOCRDataSucceed({required this.ocrData});
}

class DOPNativeGetOCRDataFailed implements DOPNativeIDVerificationConfirmState {
  final ErrorUIModel error;

  DOPNativeGetOCRDataFailed({required this.error});
}
