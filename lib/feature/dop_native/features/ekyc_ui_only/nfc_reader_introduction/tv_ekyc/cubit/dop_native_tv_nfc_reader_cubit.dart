import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:trust_vision_plugin/result/tv_nfc_info_result.dart';

import '../../../../../../../data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import '../../../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../../../data/request/dop_native/dop_native_verify_nfc_request.dart';
import '../../../../../../../data/response/dop_native/dop_native_application_form_data_entity.dart';
import '../../../../../../../data/response/dop_native/dop_native_personal_info_entity.dart';
import '../../../../../../../prepare_for_app_initiation.dart';
import '../../../../../../ekyc/model/ekyc_result_model.dart';
import '../../../../../../logging/evo_logging_event.dart';
import '../../../../../util/dop_functions.dart';
import '../../../../cif_confirm/mock_file/mock_dop_native_application_form_data_use_case.dart';
import '../../../sdk_bridge/tv_ekyc/ekyc_ui_only_bridge.dart';
import '../../constants/dop_native_nfc_entry_point.dart';
import '../../models/dop_native_nfc_shared_model.dart';
import '../mock/mock_dop_native_verify_nfc_reader_use_case.dart';

part 'dop_native_tv_nfc_reader_state.dart';

class DOPNativeTvNFCReaderCubit extends CommonCubit<DOPNativeTvNFCReaderState> {
  final DopNativeEkycUIOnlyRepo dopNativeEkycUIOnlyRepo;
  final DOPNativeRepo dopNativeRepo;
  final AppState appState;
  final EkycUiOnlyBridge ekycUiOnlyBridge;

  DOPNativeTvNFCReaderCubit({
    required this.ekycUiOnlyBridge,
    required this.dopNativeEkycUIOnlyRepo,
    required this.appState,
    required this.dopNativeRepo,
  }) : super(DOPNativeTvNFCReaderInitial());

  /// Start: Get NFC Shared Model
  String? _idCardNumber;

  String get idCardNumber => _idCardNumber ?? '';

  String? _dateOfBirth;

  String? get dateOfBirth => _dateOfBirth;

  String? _dateOfExpiry;

  String? get dateOfExpiry => _dateOfExpiry;

  String? _accessToken;

  String? get accessToken => _accessToken;

  /// End: Get NFC Shared Model

  Future<void> startNFCReader() async {
    final TVSDKResult tvSdkResult = await ekycUiOnlyBridge.readNfc(
      idCardNumber: idCardNumber,
      dateOfBirth: dateOfBirth,
      dateOfExpiry: dateOfExpiry,
    );

    if (tvSdkResult.isSuccess) {
      handleNFCReaderResult(tvSdkResult);
      return;
    }
    emit(DOPNativeTvNFCReaderError(failReason: tvSdkResult.failReason));
  }

  @visibleForTesting
  void handleNFCReaderResult(TVSDKResult tvSdkResult) {
    final TVNfcInfoResult? nfcInfoResult = ekycUiOnlyBridge.getTVNfcInfoResult();
    if (nfcInfoResult != null) {
      verifyNFC(
        sod: nfcInfoResult.sod,
        dg1: nfcInfoResult.dg1,
        dg2: nfcInfoResult.dg2,
        dg13: nfcInfoResult.dg13,
        dg14: nfcInfoResult.dg14,
        dg15: nfcInfoResult.dg15,
        com: nfcInfoResult.com,
        cccd: idCardNumber,
        cloneStatus: nfcInfoResult.verificationResult?.cloneStatus?.verdict?.name,
      );
    } else {
      emit(DOPNativeTvNFCReaderError(failReason: tvSdkResult.failReason));
      commonLog('Cannot read NFC. There is something wrong with the SDK or the bridge');
    }
  }

  void initial(NFCEntryPoint entryPoint) {
    if (entryPoint == NFCEntryPoint.dopWebView) {
      final bool hasAccessToken = receiveAccessTokenFromDOPWebView();

      if (hasAccessToken) {
        retrieveNFCSharedModel();
      }

      return;
    }

    if (entryPoint == NFCEntryPoint.dopNative) {
      getDOPNativeFormData();
      return;
    }
  }

  @visibleForTesting
  Future<void> verifyNFC({
    required String? sod,
    required String? dg1,
    required String? dg2,
    required String? dg13,
    required String? dg14,
    required String? dg15,
    required String? com,
    required String? cccd,
    required String? cloneStatus,
  }) async {
    emit(DOPNativeTvNFCReaderLoading());

    final DOPNativeVerifyNFCRequest request = DOPNativeVerifyNFCRequest(
      sod: sod,
      dg1: dg1,
      dg2: dg2,
      dg13: dg13,
      dg14: dg14,
      dg15: dg15,
      com: com,
      cccd: cccd,
      cloneStatus: cloneStatus,
    );

    final BaseEntity entity = await dopNativeEkycUIOnlyRepo.verifyNFC(
      request: request,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeVerifyNFC(MockDOPNativeVerifyNFC.verifyNFCSuccess),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS &&
        entity.verdict == BaseEntity.verdictSuccess) {
      emit(DOPNativeTvNFCReaderSuccess());
    } else {
      emit(DOPNativeTvNFCReaderFailed(error: ErrorUIModel.fromEntity(entity)));
    }
  }

  @visibleForTesting
  bool receiveAccessTokenFromDOPWebView() {
    final DOPNativeNFCSharedModel? nfcSharedModel = appState.dopNativeState.nfcSharedModel;

    _accessToken = nfcSharedModel?.accessToken;
    if (_accessToken == null || _accessToken?.isEmpty == true) {
      emit(
        DOPNativeTvNFCReaderUnableToStart(
          ErrorUIModel(verdict: 'NFCReader: accessToken from DOP Web is null or empty'),
        ),
      );
      return false;
    }

    dopUtilFunction.setDOPNativeAccessTokenHeader(_accessToken);

    return true;
  }

  @visibleForTesting
  void retrieveNFCSharedModel() {
    final DOPNativeNFCSharedModel? nfcSharedModel = appState.dopNativeState.nfcSharedModel;

    final bool isValid = checkNFCSharedModelIsValid(
      idCardNumber: nfcSharedModel?.nationalIdNumber,
      dateOfBirth: nfcSharedModel?.dateOfBirth,
      dateOfExpiry: nfcSharedModel?.dateOfExpiryIdCard,
    );

    final String? convertedDateOfBirth =
        dopUtilFunction.convertDOPToEKYCDateFormat(nfcSharedModel?.dateOfBirth);
    final String? convertedDateOfExpiry =
        dopUtilFunction.convertDOPToEKYCDateFormat(nfcSharedModel?.dateOfExpiryIdCard);

    if (!isValid || convertedDateOfBirth == null || convertedDateOfExpiry == null) {
      emit(
        DOPNativeTvNFCReaderUnableToStart(
          ErrorUIModel(
            verdict:
                'NFCReader: invalid data: nationalIdNumber: ${nfcSharedModel?.nationalIdNumber}, '
                'dateOfBirth: ${nfcSharedModel?.dateOfBirth}, '
                'dateOfExpiryIdCard: ${nfcSharedModel?.dateOfExpiryIdCard}',
          ),
        ),
      );
      return;
    }

    _idCardNumber = nfcSharedModel?.nationalIdNumber;
    _dateOfBirth = convertedDateOfBirth;
    _dateOfExpiry = convertedDateOfExpiry;

    emit(DOPNativeValidateTvNFCDataSuccess());
  }

  @visibleForTesting
  bool checkNFCSharedModelIsValid({
    String? idCardNumber,
    String? dateOfBirth,
    String? dateOfExpiry,
  }) {
    if (!checkCCCDIsValid(idCardNumber)) {
      logParamError('idCardNumber', idCardNumber);
      return false;
    }

    if (!dopUtilFunction.isCorrectDateDOPFormat(dateInputted: dateOfBirth)) {
      logParamError('dateOfBirth', dateOfBirth);
      return false;
    }

    if (!dopUtilFunction.isCorrectDateDOPFormat(dateInputted: dateOfExpiry)) {
      logParamError('dateOfExpiryIdCard', dateOfExpiry);
      return false;
    }

    return true;
  }

  @visibleForTesting
  void logParamError(String paramName, String? paramValue) {
    getIt.get<LoggingRepo>().logErrorEvent(
      errorType: EvoEventType.dopNFC.name,
      args: <String, dynamic>{
        'step': 'start',
        'error': 'invalid_params',
        'param_name': paramName,
        'param_value': paramValue ?? 'null',
      },
    );
  }

  @visibleForTesting
  bool checkCCCDIsValid(String? idCard) {
    if (idCard == null || idCard.isEmpty) {
      return false;
    }

    return dopUtilFunction.isVietnameseCitizenIdCardAfter2016(idCard);
  }

  Future<void> checkNfcSupport() async {
    emit(DOPNativeTvNFCReaderLoading());

    final bool isSupported = await ekycUiOnlyBridge.checkNfcSupport();

    if (isSupported) {
      startNFCReader();
      return;
    }

    emit(DOPNativeTvNFCUnsupported());
  }

  Future<void> getDOPNativeFormData() async {
    emit(DOPNativeTvNFCFormDataLoading());

    final DOPNativeApplicationFormDataEntity entity = await dopNativeRepo.getApplicationFormData(
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeApplicationFormData(
          MockDOPNativeApplicationFormData.getApplicationFormDataSuccess,
        ),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      final DOPNativePersonalInfoEntity? personalInfo = entity.formData?.personalInfo;

      getIt.get<AppState>().dopNativeState.nfcSharedModel = DOPNativeNFCSharedModel(
        nationalIdNumber: personalInfo?.idCard,
        dateOfBirth: personalInfo?.birthday,
        dateOfExpiryIdCard: personalInfo?.idExpiryDate,
      );

      retrieveNFCSharedModel();
    } else {
      emit(DOPNativeTvNFCFormDataFailed(error: ErrorUIModel.fromEntity(entity)));
    }
  }
}
