part of 'dop_native_tv_nfc_reader_cubit.dart';

@immutable
abstract class DOPNativeTvNFCReaderState extends BlocState {}

class DOPNativeTvNFCReaderInitial extends DOPNativeTvNFCReaderState {}

class DOPNativeTvNFCReaderLoading extends DOPNativeTvNFCReaderState {}

class DOPNativeTvNFCReaderSuccess extends DOPNativeTvNFCReaderState {}

class DOPNativeTvNFCReaderFailed extends DOPNativeTvNFCReaderState {
  final ErrorUIModel error;

  DOPNativeTvNFCReaderFailed({required this.error});
}

class DOPNativeTvNFCReaderUnableToStart extends DOPNativeTvNFCReaderState {
  final ErrorUIModel error;

  DOPNativeTvNFCReaderUnableToStart(this.error);
}

class DOPNativeTvNFCReaderError extends DOPNativeTvNFCReaderState {
  final TVSDKFailReason? failReason;

  DOPNativeTvNFCReaderError({required this.failReason});
}

class DOPNativeTvNFCUnsupported extends DOPNativeTv<PERSON><PERSON>eaderState {}

class DOPNativeTvNFCFormDataLoading extends DOPNativeTvNFCReaderState {}

class DOPNativeValidateTvNFCDataSuccess extends DOPNativeTvNFCReaderState {}

class DOPNativeTvNFCFormDataFailed extends DOPNativeTvNFCReaderState {
  final ErrorUIModel error;

  DOPNativeTvNFCFormDataFailed({required this.error});
}
