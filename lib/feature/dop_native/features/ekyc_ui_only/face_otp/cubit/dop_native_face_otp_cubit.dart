import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../../../data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import '../../../../../../data/response/dop_native/dop_native_upload_face_id_entity.dart';
import '../../../../../../data/response/dop_native/dop_native_verify_face_id_entity.dart';
import '../../../../../../prepare_for_app_initiation.dart';
import '../../../../../ekyc/model/ekyc_result_model.dart';
import '../../../../util/dop_functions.dart';
import '../../mock/mock_dop_native_face_otp.dart';
import '../../sdk_bridge/tv_ekyc/ekyc_ui_only_bridge.dart';
import '../../sdk_bridge/tv_ekyc/liveness_mode.dart';
import '../../sdk_bridge/tv_ekyc/tv_image_wrapper.dart';
import '../../ui_model/ekyc_error_ui_model.dart';

part 'dop_native_face_otp_state.dart';

class DopNativeFaceOtpCubit extends CommonCubit<DopNativeFaceOtpState> {
  final EkycUiOnlyBridge ekycUiOnlyBridge;
  final DopNativeEkycUIOnlyRepo ekycUiOnlyRepo;

  DopNativeFaceOtpCubit({
    required this.ekycUiOnlyBridge,
    required this.ekycUiOnlyRepo,
  }) : super(DopNativeFaceOtpInitial());

  @visibleForTesting
  final String labelFaceId = 'face_id';

  @visibleForTesting
  bool isInitialEKYCSDK = false;

  Future<void> initEkyc() async {
    if (isInitialEKYCSDK) {
      emit(DopNativeFaceOtpInitSDKSuccess());
      return;
    }

    emit(DopNativeFaceOtpLoading());

    /// The API GET client_settings required Auth
    /// So, We will not call the API GET client_settings to get the configuration,
    /// We will pass an empty JSON object to here
    const String emptyJsonConfiguration = '{}';
    final TVSDKResult tvSDKResult =
        await ekycUiOnlyBridge.initEkyc(jsonConfigurationByServer: emptyJsonConfiguration);

    if (!tvSDKResult.isSuccess) {
      emit(DopNativeFaceOtpInitSDKFailure(tvSDKResult: tvSDKResult));
      return;
    }

    isInitialEKYCSDK = true;
    emit(DopNativeFaceOtpInitSDKSuccess());
  }

  Future<void> startCaptureSelfie() async {
    final TVSDKResult tvSDKResult = await ekycUiOnlyBridge.startSelfieCapturing(
      livenessMode: LivenessModeWrapper.passive,
      skipConfirmScreen: true,
      onNewFrameBatchListener: (Map<String, dynamic> batch) {
        /// Do nothing
      },
    );

    if (!tvSDKResult.isSuccess) {
      emit(DopNativeFaceOtpCaptureFailure(tvSDKResult: tvSDKResult));
      return;
    }

    final List<TVImageWrapper>? selfieImages = ekycUiOnlyBridge.getSelfieImages();
    if (selfieImages == null || selfieImages.isEmpty) {
      emit(DopNativeFaceOtpCaptureFailure(tvSDKResult: tvSDKResult));
      return;
    }

    emit(DopNativeFaceOtpCaptureSuccess(selfieImage: selfieImages.first));
  }

  @visibleForTesting
  String? checkUniqueToken() {
    final AppState appState = getIt.get<AppState>();
    final String? uniqueToken = appState.dopNativeState.uniqueToken;
    if (uniqueToken == null) {
      emit(
        DopNativeFaceOtpCommonFailure(
          error: ErrorUIModel(verdict: 'FaceOTP checkUniqueToken: unique token is null'),
        ),
      );
      return null;
    }

    return uniqueToken;
  }

  Future<void> uploadFaceId({
    required String? base64Image,
  }) async {
    final String? uniqueToken = checkUniqueToken();
    if (uniqueToken == null) {
      return;
    }

    if (base64Image == null) {
      emit(
        DopNativeFaceOtpCommonFailure(
          error: ErrorUIModel(
            verdict: 'FaceOTP uploadFaceId: base64Image is null',
          ),
        ),
      );
      return;
    }

    emit(DopNativeFaceOtpLoading());

    final DOPNativeUploadFaceOtpIdEntity entity = await ekycUiOnlyRepo.uploadFaceId(
      label: labelFaceId,
      token: uniqueToken,
      file: base64Image,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeFaceOtpFileName(MockDOPNativeFaceOtpUseCase.uploadFaceOtp),
      ),
    );

    final EkycErrorUIModel? eKycErrorUIModel = getFaceOTPError(entity);
    if (eKycErrorUIModel == null) {
      emit(DopNativeFaceOtpUploadSuccess(entity: entity));
    } else {
      emit(DopNativeFaceOtpUploadFailure(error: eKycErrorUIModel));
    }
  }

  Future<void> verifyFaceId({
    required String? imageId,
    required String? signature,
  }) async {
    final String? uniqueToken = checkUniqueToken();
    if (uniqueToken == null) {
      return;
    }

    if (imageId == null || signature == null) {
      emit(
        DopNativeFaceOtpCommonFailure(
          error: ErrorUIModel(
            verdict: 'FaceOTP verifyFaceId: imageId or signature from API is null',
          ),
        ),
      );
      return;
    }

    emit(DopNativeFaceOtpLoading());

    final DOPNativeVerifyFaceOtpIdEntity entity = await ekycUiOnlyRepo.verifyFaceId(
      imageId: imageId,
      token: uniqueToken,
      signature: signature,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeFaceOtpFileName(MockDOPNativeFaceOtpUseCase.verifyFaceOtp),
      ),
    );

    final EkycErrorUIModel? eKycErrorUIModel = getFaceOTPError(entity);
    if (eKycErrorUIModel == null) {
      // Set to header of DOE
      setAuthToHeaderDOE(entity.accessToken);
      emit(DopNativeVerifyFaceOtpSuccess(entity: entity));
    } else {
      emit(DopNativeVerifyFaceOtpFailure(error: eKycErrorUIModel));
    }
  }

  @visibleForTesting
  void setAuthToHeaderDOE(String? accessToken) {
    final AppState appState = getIt.get<AppState>();
    appState.dopNativeState.dopNativeAccessToken = accessToken;
    dopUtilFunction.setDOPNativeAccessTokenHeader(accessToken);
  }

  /// get error from the selfie image response entity
  @visibleForTesting
  EkycErrorUIModel? getFaceOTPError(BaseEntity entity) {
    if (entity.verdict == BaseEntity.verdictSuccess) {
      return null;
    }

    final String? message = entity.userMessage;

    switch (entity.verdict) {
      case DOPNativeVerifyFaceOtpIdEntity.verdictFaceUnmatched:
        return EkycErrorUIModel(code: EkycErrorCode.selfieFailedMatching, message: message);
      case DOPNativeVerifyFaceOtpIdEntity.verdictLimitExceed:
        return EkycErrorUIModel(code: EkycErrorCode.limitExceed, message: message);
      case DOPNativeVerifyFaceOtpIdEntity.verdictFailure:
        if (entity.statusCode == CommonHttpClient.BAD_REQUEST) {
          return EkycErrorUIModel(code: EkycErrorCode.otherEkycError, message: message);
        }
        break;
    }

    return EkycErrorUIModel.fromHttpCode(statusCode: entity.statusCode, message: message);
  }
}
