part of 'dop_native_face_otp_cubit.dart';

@immutable
abstract class DopNativeFaceOtpState implements BlocState {}

class DopNativeFaceOtpInitial extends DopNativeFaceOtpState {}

class DopNativeFaceOtpLoading extends DopNativeFaceOtpState {}

class DopNativeFaceOtpInitSDKSuccess extends DopNativeFaceOtpState {}

class DopNativeFaceOtpInitSDKFailure extends DopNativeFaceOtpState {
  final TVSDKResult tvSDKResult;

  DopNativeFaceOtpInitSDKFailure({required this.tvSDKResult});
}

class DopNativeFaceOtpCaptureSuccess extends DopNativeFaceOtpState {
  final TVImageWrapper selfieImage;

  DopNativeFaceOtpCaptureSuccess({
    required this.selfieImage,
  });
}

class DopNativeFaceOtpCaptureFailure extends DopNativeFaceOtpState {
  final TVSDKResult tvSDKResult;

  DopNativeFaceOtpCaptureFailure({required this.tvSDKResult});
}

class DopNativeFaceOtpUploadSuccess extends DopNativeFaceOtpState {
  final DOPNativeUploadFaceOtpIdEntity entity;

  DopNativeFaceOtpUploadSuccess({required this.entity});
}

class DopNativeFaceOtpUploadFailure extends DopNativeFaceOtpState {
  final EkycErrorUIModel error;

  DopNativeFaceOtpUploadFailure({required this.error});
}

class DopNativeVerifyFaceOtpSuccess extends DopNativeFaceOtpState {
  final DOPNativeVerifyFaceOtpIdEntity entity;

  DopNativeVerifyFaceOtpSuccess({required this.entity});
}

class DopNativeVerifyFaceOtpFailure extends DopNativeFaceOtpState {
  final EkycErrorUIModel error;

  DopNativeVerifyFaceOtpFailure({required this.error});
}

class DopNativeFaceOtpCommonFailure extends DopNativeFaceOtpState {
  final ErrorUIModel error;

  DopNativeFaceOtpCommonFailure({required this.error});
}
