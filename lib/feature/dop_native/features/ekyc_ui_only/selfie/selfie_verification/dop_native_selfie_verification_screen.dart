import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../../../data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import '../../../../../../prepare_for_app_initiation.dart';
import '../../../../../../resources/global.dart';
import '../../../../../../util/ui_utils/evo_ui_utils.dart';
import '../../../../base/cubit/dop_native_application_state.dart';
import '../../../../base/dop_native_page_state_base.dart';
import '../../../../dop_native_constants.dart';
import '../../../../resources/dop_native_images.dart';
import '../../../../resources/dop_native_resources.dart';
import '../../../../resources/dop_native_ui_strings.dart';
import '../../../../util/dop_native_navigation_utils.dart';
import '../../../../util/dop_native_submit_status_polling/dop_native_submit_status_polling_impl.dart';
import '../../../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../../../widgets/dop_native_status_widget.dart';
import '../../dop_native_ekyc_config.dart';
import '../../sdk_bridge/tv_ekyc/liveness_mode.dart';
import '../../sdk_bridge/tv_ekyc/tv_image_wrapper.dart';
import '../../ui_model/ekyc_error_ui_model.dart';
import '../../utils/dop_native_ekyc_api_response_handler/dop_native_ekyc_api_response_handler.dart';
import 'cubit/dop_native_ekyc_selfie_verification_cubit.dart';

class DOPNativeSelfieVerificationScreenArg extends PageBaseArg {
  final String batchId;
  final List<TVImageWrapper>? selfieImages;
  final LivenessModeWrapper livenessMode;
  final VoidCallback onSuccess;
  final EKYCErrorCallback onFailed;

  DOPNativeSelfieVerificationScreenArg({
    required this.batchId,
    required this.selfieImages,
    required this.livenessMode,
    required this.onSuccess,
    required this.onFailed,
  });
}

class DOPNativeSelfieVerificationScreen extends PageBase {
  final DOPNativeSelfieVerificationScreenArg arg;

  const DOPNativeSelfieVerificationScreen({
    required this.arg,
    super.key,
  });

  @override
  DOPNativePageStateBase<DOPNativeSelfieVerificationScreen> createState() =>
      _DOPNativeSelfieVerificationScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativeSelfieVerificationScreen.routeName);

  /// [onSuccess]: callback when Selfie verification is successful
  /// [onFailed]: callback when Selfie verification is failed
  static void pushNamed({
    required String batchId,
    required List<TVImageWrapper>? selfieImages,
    required LivenessModeWrapper livenessMode,
    required VoidCallback onSuccess,
    required EKYCErrorCallback onFailed,
  }) {
    return navigatorContext?.pushNamed(
      Screen.dopNativeSelfieVerificationScreen.name,
      extra: DOPNativeSelfieVerificationScreenArg(
        batchId: batchId,
        selfieImages: selfieImages,
        livenessMode: livenessMode,
        onSuccess: onSuccess,
        onFailed: onFailed,
      ),
    );
  }
}

class _DOPNativeSelfieVerificationScreenState
    extends DOPNativePageStateBase<DOPNativeSelfieVerificationScreen> {
  final DopNativeEkycSelfieVerificationCubit _selfieCubit = DopNativeEkycSelfieVerificationCubit(
    dopNativeEkycUiOnlyRepo: getIt.get<DopNativeEkycUIOnlyRepo>(),
    dopNativeSubmitStatusPolling: DOPNativeSubmitStatusPollingImpl(
      intervalDuration: const Duration(
        milliseconds: DOPNativeEKYCConfig.pollingIntervalTimeInMs,
      ),
    ),
    commonApisResponseHandler: getIt.get<EkycCommonApiResponsesHandler>(),
  );

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _selfieCubit.submitGestures(
        batchId: widget.arg.batchId,
        selfieImages: widget.arg.selfieImages,
        livenessMode: widget.arg.livenessMode,
      );
    });
  }

  @override
  void dispose() {
    _selfieCubit.cancelPollingSubmitStatus();
    super.dispose();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: dopNativeColors.screenBackground,
      appBar: const DOPNativeAppBar(),
      body: SafeArea(
        child: BlocProvider<DopNativeEkycSelfieVerificationCubit>(
          create: (_) => _selfieCubit,
          child: BlocListener<DopNativeEkycSelfieVerificationCubit,
              DopNativeEkycSelfieVerificationState>(
            listener: (BuildContext context, DopNativeEkycSelfieVerificationState state) {
              _onDOPNativeSelfieStateChanged(state);
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: DOPNativeStatusWidget(
                icon: DOPNativeImages.imgSelfieVerification,
                description: DOPNativeStrings.dopNativeSelfieVerificationDescription,
                title: DOPNativeStrings.dopNativeSelfieVerificationTitle,
                iconHeight: EvoUiUtils().calculateVerticalSpace(
                    context: context,
                    heightPercentage: DOPNativeConstants.statusIconHeightPercentage),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onDOPNativeSelfieStateChanged(DopNativeEkycSelfieVerificationState state) {
    if (state is SelfieVerificationSuccess) {
      dopNativeApplicationStateCubit.getApplicationState();
      return;
    }

    if (state is SelfieVerificationFailed) {
      widget.arg.onFailed(state.error, null);
      return;
    }
  }

  @override
  void handleDOPNativeApplicationStateChanged(DOPNativeApplicationState state) {
    if (state is DOPNativeApplicationStateLoading) {
      return;
    }

    if (state is DOPNativeApplicationStateLoaded) {
      _handleApplicationStateLoaded(state);
      return;
    }

    if (state is DOPNativeApplicationStateError) {
      _handleApplicationStateError(state);
      return;
    }

    widget.arg.onFailed(const EkycErrorUIModel(code: EkycErrorCode.commonError), null);
  }

  /// when application state is [DOPNativeNavigationStep.locked] or [DOPNativeNavigationStep.failure]
  /// we need to show the corresponding error screen. Otherwise, we can evaluate the success verification.
  void _handleApplicationStateLoaded(DOPNativeApplicationStateLoaded state) {
    final String? currentStep = state.entity.currentStep;

    if (currentStep == DOPNativeNavigationStep.locked.value) {
      widget.arg.onFailed(const EkycErrorUIModel(code: EkycErrorCode.locked), null);
      return;
    }

    if (currentStep == DOPNativeNavigationStep.failure.value) {
      widget.arg.onFailed(const EkycErrorUIModel(code: EkycErrorCode.selfieFailedMatching), null);
      return;
    }

    widget.arg.onSuccess();
  }

  void _handleApplicationStateError(DOPNativeApplicationStateError state) {
    /// map the error code to
    final EkycErrorUIModel errorUIModel = EkycErrorUIModel.fromHttpCode(
        statusCode: state.error.statusCode, message: state.error.userMessage);

    widget.arg.onFailed(errorUIModel, null);
  }
}
