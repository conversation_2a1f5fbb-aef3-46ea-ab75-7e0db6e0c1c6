import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';

import '../../../../data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../base/dop_native_page_state_base.dart';
import 'cubit/dop_native_ekyc_common_cubit.dart';
import 'sdk_bridge/tv_ekyc/ekyc_ui_only_bridge.dart';
import 'ui_model/ekyc_error_ui_model.dart';

abstract class DOPNativeEKYCPageStateBase<T extends PageBase> extends DOPNativePageStateBase<T> {
  @visibleForTesting
  @protected
  final DopNativeEkycCommonCubit dopNativeEkycCubit = DopNativeEkycCommonCubit(
    ekycUiOnlyRepo: getIt.get<DopNativeEkycUIOnlyRepo>(),
    ekycUiOnlyBridge: getIt.get<EkycUiOnlyBridge>(),
  );

  @override
  void initState() {
    super.initState();

    /// Call APIs after the widget is rendered
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await initData();
    });
  }

  @visibleForTesting
  @protected
  Future<void> initData() async {
    await getServerConfigsAndInitSDKIfNeeded();
  }

  @override
  Widget buildVisibilityDetectorPage(BuildContext context) {
    return BlocProvider<DopNativeEkycCommonCubit>(
      create: (_) => dopNativeEkycCubit,
      child: BlocListener<DopNativeEkycCommonCubit, DopNativeEkycCommonState>(
        listener: (BuildContext context, DopNativeEkycCommonState state) {
          handleEkycCommonState(state);
        },
        child: super.buildVisibilityDetectorPage(context),
      ),
    );
  }

  /// Check if the eKYC SDK is initialized, if not, get server configs to init the SDK
  @protected
  @visibleForTesting
  Future<void> getServerConfigsAndInitSDKIfNeeded() async {
    if (dopNativeEkycCubit.isSDKInitialized()) {
      /// if the SDK is already initialized, and the callback is not null
      /// call the callback
      onGetServerConfigsAndInitSucceed();
      return;
    }

    // Get server configs and init eKYC SDK
    await dopNativeEkycCubit.getServerConfigsAndInitSDK();
  }

  @visibleForTesting
  Future<void> handleEkycCommonState(DopNativeEkycCommonState state) async {
    if (state is DopNativeEkycCommonLoading) {
      showDOPLoading();
      return;
    }

    hideDOPLoading();

    if (state is DopNativeEkycGetServerConfigsAndInitSucceed) {
      onGetServerConfigsAndInitSucceed();
      return;
    }

    if (state is DopNativeEkycGetServerConfigsFailed) {
      onInitEKYCServerConfigsFailed(state.error);
      return;
    }
  }

  void onInitEKYCServerConfigsFailed(EkycErrorUIModel error);

  void onGetServerConfigsAndInitSucceed() {}
}
