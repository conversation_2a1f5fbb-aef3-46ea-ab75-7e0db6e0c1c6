import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';

import '../../../../../data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import '../../../../../data/response/dop_native/dop_native_client_settings_entity.dart';
import '../../../../../data/response/dop_native/dop_native_log_credential_entity.dart';
import '../mock/mock_dop_native_ekyc_use_case.dart';
import '../sdk_bridge/tv_ekyc/ekyc_ui_only_bridge.dart';
import '../ui_model/ekyc_error_ui_model.dart';

part 'dop_native_ekyc_common_cubit_state.dart';

class DopNativeEkycCommonCubit extends CommonCubit<DopNativeEkycCommonState> {
  final DopNativeEkycUIOnlyRepo ekycUiOnlyRepo;
  final EkycUiOnlyBridge ekycUiOnlyBridge;

  DopNativeEkycCommonCubit({
    required this.ekycUiOnlyRepo,
    required this.ekycUiOnlyBridge,
  }) : super(DopNativeEkycCommonInitial());

  bool isSDKInitialized() => ekycUiOnlyBridge.isInitialized();

  Future<void> getServerConfigsAndInitSDK() async {
    emit(DopNativeEkycCommonLoading());

    DOPNativeClientSettingsEntity? entity;
    final List<dynamic> results = await Future.wait<void>(<Future<dynamic>>[
      getClientSettings(),
      logEventCredential(),
    ]);

    /// Do not need to listen to the return result from [logEventCredential]
    /// Because it does not block the eKYC flow.
    for (final dynamic result in results) {
      if (result is DOPNativeClientSettingsEntity) {
        entity = result;
      }
    }

    if (entity?.statusCode == CommonHttpClient.SUCCESS) {
      final Map<String, dynamic>? settings = entity?.settings;
      if (settings == null) {
        emit(
          DopNativeEkycGetServerConfigsFailed(
            error: const EkycErrorUIModel(
              code: EkycErrorCode.commonError,
            ),
          ),
        );
        return;
      }

      await ekycUiOnlyBridge.initEkyc(jsonConfigurationByServer: json.encode(settings));
      emit(DopNativeEkycGetServerConfigsAndInitSucceed());
      return;
    } else {
      emit(
        DopNativeEkycGetServerConfigsFailed(
          error: EkycErrorUIModel.fromHttpCode(
            statusCode: entity?.statusCode,
            message: entity?.message,
          ),
        ),
      );
    }
  }

  @visibleForTesting
  Future<DOPNativeClientSettingsEntity> getClientSettings() async {
    return await ekycUiOnlyRepo.getClientSettings(
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeEkycFileNameByCase(
          MockTestDOPNativeEkycUseCase.getClientSettings,
        ),
      ),
    );
  }

  @visibleForTesting
  Future<DOPNativeLogCredentialEntity> logEventCredential() async {
    return await ekycUiOnlyRepo.logEventCredential(
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeEkycFileNameByCase(
          MockTestDOPNativeEkycUseCase.logEventCredentialSuccess,
        ),
      ),
    );
  }
}
