import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/feature/webview/common_webview_controller.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../../resources/resources.dart';
import '../../../../webview/models/evo_webview_arg.dart';
import '../../../base/dop_native_page_state_base.dart';
import '../../../resources/dop_native_images.dart';
import '../../../resources/dop_native_resources.dart';
import '../../../widgets/appbar/dop_native_appbar_widget.dart';
import 'cubit/dop_native_sub_introduction_cubit.dart';
import 'widgets/dop_native_sub_introduction_footer.dart';

class DOPNativeSubIntroductionScreenArg extends PageBaseArg {
  final String? url;
  final bool showFooter;

  DOPNativeSubIntroductionScreenArg({
    this.url,
    this.showFooter = true,
  });
}

class DOPNativeSubIntroductionScreen extends PageBase {
  static void pushNamed({String? url, bool showFooter = true}) {
    return navigatorContext?.pushNamed(
      Screen.dopNativeSubIntroductionScreen.name,
      extra: DOPNativeSubIntroductionScreenArg(
        url: url,
        showFooter: showFooter,
      ),
    );
  }

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativeSubIntroductionScreen.routeName);

  final String? url;
  final bool showFooter;

  const DOPNativeSubIntroductionScreen({
    this.url,
    this.showFooter = true,
    super.key,
  });

  @override
  DOPNativePageStateBase<DOPNativeSubIntroductionScreen> createState() =>
      _DOPNativeSubIntroductionState();
}

class _DOPNativeSubIntroductionState
    extends DOPNativePageStateBase<DOPNativeSubIntroductionScreen> {
  final DOPNativeSubIntroductionCubit _dOPNativeSubIntroductionCubit =
      DOPNativeSubIntroductionCubit();
  final CommonWebViewController _webViewController = CommonWebViewController();

  @override
  bool get enableUserBack => true;

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<DOPNativeSubIntroductionCubit>(
      create: (_) => _dOPNativeSubIntroductionCubit,
      child: Scaffold(
        backgroundColor: dopNativeColors.background,
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              _buildWebView(url: widget.url),
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWebView({String? url}) {
    return Expanded(
      child: CommonWebView(
        arg: EvoWebViewArg(
          url: url,
          title: '',
          appBar: _buildLeading(),
          controller: _webViewController,
          onHandlePopScope: () async {
            _handleUserBack(_webViewController);
          },
        ),
      ),
    );
  }

  Widget _buildFooter() {
    if (!widget.showFooter) {
      return const SizedBox();
    }

    return const DOPNativeSubIntroductionFooter();
  }

  PreferredSizeWidget _buildLeading() {
    return DOPNativeAppBar(
      title: '',
      leading: InkWell(
        onTap: () {
          navigatorContext?.maybePop();
        },
        child: Padding(
          padding: const EdgeInsets.only(top: 20, right: 14, left: 14, bottom: 14),
          child: evoImageProvider.asset(
            DOPNativeImages.icClose,
            width: 25,
            height: 25,
          ),
        ),
      ),
      enableLeading: true,
    );
  }

  Future<void> _handleUserBack(CommonWebViewController? controller) async {
    final bool? canGoBack = await controller?.canGoBack?.call();
    if (canGoBack == true) {
      controller?.goBack?.call();
      return;
    } else {
      navigatorContext?.maybePop();
    }
  }
}
