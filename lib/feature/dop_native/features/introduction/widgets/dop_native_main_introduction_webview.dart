import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import '../../../resources/dop_native_website_url.dart';

class DOPNativeCampaignOnMainIntroductionWebView extends StatefulWidget {
  final void Function(String url) onClickUrl;
  final void Function(String message) onError;
  final VoidCallback onLoaded;

  const DOPNativeCampaignOnMainIntroductionWebView({
    required this.onClickUrl,
    required this.onError,
    required this.onLoaded,
    super.key,
  });

  @override
  State<DOPNativeCampaignOnMainIntroductionWebView> createState() =>
      _DOPNativeCampaignOnMainIntroductionWebViewState();
}

class _DOPNativeCampaignOnMainIntroductionWebViewState
    extends State<DOPNativeCampaignOnMainIntroductionWebView> with AutomaticKeepAliveClientMixin {
  @visibleForTesting
  bool isLoading = false;

  static const String urlListVoucher = DOPNativeWebsiteUrl.listingVoucher;

  final InAppWebViewSettings settings = InAppWebViewSettings(
      useShouldOverrideUrlLoading: true,
      mediaPlaybackRequiresUserGesture: false,
      transparentBackground: true,
      preferredContentMode: UserPreferredContentMode.MOBILE,
      allowsInlineMediaPlayback: true);

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return _contentWebView();
  }

  Widget _contentWebView() {
    return Container(
      color: Colors.white,
      /// Because [urlListVoucher] only have 3 items
      /// So, we can hard code the height of WebView
      /// Refer: https://www.figma.com/design/bFht7WW9fsBDkPexbH5QSf/EVO-App-Hand-Off?node-id=37015-34028&t=l2iFMhHiwwIp2r6W-4
      /// 1.672 = height / width = 627 / 375
      height: context.screenWidth * 1.672,
      child: InAppWebView(
        initialSettings: settings,
        initialUrlRequest: URLRequest(url: WebUri(urlListVoucher)),
        onWebViewCreated: (InAppWebViewController controller) async {
          commonLog('onWebViewCreated');
          isLoading = true;
        },
        onLoadStop: (InAppWebViewController controller, Uri? url) async {
          isLoading = false;
        },
        shouldOverrideUrlLoading:
            (InAppWebViewController controller, NavigationAction navigationAction) async {
          final URLRequest urlRequest = navigationAction.request;
          final Uri? uri = urlRequest.url;
          commonLog('#shouldOverrideUrlLoading url=$uri');
          commonLog('#shouldOverrideUrlLoading navigationAction=${navigationAction.toString()}');

          // prevent the WebView from navigating to the url
          if (uri.toString().removeWWWPattern() == urlListVoucher.removeWWWPattern()) {
            return NavigationActionPolicy.ALLOW;
          }

          widget.onClickUrl.call(uri.toString());
          return NavigationActionPolicy.CANCEL;
        },
        onReceivedServerTrustAuthRequest:
            (InAppWebViewController controller, URLAuthenticationChallenge challenge) async {
          //  only allow showing trusted url
          return ServerTrustAuthResponse(action: ServerTrustAuthResponseAction.PROCEED);
        },
        onReceivedError: (InAppWebViewController controller, WebResourceRequest request,
            WebResourceError error) {
          isLoading = false;
          //only show the error if it's the request for main frame
          if(request.isForMainFrame ?? false) {
            widget.onError.call(error.description);
          }
        },
      ),
    );
  }
}
