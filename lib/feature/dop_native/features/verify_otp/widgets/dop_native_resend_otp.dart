import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../resources/dop_native_resources.dart';
import '../../../resources/dop_native_ui_strings.dart';
import '../../../widgets/dop_native_gradient_text_widget.dart';

class DOPNativeResendOTP extends StatelessWidget {
  final VoidCallback? onResend;
  final int? otpExpiredRemainSeconds;

  const DOPNativeResendOTP({
    super.key,
    this.onResend,
    this.otpExpiredRemainSeconds,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Text(
          DOPNativeStrings.otpDidNotReceive,
          style: dopNativeTextStyles.bodyMedium(dopNativeColors.textActive),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            _buildResendText(),
            const SizedBox(width: 4),
            _buildRemainingTime(otpExpiredRemainSeconds),
          ],
        ),
      ],
    );
  }

  Widget _buildResendText() {
    return InkWell(
        onTap: onResend,
        child: DOPNativeGradientText(
            text: DOPNativeStrings.otpResend,
            textStyle: dopNativeTextStyles.bodyMedium(
              dopNativeColors.textGradientStart,
            )));
  }

  Widget _buildRemainingTime(int? remainSeconds) {
    if (remainSeconds == null || remainSeconds == 0) {
      return const SizedBox();
    }

    final Duration duration = Duration(seconds: remainSeconds);

    final String timerText = '(${duration.remainder()})';

    return Text(
      timerText,
      style: dopNativeTextStyles.bodyMedium(dopNativeColors.otpCountdown),
    );
  }
}
