part of 'dop_native_verify_otp_cubit.dart';

@immutable
abstract class DOPNativeVerifyOtpState implements BlocState {}

class VerifyOtpInitial extends DOPNativeVerifyOtpState {}

class ScreenLoading extends DOPNativeVerifyOtpState {}

class VerifyOtpCompleted extends DOPNativeVerifyOtpState {
  final DOPVerifyOTPEntity entity;

  VerifyOtpCompleted(this.entity);
}

class RequestOTPLoaded extends DOPNativeVerifyOtpState {
  final DOPNativeRequestOTPEntity requestOTPEntity;

  RequestOTPLoaded(this.requestOTPEntity);
}

/// Start: Error states
/// Used for the request otp error cases of the screen
class RequestOtpError extends DOPNativeVerifyOtpState {
  final ErrorUIModel error;

  RequestOtpError(this.error);
}

/// Used for the error cases of OTP verifying
class VerifyOtpError extends DOPNativeVerifyOtpState {
  final ErrorUIModel error;

  VerifyOtpError(this.error);
}

class VerifyOtpIncorrect extends DOPNativeVerifyOtpState {
  final ErrorUIModel error;

  VerifyOtpIncorrect(this.error);
}

/// End: Error states
