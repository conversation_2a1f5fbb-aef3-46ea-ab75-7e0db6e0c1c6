import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../data/response/dop_native/dop_native_request_otp_entity.dart';
import '../../../../../data/response/dop_native/dop_verify_otp_entity.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../util/dop_functions.dart';
import '../dop_native_verify_otp_screen.dart';
import '../mock/mock_dop_native_verify_otp_case.dart';

part 'dop_native_verify_otp_state.dart';

class DOPNativeVerifyOtpCubit extends CommonCubit<DOPNativeVerifyOtpState> {
  final DOPNativeRepo dopNativeRepo;
  final AppState appState;

  DOPNativeVerifyOtpCubit({
    required this.dopNativeRepo,
    required this.appState,
  }) : super(VerifyOtpInitial());

  void requestOTPIfNeeded({
    DOPNativeVerifyOtpType? verifyOtpType,
    int? validSeconds,
    int? retries,
  }) {
    if (verifyOtpType == DOPNativeVerifyOtpType.eSign && validSeconds != null && retries != null) {
      handleESignOTP(validSeconds, retries);
    } else {
      requestOTP();
    }
  }

  @visibleForTesting
  void handleESignOTP(int? validSeconds, int? retries) {
    final DOPNativeRequestOTPEntity entity = DOPNativeRequestOTPEntity(
      validSeconds: validSeconds,
      retries: retries,
    );
    emit(RequestOTPLoaded(entity));
  }

  Future<void> requestOTP() async {
    emit(ScreenLoading());
    final String? uniqueToken = appState.dopNativeState.uniqueToken;

    // Clear the accessToken in header to avoid sending the previous token
    dopUtilFunction.clearDOPNativeAccessTokenHeader();

    final DOPNativeRequestOTPEntity dopAuthenticateEntity = await dopNativeRepo.requestOTP(
      token: uniqueToken,
      mockConfig: const MockConfig(
        enable: false,
        fileName: 'dop_native_request_otp_success.json',
      ),
    );

    if (dopAuthenticateEntity.statusCode == CommonHttpClient.SUCCESS) {
      emit(RequestOTPLoaded(dopAuthenticateEntity));
      return;
    }

    emit(RequestOtpError(ErrorUIModel.fromEntity(dopAuthenticateEntity)));
  }

  Future<void> verifyOTP(String otp) async {
    emit(ScreenLoading());
    final String? uniqueToken = appState.dopNativeState.uniqueToken;

    final DOPVerifyOTPEntity entity = await dopNativeRepo.verifyOTP(
      token: uniqueToken,
      otp: otp,
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeVerifyOTPFileNameByCase(MockDOPNativeVerifyOTPCase.success),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      _handleVerifyOTPSuccess(entity);
      return;
    }

    emit(VerifyOtpError(ErrorUIModel.fromEntity(entity)));
  }

  Future<void> _handleVerifyOTPSuccess(DOPVerifyOTPEntity entity) async {
    if (entity.verdict == DOPVerifyOTPEntity.verdictIncorrectOTP) {
      emit(VerifyOtpIncorrect(ErrorUIModel.fromEntity(entity)));
      return;
    }

    final String? accessToken = entity.accessToken;
    appState.dopNativeState.dopNativeAccessToken = accessToken;
    dopUtilFunction.setDOPNativeAccessTokenHeader(accessToken);
    emit(VerifyOtpCompleted(entity));
  }
}
