import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

class DOPNativeEContractDownloadState implements BlocState {}

class DOPNativeEContractDownloadInitial extends DOPNativeEContractDownloadState {}

class DOPNativeEContractLoading extends DOPNativeEContractDownloadState {}

class DOPNativeEContractLoaded extends DOPNativeEContractDownloadState {
  final String? eContractUrl;

  DOPNativeEContractLoaded(this.eContractUrl);
}

class DOPNativeEContractError extends DOPNativeEContractDownloadState {
  final ErrorUIModel? error;

  DOPNativeEContractError(this.error);
}

class DOPNativeEContractDownloadLoading extends DOPNativeEContractDownloadState {}

class DOPNativeEContractDownloaded extends DOPNativeEContractDownloadState {}

class DOPNativeEContractDownloadError extends DOPNativeEContractDownloadState {}

class DOPNativeEContractPreviewChanged extends DOPNativeEContractDownloadState {
  final bool canPreview;

  DOPNativeEContractPreviewChanged(this.canPreview);
}
