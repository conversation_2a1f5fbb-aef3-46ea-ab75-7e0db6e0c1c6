import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/ui_utils/evo_ui_utils.dart';
import '../../base/dop_native_page_state_base.dart';
import '../../dop_native_constants.dart';
import '../../resources/dop_native_images.dart';
import '../../resources/dop_native_resources.dart';
import '../../resources/dop_native_ui_strings.dart';
import '../../util/dop_native_submit_status_polling/dop_native_submit_status_polling_impl.dart';
import '../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../widgets/dop_native_status_widget.dart';
import 'cubit/dop_native_appraising_verification_cubit.dart';
import 'cubit/dop_native_appraising_verification_state.dart';

class DOPNativeApprisingVerificationScreen extends PageBase {
  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopNativeAppraisingVerificationScreen.name,
    );
  }

  const DOPNativeApprisingVerificationScreen({
    super.key,
  });

  @override
  State<DOPNativeApprisingVerificationScreen> createState() =>
      _DOPNativeApprisingVerificationState();

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativeAppraisingVerificationScreen.routeName);

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;
}

class _DOPNativeApprisingVerificationState
    extends DOPNativePageStateBase<DOPNativeApprisingVerificationScreen> {
  final DOPNativeAppraisingVerificationCubit _dopNativeApprisingVerificationCubit =
      DOPNativeAppraisingVerificationCubit(
    dopNativeRepo: getIt.get<DOPNativeRepo>(),
    dopNativeSubmitStatusPolling: DOPNativeSubmitStatusPollingImpl(
      intervalDuration: const Duration(
        milliseconds: DOPNativeConstants.defaultPollingIntervalTimeInMs,
      ),
    ),
  );

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _dopNativeApprisingVerificationCubit.getAppraisingStatus();
    });
    super.initState();
  }

  @override
  void dispose() {
    _dopNativeApprisingVerificationCubit.cancelPollingAppraisingStatus();
    super.dispose();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: dopNativeColors.screenBackground,
      appBar: const DOPNativeAppBar(),
      body: BlocProvider<DOPNativeAppraisingVerificationCubit>(
        create: (_) => _dopNativeApprisingVerificationCubit,
        child: BlocListener<DOPNativeAppraisingVerificationCubit,
                DOPNativeAppraisingVerificationState>(
            listenWhen: (
              DOPNativeAppraisingVerificationState previous,
              DOPNativeAppraisingVerificationState current,
            ) {
              return current is AppraisingVerificationSuccessState ||
                  current is AppraisingVerificationFailedState;
            },
            listener: (BuildContext context, DOPNativeAppraisingVerificationState state) {
              _handleDOPNativeAppraisingVerificationStateChanged(state);
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: DOPNativeStatusWidget(
                icon: DOPNativeImages.imgAppraisingVerification,
                description: DOPNativeStrings.dopNativeAppraisingVerificationDesc,
                title: DOPNativeStrings.dopNativeAppraisingVerificationTitle,
                iconHeight: EvoUiUtils().calculateVerticalSpace(
                  context: context,
                  heightPercentage: DOPNativeConstants.statusIconHeightPercentage,
                ),
              ),
            )),
      ),
    );
  }

  void _handleDOPNativeAppraisingVerificationStateChanged(
      DOPNativeAppraisingVerificationState state) {
    if (state is AppraisingVerificationSuccessState) {
      dopNativeApplicationStateCubit.getApplicationState();
      return;
    }

    if (state is AppraisingVerificationFailedState) {
      handleDopEvoApiError(state.error);
      return;
    }
  }
}
