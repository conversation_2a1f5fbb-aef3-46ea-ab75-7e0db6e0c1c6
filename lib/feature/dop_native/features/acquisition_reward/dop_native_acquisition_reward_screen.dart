import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../data/response/dop_native/dop_native_metadata_item_attribute_entity.dart';
import '../../../../data/response/dop_native/dop_native_metadata_item_entity.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../base/dop_native_page_state_base.dart';
import '../../resources/dop_native_images.dart';
import '../../resources/dop_native_resources.dart';
import '../../resources/dop_native_ui_strings.dart';
import '../../util/dop_functions.dart';
import '../../util/metadata/dop_native_metadata_utils_impl.dart';
import '../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../widgets/dop_native_gradient_text_widget.dart';
import '../../widgets/dop_native_title_widget.dart';
import '../../widgets/radio_button/dop_native_radio_button_widget.dart';
import 'cubit/dop_native_acquisition_reward_cubit.dart';
import 'cubit/dop_native_acquisition_reward_state.dart';

class DOPNativeAcquisitionRewardScreen extends PageBase {
  const DOPNativeAcquisitionRewardScreen({super.key});

  static void pushNamed() {
    return navigatorContext?.pushNamed(
      Screen.dopNativeAcquisitionRewardScreen.name,
    );
  }

  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopNativeAcquisitionRewardScreen.name,
    );
  }

  @override
  DOPNativePageStateBase<DOPNativeAcquisitionRewardScreen> createState() =>
      _DOPNativeAcquisitionRewardScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativeAcquisitionRewardScreen.routeName);
}

class _DOPNativeAcquisitionRewardScreenState
    extends DOPNativePageStateBase<DOPNativeAcquisitionRewardScreen> {
  static const double backgroundAspectRadio = 375 / 242;
  static const double listViewTopPositionRadio = 192 / 375;
  static const double listTileHeight = 96.0;
  static const double listTileTitleWidth = 106.0;

  final DOPNativeAcquisitionRewardCubit _cubit = DOPNativeAcquisitionRewardCubit(
    appState: getIt<AppState>(),
    dopNativeRepo: getIt<DOPNativeRepo>(),
    metadataUtils: DOPNativeMetadataUtilsImpl(dopNativeRepo: getIt.get<DOPNativeRepo>()),
  );

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _cubit.loadRewards();
    });
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: dopNativeColors.screenBackground,
      appBar: const DOPNativeAppBar(),
      body: SafeArea(
        child: BlocProvider<DOPNativeAcquisitionRewardCubit>(
          create: (_) => _cubit,
          child: BlocListener<DOPNativeAcquisitionRewardCubit, DOPNativeAcquisitionRewardState>(
            listener: (BuildContext context, DOPNativeAcquisitionRewardState state) {
              _handleStateChanged(state);
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: <Widget>[
                      const DOPNativeTitleWidget(),
                      const SizedBox(height: 16),
                      Text(
                        DOPNativeStrings.acquisitionRewardTitle,
                        style: dopNativeTextStyles.h500(),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        DOPNativeStrings.acquisitionRewardDescription,
                        style: dopNativeTextStyles
                            .bodyLarge(dopNativeColors.textPassive)
                            .copyWith(height: 1.5),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 12),
                Expanded(
                  child: Stack(
                    fit: StackFit.expand,
                    alignment: Alignment.topCenter,
                    children: <Widget>[
                      Positioned(
                        top: 0,
                        left: 0,
                        right: 0,
                        child: AspectRatio(
                          aspectRatio: backgroundAspectRadio,
                          child: evoImageProvider.asset(
                            DOPNativeImages.imgAcquisitionReward,
                            fit: BoxFit.fill,
                          ),
                        ),
                      ),
                      Positioned(
                        top: context.screenWidth * listViewTopPositionRadio,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        child: _buildAcquisitionRewardListView(),
                      )
                    ],
                  ),
                ),
                InkWell(
                  onTap: () {
                    _cubit.openTermAndCondition();
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 10,
                      horizontal: 20,
                    ),
                    child: Text(
                      DOPNativeStrings.acquisitionRewardViewDetailTitle,
                      style: dopNativeTextStyles
                          .bodyMedium(dopNativeColors.textActive)
                          .copyWith(fontWeight: FontWeight.w700),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                _buildCTAButton(),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAcquisitionRewardListView() {
    return BlocBuilder<DOPNativeAcquisitionRewardCubit, DOPNativeAcquisitionRewardState>(
      buildWhen: (_, DOPNativeAcquisitionRewardState current) {
        return current is DOPNativeAcquisitionRewardLoaded;
      },
      builder: (BuildContext context, DOPNativeAcquisitionRewardState state) {
        if (state is! DOPNativeAcquisitionRewardLoaded) {
          return const SizedBox.shrink();
        }
        final List<DOPNativeMetadataItemEntity> entities = state.entities;
        return ListView.separated(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          itemCount: entities.length,
          itemBuilder: (BuildContext context, int index) {
            return _buildAcquisitionRewardListTile(
              index: index,
              entity: entities[index],
            );
          },
          separatorBuilder: (BuildContext context, int index) {
            return const SizedBox(height: 12);
          },
        );
      },
    );
  }

  Widget _buildAcquisitionRewardListTile({
    required DOPNativeMetadataItemEntity entity,
    required int index,
  }) {
    return BlocBuilder<DOPNativeAcquisitionRewardCubit, DOPNativeAcquisitionRewardState>(
      buildWhen: (_, DOPNativeAcquisitionRewardState current) {
        return current is DOPNativeAcquisitionRewardSelected;
      },
      builder: (BuildContext context, DOPNativeAcquisitionRewardState state) {
        int? selectedIndex;
        if (state is DOPNativeAcquisitionRewardSelected) {
          selectedIndex = state.selectedIndex;
        }
        return GestureDetector(
          onTap: () => _cubit.onSelectReward(index),
          child: SizedBox(
            height: listTileHeight,
            child: Stack(
              fit: StackFit.expand,
              children: <Widget>[
                Positioned.fill(
                  child: evoImageProvider.asset(
                    DOPNativeImages.imgAcquisitionRewardItem,
                    fit: BoxFit.fill,
                  ),
                ),
                Positioned.fill(
                  child: Row(
                    children: <Widget>[
                      const SizedBox(width: 12),
                      SizedBox(
                        width: listTileTitleWidth,
                        child: Container(
                          alignment: Alignment.centerLeft,
                          padding: const EdgeInsets.only(
                            left: 20,
                            right: 16,
                          ),
                          child: DOPNativeGradientText(
                            text: entity.name ?? '',
                            colors: <Color>[
                              dopNativeColors.acquisitionRewardGradientStart,
                              dopNativeColors.acquisitionRewardGradientEnd,
                            ],
                            textStyle: dopNativeTextStyles.h300().copyWith(
                                  letterSpacing: -0.02,
                                  height: 1.5,
                                ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      evoImageProvider.asset(
                        DOPNativeImages.imgAcquisitionRewardItemDashSeparator,
                        fit: BoxFit.fitHeight,
                      ),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                          ),
                          child: Text(
                            entity.getAttributeByName(DOPNativeMetadataItemAttributeEntity.title) ??
                                '',
                            style: dopNativeTextStyles
                                .h300(color: dopNativeColors.textActive)
                                .copyWith(
                                  letterSpacing: -0.02,
                                  height: 1.5,
                                ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      DOPNativeRadioButtonWidget(
                        selected: index == selectedIndex,
                        onTap: () => _cubit.onSelectReward(index),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCTAButton() {
    return BlocBuilder<DOPNativeAcquisitionRewardCubit, DOPNativeAcquisitionRewardState>(
      buildWhen: (_, DOPNativeAcquisitionRewardState current) {
        return current is DOPNativeAcquisitionRewardSelected;
      },
      builder: (BuildContext context, DOPNativeAcquisitionRewardState state) {
        int? selectedIndex;
        if (state is DOPNativeAcquisitionRewardSelected) {
          selectedIndex = state.selectedIndex;
        }
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: CommonButton(
            onPressed: selectedIndex == null ? null : _cubit.submitReward,
            style: dopNativeButtonStyles.primary(ButtonSize.medium),
            child: const Text(DOPNativeStrings.dopNativeNext),
          ),
        );
      },
    );
  }

  void _handleStateChanged(DOPNativeAcquisitionRewardState state) {
    if (state is DOPNativeAcquisitionRewardLoading) {
      showDOPLoading();
      return;
    }

    // after submit success -> call application state for moving to next step
    if (state is DOPNativeAcquisitionRewardSubmitSuccess) {
      dopNativeApplicationStateCubit.getApplicationState();
      return;
    }

    hideDOPLoading();
    if (state is DOPNativeAcquisitionRewardError) {
      handleDopEvoApiError(state.error);
      return;
    }

    if (state is DOPNativeAcquisitionRewardTermAndConditionPdfUrlReady) {
      dopUtilFunction.showBottomSheetPdf(
        url: state.termAndConditionPdfUrl,
        title: DOPNativeStrings.acquisitionRewardViewDetailBottomSheetTitle,
      );
      return;
    }
  }
}
