import '../../../../../resources/global.dart';
import '../../../../../util/functions.dart';
import '../../../resources/dop_native_ui_strings.dart';

class DOPNativeESuccessDescArgs {
  final String descPrefix;
  final int? credit;
  final String descSuffix;

  DOPNativeESuccessDescArgs({
    required this.descPrefix,
    required this.credit,
    required this.descSuffix,
  });

  String get creditText => evoUtilFunction.evoFormatCurrency(
        credit,
        currencySymbol: vietNamCurrencySymbol,
      );
}

class DOPNativeESuccessDescArgsPCB extends DOPNativeESuccessDescArgs {
  DOPNativeESuccessDescArgsPCB({
    required super.credit,
    super.descPrefix = '${DOPNativeStrings.dopNativePcbDescPrefix} ',
    super.descSuffix = DOPNativeStrings.dopNativePcbDescSuffix,
  });
}

class DOPNativeESuccessDescArgsCIC extends DOPNativeESuccessDescArgs {
  DOPNativeESuccessDescArgsCIC({
    required super.credit,
    super.descPrefix = '${DOPNativeStrings.dopNativeCicDescPrefix} ',
    super.descSuffix = DOPNativeStrings.dopNativeCicDescSuffix,
  });
}
