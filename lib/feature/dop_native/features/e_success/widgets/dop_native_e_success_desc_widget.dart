import 'package:flutter/material.dart';

import '../../../resources/dop_native_resources.dart';
import 'dop_native_e_success_widget_args.dart';

class DOPNativeESuccessDescWidget extends StatelessWidget {
  const DOPNativeESuccessDescWidget({required this.args, super.key});

  final DOPNativeESuccessDescArgs args;

  @override
  Widget build(BuildContext context) {
    return Text.rich(
      TextSpan(children: <InlineSpan>[
        if (args.credit != null) ...<InlineSpan>[
          TextSpan(text: '${args.descPrefix} '),
          TextSpan(text: '${args.creditText}.\n', style: dopNativeTextStyles.h300()),
        ],
        TextSpan(
          text: args.descSuffix,
        )
      ]),
      style: dopNativeTextStyles.bodyLarge(dopNativeColors.textPassive),
    );
  }
}
