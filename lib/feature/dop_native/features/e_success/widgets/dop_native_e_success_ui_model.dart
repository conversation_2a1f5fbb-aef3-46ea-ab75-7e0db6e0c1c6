import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../../widget/countdown/countdown_widget_builder.dart';
import '../../../dop_native_constants.dart';
import '../../../resources/dop_native_resources.dart';
import '../../../resources/dop_native_ui_strings.dart';
import '../../../util/dop_functions.dart';
import '../../../widgets/card_activate/dop_native_card_activate_countdown.dart';
import '../dop_native_e_success_ui_version.dart';
import 'dop_native_e_success_desc_widget.dart';
import 'dop_native_e_success_widget_args.dart';

class DOPNativeESuccessActionWidgetBuilder {
  Widget buildCTAWidget() {
    return CommonButton(
      onPressed: dopUtilFunction.openTPBAppLink,
      style: dopNativeButtonStyles.primary(ButtonSize.medium),
      child: const Text(
        DOPNativeStrings.dopNativeDownload,
      ),
    );
  }

  Widget buildCountdownWidget({
    CountdownController? countdownController,
  }) {
    if (countdownController == null) {
      return const SizedBox.shrink();
    }

    return DOPNativeCardActivateCountdown(
      countdownController: countdownController,
      secondsInPooling: DOPNativeConstants.cardStatusCountdownAllProgressInSecs,
    );
  }
}

abstract class DOPNativeESuccessUIModel {
  String? uiVersion;
  int? creditAmount;

  DOPNativeESuccessUIModel({
    required this.uiVersion,
    required this.creditAmount,
    DOPNativeESuccessActionWidgetBuilder? actionBuilder,
  }) : actionBuilder = actionBuilder ?? DOPNativeESuccessActionWidgetBuilder();

  @protected
  final DOPNativeESuccessActionWidgetBuilder actionBuilder;

  @visibleForTesting
  DOPNativeESuccessDescArgs? getDescArg() {
    if (uiVersion == DOPNativeESuccessUIVersion.autoPcb.value) {
      return DOPNativeESuccessDescArgsPCB(credit: creditAmount);
    }

    if (uiVersion == DOPNativeESuccessUIVersion.autoCic.value) {
      return DOPNativeESuccessDescArgsCIC(credit: creditAmount);
    }

    return null;
  }

  Widget buildDescription() {
    final DOPNativeESuccessDescArgs? args = getDescArg();

    if (args == null) {
      return const SizedBox.shrink();
    }

    return DOPNativeESuccessDescWidget(args: args);
  }

  Widget buildAction();

  String getBannerTitle();
}

class DOPNativeOnlineMerchantCICUIModel extends DOPNativeESuccessUIModel {
  DOPNativeOnlineMerchantCICUIModel({
    required super.creditAmount,
    required super.uiVersion,
    super.actionBuilder,
  });

  @override
  Widget buildAction({VoidCallback? onCountdownDone}) {
    return actionBuilder.buildCTAWidget();
  }

  @override
  String getBannerTitle() {
    return DOPNativeStrings.dopNativeDownloadTPBankToActive;
  }
}

class DOPNativeOfflineMerchantUIModel extends DOPNativeESuccessUIModel {
  final bool consentLinkCard;
  final CountdownController? countdownController;

  DOPNativeOfflineMerchantUIModel({
    required this.consentLinkCard,
    required super.uiVersion,
    required super.creditAmount,
    this.countdownController,
    super.actionBuilder,
  });

  @override
  Widget buildAction() {
    if (consentLinkCard) {
      return actionBuilder.buildCountdownWidget(
        countdownController: countdownController,
      );
    }

    return actionBuilder.buildCTAWidget();
  }

  @override
  String getBannerTitle() {
    if (consentLinkCard) {
      return DOPNativeStrings.dopNativeCountDownBannerTitle;
    }

    return DOPNativeStrings.dopNativeDownloadTPBankToActive;
  }
}
