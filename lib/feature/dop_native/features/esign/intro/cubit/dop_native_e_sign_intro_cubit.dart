import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../mock/mock_dop_native_e_sign_intro_use_case.dart';
import 'dop_native_e_sign_intro_state.dart';

class DOPNativeESignIntroCubit extends CommonCubit<DOPNativeESignIntroState> {
  final DOPNativeRepo dopNativeRepo;

  DOPNativeESignIntroCubit({
    required this.dopNativeRepo,
  }) : super(DOPNativeESignIntroInitial());

  Future<void> submitESignIntroNext() async {
    emit(DOPNativeESignIntroLoading());
    final BaseEntity entity = await dopNativeRepo.submitESignIntroNext(
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockDOPNativeESignIntroUseCaseFileName(MockDOPNativeESignIntroUseCase.semi),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(DOPNativeESignIntroNextSuccess());
    } else {
      emit(DOPNativeESignIntroNextFail(ErrorUIModel.fromEntity(entity)));
    }
  }
}
