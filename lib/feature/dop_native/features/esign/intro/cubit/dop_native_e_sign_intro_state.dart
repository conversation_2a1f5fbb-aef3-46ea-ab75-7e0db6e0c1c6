import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

abstract class DOPNativeESignIntroState extends BlocState {}

class DOPNativeESignIntroInitial extends DOPNativeESignIntroState {}

class DOPNativeESignIntroLoading extends DOPNativeESignIntroState {}

class DOPNativeESignIntroNextSuccess extends DOPNativeESignIntroState {}

class DOPNativeESignIntroNextFail extends DOPNativeESignIntroState {
  final ErrorUIModel error;

  DOPNativeESignIntroNextFail(this.error);
}
