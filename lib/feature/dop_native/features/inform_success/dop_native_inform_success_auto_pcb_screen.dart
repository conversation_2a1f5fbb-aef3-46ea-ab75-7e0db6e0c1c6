import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../../../base/evo_page_state_base.dart';
import '../../../../../../resources/resources.dart';
import 'base/dop_native_inform_success_auto_page_state_base.dart';

class DOPNativeInformSuccessAutoPCBScreen extends PageBase {
  const DOPNativeInformSuccessAutoPCBScreen({super.key});

  @override
  EvoPageStateBase<DOPNativeInformSuccessAutoPCBScreen> createState() =>
      _DOPNativeInformSuccessAutoPCBScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.dopNativeInformSuccessAutoPCBScreen.name);

  static void pushNamed() {
    return navigatorContext?.pushNamed(Screen.dopNativeInformSuccessAutoPCBScreen.name);
  }

  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(Screen.dopNativeInformSuccessAutoPCBScreen.name);
  }
}

class _DOPNativeInformSuccessAutoPCBScreenState
    extends DOPNativeInformSuccessAutoPageStateBase<DOPNativeInformSuccessAutoPCBScreen> {}
