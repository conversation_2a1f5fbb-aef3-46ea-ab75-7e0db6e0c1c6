import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../../../widget/countdown/countdown_widget_builder.dart';
import '../../../base/dop_native_page_state_base.dart';
import '../../../resources/dop_native_resources.dart';
import '../../../resources/dop_native_ui_strings.dart';
import '../../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../../widgets/countdown_label/countdown_label_widget.dart';
import 'cubit/dop_native_inform_success_cubit.dart';
import 'cubit/dop_native_inform_success_state.dart';

abstract class DOPNativeInformSuccessPageStateBase<T extends PageBase>
    extends DOPNativePageStateBase<T> {
  late final CountdownController countdownController;
  final DOPNativeInformSuccessCubit _dopNativeInformSuccessCubit = DOPNativeInformSuccessCubit(
    getIt.get<DOPNativeRepo>(),
  );

  @override
  void initState() {
    super.initState();
    countdownController = CountdownController(onDone: _handleNext);
    SchedulerBinding.instance.addPostFrameCallback((_) {
      countdownController.start();
    });
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<DOPNativeInformSuccessCubit>(
      create: (_) => _dopNativeInformSuccessCubit,
      child: BlocListener<DOPNativeInformSuccessCubit, DOPNativeInformSuccessState>(
        listener: (BuildContext context, DOPNativeInformSuccessState state) {
          handleDOPNativeInformSuccessStateChanged(state);
        },
        child: Scaffold(
          appBar: const DOPNativeAppBar(),
          backgroundColor: dopNativeColors.screenBackground,
          body: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: <Widget>[
                  buildContent(),
                  const SizedBox(height: 24),
                  _buildCountdown(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleNext() {
    _dopNativeInformSuccessCubit.getApplicationNextState();
  }

  @protected
  Widget buildCTA() {
    return CommonButton(
      onPressed: () {
        _handleNext();
        // this is manual event from user input
        // -> must cancel automatic countdown timer to avoid duplication
        countdownController.cancel();
      },
      isWrapContent: false,
      style: dopNativeButtonStyles.primary(ButtonSize.medium),
      child: const Text(DOPNativeStrings.dopNativeNext),
    );
  }

  /// override this function to define page layout of UI status
  Widget buildContent();

  Widget _buildCountdown() {
    return CountdownLabel(
      label: DOPNativeStrings.dopNativeAutoNavigateAfter,
      controller: countdownController,
    );
  }

  @override
  void dispose() {
    countdownController.cancel();
    super.dispose();
  }

  void handleDOPNativeInformSuccessStateChanged(DOPNativeInformSuccessState state) {
    if (state is DOPNativeApplicationNextStateLoading) {
      showDOPLoading();
      return;
    }
    hideDOPLoading();

    if (state is DOPNativeApplicationNextStateLoaded) {
      dopNativeApplicationStateCubit.getApplicationState();
      return;
    }

    if (state is DOPNativeApplicationNextStateError) {
      handleEvoApiError(state.error);
      return;
    }
  }
}
