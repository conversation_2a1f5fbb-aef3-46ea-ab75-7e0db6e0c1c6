import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../base/dop_native_page_state_base.dart';
import '../../resources/dop_native_images.dart';
import '../../resources/dop_native_resources.dart';
import '../../resources/dop_native_ui_strings.dart';
import '../../widgets/appbar/dop_native_appbar_widget.dart';
import '../../widgets/dop_native_form_header_widget.dart';
import 'cubit/dop_native_additional_form_cubit.dart';
import 'models/additional_form_data_model.dart';
import 'models/additional_form_step_model.dart';
import 'widgets/address_additional_info/dop_native_address_additional_info_widget.dart';
import 'widgets/dop_native_additional_form_controller.dart';
import 'widgets/emergency_contact/dop_native_emergency_contact_widget.dart';
import 'widgets/secret_question/dop_native_secret_question_widget.dart';
import 'widgets/subscribe_channel/dop_native_subscribe_channel_widget.dart';

class DOPNativeAdditionalFormScreen extends PageBase {
  const DOPNativeAdditionalFormScreen({super.key});

  @override
  DOPNativePageStateBase<DOPNativeAdditionalFormScreen> createState() =>
      _DOPNativeAdditionalFormScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(
        name: Screen.dopNativeAppFormAdditionalInfoScreen.routeName,
      );

  static void pushNamed() {
    return navigatorContext?.pushNamed(
      Screen.dopNativeAppFormAdditionalInfoScreen.name,
    );
  }

  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(
      Screen.dopNativeAppFormAdditionalInfoScreen.name,
    );
  }
}

class _DOPNativeAdditionalFormScreenState
    extends DOPNativePageStateBase<DOPNativeAdditionalFormScreen>
    with AutomaticKeepAliveClientMixin {
  final DOPNativeAdditionalFormCubit _cubit = DOPNativeAdditionalFormCubit(
    dopNativeRepo: getIt.get<DOPNativeRepo>(),
    appState: getIt.get<AppState>(),
  );

  late DOPNativeAdditionalFormController _secreteQuestionStepController;
  late DOPNativeAdditionalFormController _addressStepController;
  late DOPNativeAdditionalFormController _subscribeChannelStepController;
  late DOPNativeAdditionalFormController _emergencyContactStepController;
  DOPNativeAdditionalFormDataModel? _additionalFormDataModel;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _cubit.getFormData();
    });

    _createSecreteQuestionController();
    _createEmergencyContactController();
    _createAddressController();
    _createSubscribeChannelController();
  }

  void _createSecreteQuestionController() {
    _secreteQuestionStepController = DOPNativeAdditionalFormController(
      onSuccess: (DOPNativeAdditionalFormDataModel model) {
        _handleWidgetOnSuccess(model);
      },
      onError: (ErrorUIModel error) {
        _handleWidgetOnError(error);
      },
      onLoading: (bool isLoading) {
        _checkShowLoading(isLoading);
      },
      onValid: (bool isValid) {
        _validateInput(isValid);
      },
    );
  }

  void _createEmergencyContactController() {
    _emergencyContactStepController = DOPNativeAdditionalFormController(
      onSuccess: (DOPNativeAdditionalFormDataModel model) {
        _handleWidgetOnSuccess(model);
      },
      onError: (ErrorUIModel error) {
        _handleWidgetOnError(error);
      },
      onLoading: (bool isLoading) {
        _checkShowLoading(isLoading);
      },
      onValid: (bool isValid) {
        _validateInput(isValid);
      },
    );
  }

  void _validateInput(bool isValid) {
    _cubit.validateInput(isValid);
  }

  void _createAddressController() {
    _addressStepController = DOPNativeAdditionalFormController(
      onSuccess: (DOPNativeAdditionalFormDataModel model) {
        _handleWidgetOnSuccess(model);
      },
      onError: (ErrorUIModel error) {
        _handleWidgetOnError(error);
      },
      onLoading: (bool isLoading) {
        _checkShowLoading(isLoading);
      },
      onValid: (bool isValid) {
        _validateInput(isValid);
      },
    );
  }

  void _createSubscribeChannelController() {
    _subscribeChannelStepController = DOPNativeAdditionalFormController(
      onSuccess: (DOPNativeAdditionalFormDataModel model) {
        _handleWidgetOnSuccess(model);
      },
      onError: (ErrorUIModel error) {
        _handleWidgetOnError(error);
      },
      onLoading: (bool isLoading) {
        _checkShowLoading(isLoading);
      },
      onValid: (bool isValid) {
        _validateInput(isValid);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return buildVisibilityDetectorPage(context);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: dopNativeColors.screenBackground,
      appBar: const DOPNativeAppBar(),
      body: BlocProvider<DOPNativeAdditionalFormCubit>(
        create: (_) => _cubit,
        child: SafeArea(
          child: Column(
            children: <Widget>[
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      const DOPNativeFormHeaderWidget(
                        currentStep: 3,
                        titleStep: DOPNativeStrings.additionalFormTitle,
                      ),
                      const SizedBox(height: 20),
                      _buildContent(),
                    ],
                  ),
                ),
              ),
              _buildCTA(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    return BlocConsumer<DOPNativeAdditionalFormCubit, DOPNativeAdditionalFormState>(
      listener: (BuildContext context, DOPNativeAdditionalFormState state) {
        _handleListener(state);
      },
      buildWhen: (DOPNativeAdditionalFormState previous, DOPNativeAdditionalFormState current) {
        return current is DOPNativeGetFormDataSucceed ||
            current is DOPNativeGetFormDataFailed ||
            current is DOPNativeChangeStepSucceed;
      },
      builder: (_, DOPNativeAdditionalFormState state) {
        if (state is DOPNativeGetFormDataSucceed) {
          _additionalFormDataModel = state.model;
        }

        if (state is DOPNativeChangeStepSucceed) {
          _additionalFormDataModel = state.model;
        }

        final DOPNativeAdditionalFormDataModel? additionalFormDataModel = _additionalFormDataModel;
        return additionalFormDataModel == null
            ? const SizedBox.shrink()
            : Column(
                children: <Widget>[
                  _buildSubtitleTitle(
                    model: additionalFormDataModel,
                    onBackStepPressed: () {
                      _cubit.changeStep(
                        model: additionalFormDataModel,
                        goToNextStep: false,
                      );
                    },
                  ),
                  const SizedBox(height: 24),
                  _buildContentSteps(model: additionalFormDataModel),
                ],
              );
      },
    );
  }

  Widget _buildContentSteps({
    required DOPNativeAdditionalFormDataModel model,
  }) {
    final DOPNativeFormStep step = model.formStepModel.currentStep;
    if (step == DOPNativeFormStep.secretQuestion) {
      return _secreteQuestionInfoWidget(model);
    } else if (step == DOPNativeFormStep.address) {
      return _addressInfoWidget(model);
    } else if (step == DOPNativeFormStep.emergencyContact) {
      return _emergencyContactWidget(model);
    } else if (step == DOPNativeFormStep.subscribeChannel) {
      return _subscribeChannelWidget(model);
    } else {
      return const SizedBox.shrink();
    }
  }

  Widget _secreteQuestionInfoWidget(DOPNativeAdditionalFormDataModel model) {
    return DOPNativeSecretQuestionWidget(
      model: model,
      controller: _secreteQuestionStepController,
    );
  }

  Widget _addressInfoWidget(DOPNativeAdditionalFormDataModel model) {
    return DOPNativeAddressAdditionalInfoWidget(
      model: model,
      controller: _addressStepController,
    );
  }

  Widget _subscribeChannelWidget(DOPNativeAdditionalFormDataModel model) {
    return DOPNativeSubscribeChannelWidget(
      model: model,
      controller: _subscribeChannelStepController,
    );
  }

  Widget _emergencyContactWidget(DOPNativeAdditionalFormDataModel model) {
    return DOPNativeEmergencyContactWidget(
      model: model,
      controller: _emergencyContactStepController,
    );
  }

  Widget _buildSubtitleTitle({
    required DOPNativeAdditionalFormDataModel model,
    VoidCallback? onBackStepPressed,
  }) {
    final bool isShownBackStep = _hasShowBackStepButton(model.formStepModel);

    return SizedBox(
      height: 32,
      child: Row(
        children: <Widget>[
          _buildBackStepButton(
            isShownBack: isShownBackStep,
            onBackStepPressed: onBackStepPressed,
          ),
          const Spacer(),
          _buildStepTitle(model: model),
        ],
      ),
    );
  }

  Widget _buildBackStepButton({
    required bool isShownBack,
    VoidCallback? onBackStepPressed,
  }) {
    return isShownBack
        ? GestureDetector(
            onTap: onBackStepPressed,
            child: evoImageProvider.asset(
              DOPNativeImages.icBackStep,
              width: 32,
              height: 32,
            ),
          )
        : const SizedBox.shrink();
  }

  Widget _buildStepTitle({
    required DOPNativeAdditionalFormDataModel model,
  }) {
    const int totalStep = 4;
    final int currentStep = model.formStepModel.stepIndex;

    return Text('$currentStep/$totalStep',
        style: dopNativeTextStyles.bodyMedium(dopNativeColors.textActive));
  }

  Widget _buildCTA() {
    return BlocBuilder<DOPNativeAdditionalFormCubit, DOPNativeAdditionalFormState>(
        buildWhen: (_, DOPNativeAdditionalFormState state) {
      return state is DOPNativeInputValueValid || state is DOPNativeInputValueInValid;
    }, builder: (BuildContext context, DOPNativeAdditionalFormState state) {
      final bool isEnable = state is DOPNativeInputValueValid;

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
        child: CommonButton(
          isWrapContent: false,
          onPressed: isEnable
              ? () {
                  _onNextButtonPressed();
                }
              : null,
          style: dopNativeButtonStyles.primary(ButtonSize.medium),
          child: const Text(DOPNativeStrings.dopNativeNext),
        ),
      );
    });
  }

  void _onNextButtonPressed() {
    final DOPNativeAdditionalFormDataModel? formDataModel = _additionalFormDataModel;
    if (formDataModel == null) {
      commonLog('Implement wrong with biz');
      return;
    }

    _handlePressButtonOnStep(formDataModel);
  }

  void _handlePressButtonOnStep(DOPNativeAdditionalFormDataModel model) {
    if (model.formStepModel.currentStep == DOPNativeFormStep.secretQuestion) {
      _secreteQuestionStepController.onSubmitted.call();
    } else if (model.formStepModel.currentStep == DOPNativeFormStep.address) {
      _addressStepController.onSubmitted.call();
    } else if (model.formStepModel.currentStep == DOPNativeFormStep.emergencyContact) {
      _emergencyContactStepController.onSubmitted.call();
    } else if (model.formStepModel.currentStep == DOPNativeFormStep.subscribeChannel) {
      _subscribeChannelStepController.onSubmitted.call();
    }
  }

  void _handleWidgetOnSuccess(DOPNativeAdditionalFormDataModel model) {
    hideDOPLoading();

    if (model.formStepModel.currentStep == DOPNativeFormStep.subscribeChannel) {
      dopNativeApplicationStateCubit.getApplicationState();
      return;
    }

    _cubit.changeStep(
      model: model,
      goToNextStep: true,
    );
  }

  void _handleWidgetOnError(ErrorUIModel error) {
    hideDOPLoading();

    handleDopEvoApiError(error);
  }

  bool _hasShowBackStepButton(DOPNativeAdditionalFormStepModel? formStep) {
    if (formStep == null) {
      return false;
    }

    return formStep.currentStep != DOPNativeFormStep.secretQuestion;
  }

  void _handleListener(DOPNativeAdditionalFormState state) {
    if (state is DOPNativeAdditionalFormLoading) {
      showDOPLoading();
      return;
    }

    hideDOPLoading();

    if (state is DOPNativeGetFormDataSucceed) {
      return;
    }

    if (state is DOPNativeGetFormDataFailed) {
      handleDopEvoApiError(state.error);
      return;
    }
  }

  void _checkShowLoading(bool isLoading) {
    if (isLoading) {
      showDOPLoading();
    } else {
      hideDOPLoading();
    }
  }
}
