part of 'dop_native_additional_form_cubit.dart';

@immutable
abstract class DOPNativeAdditionalFormState implements BlocState {}

class DOPNativeAdditionalFormInitial implements DOPNativeAdditionalFormState {}

class DOPNativeAdditionalFormLoading implements DOPNativeAdditionalFormState {}

class DOPNativeGetFormDataSucceed implements DOPNativeAdditionalFormState {
  final DOPNativeAdditionalFormDataModel model;

  DOPNativeGetFormDataSucceed({required this.model});
}

class DOPNativeGetFormDataFailed implements DOPNativeAdditionalFormState {
  final ErrorUIModel error;

  DOPNativeGetFormDataFailed({required this.error});
}

class DOPNativeChangeStepSucceed implements DOPNativeAdditionalFormState {
  final DOPNativeAdditionalFormDataModel model;

  DOPNativeChangeStepSucceed({required this.model});
}

class DOPNativeInputValueValid implements DOPNativeAdditionalFormState {
  DOPNativeInputValueValid();
}

class DOPNativeInputValueInValid implements DOPNativeAdditionalFormState {
  DOPNativeInputValueInValid();
}
