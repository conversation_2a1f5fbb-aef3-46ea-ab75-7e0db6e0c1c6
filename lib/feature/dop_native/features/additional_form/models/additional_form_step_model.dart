import 'package:flutter_common_package/common_package/common_package.dart';

const String secretQuestionStep = 'af.addinfo.secret_question';
const String addressStep = 'af.addinfo.address';
const String emergencyContactStep = 'af.addinfo.emergency_contact';
const String subscribeChannelStep = 'af.addinfo.subscribe_channel';
const String unknownStep = 'unknown';

enum DOPNativeFormStep {
  secretQuestion(secretQuestionStep),
  address(addressStep),
  emergencyContact(emergencyContactStep),
  subscribeChannel(subscribeChannelStep),
  unknown(unknownStep);

  final String value;

  const DOPNativeFormStep(this.value);

  static DOPNativeFormStep fromValue(String? value) {
    switch (value) {
      case secretQuestionStep:
        return DOPNativeFormStep.secretQuestion;
      case addressStep:
        return DOPNativeFormStep.address;
      case emergencyContactStep:
        return DOPNativeFormStep.emergencyContact;
      case subscribeChannelStep:
        return DOPNativeFormStep.subscribeChannel;
      default:
        return DOPNativeFormStep.unknown;
    }
  }
}

class DOPNativeAdditionalFormStepModel extends Equatable {
  final DOPNativeFormStep currentStep;
  final DOPNativeFormStep? nextStep;
  final DOPNativeFormStep? previousStep;

  // If cannot load the form step,
  // we will use secretQuestionStep as the default step
  final int stepIndex;

  const DOPNativeAdditionalFormStepModel({
    required this.currentStep,
    required this.nextStep,
    required this.previousStep,
    required this.stepIndex,
  });

  factory DOPNativeAdditionalFormStepModel.secretQuestionStep() {
    return const DOPNativeAdditionalFormStepModel(
      currentStep: DOPNativeFormStep.secretQuestion,
      nextStep: DOPNativeFormStep.address,
      previousStep: DOPNativeFormStep.unknown,
      stepIndex: 1,
    );
  }

  factory DOPNativeAdditionalFormStepModel.addressStep() {
    return const DOPNativeAdditionalFormStepModel(
      currentStep: DOPNativeFormStep.address,
      nextStep: DOPNativeFormStep.emergencyContact,
      previousStep: DOPNativeFormStep.secretQuestion,
      stepIndex: 2,
    );
  }

  factory DOPNativeAdditionalFormStepModel.emergencyContactStep() {
    return const DOPNativeAdditionalFormStepModel(
      currentStep: DOPNativeFormStep.emergencyContact,
      nextStep: DOPNativeFormStep.subscribeChannel,
      previousStep: DOPNativeFormStep.address,
      stepIndex: 3,
    );
  }

  factory DOPNativeAdditionalFormStepModel.subscribeChannelStep() {
    return const DOPNativeAdditionalFormStepModel(
      currentStep: DOPNativeFormStep.subscribeChannel,
      nextStep: DOPNativeFormStep.unknown,
      previousStep: DOPNativeFormStep.emergencyContact,
      stepIndex: 4,
    );
  }

  @override
  List<Object?> get props => <Object?>[
        currentStep,
        nextStep,
        previousStep,
      ];
}
