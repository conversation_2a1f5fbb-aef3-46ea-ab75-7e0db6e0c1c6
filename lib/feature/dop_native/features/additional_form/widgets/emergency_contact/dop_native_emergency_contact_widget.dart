import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../../../data/repository/dop_native_repo/dop_native_repo.dart';
import '../../../../../../data/response/dop_native/dop_native_reference_info_entity.dart';
import '../../../../../../prepare_for_app_initiation.dart';
import '../../../../resources/dop_native_resources.dart';
import '../../../../resources/dop_native_ui_strings.dart';
import '../../../../widgets/dop_native_gradient_text_widget.dart';
import '../../../../widgets/text_field/dop_native_text_field_widget.dart';
import '../../models/additional_form_data_model.dart';
import '../dop_native_additional_form_controller.dart';
import 'cubit/dop_native_emergency_contact_cubit.dart';
import 'cubit/dop_native_emergency_contact_state.dart';

class DOPNativeEmergencyContactWidget extends StatefulWidget {
  const DOPNativeEmergencyContactWidget({
    required this.controller,
    required this.model,
    super.key,
  });

  final DOPNativeAdditionalFormDataModel model;
  final DOPNativeAdditionalFormController controller;

  @override
  State<DOPNativeEmergencyContactWidget> createState() => _DOPNativeEmergencyContactWidgetState();
}

class _DOPNativeEmergencyContactWidgetState extends State<DOPNativeEmergencyContactWidget> {
  final DOPNativeEmergencyContactCubit _cubit = DOPNativeEmergencyContactCubit(
    appState: getIt.get<AppState>(),
    dopNativeRepo: getIt.get<DOPNativeRepo>(),
  );
  final TextEditingController _phone1InputController = TextEditingController();
  final TextEditingController _phone2InputController = TextEditingController();

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.controller.onSubmitted = _submit;
      _initializeForm();
    });
  }

  @override
  Widget build(BuildContext context) {
    const Widget spacing12 = SizedBox(height: 12);
    const Widget spacing16 = SizedBox(height: 16);
    const Widget spacing32 = SizedBox(height: 32);
    return BlocProvider<DOPNativeEmergencyContactCubit>(
      create: (BuildContext context) => _cubit,
      child: BlocListener<DOPNativeEmergencyContactCubit, DOPNativeEmergencyContactState>(
        listener: (_, DOPNativeEmergencyContactState state) {
          _handleStateChanged(state);
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Text(
              DOPNativeStrings.dopNativeEmergencyContactTitle,
              style: dopNativeTextStyles.h500(),
            ),
            spacing12,
            Text(
              DOPNativeStrings.dopNativeEmergencyContactDesc,
              style: dopNativeTextStyles.bodyLarge(
                dopNativeColors.textPassive,
              ),
            ),
            spacing12,
            _buildNoticeMessage(),
            spacing32,
            _buildEmergencyContact1(),
            spacing16,
            _buildEmergencyContact2(),
            spacing16,
          ],
        ),
      ),
    );
  }

  Widget _buildNoticeMessage() {
    return DOPNativeGradientText(
      text: DOPNativeStrings.dopNativeEmergencyContactNotice,
      textStyle: dopNativeTextStyles.bodySmall(
        color: dopNativeColors.textGradientStart,
      ),
    );
  }

  Widget _buildEmergencyContact1() {
    return BlocBuilder<DOPNativeEmergencyContactCubit, DOPNativeEmergencyContactState>(
        buildWhen: (_, DOPNativeEmergencyContactState state) {
      return state is HasInValidPhoneNumber1 || state is HasValidPhoneNumber1;
    }, builder: (_, DOPNativeEmergencyContactState state) {
      final String? errorMessage = state is HasInValidPhoneNumber1 ? state.errorMsg : null;
      return DOPNativeTextField(
        label: DOPNativeStrings.dopNativeEmergencyContact1Label,
        hintText: DOPNativeStrings.dopNativeEmergencyContact1Label,
        keyboardType: TextInputType.phone,
        controller: _phone1InputController,
        errorText: errorMessage,
        onChanged: (String text) {
          _cubit.setInput1Edited();

          _cubit.validateForm(
            firstPhoneNumber: text,
            secondPhoneNumber: _phone2InputController.text,
          );
        },
      );
    });
  }

  Widget _buildEmergencyContact2() {
    return BlocBuilder<DOPNativeEmergencyContactCubit, DOPNativeEmergencyContactState>(
        buildWhen: (_, DOPNativeEmergencyContactState state) {
      return state is HasInValidPhoneNumber2 || state is HasValidPhoneNumber2;
    }, builder: (_, DOPNativeEmergencyContactState state) {
      final String? errorMessage = state is HasInValidPhoneNumber2 ? state.errorMsg : null;
      return DOPNativeTextField(
        label: DOPNativeStrings.dopNativeEmergencyContact2Label,
        hintText: DOPNativeStrings.dopNativeEmergencyContact2Label,
        controller: _phone2InputController,
        keyboardType: TextInputType.phone,
        errorText: errorMessage,
        onChanged: (String text) {
          _cubit.setInput2Edited();

          _cubit.validateForm(
            firstPhoneNumber: _phone1InputController.text,
            secondPhoneNumber: text,
          );
        },
      );
    });
  }

  void _handleStateChanged(DOPNativeEmergencyContactState state) {
    if (state is HasValidForm || state is HasInvalidForm) {
      widget.controller.onValid(state is HasValidForm);
    }

    if (state is EmergencyContactLoadingState) {
      widget.controller.onLoading(true);
    }

    if (state is FormSubmitSucceed) {
      widget.controller.onSuccess(state.model);
    }

    if (state is FormSubmitFailure) {
      widget.controller.onError(state.error);
    }
  }

  void _submit() {
    commonLog('_submit is pressed at DOPNativeEmergencyContactWidget');

    FocusManager.instance.primaryFocus?.unfocus();

    final bool hasChangedInfo = _checkInfoChanged();
    if (!hasChangedInfo) {
      widget.controller.onSuccess(widget.model);
      return;
    }

    _cubit.submit(
      firstEmergencyPhone: _phone1InputController.text,
      secondEmergencyPhone: _phone2InputController.text,
      model: widget.model,
    );
  }

  // Check the user changed information
  bool _checkInfoChanged() {
    final DOPNativeReferenceInfoEntity? refInfo =
        widget.model.formDataEntity?.formData?.referenceInfo;

    return refInfo?.referencePhone1 != _phone1InputController.text ||
        refInfo?.referencePhone2 != _phone2InputController.text;
  }

  void _initializeForm() {
    final DOPNativeReferenceInfoEntity? refInfo =
        widget.model.formDataEntity?.formData?.referenceInfo;

    if (refInfo == null) {
      widget.controller.onValid(false);
      return;
    }

    if (refInfo.referencePhone1?.isNotEmpty == true) {
      _cubit.setInput1Edited();
      _phone1InputController.text = refInfo.referencePhone1!;
    }
    if (refInfo.referencePhone2?.isNotEmpty == true) {
      _cubit.setInput2Edited();
      _phone2InputController.text = refInfo.referencePhone2!;
    }

    _cubit.validateForm(
      firstPhoneNumber: _phone1InputController.text,
      secondPhoneNumber: _phone2InputController.text,
    );
  }
}
