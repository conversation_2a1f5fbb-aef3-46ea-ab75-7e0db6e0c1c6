import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/log_error_mixin.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/cancelable_task_controller.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../../../../data/response/dop_native/dop_native_metadata_suggestion_entity.dart';
import '../../../../../util/metadata/dop_native_metadata_utils.dart';

part 'dop_native_search_company_state.dart';

class DOPNativeSearchCompanyCubit extends CommonCubit<DOPNativeSearchCompanyState>
    with LogErrorMixin {
  DOPNativeSearchCompanyCubit({
    required this.metadataUtils,
    required this.searchCompanyNameTaskController,
  }) : super(DOPNativeSearchCompanyInitial());

  final DOPNativeMetadataUtils metadataUtils;
  final CancelableTaskController searchCompanyNameTaskController;

  Future<void> handleSearchCompanySuggestions({required String searchPrefix}) async {
    try {
      // Cancel previous search
      cancelSearch();

      await getCompanySuggestions(searchPrefix);
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      /// task is cancelable, so we need to catch the exception
      commonLog('Cancel call API Search Company Name Success');
    }
  }

  @visibleForTesting
  Future<void> getCompanySuggestions(String searchPrefix) async {
    final DOPNativeMetadataSuggestionEntity? entity =
        await searchCompanyNameTaskController.perform<DOPNativeMetadataSuggestionEntity?>(
      metadataUtils.getCompanySuggestions(
        searchPrefix: searchPrefix,
      ),
    );

    if (entity?.statusCode == CommonHttpClient.SUCCESS) {
      // Because with statusCode == 200, the entity is not null
      emit(DOPNativeSearchCompanySucceed(companies: entity!));
    } else {
      emit(DOPNativeSearchCompanyFailed(error: ErrorUIModel.fromEntity(entity)));
    }
  }

  void showCompanySuggestions() {
    emit(DOPNativeSearchCompanyShown());
  }

  void hideCompanySuggestions() {
    emit(DOPNativeSearchCompanyHidden());
  }

  void cancelSearch() {
    commonLog('Cancel call API Search Company Name');
    searchCompanyNameTaskController.cancel();
  }
}
