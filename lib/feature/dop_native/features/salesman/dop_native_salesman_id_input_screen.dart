import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../../base/evo_page_state_base.dart';
import '../../../../../resources/resources.dart';
import '../../resources/dop_native_resources.dart';
import '../../resources/dop_native_ui_strings.dart';
import '../../widgets/dop_native_form_header_widget.dart';
import 'dop_native_salesman_page_state_base.dart';

class DOPNativeSalesmanIDInputScreen extends PageBase {
  const DOPNativeSalesmanIDInputScreen({super.key});

  @override
  EvoPageStateBase<DOPNativeSalesmanIDInputScreen> createState() =>
      _DOPNativeSalesmanIDInputScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.dopNativeSalesmanScreen.name);

  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(Screen.dopNativeSalesmanScreen.name);
  }
}

class _DOPNativeSalesmanIDInputScreenState
    extends DOPNativeSalesmanPageStateBase<DOPNativeSalesmanIDInputScreen> {
  final int _currentStep = 1;

  @override
  StaffIdScreenType get staffIdScreenType => StaffIdScreenType.inputId;

  @override
  String get description => DOPNativeStrings.salesmanIDDesc;

  @override
  String get title => DOPNativeStrings.salesmanID;

  @override
  Widget buildHeader() {
    return DOPNativeFormHeaderWidget(
      currentStep: _currentStep,
      titleStep: DOPNativeStrings.storeInfo,
      titleStyle: dopNativeTextStyles.h300(),
    );
  }
}
