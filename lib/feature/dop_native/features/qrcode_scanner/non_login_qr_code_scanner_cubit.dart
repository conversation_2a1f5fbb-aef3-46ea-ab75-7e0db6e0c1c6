import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../../prepare_for_app_initiation.dart';
import '../../../../util/functions.dart';
import '../../../deep_link/deep_link_handler.dart';
import '../../../deep_link/deep_link_utils.dart';
import '../../../deep_link/model/deep_link_model.dart';

part 'non_login_qr_code_scanner_state.dart';

class NonLoginQrCodeScannerCubit extends CommonCubit<NonLoginQrCodeScannerState> {
  /// This flag is used to prevent handle multi QR code at same time.
  @visibleForTesting
  bool isLocked = false;

  final DeepLinkHandler deepLinkHandler;
  final AppState appState;

  NonLoginQrCodeScannerCubit({
    required this.deepLinkHandler,
    required this.appState,
  }) : super(NonLoginQrCodeScannerInitialState());

  Future<void> parseQrCode(String? rawData) async {
    /// Don't allow handle multi QR code at same time.
    if (isLocked) {
      return;
    }

    lockParsingQrCode();
    if (rawData == null || rawData.isEmpty) {
      emit(NonLoginInvalidQrCodeState());
      return;
    }

    if (evoUtilFunction.isGoEvoURL(rawData)) {
      emit(NonLoginDetectGoEvoUrlQrCodeState(rawData));
      return;
    }

    final String? evoDeepLink = deepLinkUtils.extractEvoDeepLinkFromUrl(rawData);
    if (evoDeepLink == null) {
      emit(NonLoginInvalidQrCodeState());
      return;
    }

    final DeepLinkModel deepLinkModel =
        deepLinkUtils.generateDeepLinkModel(deepLinkValue: evoDeepLink);
    final bool isProceed = await deepLinkHandler.executeDeepLink(
      deepLink: deepLinkModel,
      isLoggedIn: appState.isUserLogIn,
    );
    if (!isProceed) {
      emit(NonLoginInvalidQrCodeState());
      return;
    }

    emit(NonLoginValidQrCodeState());
  }

  void unlockParsingQrCode() {
    isLocked = false;
  }

  void lockParsingQrCode() {
    isLocked = true;
  }
}
