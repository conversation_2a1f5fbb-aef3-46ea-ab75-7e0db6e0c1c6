import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:pdfrx/pdfrx.dart';

import '../../../../resources/resources.dart';
import '../../../../util/ui_utils/evo_ui_utils.dart';
import '../../dop_native_constants.dart';
import '../../features/pdf_view/dop_native_pdf_screen.dart';
import '../../resources/dop_native_images.dart';
import '../../resources/dop_native_resources.dart';
import '../../resources/dop_native_ui_strings.dart';
import '../../util/dop_functions.dart';
import '../dop_loading/dop_loading_widget.dart';
import 'cubit/dop_native_pdf_review_cubit.dart';
import 'cubit/dop_native_pdf_review_state.dart';

class DOPNativePdfReviewWidgetController {
  VoidCallback? resetPdf;
  void Function(String? url)? loadData;
}

class DOPNativePdfReviewWidgetCallback {
  final VoidCallback? onRetry;

  final VoidCallback? onLoading;
  final VoidCallback? onLoaded;
  final VoidCallback? onError;

  DOPNativePdfReviewWidgetCallback({
    this.onRetry,
    this.onLoading,
    this.onLoaded,
    this.onError,
  });
}

class DOPNativePdfReviewWidget extends StatefulWidget {
  final String? pdfUrl;
  final double? width;
  final double? height;

  final DOPNativePdfReviewWidgetCallback? callback;
  final DOPNativePdfReviewWidgetController? controller;

  const DOPNativePdfReviewWidget({
    this.pdfUrl,
    this.width,
    this.height,
    this.callback,
    this.controller,
    super.key,
  });

  @override
  State<DOPNativePdfReviewWidget> createState() => _DOPNativePdfReviewWidgetState();
}

class _DOPNativePdfReviewWidgetState extends State<DOPNativePdfReviewWidget> {
  static const double ctaWidthPercentage = 91 / DOPNativeConstants.figmaScreenWidth;
  final DOPNativePdfReviewCubit _pdfReviewCubit = DOPNativePdfReviewCubit();

  String? _pdfUrl;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.controller?.resetPdf = _pdfReviewCubit.resetPdf;
      widget.controller?.loadData = loadData;

      _loadPdfAtFirstTimeIfNeed();
    });
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: BlocProvider<DOPNativePdfReviewCubit>(
        create: (_) => _pdfReviewCubit,
        child: BlocListener<DOPNativePdfReviewCubit, DOPNativePdfReviewState>(
          listener: (BuildContext context, DOPNativePdfReviewState state) {
            _handleDOPNativePdfReviewStateChanged(state);
          },
          child: Container(
            width: widget.width,
            height: widget.height,
            decoration: ShapeDecoration(
              color: dopNativeColors.background,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              shadows: <BoxShadow>[
                BoxShadow(
                  color: dopNativeColors.pdfReviewShadow,
                  blurRadius: 4,
                )
              ],
            ),
            child: BlocBuilder<DOPNativePdfReviewCubit, DOPNativePdfReviewState>(
              builder: (BuildContext context, DOPNativePdfReviewState state) {
                if (state is DOPNativePdfReviewError) {
                  return _buildError();
                }

                String? url;

                final bool isLoading = state is DOPNativePdfReviewLoading;
                if (isLoading) {
                  url = state.url;
                }

                final bool isPdfLoaded = state is DOPNativePdfReviewLoaded;
                if (isPdfLoaded) {
                  url = state.url;
                }

                if (url == null) {
                  return _buildLoading();
                }

                return GestureDetector(
                  onTap: isPdfLoaded ? _viewDetailPdf : null,

                  /// Use AbsorbPointer to avoid the user from interacting with the PDF viewer, like zooming in/out, scrolling, etc.
                  child: AbsorbPointer(
                    child: Stack(
                      children: <Widget>[
                        PdfViewer.uri(
                          Uri.parse(url),
                          params: PdfViewerParams(
                            margin: 0,
                            onViewerReady: _onPdfLoaded,
                            errorBannerBuilder: (
                              BuildContext context,
                              Object error,
                              StackTrace? stackTrace,
                              PdfDocumentRef documentRef,
                            ) {
                              _pdfReviewCubit.onPdfLoadFailed();
                              return const SizedBox.shrink();
                            },
                            pageDropShadow: null,
                            scaleEnabled: false,
                          ),
                        ),
                        if (isLoading) _buildLoading(),
                        if (isPdfLoaded)
                          Positioned(
                            bottom: 0,
                            left: 0,
                            right: 0,
                            child: _buildZoomInGuide(),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoading() => DOPLoadingWidget(maskColor: dopNativeColors.background);

  Widget _buildZoomInGuide() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: dopNativeColors.dopNativeZoomInEContractGuide,
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(8)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          evoImageProvider.asset(DOPNativeImages.icZoomIn, width: 16, height: 16),
          const SizedBox(width: 14),
          Text(
            DOPNativeStrings.dopNativeEContractDownloadZoomInGuide,
            style: dopNativeTextStyles.bodyMedium(dopNativeColors.textPassive2).copyWith(
                  fontWeight: FontWeight.w700,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildError() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            DOPNativeStrings.dopNativePdfViewError,
            style: dopNativeTextStyles.bodyLarge(dopNativeColors.textActive),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          CommonButton(
            onPressed: () {
              widget.callback?.onRetry?.call();
            },
            style: dopNativeButtonStyles.primary(ButtonSize.small),
            child: SizedBox(
                width: EvoUiUtils().calculateHorizontalSpace(
                  context: context,
                  widthPercentage: ctaWidthPercentage,
                ),
                child: const Center(child: Text(DOPNativeStrings.dopNativeRetry))),
          ),
        ],
      ),
    );
  }

  void _viewDetailPdf() {
    dopUtilFunction.openDOPPDFView(DOPNativePDFViewArg(url: _pdfUrl));
  }

  void _loadPdfAtFirstTimeIfNeed() {
    if (widget.pdfUrl != null) {
      loadData(widget.pdfUrl);
    }
  }

  void loadData(String? url) {
    _pdfUrl = url;
    _pdfReviewCubit.loadData(url);
  }

  void _handleDOPNativePdfReviewStateChanged(DOPNativePdfReviewState state) {
    if (state is DOPNativePdfReviewLoading || state is DOPNativePdfReviewInitial) {
      widget.callback?.onLoading?.call();
      return;
    }

    if (state is DOPNativePdfReviewLoaded) {
      widget.callback?.onLoaded?.call();
      return;
    }

    if (state is DOPNativePdfReviewError) {
      widget.callback?.onError?.call();
      return;
    }
  }

  void _onPdfLoaded(PdfDocument document, PdfViewerController controller) {
    _pdfReviewCubit.onPdfLoaded();
  }
}
