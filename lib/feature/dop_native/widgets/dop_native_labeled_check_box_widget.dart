import 'package:flutter/material.dart';

import '../resources/dop_native_resources.dart';
import 'dop_native_checkbox_widget.dart';

class DOPNativeLabeledCheckboxWidget extends StatelessWidget {
  const DOPNativeLabeledCheckboxWidget({
    required this.label,
    required this.value,
    required this.onChanged,
    this.labelStyle,
    super.key,
  });

  final String label;
  final bool value;
  final ValueChanged<bool>? onChanged;
  final TextStyle? labelStyle;

  @override
  Widget build(BuildContext context) {
    final void Function()? onTap = onChanged != null
        ? () {
            onChanged!(!value);
          }
        : null;

    return InkWell(
      onTap: onTap,
      child: Row(
        children: <Widget>[
          DOPNativeCheckBox(
            value: value,
            onTap: onTap,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: labelStyle ?? dopNativeTextStyles.bodyMedium(dopNativeColors.textActive),
            ),
          ),
        ],
      ),
    );
  }
}
