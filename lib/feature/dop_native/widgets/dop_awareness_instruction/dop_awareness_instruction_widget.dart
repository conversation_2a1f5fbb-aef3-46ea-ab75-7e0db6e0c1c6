import 'package:flutter/material.dart';

import '../../resources/dop_native_resources.dart';
import '../../resources/dop_native_ui_strings.dart';
import 'dop_awareness_item.dart';
import 'dop_awareness_item_widget.dart';

class DOPNativeAwarenessInstruction extends StatelessWidget {
  const DOPNativeAwarenessInstruction({required this.items, super.key});

  final List<DOPNativeAwarenessItem> items;
  final double topSpacing = 12.0;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        Container(
          margin: EdgeInsets.only(top: topSpacing),
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: topSpacing),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: Colors.white,
          ),
          child: Row(
              crossAxisAlignment: CrossAxisAlignment.start, children: _buildAwarenessItems(items)),
        ),
        _buildHeaderTag()
      ],
    );
  }

  List<Widget> _buildAwarenessItems(List<DOPNativeAwarenessItem> items) {
    final List<Widget> widgets = <Widget>[];
    for (final DOPNativeAwarenessItem item in items) {
      widgets.add(Expanded(
          child: DOPNativeAwarenessItemWidget(
        item: item,
      )));
    }
    return widgets;
  }

  Widget _buildHeaderTag() {
    return Align(
      alignment: Alignment.topCenter,
      child: Container(
        padding: const EdgeInsets.symmetric(
          vertical: 4,
          horizontal: 12,
        ),
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(12), color: Colors.white),
        child: Text(
          DOPNativeStrings.dopNativeNote,
          style: dopNativeTextStyles.h200(),
        ),
      ),
    );
  }
}
