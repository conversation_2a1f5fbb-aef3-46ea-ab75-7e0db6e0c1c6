import 'package:flutter/material.dart';

import '../../../../resources/resources.dart';
import '../../../../util/ui_utils/evo_ui_utils.dart';
import '../../resources/dop_native_resources.dart';
import 'dop_awareness_item.dart';

class DOPNativeAwarenessItemWidget extends StatelessWidget {
  const DOPNativeAwarenessItemWidget({required this.item, super.key});

  final DOPNativeAwarenessItem item;
  final double imageHeightPercentage = 70 / 812;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        evoImageProvider.asset(item.imgAsset,
            fit: BoxFit.cover,
            height: EvoUiUtils()
                .calculateVerticalSpace(context: context, heightPercentage: imageHeightPercentage)),
        Padding(
            padding: const EdgeInsets.all(8.0),
            child: RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                  style: dopNativeTextStyles.bodySmall(color: dopNativeColors.textPassive),
                  children: _buildDescriptions(item.descriptions)),
            ))
      ],
    );
  }

  List<InlineSpan> _buildDescriptions(List<DescriptionInlineSpan> phrases) {
    final List<InlineSpan> widgets = <InlineSpan>[];
    for (final DescriptionInlineSpan phrase in phrases) {
      widgets.add(TextSpan(
          text: phrase.value,
          style: dopNativeTextStyles.bodySmall(
              color: phrase.negative ? dopNativeColors.error : null)));
    }
    return widgets;
  }
}
