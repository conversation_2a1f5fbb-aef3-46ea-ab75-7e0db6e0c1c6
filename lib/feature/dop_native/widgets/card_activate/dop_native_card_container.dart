import 'package:flutter/material.dart';

import '../../resources/dop_native_resources.dart';

class DOPNativeCardContainer extends StatelessWidget {
  const DOPNativeCardContainer({
    required this.child,
    this.backgroundImage,
    super.key,
    this.padding = EdgeInsets.zero,
  });

  final Widget child;
  final DecorationImage? backgroundImage;
  final EdgeInsets padding;

  List<BoxShadow> _getShadow() => <BoxShadow>[
        BoxShadow(
          color: dopNativeColors.dopNativeCardActivateShadow1,
          offset: const Offset(0, 32),
          blurRadius: 48,
          spreadRadius: -28,
        ),
        BoxShadow(
          color: dopNativeColors.dopNativeCardActivateShadow2,
          offset: const Offset(0, 2),
          blurRadius: 72,
          spreadRadius: -4,
        ),
      ];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: dopNativeColors.dopNativeCardActivateBackground,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: dopNativeColors.dopNativeCardActivateBorder),
        image: backgroundImage,
        boxShadow: _getShadow(),
      ),
      child: child,
    );
  }
}
