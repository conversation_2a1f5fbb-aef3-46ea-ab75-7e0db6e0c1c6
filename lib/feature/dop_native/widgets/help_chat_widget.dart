import 'package:flutter/material.dart';

import '../../../resources/resources.dart';
import '../resources/dop_native_images.dart';
import '../resources/dop_native_resources.dart';
import '../resources/dop_native_ui_strings.dart';

class DopAliceChatEntryPointWidget extends StatelessWidget {
  const DopAliceChatEntryPointWidget({
    required this.onTap,
    super.key,
  });

  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
        decoration: BoxDecoration(
          color: dopNativeColors.primary,
          borderRadius: BorderRadius.circular(21),
        ),
        child: Row(
          children: <Widget>[
            evoImageProvider.asset(DOPNativeImages.icHelpChat),
            const SizedBox(width: 6.5),
            Text(
              DOPNativeStrings.dopNativeHelpChat,
              style: dopNativeTextStyles.bodySmall(color: dopNativeColors.textPassive2),
            ),
          ],
        ),
      ),
    );
  }
}
