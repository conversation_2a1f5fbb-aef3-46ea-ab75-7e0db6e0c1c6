import 'package:flutter/material.dart';

import 'base_dop_native_app_bar_widget.dart';

class DOPNativeAppBar extends BaseDOPNativeAppBar {
  const DOPNativeAppBar({
    super.title,
    super.leading,
    super.enableLeading = false,

    /// Default value is false. Because the close button is hidden on AppBar.
    /// Ref: https://trustingsocial1.atlassian.net/browse/EMA-4004
    super.enableCloseButton = false,
    super.onExitDOP,
    super.key,
  });

  @override
  State<DOPNativeAppBar> createState() => _DOPNativeAppBarState();
}

class _DOPNativeAppBarState extends BaseDOPNativeAppBarState<DOPNativeAppBar> {}
