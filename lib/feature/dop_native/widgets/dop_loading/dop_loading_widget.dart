import 'package:flutter/material.dart';

import '../../../../widget/animation/lottie_animation_widget.dart';
import '../../../../widget/evo_overlay/widgets/evo_overlay_container.dart';
import '../../dop_native_constants.dart';
import '../../resources/dop_animations.dart';

class DOPLoadingWidget extends StatelessWidget {
  final Color? maskColor;

  const DOPLoadingWidget({
    super.key,
    this.maskColor,
  });

  @override
  Widget build(BuildContext context) {
    return EvoOverlayContainer(
      overlayChild: const LottieAnimationWidget(
        DOPAnimation.dopLoadingAnimation,
        size: DOPNativeConstants.dopNativeLoadingAnimationSize,
      ),
      maskColor: maskColor,
    );
  }
}
