import 'package:flutter/material.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../base/evo_base_dialog_widget.dart';
import '../widgets/dop_loading/dop_loading_widget.dart';
import 'dop_native_api_error_handler_mixin.dart';

abstract class DOPNativeBaseDialogWidgetState<T extends EvoBaseDialogWidget>
    extends EvoBaseDialogWidgetState<T> with DopNativeApiErrorHandlerMixin {
  final ValueNotifier<bool> _loadingNotifier = ValueNotifier<bool>(false);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        buildDialog(context),
        ValueListenableBuilder<bool>(
          valueListenable: _loadingNotifier,
          builder: (_, bool loading, __) {
            return loading ? const DOPLoadingWidget() : const SizedBox.shrink();
          },
        ),
      ],
    );
  }

  Widget buildDialog(BuildContext context);

  void showDOPLoading() {
    _loadingNotifier.value = true;
  }

  void hideDOPLoading() {
    _loadingNotifier.value = false;
  }

  Future<void> handleEvoApiError(ErrorUIModel? errorUIModel) async {
    handleDopEvoApiError(errorUIModel);
  }
}
