part of 'dop_native_complete_onboarding_cubit.dart';

abstract class DOPNativeCompleteOnboardingState extends BlocState {}

class DOPNativeCompleteOnboardingInitial extends DOPNativeCompleteOnboardingState {
  DOPNativeCompleteOnboardingInitial();
}

class DOPNativeCompleteOnboardingLoading extends DOPNativeCompleteOnboardingState {}

class DOPNativeSignInToEVOSuccess extends DOPNativeCompleteOnboardingState {
  final SignInOtpEntity? entity;
  final String phone;

  DOPNativeSignInToEVOSuccess({
    required this.entity,
    required this.phone,
  });
}

class DOPNativeAlreadySignInEvo extends DOPNativeCompleteOnboardingState {}

class DOPNativeCompleteWithAnotherPhone extends DOPNativeCompleteOnboardingState {}

class DOPNativeSignInToEvoFailed extends DOPNativeCompleteOnboardingState {}
