import 'package:flutter_common_package/base/page_base.dart';

import '../../../widget/countdown/countdown_widget_builder.dart';
import 'dop_native_page_state_base.dart';

mixin DOPNativeCountdownMixin<T extends PageBase> on DOPNativePageStateBase<T> {
  abstract final CountdownController countdownController;

  @override
  void didPushNext() {
    super.didPushNext();
    countdownController.pause();
  }

  @override
  void didPopNext() {
    super.didPopNext();
    countdownController.resume();
  }

  @override
  void onPagePaused() {
    super.onPagePaused();
    countdownController.pause();
  }

  @override
  void onPageResumed() {
    super.onPageResumed();
    countdownController.resume();
  }
}
