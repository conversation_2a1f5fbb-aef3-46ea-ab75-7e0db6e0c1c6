import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import '../../../features/pdf_view/dop_native_pdf_screen.dart';
import '../../../resources/dop_native_pdf_url.dart';
import '../../../resources/dop_native_resources.dart';
import '../../../resources/dop_native_ui_strings.dart';
import '../../../util/dop_functions.dart';
import '../../../widgets/dop_native_checkbox_widget.dart';

class DOPNativeTermAndConditionWidget extends StatelessWidget {
  const DOPNativeTermAndConditionWidget({
    required this.onTap,
    required this.consentValue,
    super.key,
  });

  final bool consentValue;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        DOPNativeCheckBox(
          value: consentValue,
          onTap: onTap,
        ),
        const SizedBox(width: 12),
        Expanded(child: _buildText()),
      ],
    );
  }

  Widget _buildText() {
    return RichText(
      text: TextSpan(
        children: <TextSpan>[
          TextSpan(
            text: '${DOPNativeStrings.dopNativeInputPhoneNumberDescription} ',
            style: dopNativeTextStyles.bodyMedium(dopNativeColors.textPassive),
          ),
          TextSpan(
            text: DOPNativeStrings.dopNativeInputPhoneNumberTermConditionAppDescription,
            style: dopNativeTextStyles.bodyMedium(dopNativeColors.textPrimary),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                dopUtilFunction.openDOPPDFView(DOPNativePDFViewArg(
                    url: getDOPNativePDFURLByFlavor(DOPNativePDFUrl.termConditionUrl)));
              },
          ),
          TextSpan(
            text: ' ${DOPNativeStrings.dopNativeAnd} ',
            style: dopNativeTextStyles.bodyMedium(dopNativeColors.textPassive),
          ),
          TextSpan(
            text: DOPNativeStrings.dopNativeInputPhoneNumberTermConditionPersonalDescription,
            style: dopNativeTextStyles.bodyMedium(dopNativeColors.textPrimary),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                dopUtilFunction.openDOPPDFView(DOPNativePDFViewArg(
                    url: getDOPNativePDFURLByFlavor(DOPNativePDFUrl.dataProtectionUrl)));
              },
          ),
          TextSpan(
            text: ' ${DOPNativeStrings.dopNativeOur}.',
            style: dopNativeTextStyles.bodyMedium(dopNativeColors.textPassive),
          ),
        ],
      ),
    );
  }
}
