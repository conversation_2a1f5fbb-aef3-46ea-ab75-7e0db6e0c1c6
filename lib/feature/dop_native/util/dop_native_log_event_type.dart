import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';

/// Event use for logging data on DOP
class DOPNativeLogEventType extends EventType {
  /// Use when user choose flow on switch flow feature
  /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/**********/CO-9705+TPBank+EVO+Web+DOE+Dive+In+Implement+the+solution+to+allow+user+to+switch+between+different+UI+flows#:~:text=in%20web/webview%20%3F-,Event%20log,-M%C3%ACnh%20c%E1%BA%A7n%20log
  static const DOPNativeLogEventType leadSourceSelected =
      DOPNativeLogEventType._create('register.lead_source_selected');

  const DOPNativeLogEventType._create(super.name);
}

/// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/**********/CO-9705+TPBank+EVO+Web+DOE+Dive+In+Implement+the+solution+to+allow+user+to+switch+between+different+UI+flows#:~:text=in%20web/webview%20%3F-,Event%20log,-M%C3%ACnh%20c%E1%BA%A7n%20log
enum DOPSwitchFlowType {
  onboardingApp('onboarding_app'),
  existingApp('existing_app');

  final String value;

  const DOPSwitchFlowType(this.value);
}
