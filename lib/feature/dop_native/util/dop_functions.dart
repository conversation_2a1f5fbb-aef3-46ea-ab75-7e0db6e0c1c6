import 'dart:ui';

import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/intl.dart';
import 'package:flutter_common_package/common_package/url_launcher.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/button_styles.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../../data/constants.dart';
import '../../../data/response/dop_native/dop_native_application_state_entity.dart';
import '../../../data/response/dop_native/dop_native_cif_info_entity.dart';
import '../../../model/evo_dialog_id.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/evo_flutter_wrapper.dart';
import '../../../util/extension.dart';
import '../../../util/functions.dart';
import '../../../util/ui_utils/evo_dialog_helper.dart';
import '../../../util/url_launcher_uri_wrapper.dart';
import '../../../widget/animation/lottie_animation_widget.dart';
import '../../../widget/evo_dialog/dialog_confirm.dart';
import '../../../widget/pdf_view/pdf_view_widget.dart';
import '../../sharing/sharing_feature.dart';
import '../../user_journey/user_journey_handler.dart';
import '../dialogs/input_phone_number/dop_native_input_phone_number_dialog.dart';
import '../dialogs/setup_pos_limit/dop_native_setup_pos_limit_dialog.dart';
import '../dop_native_constants.dart';
import '../features/cif_confirm/dop_native_cif_confirm_case.dart';
import '../features/cif_confirm/dop_native_cif_confirm_ui_version.dart';
import '../features/pdf_view/dop_native_pdf_screen.dart';
import '../features/web_view/dop_native_webview_screen.dart';
import '../models/dop_native_dialog_id.dart';
import '../resources/dop_animations.dart';
import '../resources/dop_native_images.dart';
import '../resources/dop_native_resources.dart';
import '../resources/dop_native_ui_strings.dart';
import '../resources/dop_native_website_url.dart';
import '../widgets/dop_loading/dop_loading_widget.dart';
import 'validation/dop_native_validation_case_model.dart';

final DOPUtilFunctions dopUtilFunction = getIt.get<DOPUtilFunctions>();

class DOPUtilFunctions {
  Map<String, String?> getDOPNativeAccessTokenHeader(String? accessToken) => <String, String?>{
        HeaderKey.dopNativeAuthorization:
            accessToken?.isNotEmpty == true ? 'Bearer $accessToken' : null
      };

  void setDOPNativeAccessTokenHeader(String? accessToken) {
    final Map<String, String?> headers = getDOPNativeAccessTokenHeader(accessToken);
    final CommonHttpClient dopHttpClient =
        getIt.get<CommonHttpClient>(instanceName: getItInstanceNameForDOPNative);
    dopHttpClient.addHeaders(headers);
  }

  List<T> queryDiacriticsFilter<T>({
    required String query,
    required List<T> data,
    required String Function(T data) filterBy,
  }) {
    String lowerCaseNonDiacritics(String str) => str.removeDiacritics.toLowerCase();
    if (query.isEmpty) {
      return data;
    }
    final List<T> results = <T>[];
    for (final T item in data) {
      final String indexed = lowerCaseNonDiacritics(filterBy(item));
      if (indexed.contains(lowerCaseNonDiacritics(query))) {
        results.add(item);
      }
    }
    return results;
  }

  void clearDOPNativeAccessTokenHeader() {
    final CommonHttpClient dopHttpClient =
        getIt.get<CommonHttpClient>(instanceName: getItInstanceNameForDOPNative);
    dopHttpClient.removeHeader(HeaderKey.dopNativeAuthorization);
  }

  void clearDOPNativeData() {
    getIt.get<AppState>().dopNativeState.clear();
    clearDOPNativeAccessTokenHeader();
  }

  void clearDOPSessionData() {
    final AppState appState = getIt.get<AppState>();
    appState.dopNativeState.clearDOPSessionData();
  }

  Future<void> showDialogConfirm({
    required String textPositive,
    required DOPNativeDialogId dialogId,
    String? content,
    String? title,
    String? textNegative,
    Widget? footer,
    VoidCallback? onClickPositive,
    VoidCallback? onClickNegative,
    Widget? imageHeader,
    bool isDismissible = true,
    ButtonStyle? positiveButtonStyle,
    ButtonStyle? negativeButtonStyle,
    TextStyle? titleTextStyle,
    TextStyle? contentTextStyle,
    bool isShowButtonClose = false,
    TextAlign? titleTextAlign,
    TextAlign? contentTextAlign,
    Key? key,
    ButtonListOrientation? buttonListOrientation,
    int? positiveDelayInSeconds,
    Map<String, dynamic>? loggingEventOnShowMetaData,
    Map<String, dynamic>? loggingEventMetaData,
  }) async {
    return await evoFlutterWrapper.showDialog<void>(
      barrierDismissible: isDismissible,
      builder: (_) => PopScope(
        canPop: isDismissible,
        child: EvoDialogConfirm(
          key: key,
          content: content,
          textPositive: textPositive,
          title: title,
          textNegative: textNegative,
          footer: footer,
          onClickPositive: onClickPositive,
          onClickNegative: onClickNegative,
          imageHeader: imageHeader,
          positiveButtonStyle:
              positiveButtonStyle ?? getIt.get<CommonButtonStyles>().primary(ButtonSize.xLarge),
          negativeButtonStyle:
              negativeButtonStyle ?? getIt.get<CommonButtonStyles>().tertiary(ButtonSize.xLarge),
          titleTextStyle: titleTextStyle,
          contentTextStyle: contentTextStyle,
          dialogId: dialogId.id,
          isShowButtonClose: isShowButtonClose,
          titleTextAlign: titleTextAlign,
          contentTextAlign: contentTextAlign,
          buttonListOrientation: buttonListOrientation,
          positiveDelayOverlay: positiveDelayInSeconds != null
              ? (_) => const Padding(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    child: LottieAnimationWidget(
                      DOPAnimation.dopButtonDelayAnimation,
                      fit: BoxFit.contain,
                    ),
                  )
              : null,
          positiveDelayInSeconds: positiveDelayInSeconds,
          loggingEventOnShowMetaData: loggingEventOnShowMetaData,
          loggingEventMetaData: loggingEventMetaData,
        ),
      ),
    );
  }

  bool isVietnameseCitizenIdCardBefore2016(String? idCard) {
    if (idCard == null) {
      return false;
    }

    return CommonValidator().validateVietnameseCitizenIdCard(idCard: idCard) ==
        VietnameseCitizenIdCardType.before_2016;
  }

  bool isVietnameseCitizenIdCardAfter2016(String? idCard) {
    if (idCard == null) {
      return false;
    }

    return CommonValidator().validateVietnameseCitizenIdCard(idCard: idCard) ==
        VietnameseCitizenIdCardType.after_2016;
  }

  bool isValidDOPAddress({required String address}) {
    const String patternDopAddress = r'^[\w\sÀ-ỹ-.,_/]+$';

    // Refer: https://docs.google.com/spreadsheets/d/1aCdNd3wrExqfgvFiz378txeLr4SWaYmD/edit#gid=559505482
    final bool addressValid = RegExp(patternDopAddress).hasMatch(address);
    return addressValid;
  }

  String? formatDOPBirthday(String? birthday) {
    if (birthday == null || birthday.isEmpty) {
      return null;
    }

    final DateTime dateTime = DateTime.parse(birthday);

    final String formattedDate = DateFormat('dd/MM/yyyy').format(dateTime);

    return formattedDate;
  }

  double? formatIncomeAmount({required String text}) {
    final String formatted = text.replaceAll(DOPNativeConstants.incomeDecimalSeparator, '.');
    return double.tryParse(formatted);
  }

  int? convertIncome(String? text) {
    if (text == null) {
      return null;
    }
    final double? value = dopUtilFunction.formatIncomeAmount(
      text: text,
    );
    if (value == null) {
      return null;
    }
    return (value * 1e6).toInt();
  }

  IncomeCase getIncomeCase(String text) {
    if (text.isEmpty) {
      return IncomeCase.empty;
    }
    final double? value = formatIncomeAmount(
      text: text,
    );
    if (value == null) {
      return IncomeCase.invalid;
    }
    if (value <= DOPNativeConstants.incomeThreshold) {
      return IncomeCase.tooSmall;
    }
    if (value > DOPNativeConstants.maxIncomeInBillions) {
      return IncomeCase.exceed;
    }
    return IncomeCase.valid;
  }

  AddressCase getAddressCase(String address) {
    if (address.isEmpty) {
      return AddressCase.empty;
    }

    if (address.length > DOPNativeConstants.dopNativeMaxAddressLength) {
      return AddressCase.tooLong;
    }

    if (isValidDOPAddress(address: address)) {
      return AddressCase.valid;
    }

    return AddressCase.hasSpecialCharacter;
  }

  PhoneCase getPhoneCase(String phone) {
    if (phone.isEmpty) {
      return PhoneCase.empty;
    }

    if (isPhoneNumberValid(phone)) {
      return PhoneCase.valid;
    }

    return PhoneCase.invalid;
  }

  bool validateEmail({required String email}) {
    return CommonValidator().isValidEmailAddress(email: email);
  }

  CompanyNameCase getCompanyNameCase(String companyName) {
    if (companyName.isEmpty) {
      return CompanyNameCase.empty;
    }

    if (companyName.length > DOPNativeConstants.dopNativeMaxCompanyNameLength) {
      return CompanyNameCase.tooLong;
    }

    return CompanyNameCase.valid;
  }

  PosLimitCase validatePOSLimit({
    required int posLimitInputted,
    required int posLimitAllowed,
  }) {
    if (posLimitInputted > posLimitAllowed || posLimitInputted <= 0) {
      return PosLimitCase.invalid;
    }

    return PosLimitCase.valid;
  }

  Future<bool> launchUrl(
    Uri url, {
    LaunchMode mode = LaunchMode.platformDefault,
    WebViewConfiguration webViewConfiguration = const WebViewConfiguration(),
    String? webOnlyWindowName,
  }) {
    return urlLauncherWrapper.launchUrl(
      url,
      mode: mode,
      webViewConfiguration: webViewConfiguration,
      webOnlyWindowName: webOnlyWindowName,
    );
  }

  Future<void> openDOPPDFView(DOPNativePDFViewArg webViewArg) async {
    DOPNativePDFViewScreen.pushNamed(arg: webViewArg);
  }

  Future<void> openDOPWebView(DOPNativeWebViewArg webViewArg) async {
    DOPNativeWebViewScreen.pushNamed(arg: webViewArg);
  }

  bool isPhoneNumberValid(String phoneNumber) {
    // regex for DOP Native only
    // Ref: https://trustingsocial1.atlassian.net/browse/EMA-2591
    final RegExp regExp = RegExp(r'^0([35789])(\d{8})$');
    return regExp.hasMatch(phoneNumber);
  }

  void showBlurredTooltip({
    required BuildContext context,
    required GlobalKey targetKey,
    required String message,
    double left = 90,
    double right = 20,
    double bottom = 12,
  }) {
    final OverlayState overlay = Overlay.of(context);
    final RenderBox? renderBox = targetKey.currentContext?.findRenderObject() as RenderBox?;
    final Offset targetOffset = renderBox?.localToGlobal(Offset.zero) ?? Offset.zero;
    final double tooltipBottom = MediaQuery.of(context).size.height - targetOffset.dy + bottom;
    OverlayEntry? overlayEntry;
    overlayEntry = OverlayEntry(
      builder: (BuildContext context) {
        return Stack(
          children: <Widget>[
            GestureDetector(
              onTap: () {
                overlayEntry?.remove();
                overlayEntry = null;
              },
              key: Key('remove_blur_button'), //key used in widget test
              child: Container(
                color: Colors.transparent,
                constraints: const BoxConstraints.expand(),
              ),
            ),
            Positioned(
              left: left,
              right: right,
              bottom: tooltipBottom,
              child: ClipRect(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 6, sigmaY: 6),
                  child: Container(
                    padding: const EdgeInsets.all(15),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(6),
                      color: dopNativeColors.dopNativeTooltipBackground,
                    ),
                    child: Text(
                      message,
                      style: dopNativeTextStyles
                          .h200(
                            color: dopNativeColors.textPassive2,
                          )
                          .copyWith(height: 1.4),
                    ), // Replace with your custom widget if needed
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );

    if (overlayEntry != null) {
      overlay.insert(overlayEntry!);
    }
    Future<void>.delayed(const Duration(seconds: 3), () {
      if (overlayEntry != null) {
        overlayEntry?.remove();
        overlayEntry = null;
      }
    });
  }

  void openOtherProductWeb() {
    openDOPWebView(
      DOPNativeWebViewArg(url: DOPNativeWebsiteUrl.otherProductWebUrl),
    );
  }

  void openTPBAppLink() {
    launchUrl(
      Uri.parse(
        DOPNativeWebsiteUrl.tpbAppLink,
      ),
    );
  }

  double getPaddingBottom(BuildContext context) {
    return MediaQuery.of(context).viewPadding.bottom == 0 ? 32 : 0;
  }

  /// [onFailed] will be called when the POS limit dialog cannot be opened
  /// Because the POS limit number is null
  void openPOSLimitDialog({required void Function(String) onFailed}) {
    final AppState appState = getIt.get<AppState>();
    final int? posLimitNumber = appState.dopNativeState.posLimitNumber;
    if (posLimitNumber == null) {
      onFailed.call('openPOSLimitDialog: POS limit number is null');
      return;
    }

    DOPNativeSetupPosLimitDialog.show(posLimitNumber: posLimitNumber);
  }

  int? getAmountFromStr(String amount) {
    return evoUtilFunction.getAmountFromStr(amount);
  }

  /// date format of eKYC is dd/MM/yyyy
  bool isCorrectDateEKYCFormat({
    required String? dateInputted,
  }) {
    if (dateInputted == null || dateInputted.isEmpty) {
      return false;
    }

    final RegExp regExp = RegExp(r'^\d{2}/\d{2}/\d{4}$');
    if (!regExp.hasMatch(dateInputted)) {
      return false;
    }

    // Check the date is valid
    // E.g: 12/13/2022 -> false
    //      30/02/2021 -> false
    //      0/01/2021  -> false
    try {
      DateFormat(DOPNativeConstants.tvNfcSdkDateFormat).parseStrict(dateInputted);
      return true;
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      return false;
    }
  }

  /// date format of DOP is yyyy-MM-dd
  bool isCorrectDateDOPFormat({
    required String? dateInputted,
  }) {
    if (dateInputted == null || dateInputted.isEmpty) {
      return false;
    }

    final RegExp regExp = RegExp(r'^\d{4}-\d{2}-\d{2}$');
    if (!regExp.hasMatch(dateInputted)) {
      return false;
    }

    // Check the date is valid
    // E.g: 2022-13-12 -> false
    //      2021-02-30 -> false
    //      2021-01-0  -> false
    try {
      DateFormat(DOPNativeConstants.dopDateFormat).parseStrict(dateInputted);
      return true;
      // ignore: avoid_catches_without_on_clauses
    } catch (e) {
      return false;
    }
  }

  /// get device platform
  String getDevicePlatform() {
    return getIt.get<DevicePlatform>().isAndroid()
        ? DopDevicePlatformConstant.android
        : DopDevicePlatformConstant.ios;
  }

  String? convertDOPToEKYCDateFormat(String? inputDate) {
    if (inputDate == null) {
      return null;
    }

    try {
      final DateTime date = DateTime.parse(inputDate);
      return DateFormat(DOPNativeConstants.tvNfcSdkDateFormat).format(date);
      // ignore: avoid_catches_without_on_clauses
    } catch (exception) {
      return null;
    }
  }

  /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3565223937/CIF+Flow#In-state-appraising.cif
  /// at section "Logic show wording"
  CifCase getCifCase(
      {required DOPNativeApplicationStateEntity? dopApplicationState,
      DOPNativeCifInfoEntity? cifInfo}) {
    final String? uiVersion = dopApplicationState?.uiVersion;
    if (cifInfo?.useNewCif != null) {
      return uiVersion == DOPNativeCifConfirmUIVersion.difNationId.value
          ? CifCase.difNationIdWithCifInfo
          : CifCase.otherDifWithCifInfo;
    }

    if (uiVersion == DOPNativeCifConfirmUIVersion.difNationId.value) {
      return CifCase.difNationIdWithoutCifInfo;
    }

    if (uiVersion == DOPNativeCifConfirmUIVersion.difPhone.value) {
      return CifCase.difPhone;
    }

    if (uiVersion == DOPNativeCifConfirmUIVersion.difCif.value) {
      return CifCase.difCif;
    }

    if (uiVersion == DOPNativeCifConfirmUIVersion.difInfo.value) {
      return CifCase.difInfo;
    }

    if (uiVersion == DOPNativeCifConfirmUIVersion.cifReopen.value) {
      return CifCase.cifReopen;
    }

    return CifCase.unknown;
  }

  bool isMWGSalesmanIDValid(String code) {
    // regex for MWG salesman id
    // Ref: https://trustingsocial1.atlassian.net/browse/EMA-3039
    final RegExp regExp = RegExp(r'^\d{4,6}$');
    return regExp.hasMatch(code);
  }

  bool isViettelStoreSalesmanIDValid(String code) {
    // regex for Viettel store salesman id
    // Ref: https://trustingsocial1.atlassian.net/browse/EMA-3039
    final RegExp regExp = RegExp(r'^VST[a-z0-9]{1,6}$', caseSensitive: false);
    return regExp.hasMatch(code);
  }

  bool isFRTSalesmanIDValid(String code) {
    // regex for FRT store salesman id
    // Ref: https://trustingsocial1.atlassian.net/browse/EMA-3039
    final RegExp regExp = RegExp(r'^\d{1,6}$');
    return regExp.hasMatch(code);
  }

  Future<void> showBottomSheetPdf({
    required String url,
    String? title,
    Widget loadingWidget = const DOPLoadingWidget(),
    //loading widget is passed from param to make the function testable
    //because the infinite loading widget will make pumpAndSettle timeout
  }) {
    final BuildContext? context = navigatorContext;
    if (context == null) {
      return Future<void>.value();
    }
    return showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      enableDrag: false,
      backgroundColor: Colors.white,
      constraints: BoxConstraints(
        maxHeight: context.screenHeight -
            DOPNativeConstants.dopNativeAppBarHeight -
            context.screenPadding.top,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (_) {
        return Column(
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.only(
                top: 10,
                left: 20,
                bottom: 4,
                right: 10,
              ),
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      title ?? '',
                      style: dopNativeTextStyles.h400(),
                    ),
                  ),
                  GestureDetector(
                    onTap: () => context.pop(),
                    key: Key('close_pdf_bottom_sheet'), //key used in widget test
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      color: Colors.transparent,
                      child: evoImageProvider.asset(
                        DOPNativeImages.icCloseFlat,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: PdfViewWidget(
                url: url,
                loadingWidget: loadingWidget,
              ),
            ),
          ],
        );
      },
    );
  }

  /// Initiates sharing of the provided data on the user phone
  /// Eg. share via sms, chat app, bluetooth...
  Future<void> sharingData(String? data) {
    final SharingFeature sharingFeature = getIt.get<SharingFeature>();

    if (data == null) {
      return Future<void>.value();
    }
    return sharingFeature.share(data);
  }

  void showDOPInputPhoneDialog({bool? autoRequestOTP, String? phoneNumber}) {
    DOPNativeInputPhoneNumberDialog.show(
      autoRequestOTP: autoRequestOTP,
      phoneNumber: phoneNumber,
    );
  }

  Future<void> showOpenAppSettingCameraDialog({
    required String title,
    required String content,
    required String textPositive,
    required String textNegative,
  }) {
    return EvoDialogHelper().showDialogConfirm(
      onClickPositive: () async {
        AppSettings.openAppSettings();
        await UserJourneyHandler().saveInfoDOPNativeJourney();
        navigatorContext?.pop();
      },
      title: title,
      content: content,
      textPositive: textPositive,
      textNegative: textNegative,
      dialogId: EvoDialogId.activateCameraPermissionDialog,
    );
  }

  String formatDateTimeToHourMinute(DateTime time) {
    final String hour = DateFormat('HH').format(time);
    final String minute = DateFormat('mm').format(time);

    return '$hour ${DOPNativeStrings.dopNativeHour} $minute ${DOPNativeStrings.dopNativeMinute}';
  }
}
