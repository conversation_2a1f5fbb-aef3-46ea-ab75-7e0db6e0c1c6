import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../dop_native_constants.dart';
import '../../../resources/dop_native_ui_strings.dart';
import '../../dop_functions.dart';
import '../dop_native_validation_case_model.dart';

part 'dop_native_validation_utils_state.dart';

// Refer: https://docs.google.com/spreadsheets/d/1aCdNd3wrExqfgvFiz378txeLr4SWaYmD/edit#gid=559505482
class DOPNativeValidationUtilsCubit extends CommonCubit<DOPNativeValidationUtilsState> {
  DOPNativeValidationUtilsCubit() : super(OPNativeValidationUtilsInitial());

  bool validateDOPNativeAddress(String address) {
    final AddressCase addressCase = dopUtilFunction.getAddressCase(address);
    if (addressCase == AddressCase.empty) {
      emit(DOPNativeAddressFailed(
        errorMsg: DOPNativeStrings.dopNativePermanentAddressEmptyFailed,
      ));
      return false;
    }

    if (addressCase == AddressCase.tooLong) {
      emit(DOPNativeAddressFailed(
          errorMsg: DOPNativeStrings.dopNativeInputPermanentAddressTooLongFailed
              .replaceVariableByValue(
                  <String>[DOPNativeConstants.dopNativeMaxAddressLength.toString()])));
      return false;
    }

    if (addressCase == AddressCase.hasSpecialCharacter) {
      emit(DOPNativeAddressFailed(
          errorMsg: DOPNativeStrings.dopNativeInputPermanentAddressSpecialCharacterFailed));
      return false;
    }

    emit(DOPNativeAddressValid(address: address));
    return true;
  }

  bool validateDOPNativeIncome(String income) {
    final IncomeCase incomeInCase = dopUtilFunction.getIncomeCase(income);
    switch (incomeInCase) {
      case IncomeCase.empty:
        emit(DOPNativeIncomeFailed(errorMsg: DOPNativeStrings.dopNativeIncomeEmptyErrorMsg));
        return false;
      case IncomeCase.tooSmall:
        final String msg = DOPNativeStrings.dopNativeIncomeTooSmallErrorMsg
            .replaceVariableByValue(<String>[DOPNativeConstants.incomeThreshold.toString()]);
        emit(DOPNativeIncomeFailed(errorMsg: msg));
        return false;
      case IncomeCase.exceed:
        final String exceedIncomeLimit =
            '${DOPNativeStrings.dopNativeIncomeExceedCapPrefix} ${DOPNativeConstants.maxIncomeInBillions.toInt()} ${DOPNativeStrings.dopNativeIncomeExceedCapPostfix}';

        emit(DOPNativeIncomeFailed(errorMsg: exceedIncomeLimit));
        return false;
      case IncomeCase.invalid:
        emit(DOPNativeIncomeFailed(errorMsg: DOPNativeStrings.dopNativeIncomeInvalidErrorMsg));
        return false;
      case IncomeCase.valid:
        emit(DOPNativeIncomeValid(income: income));
        return true;
    }
  }

  bool validateDOPNativeEmail(String email) {
    if (email.isEmpty) {
      emit(DOPNativeEmailFailed(errorMsg: DOPNativeStrings.dopNativeEmailEmptyErrorMsg));
      return false;
    }

    final bool isValid = dopUtilFunction.validateEmail(email: email);
    if (!isValid) {
      emit(DOPNativeEmailFailed(errorMsg: DOPNativeStrings.dopNativeEmailInvalidErrorMsg));
      return false;
    }

    emit(DOPNativeEmailValid(email: email));
    return true;
  }

  bool validateEmployment(String text) {
    if (text.isEmpty) {
      emit(DOPNativeEmploymentFailed(
        errorMsg: DOPNativeStrings.dopNativeEmploymentEmptyErrorMsg,
      ));
      return false;
    }

    emit(DOPNativeEmploymentValid(employment: text));
    return true;
  }

  bool validateResidenceAddress(String? residenceAddress) {
    if (residenceAddress == null || residenceAddress.isEmpty) {
      emit(DOPNativeResidenceAddressFailed(
          errorMsg: DOPNativeStrings.dopNativeResidenceAddressEmptyFailed));
      return false;
    }

    emit(DOPNativeResidenceAddressValid(residenceAddress: residenceAddress));
    return true;
  }

  bool validateCompanyName({required String companyName}) {
    final CompanyNameCase companyNameCase = dopUtilFunction.getCompanyNameCase(companyName);
    if (companyNameCase == CompanyNameCase.empty) {
      emit(DOPNativeCompanyNameFailed(errorMsg: DOPNativeStrings.dopNativeCompanyNameEmptyFailed));
      return false;
    }

    if (companyNameCase == CompanyNameCase.tooLong) {
      emit(DOPNativeCompanyNameFailed(
          errorMsg: DOPNativeStrings.dopNativeCompanyNameTooLongFailed.replaceVariableByValue(
              <String>[DOPNativeConstants.dopNativeMaxCompanyNameLength.toString()])));
      return false;
    }

    emit(DOPNativeCompanyNameValid(companyName: companyName));
    return true;
  }

  bool validateDOPNativeCompanyAddress(String companyAddress) {
    final AddressCase addressCase = dopUtilFunction.getAddressCase(companyAddress);
    if (addressCase == AddressCase.empty) {
      emit(DOPNativeCompanyAddressFailed(
          errorMsg: DOPNativeStrings.dopNativePermanentAddressEmptyFailed));
      return false;
    }

    if (addressCase == AddressCase.tooLong) {
      emit(DOPNativeCompanyAddressFailed(
          errorMsg: DOPNativeStrings.dopNativeInputPermanentAddressTooLongFailed
              .replaceVariableByValue(
                  <String>[DOPNativeConstants.dopNativeMaxAddressLength.toString()])));
      return false;
    }

    if (addressCase == AddressCase.hasSpecialCharacter) {
      emit(DOPNativeCompanyAddressFailed(
          errorMsg: DOPNativeStrings.dopNativeInputPermanentAddressSpecialCharacterFailed));
      return false;
    }

    emit(DOPNativeCompanyAddressValid(companyAddress: companyAddress));
    return true;
  }

  bool validatePOSLimit({
    required String posLimitInputted,
    required int posLimitAllowed,
  }) {
    if (posLimitInputted.isEmpty) {
      emit(DOPNativePOSLimitFailed(isPosLimitInputtedEmpty: true));
      return false;
    }

    final int posLimitNumber = dopUtilFunction.getAmountFromStr(posLimitInputted) ?? 0;
    final PosLimitCase posLimitCase = dopUtilFunction.validatePOSLimit(
      posLimitInputted: posLimitNumber,
      posLimitAllowed: posLimitAllowed,
    );

    if (posLimitCase == PosLimitCase.invalid) {
      emit(DOPNativePOSLimitFailed());
      return false;
    }

    emit(DOPNativePOSLimitValid(posLimitNumber: posLimitNumber));
    return true;
  }
}
