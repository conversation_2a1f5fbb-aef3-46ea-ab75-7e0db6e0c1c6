enum MockDOPNativeCardStatus {
  getCardStatusOnlineMerchantSuccess('dop_native_get_card_status_online_merchant_success.json'),
  getCardStatusOfflineMerchantSuccess('dop_native_get_card_status_offline_merchant_success.json'),
  getCardStatusBlocked('dop_native_get_card_status_blocked.json'),
  getCardStatusContainNullField('dop_native_get_card_status_contain_null_field.json'),

  /// underwriting in progress
  underwritingInProgressInvalidCase('dop_native_underwriting_in_progress_invalid_case.json'),

  /// underwriting card issued
  underwritingCardActivated('dop_native_underwriting_card_activated_app.json'),
  underwritingCardIssuedActiveCard('dop_native_underwriting_card_issued_active_card.json'),
  underwritingCardIssuedDownloadApp('dop_native_underwriting_card_issued_download_app.json'),
  underwritingCardIssuedMockFail('dop_native_underwriting_card_issued_mock_fail.json'),

  /// card status information
  getCardStatusActive('dop_native_get_card_status_activated.json'),
  getCardStatusFailed('dop_native_get_card_status_active_failed.json'),
  getCardStatusRetry('dop_native_get_card_status_active_retry.json'),
  getCardStatusPOSRetry('dop_native_get_card_status_pos_retry.json'),
  getCardStatusPOSFailed('dop_native_get_card_status_pos_failed.json');

  final String value;

  const MockDOPNativeCardStatus(this.value);
}

String getMockDOPNativeCardStatus(MockDOPNativeCardStatus mockCase) {
  return mockCase.value;
}
