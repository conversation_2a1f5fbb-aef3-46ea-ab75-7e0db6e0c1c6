import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../data/response/dop_native/dop_native_card_status_entity.dart';

abstract class DOPNativeCardStatusState implements BlocState {}

class GetCardStatusInitial extends DOPNativeCardStatusState {}

class GetCardStatusLoading extends DOPNativeCardStatusState {}

class GetCardStatusFailure extends DOPNativeCardStatusState {
  final ErrorUIModel error;

  GetCardStatusFailure(this.error);
}

class GetCardStatusBlocked extends DOPNativeCardStatusState {}

/// Underwriting sub flow state
class UnderwritingOfflineMerchantAndLinkCard extends DOPNativeCardStatusState {
  final DOPNativeCardStatusEntity cardStatus;

  UnderwritingOfflineMerchantAndLinkCard(this.cardStatus);
}

class UnderwritingNoneOfflineMerchantOrNoneLinkCard extends DOPNativeCardStatusState {}

class UnderwritingCardIssuedCanActivateCard extends DOPNativeCardStatusState {}

class UnderwritingCardIssuedCannotActivateCard extends DOPNativeCardStatusState {}

class UnderwritingCardIssuedCardActivated extends DOPNativeCardStatusState {}

/// Card status information
class CardStatusInformationActivated extends DOPNativeCardStatusState {}

class CardStatusInformationActivatedRetryPosLimit extends DOPNativeCardStatusState {}

class CardStatusInformationActivatedPosFailed extends DOPNativeCardStatusState {}

class CardStatusInformationRetry extends DOPNativeCardStatusState {}

class CardStatusInformationFail extends DOPNativeCardStatusState {}

/// Underwriting card status
class UnderwritingCardStatusCardActivated extends DOPNativeCardStatusState {}
