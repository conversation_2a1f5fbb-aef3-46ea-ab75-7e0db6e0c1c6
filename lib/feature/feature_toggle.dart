import '../data/response/remote_config_feature_toggle_entity.dart';

enum DeleteAccountFeatureVersion {
  /// version 1: Disable account - implement delete account flow in profile screen
  /// https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/**********/4+-+Manage+User+profile+Settings+T+C+Support#1.1---View-detail-user%E2%80%99s-informations
  version_1,

  /// version 2 : Permanent deletion - remove delete account flow from profile screen & adding new flow to setting screen.
  /// https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/**********/Delete+account+with+auto+unlink+by+system+not+manual+unlink+by+user
  version_2,
}

enum CardStatusFeatureVersion {
  /// version 1 : without manual link card, with many status of the card
  /// https://trustingsocial1.atlassian.net/wiki/pages/viewpage.action?pageId=**********&pageVersion=1
  version_1,

  /// version 2 : without manual link card, only show the card
  /// https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/**********/View+User+s+EVO+Card+Image+View+Card+Status+version+2
  version_2,

  /// version 3 : with manual link card. Does not show the card, but show Credit Limit Amount, and CTA to link & activate card
  /// https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/**********/Card+Status+Tracking+-+General+rules+to+display+on+EVO+app
  version_3,

  /// Temporary disable card status feature, because card status not sync, will remove in the future
  /// Refer ticket: https://trustingsocial1.atlassian.net/browse/EMA-5226
  version_4,

  /// version 5 : Does not show the card, but show Credit Limit Amount, and hide CTA to link & activate card
  /// Refer ticket: https://trustingsocial1.atlassian.net/browse/EMA-5401
  version_5,
}

enum QrCodeFeatureVersion {
  /// https://trustingsocial1.atlassian.net/browse/EMA-1276

  /// version 1: only support static QR
  /// https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/3250880745/Scan+Static+QR+Code+to+Create+Order
  version_1,

  /// version 2: support static Qr & dynamic QR
  /// https://trustingsocial1.atlassian.net/wiki/spaces/EVO/pages/**********/Checkout+with+Dynamic+QR#Use-EVO-App-scan-EVO-Dynamic-QR
  version_2,
}

enum EmiFeatureVersion {
  /// don't support EMI feature for payment
  notSupported,

  /// support EMI feature for payment
  /// https://trustingsocial1.atlassian.net/wiki/spaces/EVO/pages/**********/EVO+EMI
  version_1,
}

enum AliceVersion {
  version_2,
  version_3,
  none,
}

enum NfcSdkProvider {
  /// before v3.0.0 use TrustVision SDK (with NFC)
  trustVision,

  /// since v3.0.0 use FPT SDK to Adapt with Circular 2345 & 18 NHNN
  /// https://trustingsocial1.atlassian.net/browse/EMA-3403
  /// FPT SDK + TrustVision SDK (non-NFC)
  fpt,
}

enum FacialVerificationVersion {
  /// Not enable facial-verification for Sign-In, just Link-Card (FaceOTP)
  version_1,

  /// FaceOTP for Sign-in and Link-Card
  version_2,

  /// FaceAuth for Sign-in and Link-Card
  version_3,
}

enum TransactionHistoryVersion {
  /// Separate: Instant Cashback and Transaction History
  version_1,

  /// Cashback and Transaction History as a separate record in Transaction History List
  version_2,
}

class FeatureToggle {
  ///true: show UI
  ///false: hide/remove
  ///in MPV1 Reminder removed so [reminderVisibility] by default is false
  final bool _reminderVisibility = false;

  bool get reminderVisibility => _reminderVisibility;

  final bool _enableTransactionNotificationFeature = false;

  bool get enableTransactionNotificationFeature => _enableTransactionNotificationFeature;

  /// Card status feature toggle to control which version of card status feature is enabled
  final CardStatusFeatureVersion _enableCardStatusFeatureVersion =
      CardStatusFeatureVersion.version_5;

  CardStatusFeatureVersion get enableCardStatusFeatureVersion => _enableCardStatusFeatureVersion;

  /// Delete Account feature toggle to control which version of delete account feature is enabled
  final DeleteAccountFeatureVersion _enableDeleteAccountFeatureVersion =
      DeleteAccountFeatureVersion.version_2;

  DeleteAccountFeatureVersion get enableDeleteAccountFeatureVersion =>
      _enableDeleteAccountFeatureVersion;

  /// Enable/disable scan dynamic QR code feature
  /// https://trustingsocial1.atlassian.net/browse/EMA-1276
  final QrCodeFeatureVersion _enableDynamicQrCodeFeature = QrCodeFeatureVersion.version_2;

  QrCodeFeatureVersion get enableDynamicQrCodeFeature => _enableDynamicQrCodeFeature;

  /// Enable/Disable EMI feature
  final EmiFeatureVersion _enableEmiFeatureVersion = EmiFeatureVersion.version_1;

  EmiFeatureVersion get enableEmiFeatureVersion => _enableEmiFeatureVersion;

  /// Enable/disable pre-apply voucher feature
  final bool _enablePreApplyVoucher = true;

  bool get enablePreApplyVoucher => _enablePreApplyVoucher;

  /// Enable which version Evo Alice: Chatwoot, V3 or none (disable)
  /// https://trustingsocial1.atlassian.net/wiki/spaces/EVO/pages/**********/Alice+in+EVO+Product+Discovery+integration#Open-Chat-to-EVO
  /// Alice V3 refer ticket: https://trustingsocial1.atlassian.net/browse/EMA-3994
  final AliceVersion _enableEvoAliceChat = AliceVersion.version_3;

  AliceVersion get enableEvoAliceChat => _enableEvoAliceChat;

  /// Enable/disable event tracking feature
  final bool _enableEventTrackingFeature = true;

  bool get enableEventTrackingFeature => _enableEventTrackingFeature;

  /// Enable/disable DOP native feature
  final bool _enableDOPNativeFeature = true;

  bool get enableDOPNativeFeature => _enableDOPNativeFeature;

  final FacialVerificationVersion _facialVerificationVersion = FacialVerificationVersion.version_1;

  FacialVerificationVersion get facialVerificationVersion => _facialVerificationVersion;

  /// Enable/Disable Remote-Config Feature
  /// https://trustingsocial1.atlassian.net/browse/EMA-4098
  final bool _enableRemoteConfigFeature = false;

  bool get enableRemoteConfigFeature => _enableRemoteConfigFeature;

  /// Enable/Disable EMI Management Feature
  /// https://trustingsocial1.atlassian.net/browse/EMA-2428
  final bool _enableEmiManagementFeature = true;

  bool get enableEmiManagementFeature => _enableEmiManagementFeature;

  /// Switch NFC SDK Provider
  /// how to toggle: -> search and update all file contain keyword "Switch NFC SDK Provider"
  /// https://trustingsocial1.atlassian.net/browse/EMA-3403
  final NfcSdkProvider _nfcSdkProvider = NfcSdkProvider.fpt;

  NfcSdkProvider get nfcSdkProvider => _nfcSdkProvider;

  void mapFromRemoteConfigEntity(RemoteConfigFeatureToggleEntity entity) {
    /// updated here if any new feature toggle is used
  }

  /// Activate POS limit on EVO Payment flow
  /// https://trustingsocial1.atlassian.net/browse/EMA-4543
  final bool _enableActivatePOSLimitFeature = true;

  bool get enableActivatePOSLimitFeature => _enableActivatePOSLimitFeature;

  /// Enable/Disable DOP Native Collect Location
  /// https://trustingsocial1.atlassian.net/browse/EMA-5192

  /// how to toggle: -> search and update all file contain keyword "DOP Native Collect Location"
  /// Note: for IOS - update Info-Stag.plish and Info.plish, rollback keys NSLocationWhenInUseUsageDescription and NSLocationAlwaysAndWhenInUseUsageDescription
  final bool _enableDOPNativeCollectLocationFeature = true;

  bool get enableDOPNativeCollectLocationFeature => _enableDOPNativeCollectLocationFeature;

  /// Enable/Disable Instant Cashback Feature
  /// Refer: https://trustingsocial1.atlassian.net/browse/EMA-3188
  final bool _enableInstantCashbackFeature = true;

  bool get enableInstantCashbackFeature => _enableInstantCashbackFeature;

  /// Enable/Disable Revamp UI
  /// Refer: https://trustingsocial1.atlassian.net/browse/EMA-7022
  final bool _enableRevampUiFeature = true;

  bool get enableRevampUiFeature => true;

  /// Transaction History Version for changing UI and API
  /// Refer: https://trustingsocial1.atlassian.net/browse/EMA-6375
  final TransactionHistoryVersion _transactionHistoryVersion = TransactionHistoryVersion.version_2;

  TransactionHistoryVersion get transactionHistoryVersion => _transactionHistoryVersion;

  /// Enable/Disable Request Review/Rating Popup
  /// Refer: https://trustingsocial1.atlassian.net/browse/EMA-4432
  final bool _enableRequestReviewRatingFeature = true;

  bool get enableRequestReviewRatingFeature => _enableRequestReviewRatingFeature;
}
