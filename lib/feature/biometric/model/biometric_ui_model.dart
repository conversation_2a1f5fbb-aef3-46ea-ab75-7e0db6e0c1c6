import '../../../resources/resources.dart';

class BiometricTypeUIModel {
  final String iconPath;
  final String biometricTypeName;
  final String iconSettingPath;

  BiometricTypeUIModel._(
      {required this.biometricTypeName, required this.iconPath, required this.iconSettingPath});

  factory BiometricTypeUIModel.finger() {
    return BiometricTypeUIModel._(
        biometricTypeName: EvoStrings.fingerText,
        iconPath: EvoImages.icFingerId,
        iconSettingPath: EvoImages.icSettingFingerId);
  }

  factory BiometricTypeUIModel.face() {
    return BiometricTypeUIModel._(
        biometricTypeName: EvoStrings.faceText,
        iconPath: EvoImages.icFaceId,
        iconSettingPath: EvoImages.icSettingFaceId);
  }

  factory BiometricTypeUIModel.faceAndFinger() {
    return BiometricTypeUIModel._(
        biometricTypeName: EvoStrings.faceFingerText,
        iconPath: EvoImages.icFaceFingerId,
        iconSettingPath: EvoImages.icSettingFaceFingerId);
  }

  @override
  String toString() {
    return 'BiometricTypeUIModel{iconPath: $iconPath, biometricTypeName: $biometricTypeName, iconSettingPath: $iconSettingPath}';
  }
}
