import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../biometric_token_module/biometric_change_mixin.dart';
import '../biometric_token_module/biometric_token_usability_mixin.dart';
import '../biometric_token_module/biometrics_token_module.dart';

enum BiometricStatus {
  notSetup,
  deviceSettingChanged,
  biometricTokenUnusable,
  usable,
}

/// This class will be handle for cases:
/// Biometric change [BiometricChangeMixin]
/// Biometric token is expired [BiometricTokenUsabilityMixin]
/// Enable biometric [BiometricsTokenModule]
class BiometricStatusChangeNotifier extends ValueNotifier<BiometricStatus> {
  BiometricStatusChangeNotifier(super.value);

  void update(BiometricStatus newValue) {
    if (value == newValue) {
      commonLog('Ignore update because newValue is same value with current data');
      return;
    }
    value = newValue;
  }
}
