import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_theme.dart';
import 'package:flutter_common_package/widget/pin_code/pin_code_widget.dart';

import '../../../../data/repository/user_repo.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/ui_utils/evo_ui_utils.dart';
import '../../../../widget/default_obscure_widget.dart';
import '../../../../widget/evo_pin_code/evo_pin_code_config.dart';
import '../../base/biometric_token_cubit.dart';
import '../../biometric_token_module/extra_biometric_challenge.dart';
import 'enter_pin_popup_cubit.dart';

class EnterPinPopup extends BiometricChallengeWidget {
  const EnterPinPopup({super.key, super.callback});

  @override
  State<EnterPinPopup> createState() => _EnterPinWidgetState();

  static void show(BuildContext context, {BiometricChallengeCallback? callback}) {}
}

class _EnterPinWidgetState extends BiometricChallengeState<EnterPinPopup> {
  static const double padding = 0;
  final EnterPinPopupCubit _enterPinPopupCubit = EnterPinPopupCubit();

  final BiometricTokenCubit _biometricTokenCubit = BiometricTokenCubit(getIt.get<UserRepo>());

  TextEditingController pinTextController = TextEditingController();

  @override
  void dispose() {
    widget.callback?.onBioChallengeCancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: <BlocProvider<dynamic>>[
        BlocProvider<EnterPinPopupCubit>(
          create: (_) => _enterPinPopupCubit,
        ),
        BlocProvider<BiometricTokenCubit>(
          create: (_) => _biometricTokenCubit,
        ),
      ],
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          return MultiBlocListener(
            listeners: <BlocListener<dynamic, dynamic>>[
              BlocListener<BiometricTokenCubit, BiometricTokenState>(
                listener: (BuildContext context, BiometricTokenState currState) {
                  _listenBiometricToken(currState);
                },
              ),
              BlocListener<EnterPinPopupCubit, EnterPinPopupState>(
                  listener: (BuildContext context, EnterPinPopupState currState) {
                _listenEnterPinState(currState);
              })
            ],
            child: Padding(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom +
                      context.screenPadding.bottom +
                      padding),
              child: Column(
                children: <Widget>[
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        navigatorContext?.pop();
                      },
                      child: Container(
                        color: Colors.transparent,
                      ),
                    ),
                  ),
                  _getContent(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _getContent() {
    return BlocBuilder<EnterPinPopupCubit, EnterPinPopupState>(
        buildWhen: (EnterPinPopupState prevState, EnterPinPopupState currState) =>
            currState is! LoadingEnterPinState,
        builder: (BuildContext context, EnterPinPopupState state) {
          bool hasError = false;
          String errMessage = '';
          if (state is ErrorEnterPinState &&
              state.errorUIModel.statusCode != CommonHttpClient.LIMIT_EXCEEDED) {
            hasError = true;
            errMessage = state.errorUIModel.userMessage ?? CommonStrings.otherGenericErrorMessage;
          }

          return Container(
            width: context.screenWidth,
            padding: const EdgeInsets.all(20),
            decoration: const BoxDecoration(
              borderRadius:
                  BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24)),
              color: Colors.white,
            ),
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Text(
                    EvoStrings.enterPinTitle,
                    style: evoTextStyles.h600(evoColors.foreground).copyWith(height: 1.2),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    EvoStrings.enterPinToAuthenticate,
                    style: evoTextStyles.h400().copyWith(
                        color: evoColors.textPassive,
                        height: 1.5,
                        fontSize: 16,
                        fontWeight: FontWeight.w400),
                  ),
                  const SizedBox(height: 16),
                  CommonPinCode(
                    pinTheme: CommonPinTheme(
                      fieldHeight: EvoPinCodeConfig.defaultPinCodeFieldHeight,
                    ),
                    textController: pinTextController,
                    onSubmit: _verifyPin,
                    animationDuration: Duration.zero,
                    obscuringWidget:
                        _enterPinPopupCubit.obscurePinText ? const DefaultObscureWidget() : null,
                    autoDismissKeyboard: false,
                  ),
                  Container(
                    margin: const EdgeInsets.symmetric(vertical: 4),
                    child: Row(
                      children: <Widget>[
                        Expanded(
                            child: Visibility(
                          visible: hasError,
                          child: Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: Text(errMessage,
                                style: EvoTextStyles().bodyMedium(evoColors.error)),
                          ),
                        )),
                        InkWell(
                          onTap: () {
                            _enterPinPopupCubit.toggleObscurePinText();
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 4.0),
                            child: evoImageProvider.asset(_enterPinPopupCubit.obscurePinText
                                ? EvoImages.icShowOffPin
                                : EvoImages.icShowOnPin),
                          ),
                        ),
                      ],
                    ),
                  )
                ]),
          );
        });
  }

  void _verifyPin(String pin) {
    _biometricTokenCubit.getBiometricTokenByPin(pin);
  }

  Future<void> _listenBiometricToken(BiometricTokenState currState) async {
    if (currState is BiometricTokenLoadingState) {
      _enterPinPopupCubit.showLoading(true);
    }
    if (currState is BiometricTokenErrorState) {
      _enterPinPopupCubit.showLoading(false);
      _enterPinPopupCubit.handleError(currState.errorUIModel);
    } else if (currState is BiometricTokenSuccessState) {
      _enterPinPopupCubit.showLoading(false);
      navigatorContext?.pop();
      widget.callback?.onBioChallengeSuccess(currState.biometricToken);
    }
  }

  void _listenEnterPinState(EnterPinPopupState currState) {
    if (currState is LoadingEnterPinState && currState.isShowLoading) {
      EvoUiUtils().showHudLoading();
      return;
    }

    EvoUiUtils().hideHudLoading();

    if (currState is ErrorEnterPinState) {
      pinTextController.clear();
      if (currState.errorUIModel.statusCode == CommonHttpClient.LIMIT_EXCEEDED ||
          currState.isDismissPopup) {
        widget.callback?.onBioChallengeError(currState.errorUIModel);
        navigatorContext?.pop();
      }
      return;
    }
  }
}
