import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

part 'enter_pin_popup_state.dart';

class EnterPinPopupCubit extends CommonCubit<EnterPinPopupState> {
  bool _obscurePinText = true;

  bool get obscurePinText => _obscurePinText;

  @visibleForTesting
  set setObscurePinText(bool value) {
    _obscurePinText = value;
  }

  EnterPinPopupCubit() : super(InitialEnterPinState());

  void toggleObscurePinText() {
    _obscurePinText = !_obscurePinText;
    emit(ObscureEnterPinState(obscure: _obscurePinText));
  }

  void showLoading(bool isShow) {
    emit(LoadingEnterPinState(isShowLoading: isShow));
  }

  Future<void> handleError(ErrorUIModel error) async {
    if (error.statusCode == CommonHttpClient.BAD_REQUEST) {
      emit(ErrorEnterPinState(errorUIModel: error));
    } else {
      emit(ErrorEnterPinState(errorUIModel: error, isDismissPopup: true));
    }
  }
}
