import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/app_setting_util.dart';
import '../../../util/functions.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../main_screen/main_screen.dart';
import '../biometric_token_module/biometrics_token_module.dart';
import '../model/biometric_ui_model.dart';
import '../utils/bio_auth_result.dart';
import '../utils/biometrics_authenticate.dart';
import 'active_biometric_cubit.dart';
import 'active_biometric_state.dart';

class ActiveBiometricArg extends PageBaseArg {
  final String? pinCode;

  ActiveBiometricArg({this.pinCode});
}

class ActiveBiometricScreen extends PageBase {
  static void goNamed(String? pinCode) {
    return navigatorContext?.goNamed(Screen.activeBiometric.name,
        extra: ActiveBiometricArg(pinCode: pinCode));
  }

  final String? pinCode;

  const ActiveBiometricScreen({required this.pinCode, super.key});

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.activeBiometric.routeName);

  @override
  EvoPageStateBase<ActiveBiometricScreen> createState() => _ActiveBiometricScreenState();
}

class _ActiveBiometricScreenState extends EvoPageStateBase<ActiveBiometricScreen>
    with AppSettingUtil
    implements BiometricTokenModuleCallback {
  final ActiveBiometricCubit _activeBiometricCubit = ActiveBiometricCubit(
    bioAuth: getIt.get<BiometricsAuthenticate>(),
    localStorage: getIt.get<EvoLocalStorageHelper>(),
  );

  final BiometricsTokenModule _biometricsTokenModule = getIt.get<BiometricsTokenModule>();

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<ActiveBiometricCubit>(
      create: (_) => _activeBiometricCubit,
      child: Scaffold(
        backgroundColor: evoColors.primary,
        body: BlocListener<ActiveBiometricCubit, ActiveBiometricState>(
          listener: (BuildContext context, ActiveBiometricState currState) {
            _listenActiveBiometric(currState);
          },
          child: PopScope(
            canPop: false,
            child: Stack(
              children: <Widget>[
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    SizedBox(
                      height: 106,
                      width: 106,
                      child: evoImageProvider.asset(EvoImages.icChecked),
                    ),
                    const SizedBox(height: 20),
                    _itemText(
                        EvoStrings.loginSuccess, evoTextStyles.h500(color: evoColors.background)),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 40),
                      child: _itemText(
                          EvoStrings.descSetUpPinSuccess,
                          evoTextStyles
                              .bodyMedium(evoColors.background.withOpacity(0.8))
                              .copyWith(height: 1.5)),
                    ),
                    _itemActiveBiometric()
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _listenActiveBiometric(ActiveBiometricState currState) {
    if (currState is EnrolledBiometricState) {
      if (currState.hasEnrolledBiometrics) {
        _biometricsTokenModule.enable(callback: this);
      } else {
        showPopupAskGoToDeviceSecuritySetting(
          footer: getDefaultFooterSecuritySettingPopup(),
        );
      }
    }
  }

  Widget _itemText(String content, TextStyle style) =>
      Text(content, textAlign: TextAlign.center, style: style);

  Widget _itemActiveBiometric() {
    final BiometricTypeUIModel bioTypeInfo = getIt.get<AppState>().bioTypeInfo;
    final String methodBiometric =
        '${EvoStrings.authenticateText} ${bioTypeInfo.biometricTypeName}';
    return Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.all(16),
        decoration:
            BoxDecoration(color: evoColors.background, borderRadius: BorderRadius.circular(16)),
        child: Column(children: <Widget>[
          Padding(
              padding: const EdgeInsets.all(8.0),
              child: RichText(
                text: TextSpan(
                    style: evoTextStyles.bodyLarge(evoColors.textActive),
                    children: <TextSpan>[
                      const TextSpan(text: EvoStrings.titlePreFixActiveBiometric),
                      TextSpan(
                          text: methodBiometric,
                          style: evoTextStyles
                              .bodyLarge(evoColors.textActive)
                              .copyWith(fontWeight: FontWeight.w700)),
                      const TextSpan(text: EvoStrings.titleSubFixActiveBiometric),
                    ]),
                textAlign: TextAlign.center,
              )),
          _itemButtonCommon(
              EvoStrings.textPositiveActiveBiometric,
              evoTextStyles.bodyLarge(evoColors.background).copyWith(fontWeight: FontWeight.w700),
              evoButtonStyles.primary(ButtonSize.xLarge),
              _actionVerifyBiometric),
          _itemButtonCommon(
              EvoStrings.continueBtn,
              evoTextStyles.bodyLarge(evoColors.textActive).copyWith(fontWeight: FontWeight.w700),
              evoButtonStyles.tertiary(ButtonSize.xLarge, isHasShadow: false),
              () => _actionGoToHomeScreen()),
        ]));
  }

  void _actionGoToHomeScreen() {
    // BugFix: https://trustingsocial1.atlassian.net/browse/EMA-1135
    // Setting old device when the user create PIN success
    _activeBiometricCubit.setNewDevice(false);
    MainScreen.goNamed(isLoggedIn: true);
  }

  void _actionVerifyBiometric() {
    _activeBiometricCubit.checkEnrolledBiometric();
  }

  Widget _itemButtonCommon(
          String title, TextStyle textStyle, ButtonStyle style, VoidCallback onPressed) =>
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 6),
        child: CommonButton(
            isWrapContent: false,
            onPressed: onPressed,
            style: style,
            child: Text(title, style: textStyle)),
      );

  @override
  void onError(
      {required BiometricTokenModuleErrorType type,
      String? userMessage,
      ErrorUIModel? error,
      BioAuthError? bioError}) {
    switch (type) {
      case BiometricTokenModuleErrorType.biometrics:
        evoUtilFunction.handleBioError(bioError);
        break;
      case BiometricTokenModuleErrorType.apiError:
        handleEvoApiError(error);
        break;
      default:
        showSnackBarError(userMessage ?? CommonStrings.otherGenericErrorMessage);
        break;
    }
  }

  @override
  void onSuccess() {
    _actionGoToHomeScreen();
  }
}
