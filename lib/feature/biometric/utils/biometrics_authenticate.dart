import 'package:local_auth/local_auth.dart';

import 'bio_auth_result.dart';

abstract class BiometricsAuthenticate {
  Future<bool> isDeviceSupportBiometrics();

  Future<BioAuthResult> authenticate({required String localizedReason});

  Future<bool> hasEnrolledBiometric();

  Future<List<BiometricType>> getAvailableBiometricType();
}

enum TsBiometricType {
  face,
  finger,
  androidBio, //this type just used for Android device
  unknown,
}
