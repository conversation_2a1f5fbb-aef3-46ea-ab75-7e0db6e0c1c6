import '../../../util/functions.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import 'request_user_active_biometric_popup.dart';

class RequestUserActiveBiometricUtil {
  // https://trustingsocial1.atlassian.net/browse/EMA-409
  static const int timeEnableBiometricInHours = 24;

  final EvoLocalStorageHelper localStorageHelper;

  RequestUserActiveBiometricUtil({required this.localStorageHelper});

  RequestUserActiveBiometricPopup getRequestUserActiveBiometricPopup() {
    return RequestUserActiveBiometricPopup();
  }

  Future<bool?> getEnableBiometricAuthenticator() async {
    return await localStorageHelper.isEnableBiometricAuthenticator();
  }

  bool checkTimeShowBiometric(String? lastTimeLocal) {
    if (lastTimeLocal == null) {
      return true;
    } else {
      final DateTime lastTime = DateTime.parse(lastTimeLocal);
      final DateTime timeNow = evoUtilFunction.getCurrentTime();
      return timeNow.difference(lastTime).inHours >= timeEnableBiometricInHours;
    }
  }

  Future<void> saveTimeShowBiometric() async {
    final String timeNow = evoUtilFunction.getCurrentTimeString();
    await localStorageHelper.saveTimeShowBiometric(timeNow);
  }

  Future<bool> isNewDevice() async {
    final bool isNewDevice = await localStorageHelper.isNewDevice();
    return isNewDevice;
  }

  Future<String?> getTimeShowBiometric() async {
    final String? lastTimeLocal = await localStorageHelper.getTimeShowBiometric();
    return lastTimeLocal;
  }
}
