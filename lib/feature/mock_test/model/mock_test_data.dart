import 'mock_test_manual_link_card_flow.dart';
import 'mock_test_payment_flow.dart';
import 'mock_test_sign_in_flow.dart';

class MockTestData {
  final MockTestPaymentFlow? paymentFlow;
  final MockTestManualLinkCardFlow? manualLinkCardFlow;
  final MockTestSignInFlow? signInFlow;
  String? dirPath;

  MockTestData({
    this.paymentFlow,
    this.manualLinkCardFlow,
    this.signInFlow,
    this.dirPath,
  });

  MockTestData.fromJson(Map<String, dynamic> json)
      : paymentFlow = (json['payment_flow'] as Map<dynamic, dynamic>?) != null
            ? MockTestPaymentFlow.fromJson(
                json['payment_flow'] as Map<dynamic, dynamic>,
              )
            : null,
        manualLinkCardFlow = (json['manual_link_card_flow'] as Map<dynamic, dynamic>?) != null
            ? MockTestManualLinkCardFlow.fromJson(
                json['manual_link_card_flow'] as Map<dynamic, dynamic>,
              )
            : null,
        signInFlow = (json['sign_in_flow'] as Map<dynamic, dynamic>?) != null
            ? MockTestSignInFlow.fromJson(
                json['sign_in_flow'] as Map<dynamic, dynamic>,
              )
            : null;
}
