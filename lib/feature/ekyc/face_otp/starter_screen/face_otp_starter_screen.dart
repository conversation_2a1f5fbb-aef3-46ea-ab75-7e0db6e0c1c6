import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../base/evo_page_state_base.dart';
import '../../../../data/response/ekyc_info_entity.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../widget/evo_appbar.dart';
import '../../../../widget/evo_appbar_leading_button.dart';
import '../../ekyc_flow_callback.dart';
import '../../ekyc_flow_failed_reason.dart';
import '../../ekyc_flow_mixin.dart';
import '../../ekyc_sdk_helper/init_ekyc_sdk_helper.dart';
import '../../model/ekyc_for_flow_type.dart';
import '../../widgets/ekyc_block_widget.dart';
import '../../widgets/ekyc_loading_widget.dart';
import '../instruction/face_otp_instruction_screen.dart';
import 'face_otp_starter_cubit.dart';

class FaceOtpStarterScreenArg extends PageBaseArg {
  final EkycFlowType flowType;
  final EkycFlowCallback callback;
  final EKYCSessionEntity? ekycSessionEntity;

  FaceOtpStarterScreenArg({
    required this.flowType,
    required this.callback,
    this.ekycSessionEntity,
  });
}

class FaceOtpStarterScreen extends PageBase {
  static void pushReplacementNamed({
    required EkycFlowType flowType,
    required EkycFlowCallback callback,
    required EKYCSessionEntity? ekycSessionEntity,
  }) {
    return navigatorContext?.pushReplacementNamed(Screen.faceOTPStarterScreen.name,
        extra: FaceOtpStarterScreenArg(
          flowType: flowType,
          callback: callback,
          ekycSessionEntity: ekycSessionEntity,
        ));
  }

  static void pushNamed({
    required EkycFlowType flowType,
    required EkycFlowCallback callback,
    required EKYCSessionEntity? ekycSessionEntity,
  }) {
    return navigatorContext?.pushNamed(Screen.faceOTPStarterScreen.name,
        extra: FaceOtpStarterScreenArg(
          flowType: flowType,
          callback: callback,
          ekycSessionEntity: ekycSessionEntity,
        ));
  }

  final EkycFlowType flowType;
  final EkycFlowCallback callback;
  final EKYCSessionEntity? ekycSessionEntity;

  const FaceOtpStarterScreen({
    required this.flowType,
    required this.callback,
    this.ekycSessionEntity,
    super.key,
  });

  @override
  State<FaceOtpStarterScreen> createState() => _FaceOtpStarterState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.faceOTPStarterScreen.routeName);
}

class _FaceOtpStarterState extends EvoPageStateBase<FaceOtpStarterScreen> with EkycFlowMixin {
  final FaceOtpStaterCubit cubit = FaceOtpStaterCubit(
    initEKYCSdkHelper: getIt.get<InitEKYCSdkHelper>(),
    appState: getIt.get<AppState>(),
  );

  @override
  void initState() {
    super.initState();
    cubit.eKycFlowCallback = widget.callback;

    if (widget.flowType == EkycFlowType.linkCard) {
      // Always create new eKYC session when enter this screen
      cubit.createEKYCSession(flowType: widget.flowType, forceCreateNewSession: true);
    } else if (widget.flowType == EkycFlowType.faceOtpSignIn) {
      cubit.createEKYCSessionForFaceOtp(previousEKYCSessionEntity: widget.ekycSessionEntity);
    }
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<FaceOtpStaterCubit>(
      create: (_) => cubit,
      child: BlocConsumer<FaceOtpStaterCubit, FaceOtpStarterState>(
        listener: (BuildContext context, FaceOtpStarterState state) {
          _handleListener(state);
        },
        builder: (BuildContext context, FaceOtpStarterState state) {
          return _buildContent(state);
        },
      ),
    );
  }

  Widget _buildContent(FaceOtpStarterState state) {
    if (state is FaceOtpLoadingState) {
      return _buildLoadingContent();
    } else if (state is FaceOtpExceedLimitationState) {
      return _exceedLimitationWidget();
    } else if (state is FaceOtpSessionDataLoadedState) {
      return const SizedBox.shrink();
    } else {
      return const SizedBox.shrink();
    }
  }

  Widget _buildLoadingContent() {
    return PopScope(
      canPop: false,
      child: SafeArea(
        child: Scaffold(
          appBar: EvoAppBar(
            leading: CloseButton(
              color: evoColors.icon,
              onPressed: () {
                _onBackPressed(reason: EkycFlowFailedReason.userCancelled);
              },
            ),
          ),
          body: const EKYCLoadingWidget(
            title: EvoStrings.eKYCLoadingTitle,
            content: EvoStrings.eKYCLoadingContent,
          ),
        ),
      ),
    );
  }

  void _handleListener(FaceOtpStarterState state) {
    if (state is FaceOtpSessionDataLoadedState) {
      // Reuse old eKYC_session_token is created
      FaceOTPInstructionScreen.pushReplacementNamed(
        flowType: widget.flowType,
        forceCreateNewEKYCSession: false,
      );
      return;
    }

    if (state is FaceOtpStaterErrorState) {
      handleEvoApiError(state.error);
      // Note: Prevent call to context.pop() when showing expired dialog
      if (state.error.statusCode != CommonHttpClient.INVALID_TOKEN) {
        // back to entry point
        _onBackPressed(error: state.error);
      }
      return;
    }
  }

  Widget _exceedLimitationWidget() {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: EvoAppBar(
          leading: EvoAppBarLeadingButton(
            onPressed: () {
              _onBackPressed(reason: EkycFlowFailedReason.limitExceed);
            },
          ),
        ),
        backgroundColor: evoColors.background,
        body: SafeArea(
          child: EKYCBlockWidget(
            description: EvoStrings.faceOtpExceedLimitationDescription,
            imageUrl: EvoImages.imgLimitationAccess,
            title: EvoStrings.faceOtpExceedLimitErrorTitle,
            buttonTitle: EvoStrings.faceOtpBackButtonTitle,
            onButtonTap: () {
              _onBackPressed(reason: EkycFlowFailedReason.limitExceed);
            },
          ),
        ),
      ),
    );
  }

  void _onBackPressed({ErrorUIModel? error, EkycFlowFailedReason? reason}) {
    onEkycFailed(
      context: context,
      reason: reason ?? EkycFlowFailedReason.unknown,
      userMessage: error?.userMessage,
    );
  }
}
