import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../data/response/ekyc_info_entity.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../ekyc_flow_callback.dart';
import '../../ekyc_sdk_helper/init_ekyc_sdk_helper.dart';
import '../../model/ekyc_for_flow_type.dart';

part 'face_otp_starter_state.dart';

class FaceOtpStaterCubit extends CommonCubit<FaceOtpStarterState> {
  final InitEKYCSdkHelper initEKYCSdkHelper;
  final AppState appState;

  FaceOtpStaterCubit({required this.initEKYCSdkHelper, required this.appState})
      : super(FaceOtpStarterInitial());

  Future<void> createEKYCSession({
    required EkycFlowType flowType,
    required bool forceCreateNewSession,
    EKYCSessionEntity? entity,
  }) async {
    emit(FaceOtpLoadingState());
    final EKYCSessionEntity eKYCSessionEntity =
        await initEKYCSdkHelper.createNewEKYCSessionIfNeeded(
      flowType: flowType,
      forceCreateNewSession: forceCreateNewSession,
    );

    _checkEKYCSession(eKYCSessionEntity: eKYCSessionEntity);
  }

  Future<void> createEKYCSessionForFaceOtp({
    required EKYCSessionEntity? previousEKYCSessionEntity,
  }) async {
    emit(FaceOtpLoadingState());

    final EKYCSessionEntity? eKYCSessionEntity =
        await initEKYCSdkHelper.createEKYCSessionWithPreviousSession(
      entity: previousEKYCSessionEntity,
    );

    _checkEKYCSession(eKYCSessionEntity: eKYCSessionEntity);
  }

  void _checkEKYCSession({
    required EKYCSessionEntity? eKYCSessionEntity,
  }) {
    if (eKYCSessionEntity?.statusCode == CommonHttpClient.SUCCESS) {
      commonLog('createEKYCSession succeed');
      emit(FaceOtpSessionDataLoadedState());
      return;
    }

    if (eKYCSessionEntity?.statusCode == CommonHttpClient.LIMIT_EXCEEDED &&
        eKYCSessionEntity?.verdict == EKYCSessionEntity.verdictLimitExceeded) {
      // Check if user reach limitation of Face OTP
      commonLog('createEKYCSession succeed but Exceed Limitation');
      emit(FaceOtpExceedLimitationState());
      return;
    }

    commonLog('create eKYC session failed');
    emit(FaceOtpStaterErrorState(ErrorUIModel.fromEntity(eKYCSessionEntity)));
  }

  set eKycFlowCallback(EkycFlowCallback callback) {
    appState.ekycState.callback = callback;
  }
}
