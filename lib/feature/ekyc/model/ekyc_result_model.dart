enum TVSDKFailReason {
  /// init
  exceedLimit('exceed_limit'),
  initWithInvalidSession('init_with_invalid_session'),

  /// selfie
  selfieImageUploadFailed('selfie_image_upload_failed'),
  selfieVerifySanityFailed('selfie_verify_sanity_failed'),
  selfieVerifyLivenessFailed('selfie_verify_liveness_failed'),

  /// id card
  /// replace the [idResultGenericFailed] with a specific one when there is a need to verify id card, and the flow of Id card verification is defined
  idResultGenericFailed('id_result_generic_failed'),
  idFrontSideImageUploadFailed('id_front_side_image_upload_failed'),
  idFrontSideVerifySanityFailed('id_front_side_verify_sanity_failed'),
  idFrontSideVerifyTamperingFailed('id_front_side_verify_tampering_failed'),
  idBackSideImageUploadFailed('id_back_side_image_upload_failed'),
  idBackSideVerifySanityFailed('id_back_side_verify_sanity_failed'),
  idBackSideVerifyTamperingFailed('id_back_side_verify_tampering_failed'),
  qrImageUploadFailed('qr_image_upload_failed'),
  qrVerifySanityFailed('qr_verify_sanity_failed'),
  qrVerifyTamperingFailed('qr_verify_tampering_failed'),

  /// face matching
  cantStartFaceMatching('can_not_start_face_matching'),
  faceMatchingUnmatched('face_matching_unmatched'),

  /// QR code
  qrTimeout('qr_timeout'),
  qrSkip('qr_skip'),

  /// common
  userCancelled('user_cancelled'),
  sessionExpired('session_expired'),
  unknown('unknown');

  final String value;

  const TVSDKFailReason(this.value);
}

class TVSDKResult {
  final TVSDKFailReason? failReason;

  factory TVSDKResult.succeed() => const TVSDKResult._();

  factory TVSDKResult.failed({required TVSDKFailReason reason}) =>
      TVSDKResult._(failReason: reason);

  const TVSDKResult._({this.failReason});
}

extension ExtTVSDKResult on TVSDKResult {
  bool get isSuccess => failReason == null;
}

enum EkycCardSide { front, back }
