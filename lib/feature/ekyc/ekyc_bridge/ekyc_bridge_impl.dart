import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:trust_vision_plugin/enums.dart';
import 'package:trust_vision_plugin/result/selfie_image.dart';
import 'package:trust_vision_plugin/result/tv_detection_result.dart';
import 'package:trust_vision_plugin/trust_vision_plugin.dart';

import '../../../data/response/ekyc_info_entity.dart';
import '../../logging/evo_logging_event.dart';
import '../model/ekyc_result_model.dart';
import 'ekyc_bridge.dart';

class EkycBridgeImpl implements EkycBridge {
  static const String stepInitEkycSdk = 'init_ekyc_sdk';
  static const String stepSelfieCapturing = 'selfie_capturing';
  static const String stepResetFlow = 'reset_flow';

  final LoggingRepo loggingRepo;
  final TrustVisionPlugin trustVisionPlugin;

  TVDetectionResult? selfieDetectionResult;
  EKYCSessionEntity? ekycSession;

  EkycBridgeImpl(this.trustVisionPlugin, this.loggingRepo);

  @override
  void setSession(EKYCSessionEntity? ekycSession) {
    this.ekycSession = ekycSession;
  }

  @override
  EKYCSessionEntity? getSession() {
    return ekycSession;
  }

  @override
  String? getSelfieFrontalImageId() {
    final List<SelfieImage>? images = selfieDetectionResult?.selfieImages;
    if (images == null || images.isEmpty) {
      return null;
    }
    return images.first.frontalImage?.imageId;
  }

  @override
  Future<TVSDKResult> initEkyc({
    required String? accessKeyId,
    required String? accessKeySecret,
    required String? endpoint,
    required String? xRequestId,
    required String? xLenderRequestId,
  }) async {
    logEvent(step: stepInitEkycSdk, stepStatus: EkycStepStatus.start);

    if (ekycSession == null ||
        accessKeySecret == null ||
        accessKeyId == null ||
        endpoint == null ||
        xLenderRequestId == null ||
        xRequestId == null) {
      return TVSDKResult.failed(reason: TVSDKFailReason.initWithInvalidSession);
    }

    try {
      await trustVisionPlugin.initialize(
          accessKeyId: accessKeyId,
          accessKeySecret: accessKeySecret,
          endpoint: endpoint,
          xRequestId: xRequestId,
          xLenderRequestId: xLenderRequestId);
    } on PlatformException catch (exception) {
      return _handlePlatformException(stepInitEkycSdk, exception);
    }

    return handleSucceed(stepInitEkycSdk);
  }

  @override
  Future<TVSDKResult> startSelfieCapturing(
      {required bool needVerifySanity,
      required bool needVerifyLiveness,
      bool skipConfirmScreen = false}) async {
    logEvent(step: stepSelfieCapturing, stepStatus: EkycStepStatus.start);

    final Map<String, dynamic> selfieConfig = getSelfieConfigMap(
      needVerifySanity: needVerifySanity,
      needVerifyLiveness: needVerifyLiveness,
      skipConfirmScreen: skipConfirmScreen,
    );

    try {
      selfieDetectionResult = await trustVisionPlugin.captureSelfie(selfieConfig);
    } on PlatformException catch (exception) {
      commonLog('PlatformException $exception');
      return _handlePlatformException(stepSelfieCapturing, exception);
      // ignore: avoid_catches_without_on_clauses
    } catch (exception) {
      commonLog('OtherException $exception');
      return handleFailed(
        step: stepSelfieCapturing,
        reason: TVSDKFailReason.unknown,
        errorDetail: exception.toString(),
      );
    }

    final TVSDKFailReason? failReason = getSelfieFailReason(
        needVerifySanity: needVerifySanity, needVerifyLiveness: needVerifyLiveness);
    if (failReason == null) {
      return handleSucceed(stepSelfieCapturing);
    } else {
      return handleFailed(step: stepSelfieCapturing, reason: failReason);
    }
  }

  TVSDKFailReason? getSelfieFailReason(
      {required bool needVerifySanity, required bool needVerifyLiveness}) {
    if (getSelfieFrontalImageId() == null) {
      return TVSDKFailReason.selfieImageUploadFailed;
    }

    if (needVerifySanity) {
      final bool isGoodSanity =
          selfieDetectionResult?.selfieSanityResult?.isGood == true; // qualified image
      if (!isGoodSanity) {
        return TVSDKFailReason.selfieVerifySanityFailed;
      }
    }

    if (needVerifyLiveness) {
      final bool isLivenessPassed =
          selfieDetectionResult?.livenessResult?.isLive == true; // liveness passed
      if (!isLivenessPassed) {
        return TVSDKFailReason.selfieVerifyLivenessFailed;
      }
    }

    // Succeed
    return null;
  }

  @override
  void resetFlow() {
    logEvent(step: stepResetFlow, stepStatus: EkycStepStatus.start);
    selfieDetectionResult = null;
  }

  TVSDKResult handleSucceed(String step) {
    logEvent(step: step, stepStatus: EkycStepStatus.succeed);
    return TVSDKResult.succeed();
  }

  TVSDKResult _handlePlatformException(String step, PlatformException exception) {
    TVSDKFailReason reason;
    if (exception.code == TVErrorCode.sdk_canceled.name) {
      reason = TVSDKFailReason.userCancelled;
    } else if (exception.code == TVErrorCode.access_denied_exception.name) {
      reason = TVSDKFailReason.sessionExpired;
    } else {
      reason = TVSDKFailReason.unknown;
    }

    return handleFailed(
      step: step,
      reason: reason,
      errorDetail: exception.toString(),
    );
  }

  TVSDKResult handleFailed({
    required String step,
    required TVSDKFailReason reason,
    String? errorDetail,
  }) {
    logEvent(
      step: step,
      stepStatus: EkycStepStatus.failed,
      errorCode: reason.value,
      errorDetail: errorDetail,
    );
    return TVSDKResult.failed(reason: reason);
  }

  void logEvent({
    required String step,
    required EkycStepStatus stepStatus,
    String? errorCode,
    String? errorDetail,
  }) {
    final Map<String, dynamic> data = <String, dynamic>{
      'step': step,
      'stepStatus': stepStatus.name,
    };
    if (errorCode != null) {
      data['errorCode'] = errorCode;
    }
    if (errorDetail != null) {
      data['errorDetail'] = errorDetail;
    }
    loggingRepo.logEvent(eventType: EvoEventType.ekyc, data: data);
  }

  @visibleForTesting
  Map<String, dynamic> getSelfieConfigMap(
      {required bool needVerifySanity,
      required bool needVerifyLiveness,
      bool skipConfirmScreen = false}) {
    return <String, dynamic>{
      // use front camera
      'cameraOption': SelfieCameraMode.front.name,
      // enable guiding sound
      'isEnableSound': true,
      // enable sanity checking
      'isEnableSanityCheck': needVerifySanity,
      // enable liveness checking
      'isEnableVerifyLiveness': needVerifyLiveness,
      // skip confirm screen
      'skipConfirmScreen': skipConfirmScreen
    };
  }
}
