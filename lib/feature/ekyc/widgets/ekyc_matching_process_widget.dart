import 'package:flutter/material.dart';

import '../../../resources/resources.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../../widget/animation/lottie_animation_widget.dart';

/// this widget is used to show loading of eKYC process when matching process
/// such as:
///   - Matching face OTP
///   - ....
class EKYCMatchingProcessWidget extends StatelessWidget {
  @visibleForTesting
  final double loadingHeightPercentage = 0.122;

  @visibleForTesting
  final double loadingPaddingTopPercentage = 0.25;

  @visibleForTesting
  final double loadingPaddingBottomPercentage = 0.068;

  const EKYCMatchingProcessWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: <Widget>[
          SizedBox(
            height: EvoUiUtils().calculateVerticalSpace(
              context: context,
              heightPercentage: loadingPaddingTopPercentage,
            ),
          ),
          LottieAnimationWidget(
            EvoAnimation.animationHubLoading,
            size: EvoUiUtils().calculateVerticalSpace(
              context: context,
              heightPercentage: loadingHeightPercentage,
            ),
          ),
          SizedBox(
            height: EvoUiUtils().calculateVerticalSpace(
              context: context,
              heightPercentage: loadingPaddingBottomPercentage,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 45),
            child: Text(
              EvoStrings.faceOtpMatchingProcessing,
              style: evoTextStyles.h300(),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
