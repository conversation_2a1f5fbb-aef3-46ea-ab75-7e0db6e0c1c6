import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/init_common_package.dart';

import '../../../data/repository/ekyc_repo.dart';
import '../../../data/response/ekyc_info_entity.dart';
import '../ekyc_bridge/ekyc_bridge.dart';
import '../model/ekyc_for_flow_type.dart';
import 'init_ekyc_sdk_helper.dart';
import 'mock_file/mock_create_ekyc_session_file_name.dart';

class InitEKYCSdkHelperImpl implements InitEKYCSdkHelper {
  final EKYCRepo eKYCRepo = getIt.get<EKYCRepo>();
  final EkycBridge eKYCBridge = getIt.get<EkycBridge>();

  @override
  Future<EKYCSessionEntity> createNewEKYCSessionIfNeeded({
    required EkycFlowType flowType,
    required bool forceCreateNewSession,
  }) async {
    final EKYCSessionEntity? eKYCSessionEntity = eKYCBridge.getSession();
    if (forceCreateNewSession || eKYCSessionEntity == null) {
      return await _createNewEKYCSession(flowType);
    }

    return eKYCSessionEntity;
  }

  @override
  Future<EKYCSessionEntity?> createEKYCSessionWithPreviousSession({
    EKYCSessionEntity? entity,
  }) async {
    eKYCBridge.resetFlow();
    eKYCBridge.setSession(entity);
    return entity;
  }

  Future<EKYCSessionEntity> _createNewEKYCSession(EkycFlowType flowType) async {
    eKYCBridge.resetFlow();
    final EKYCSessionEntity entity = await eKYCRepo.createEKYCSession(
      flowType: flowType,
      mockConfig: MockConfig(
        enable: false,
        fileName: getInitFaceOTPSessionMockFileName(),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      eKYCBridge.setSession(entity);
    }

    return entity;
  }
}
