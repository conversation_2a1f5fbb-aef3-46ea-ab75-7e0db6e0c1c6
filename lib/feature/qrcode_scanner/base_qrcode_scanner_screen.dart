import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';
import '../../../widget/evo_appbar.dart';
import '../../../widget/evo_appbar_leading_button.dart';
import '../../prepare_for_app_initiation.dart';
import '../../util/ui_utils/evo_ui_utils.dart';
import '../camera_permission/camera_permission_cubit.dart';
import '../payment/qrcode_scanner/qrcode_scanner_screen_mock_test.dart';
import '../payment/qrcode_scanner/widget/evo_qr_code_controller.dart';
import '../payment/qrcode_scanner/widget/scan_qr_code_view.dart';

abstract class BaseQrCodeScannerScreen extends PageBase {
  final CameraPermissionCubit? cameraPermissionCubit;
  final EvoMobileScannerController? scannerController;

  const BaseQrCodeScannerScreen({
    this.cameraPermissionCubit,
    this.scannerController,
    super.key,
  });
}

abstract class BaseQrCodeScannerScreenState<S extends BaseQrCodeScannerScreen>
    extends EvoPageStateBase<S>
    with QrCodeScannerScreenMockTest
    implements QRCodeScannerCallback, RouteAware {
  late final CameraPermissionCubit cameraPermissionCubit =
      widget.cameraPermissionCubit ?? CameraPermissionCubit();

  /// [formats] is a list of barcode formats to scan for. list of supported formats can be found in [BarcodeFormat].
  /// [autoStart] is whether the scanner should start automatically when app moves to foreground from background.
  late final EvoMobileScannerController scannerController =
      widget.scannerController ?? getIt<EvoMobileScannerController>();

  /// this function is called when permission is granted and scanner is started
  void onInitializeCompleted();

  Widget buildPermissionGuideWidget();

  @override
  void initState() {
    super.initState();
    init();
  }

  @visibleForTesting
  void init() {
    if (enableMockTestFlow()) {
      requestStoragePermissionForMockTest();
    } else {
      cameraPermissionCubit.requestPermission();
    }
  }

  @override
  Future<void> onPageResumed() async {
    super.onPageResumed();
    if (!enableMockTestFlow()) {
      cameraPermissionCubit.checkPermissionOnResume();
    }
  }

  @override
  void onPagePaused() {
    super.onPagePaused();
    if (!enableMockTestFlow()) {
      /// reset cameraPermission state if app move to Background, then user resume this page we will check permission again
      cameraPermissionCubit.resetState();
    }
  }

  @override
  Future<void> onPageInactive() async {
    super.onPageInactive();
    if (!enableMockTestFlow()) {
      /// reset cameraPermission state if app move to Inactive State (such as: open system dialog, open control center ),
      /// then user resume this page we will check permission again
      await scannerController.stop();
      cameraPermissionCubit.resetState();
    }
  }

  /// clear state to ready scan new QR code when user back from another page to this page
  /// or close popup bottom-sheet, dialog
  @override
  Future<void> didPopNext() async {
    super.didPopNext();

    if (!enableMockTestFlow()) {
      await scannerController.start();
    }
  }

  /// Stop camera if user move to another page.
  /// If [isTopVisible()] is true, that means a popup is being displayed on top of this page.
  /// In that case we will let the camera open to prevent it from showing dark frame
  /// We will block the QR scanning by using [_baseQrCodeScannerCubit.block()] when start showing the popup.
  @override
  Future<void> didPushNext() async {
    super.didPushNext();
    if (!enableMockTestFlow() && !isTopVisible()) {
      await scannerController.stop();
    }
  }

  @override
  void dispose() {
    /// QRScanner still process during user swipe to back previous screen
    /// and we can not capture this event to close camera, pls refer https://github.com/flutter/flutter/issues/28237
    /// so some case showHubLoading still call and can not close when user back to previous screen
    EvoUiUtils().hideHudLoading();
    scannerController.stop();
    super.dispose();
  }

  @override
  void onDetectQRCode(String? rawData) {
    commonLog('onDetectQRCode: $rawData');
  }

  @override
  void onScanQrError(QrErrorCode errorCode, String? errMsg) {
    showSnackBarError(errMsg ?? CommonStrings.otherGenericErrorMessage);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return MultiBlocProvider(
      providers: <BlocProvider<dynamic>>[
        BlocProvider<CameraPermissionCubit>(
          create: (_) => cameraPermissionCubit,
        ),
      ],
      child: MultiBlocListener(
        listeners: <BlocListener<dynamic, dynamic>>[
          BlocListener<CameraPermissionCubit, CameraPermissionState>(
            listener: listenPermissionState,
          ),
        ],
        child: Scaffold(
          appBar: EvoAppBar(
            title: EvoStrings.scanQRCodeTitle,
            styleTitle: evoTextStyles.h400(color: Colors.white),
            leading: EvoAppBarLeadingButton(
                image: evoImageProvider.asset(EvoImages.icArrowLeft, color: Colors.white)),
          ),
          extendBodyBehindAppBar: true,
          body: Builder(
            builder: (BuildContext context) {
              return Stack(
                fit: StackFit.expand,
                children: <Widget>[
                  buildScanQRView(),
                  imageProvider.asset(EvoImages.evoScanQRMaskView, fit: BoxFit.cover),
                  Align(alignment: Alignment.bottomCenter, child: buildInfoView()),
                  buildBasePermissionGuideWidget(),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  @visibleForTesting
  Widget buildBasePermissionGuideWidget() {
    return BlocBuilder<CameraPermissionCubit, CameraPermissionState>(
      buildWhen: (CameraPermissionState preState, CameraPermissionState curState) {
        return curState is CameraPermissionDeniedState || curState is CameraPermissionGrantedState;
      },
      builder: (BuildContext context, CameraPermissionState state) {
        final bool isDenied = state is CameraPermissionDeniedState;
        if (!isDenied) {
          return const SizedBox.shrink();
        }

        return buildPermissionGuideWidget();
      },
    );
  }

  @visibleForTesting
  Widget buildScanQRView() {
    return BlocBuilder<CameraPermissionCubit, CameraPermissionState>(
      buildWhen: (CameraPermissionState prevState, CameraPermissionState curState) {
        return curState is CameraPermissionGrantedState;
      },
      builder: (BuildContext context, CameraPermissionState state) {
        final bool isShow = state is CameraPermissionGrantedState;
        return (isShow && !enableMockTestFlow())
            ? QRCodeScannerView(
                callback: this,
                evoMobileScannerController: scannerController,
              )
            : const ColoredBox(color: Colors.black);
      },
    );
  }

  @visibleForTesting
  Widget buildInfoView() {
    return BlocBuilder<CameraPermissionCubit, CameraPermissionState>(
      buildWhen: (CameraPermissionState prevState, CameraPermissionState curState) {
        return curState is CameraPermissionGrantedState;
      },
      builder: (BuildContext context, CameraPermissionState state) {
        final bool isGranted = state is CameraPermissionGrantedState;
        if (!isGranted) {
          return const SizedBox.shrink();
        }
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                SizedBox(
                  height: 40,
                  width: 40,
                  child: imageProvider.asset(EvoImages.icQrCode),
                ),
                const SizedBox(width: 16),
                FittedBox(
                  child: Text(
                    EvoStrings.scanQRCodeGuideline,
                    overflow: TextOverflow.fade,
                    style: evoTextStyles.bodyLarge(Colors.white),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @visibleForTesting
  Future<void> listenPermissionState(BuildContext context, CameraPermissionState state) async {
    if (state is CameraPermissionGrantedState) {
      /// there is issue that calling [_scannerController.start()] & [getPaymentMethod] in parallel will cause
      /// app crash on Vivo phone. So we need to call [getPaymentMethod] after [_scannerController.start()] is done
      /// issue: https://trustingsocial1.atlassian.net/browse/EMA-1236
      await scannerController.start();

      onInitializeCompleted();
      return;
    }
  }

  @mustCallSuper
  Future<void> handleApiError(
    ErrorUIModel? error, {
    void Function(ErrorUIModel? error)? onShowUIError,
  }) async {
    switch (error?.statusCode) {
      case CommonHttpClient.INVALID_TOKEN:
      case CommonHttpClient.NO_INTERNET:
      case CommonHttpClient.SOCKET_ERRORS:
      case CommonHttpClient.UNKNOWN_ERRORS:
      case CommonHttpClient.CONNECTION_TIMEOUT:
      case CommonHttpClient.INTERNAL_SERVER_ERROR:
        handleEvoApiError(error);
        break;

      default:
        if (onShowUIError != null) {
          onShowUIError.call(error);
        } else {
          handleEvoApiError(error);
        }
        break;
    }
  }

  /// Start - Mock test
  @override
  void onMockDataIsReady() {
    onInitializeCompleted();
  }

  @override
  void onIgnoreMockTest() {
    init();
  }

  Future<void> parseMockQRCodeIfNeed() async {
    if (isMockDataAvailable == false) {
      return;
    }
    final BarcodeCapture? result = await scannerController.scannerController.analyzeImage(
      mockQRCodeData,
    );
    if (result == null) {
      return;
    }
    onDetectQRCode(result.barcodes.first.rawValue);
  }

  /// End - Mock test
}
