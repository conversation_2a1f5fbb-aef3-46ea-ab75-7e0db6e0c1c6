part of 'delete_account_survey_cubit.dart';

abstract class DeleteAccountSurveyState extends BlocState {}

class DeleteAccountSurveyInitialState extends DeleteAccountSurveyState {}

class UpdatePickedReasonState extends DeleteAccountSurveyState {
  final List<DeleteAccountReasonModel> reasonUIModels;

  UpdatePickedReasonState(this.reasonUIModels);
}

class ReadyToDeleteAccountState extends DeleteAccountSurveyState {
  final List<int> reasonIds;

  ReadyToDeleteAccountState(this.reasonIds);
}

class NoReasonPickedState extends DeleteAccountSurveyState {}
