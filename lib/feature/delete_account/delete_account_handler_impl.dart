import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../data/repository/user_repo.dart';
import '../../data/response/user_deletion_verification_entity.dart';
import '../../model/evo_dialog_id.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/ui_utils/evo_dialog_helper.dart';
import '../../util/ui_utils/evo_ui_utils.dart';
import 'delete_account_handler.dart';
import 'model/request_delete_user_challenge_type.dart';
import 'survey/delete_account_survey_screen.dart';
import 'verify_pin/delete_account_verify_pin_screen.dart';

class DeleteAccountHandlerImpl implements DeleteAccountHandler {
  /// Http code for locked resource
  static const int lockedResourceHttpCode = 423;

  final UserRepo userRepo;
  final AppState appState;

  DeleteAccountHandlerImpl({required this.userRepo, required this.appState});

  @override
  Future<void> requestDeleteAccount(RequestDeleteAccountCallback callback) async {
    EvoUiUtils().showHudLoading();

    /// check user to eligible to delete account
    final UserDeletionVerificationEntity entity = await userRepo.verifyUserDeletionRequest(
        mockConfig: const MockConfig(
            enable: false, fileName: 'user_verification_deletion_challenge_type_verify_pin.json'));

    EvoUiUtils().hideHudLoading();
    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      handleDataIfUserEligibleToDelete(entity);
    } else if (entity.statusCode == lockedResourceHttpCode &&
        entity.verdict == UserDeletionVerificationEntity.verdictLockedResource) {
      _showPopupForLockedUserDeletion();
    } else {
      callback.onRequestDeleteError?.call(ErrorUIModel.fromEntity(entity));
    }
  }

  @visibleForTesting
  void handleDataIfUserEligibleToDelete(UserDeletionVerificationEntity entity) {
    if (entity.challengeType == RequestDeleteUserChallengeType.none.value) {
      _showConfirmationPopupForUserDeletion(onConfirm: () {
        DeleteAccountSurveyScreen.pushReplacementNamed(
          reasons: entity.reasons,
          sessionToken: entity.sessionToken,
        );
      });
    } else if (entity.challengeType == RequestDeleteUserChallengeType.verifyPin.value) {
      _showConfirmationPopupForUserDeletion(onConfirm: () {
        DeleteAccountVerifyPinScreen.pushNamed(
          sessionToken: entity.sessionToken,
        );
      });
    } else {
      throw Exception('Unknown challenge type');
    }
  }

  Future<void> _showPopupForLockedUserDeletion() {
    return EvoDialogHelper().showDialogConfirm(
      title: EvoStrings.lockAccountToDeleteTitle,
      content: EvoStrings.lockAccountToDeleteContent,
      textPositive: EvoStrings.close,
      dialogId: EvoDialogId.lockUserToDeleteAccountDialog,
      onClickPositive: () {
        ///dismiss popup
        navigatorContext?.pop();
      },
    );
  }

  Future<void> _showConfirmationPopupForUserDeletion({void Function()? onConfirm}) {
    return EvoDialogHelper().showDialogConfirm(
      title: EvoStrings.confirmToDeleteAccountTitle,
      content: EvoStrings.confirmToDeleteAccountDescription,
      textPositive: EvoStrings.deleteAccountTextBtn,
      textNegative: EvoStrings.cancelToDeleteAccountTextBtn,
      dialogId: EvoDialogId.confirmToDeleteAccountDialog,
      isDismissible: false,
      positiveButtonStyle: getIt.get<CommonButtonStyles>().tertiary(ButtonSize.xLarge).copyWith(
          padding:
              WidgetStateProperty.all(const EdgeInsets.symmetric(horizontal: 12, vertical: 16))),
      negativeButtonStyle: getIt.get<CommonButtonStyles>().primary(ButtonSize.xLarge).copyWith(
          padding:
              WidgetStateProperty.all(const EdgeInsets.symmetric(horizontal: 12, vertical: 16))),
      onClickPositive: () {
        ///dismiss popup
        navigatorContext?.pop();

        onConfirm?.call();
      },
      onClickNegative: () {
        ///dismiss popup
        navigatorContext?.pop();
      },
    );
  }

  @override
  Future<void> handleDeleteAccountSessionExpired() {
    return EvoDialogHelper().showDialogConfirm(
      title: EvoStrings.deleteAccountSessionExpiredTitle,
      content: EvoStrings.deleteAccountSessionExpiredContent,
      textPositive: EvoStrings.deleteAccountSessionExpiredButton,
      dialogId: EvoDialogId.deleteAccountSessionExpiredDialog,
      isDismissible: false,
      onClickPositive: () {
        /// Dismiss popup
        navigatorContext?.pop();

        /// Back to entry point screen
        navigatorContext?.pop();
      },
    );
  }
}
