import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../../widget/evo_appbar.dart';
import '../../main_screen/main_screen.dart';

class DeleteAccountSuccessScreen extends PageBase {
  static void pushReplacementNamed() {
    return navigatorContext?.pushReplacementNamed(Screen.deleteAccountSuccessScreen.name);
  }

  const DeleteAccountSuccessScreen({super.key});

  @override
  State<DeleteAccountSuccessScreen> createState() => _DeleteAccountSuccessState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.deleteAccountSuccessScreen.routeName);
}

class _DeleteAccountSuccessState extends EvoPageStateBase<DeleteAccountSuccessScreen> {
  final double _topPaddingPercentage = 0.136;

  @override
  Widget getContentWidget(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, _) {
        if (didPop) {
          return;
        }

        _gotoMainScreen();
      },
      child: Scaffold(
        appBar: EvoAppBar(
          leading: CloseButton(
            color: evoColors.icon,
            onPressed: () {
              _gotoMainScreen();
            },
          ),
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: <Widget>[
                SizedBox(
                  height: EvoUiUtils().calculateVerticalSpace(
                    context: context,
                    heightPercentage: _topPaddingPercentage,
                  ),
                ),
                evoImageProvider.asset(
                  EvoImages.imgDeleteAccountSuccess,
                  fit: BoxFit.fitWidth,
                  width: double.infinity,
                ),
                const SizedBox(height: 24),
                Text(
                  EvoStrings.deleteAccountSuccessTitle,
                  style: evoTextStyles.h500(color: evoColors.deleteAccountSuccessTitle),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Text(
                  EvoStrings.deleteAccountSuccessDescription,
                  style: evoTextStyles.bodyLarge(evoColors.deleteAccountSuccessTitle),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: CommonButton(
                    onPressed: () {
                      _gotoMainScreen();
                    },
                    style: evoButtonStyles.primary(ButtonSize.large),
                    isWrapContent: false,
                    child: const Text(EvoStrings.moveToHome),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _gotoMainScreen() {
    MainScreen.removeUntilAndPushReplacementNamed(isLoggedIn: false);
  }
}
