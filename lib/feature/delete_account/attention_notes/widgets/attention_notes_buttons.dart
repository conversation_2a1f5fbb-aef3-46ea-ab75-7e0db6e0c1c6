import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../resources/resources.dart';

class AttentionNotesButtons extends StatelessWidget {
  final VoidCallback? cancelDeleteAccount;
  final VoidCallback? confirmDeleteAccount;

  const AttentionNotesButtons({
    super.key,
    this.cancelDeleteAccount,
    this.confirmDeleteAccount,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Expanded(
          child: CommonButton(
            onPressed: cancelDeleteAccount,
            style: evoButtonStyles.primary(ButtonSize.xLarge),
            child: const Text(EvoStrings.attentionNotesCancelDelete),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: CommonButton(
            onPressed: confirmDeleteAccount,
            style: evoButtonStyles.tertiary(ButtonSize.xLarge),
            child: const Text(EvoStrings.attentionNotesConfirmDelete),
          ),
        ),
      ],
    );
  }
}
