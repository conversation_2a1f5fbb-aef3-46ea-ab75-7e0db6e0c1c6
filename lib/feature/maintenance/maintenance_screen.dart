import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../base/evo_page_state_base.dart';
import '../../data/response/maintenance_info_entity.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/functions.dart';
import '../../util/ui_utils/evo_ui_utils.dart';
import 'maintenance_handler.dart';

class MaintenanceArg extends PageBaseArg {
  final MaintenanceInfoEntity maintenanceInfo;

  MaintenanceArg(this.maintenanceInfo);
}

class MaintenanceScreen extends PageBase {
  final MaintenanceArg? arg;

  static void pushReplacementNamed({MaintenanceArg? arg}) {
    return navigatorContext?.pushReplacementNamed(
      Screen.maintenanceScreen.name,
      extra: arg,
    );
  }

  const MaintenanceScreen({super.key, this.arg});

  @override
  State<MaintenanceScreen> createState() => MaintenanceScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.feedbackScreen.routeName);
}

class MaintenanceScreenState extends EvoPageStateBase<MaintenanceScreen> {
  final double imageHeightPercentage = 118 / 812;

  @override
  void onTopVisiblePageChanged(bool isOnTopAndVisible) {
    if (isOnTopAndVisible) {
      dismissOverlays();
    }
    super.onTopVisiblePageChanged(isOnTopAndVisible);
  }

  @visibleForTesting
  void dismissOverlays() {
    if (!mounted) {
      return;
    }

    final GlobalKey<NavigatorState> navigatorKey = globalKeyProvider.navigatorKey;
    // Check if maintenance screen's route is the top-most PAGE route.
    // We need to allow popping overlays above it.
    final ModalRoute<dynamic>? currentRoute = ModalRoute.of(context);
    if (currentRoute != null && navigatorKey.currentState?.canPop() == true) {
      // Pop until the current route (MaintainScreen) is the top-most route.
      // This removes dialogs, bottom sheets etc., shown above it.
      navigatorKey.currentState?.popUntil((Route<dynamic> route) => route == currentRoute);
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.inactive) {
      // Reset handled state to allow showing maintenance screen again.
      getIt.get<MaintenanceHandler>().setHandledMaintenance(false);
      return;
    }
  }

  @override
  Widget buildVisibilityDetectorPage(BuildContext context) {
    return PopScope(
      canPop: false,
      child: super.buildVisibilityDetectorPage(context),
    );
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: evoColors.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              const SizedBox(height: 124),
              evoImageProvider.asset(
                EvoImages.icScanError,
                height: EvoUiUtils().calculateVerticalSpace(
                  heightPercentage: imageHeightPercentage,
                  context: context,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                EvoStrings.maintenanceTitle,
                style: evoTextStyles.h500(),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Text.rich(
                TextSpan(
                  text: EvoStrings.systemDuringMaintain,
                  style: evoTextStyles.bodyMedium(evoColors.textPassive),
                  children: <TextSpan>[
                    ..._buildDateTime(
                      time: widget.arg?.maintenanceInfo.start,
                      prefix: EvoStrings.from,
                    ),
                    ..._buildDateTime(
                      time: widget.arg?.maintenanceInfo.end,
                      prefix: EvoStrings.to,
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Text(
                EvoStrings.pleaseComebackLater,
                style: evoTextStyles.bodyMedium(evoColors.textPassive),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<TextSpan> _buildDateTime({
    required String? time,
    required String prefix,
  }) {
    final DateTime? dateTime = commonUtilFunction.toDateTime(time);

    if (dateTime == null) {
      return <TextSpan>[];
    }

    final String date = evoUtilFunction.getDDMMYYFormatFromDateTime(dateTime);

    return <TextSpan>[
      TextSpan(text: ' $prefix'),
      TextSpan(
        text: ' ${dateTime.hour} ${EvoStrings.hour}',
        style: evoTextStyles.h200(color: evoColors.textPassive),
      ),
      TextSpan(
        text: dateTime.minute > 0 ? ' ${dateTime.minute} ${EvoStrings.minute}' : '',
        style: evoTextStyles.h200(color: evoColors.textPassive),
      ),
      TextSpan(text: ' ${EvoStrings.day}'),
      TextSpan(
        text: ' $date',
        style: evoTextStyles.h200(color: evoColors.textPassive),
      ),
    ];
  }
}
