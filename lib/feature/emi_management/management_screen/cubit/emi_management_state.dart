part of 'emi_management_cubit.dart';

abstract class EmiManagementState {}

class EmiManagementInitialState extends EmiManagementState {}

class EmiManagementLoadingState extends EmiManagementState {}

class EmiManagementSucceedState extends EmiManagementState {
  final List<EmiRecordEntity>? emiRecords;
  final bool isLoadMore;

  EmiManagementSucceedState({
    required this.emiRecords,
    this.isLoadMore = false,
  });
}

class EmiManagementFailureState extends EmiManagementState {
  final ErrorUIModel errorUIModel;
  final bool isRefresh;

  EmiManagementFailureState({
    required this.errorUIModel,
    required this.isRefresh,
  });
}
