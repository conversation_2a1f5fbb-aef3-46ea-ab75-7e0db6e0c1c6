import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import '../../../../resources/resources.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../widget/evo_checkbox_widget.dart';
import '../../../widget/evo_overlay/evo_overlay_util_functions.dart';
import '../../feature_toggle.dart';
import '../term_and_condition_utils.dart';
import 'term_and_condition_checkbox_widget.dart';

class TermAndConditionWidget extends StatelessWidget {
  const TermAndConditionWidget({
    required this.onTap,
    required this.consentValue,
    super.key,
  });

  final bool consentValue;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        getIt.get<FeatureToggle>().enableRevampUiFeature
            ? EvoCheckboxWidget(
                onTap: onTap,
                value: consentValue,
              )
            : TermAndConditionCheckBoxWidget(
                value: consentValue,
                onTap: onTap,
              ),
        const SizedBox(width: 16),
        Expanded(child: _buildText()),
      ],
    );
  }

  Widget _buildText() {
    return RichText(
      text: TextSpan(
        children: <TextSpan>[
          TextSpan(
            text: '${EvoStrings.termAndConditionPopupContent1} ',
            style: evoTextStyles.bodyMedium(evoColors.textActive),
          ),
          TextSpan(
            text: EvoStrings.termAndConditionPopupContent2,
            style: evoTextStyles.bodyMedium(evoColors.primary),
            recognizer: TapGestureRecognizer()
              ..onTap = () async {
                /// hide TermAndConditionOverlay so the user can read the terms and condition
                await evoOverlayUtilFunctions.hideTermAndConditionOverlay();
                TermAndConditionUtils().openTermAndCondition(needShowAgainAfterPop: true);
              },
          ),
          TextSpan(
            text: ' ${EvoStrings.termAndConditionPopupContent3}',
            style: evoTextStyles.bodyMedium(evoColors.textActive),
          ),
        ],
      ),
    );
  }
}
