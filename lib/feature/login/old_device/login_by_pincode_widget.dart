import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_theme.dart';
import 'package:flutter_common_package/widget/pin_code/pin_code_widget.dart';

import '../../../resources/resources.dart';
import '../../../widget/default_obscure_widget.dart';
import '../../../widget/evo_pin_code/evo_pin_code_config.dart';

class LoginByPinCodeWidget extends StatefulWidget {
  final void Function(String)? onSubmit;
  final void Function(String)? onChange;
  final void Function()? onResetPin;
  final String? errorMessage;
  final int pinLen;

  const LoginByPinCodeWidget({
    super.key,
    this.errorMessage,
    this.pinLen = EvoPinCodeConfig.maxPinCodeLength,
    this.onSubmit,
    this.onChange,
    this.onResetPin,
  });

  @override
  State<LoginByPinCodeWidget> createState() => LoginByPinCodeWidgetState();
}

class LoginByPinCodeWidgetState extends State<LoginByPinCodeWidget> {
  TextEditingController pinCodeController = TextEditingController();

  @visibleForTesting
  bool isHidePinCode = true;

  final FocusNode _focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    final bool hasError = widget.errorMessage?.isNotEmpty == true;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        CommonPinCode(
          pinLength: widget.pinLen,
          focusNode: _focusNode,
          textController: pinCodeController,
          onSubmit: widget.onSubmit,
          autoFocus: false,
          pinTheme: CommonPinTheme(
            fieldHeight: EvoPinCodeConfig.defaultPinCodeFieldHeight,
            inactiveColor: hasError ? evoColors.error : evoColors.inputUnfocusedColor,
            activeColor: hasError ? evoColors.error : evoColors.inputUnfocusedColor,
          ),
          animationDuration: Duration.zero,
          onChange: widget.onChange,
          obscuringWidget: isHidePinCode ? const DefaultObscureWidget() : null,
        ),
        Text(widget.errorMessage ?? '', style: evoTextStyles.bodyMedium(evoColors.error)),
        Row(
          children: <Widget>[
            Expanded(
              child: GestureDetector(
                onTap: () {
                  widget.onResetPin?.call();
                },
                child: Text(
                  EvoStrings.loginScreenResetPin,
                  style: evoTextStyles.bodyLarge(evoColors.textPassive),
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                setState(() {
                  isHidePinCode = !isHidePinCode;
                });
              },
              child: Container(
                color: Colors.transparent,
                padding: const EdgeInsets.only(left: 30, top: 10, bottom: 10),
                child: evoImageProvider
                    .asset(isHidePinCode ? EvoImages.icShowOffPin : EvoImages.icShowOnPin),
              ),
            ),
          ],
        )
      ],
    );
  }

  void openKeyboard() {
    commonUtilFunction.delayAndRequestFocus(_focusNode);
  }
}
