import '../../../util/evo_authentication_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../data/response/sign_in_otp_entity.dart';
import '../../../model/evo_dialog_id.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/evo_action_handler.dart';
import '../../../util/functions.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/token_utils/jwt_helper.dart';
import '../../../util/ui_utils/evo_dialog_helper.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../../widget/evo_appbar.dart';
import '../../../widget/evo_appbar_leading_button.dart';
import '../../../widget/evo_divider_widget.dart';
import '../../../widget/version_app_widget.dart';
import '../../biometric/biometric_token_module/biometrics_token_module.dart';
import '../../biometric/utils/biometrics_authenticate.dart';
import '../../create_evo_card/create_evo_card_page.dart';
import '../../ekyc/ekyc_flow_callback.dart';
import '../../ekyc/ekyc_flow_failed_reason.dart';
import '../../ekyc/model/ekyc_for_flow_type.dart';
import '../../ekyc_v2/ekyc_v2_flow_callback.dart';
import '../../ekyc_v2/ekyc_v2_flow_type.dart';
import '../../ekyc_v2/face_auth/face_auth_handler.dart';
import '../../ekyc_v2/face_auth/face_auth_handler_impl.dart';
import '../../login/utils/face_otp/face_otp_handler.dart';
import '../../login/utils/face_otp/face_otp_handler_impl.dart';
import '../../main_screen/main_screen.dart';
import '../../pin/reset_pin/reset_pin_callback.dart';
import '../../pin/reset_pin/reset_pin_handler.dart';
import '../../push_notification/notification_handler.dart';
import '../../verify_otp/verify_otp_cubit.dart';
import '../../verify_otp/verify_otp_page.dart';
import '../new_device/input_phone_number/input_phone_number_page.dart';
import 'biometric/biometric_cubit.dart';
import 'login_by_biometric_widget.dart';
import 'login_by_pincode_widget.dart';
import 'login_on_old_device_cubit.dart';
import 'pincode/pin_code_cubit.dart';

class LoginOnOldDeviceScreenArg extends PageBaseArg {
  final bool isAutoLoginWithBiometric;

  LoginOnOldDeviceScreenArg({this.isAutoLoginWithBiometric = true});
}

class LoginOnOldDeviceScreen extends PageBase {
  final bool isAutoLoginWithBiometric;

  const LoginOnOldDeviceScreen({super.key, this.isAutoLoginWithBiometric = true});

  static void goNamed({bool isAutoLoginWithBiometric = true}) {
    return navigatorContext?.goNamed(Screen.loginOnOldDeviceScreen.name,
        extra: LoginOnOldDeviceScreenArg(isAutoLoginWithBiometric: isAutoLoginWithBiometric));
  }

  static void pushNamed({bool isAutoLoginWithBiometric = true}) {
    return navigatorContext?.pushNamed(Screen.loginOnOldDeviceScreen.name,
        extra: LoginOnOldDeviceScreenArg(isAutoLoginWithBiometric: isAutoLoginWithBiometric));
  }

  static void pushReplacementNamed({bool isAutoLoginWithBiometric = true}) {
    return navigatorContext?.pushReplacementNamed(Screen.loginOnOldDeviceScreen.name,
        extra: LoginOnOldDeviceScreenArg(isAutoLoginWithBiometric: isAutoLoginWithBiometric));
  }

  @override
  State<LoginOnOldDeviceScreen> createState() => _LoginScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.loginOnOldDeviceScreen.routeName);
}

class _LoginScreenState extends EvoPageStateBase<LoginOnOldDeviceScreen>
    with NotificationUserInteractionModule {
  final LoginOnOldDeviceCubit _loginCubit = LoginOnOldDeviceCubit(
    getIt.get<AuthenticationRepo>(),
    getIt.get<EvoLocalStorageHelper>(),
    getIt.get<BiometricsAuthenticate>(),
    getIt.get<JwtHelper>(),
    getIt.get<BiometricsTokenModule>(),
  );

  final BiometricCubit _biometricCubit = BiometricCubit(
    getIt.get<EvoLocalStorageHelper>(),
    getIt.get<JwtHelper>(),
    getIt.get<BiometricsAuthenticate>(),
    getIt.get<BiometricsTokenModule>(),
    getIt.get<AppState>().bioTypeInfo,
  );

  final PinCodeCubit _pinCodeCubit = PinCodeCubit();

  final GlobalKey<LoginByPinCodeWidgetState> _pinCodeKey = GlobalKey<LoginByPinCodeWidgetState>();

  final ResetPinHandler _resetPinHandler = getIt.get<ResetPinHandler>();

  final ResetPinCallback _resetPinCallback = ResetPinCallback();

  String? phoneNumber;

  @override
  void initState() {
    super.initState();
    _initSetup();
    appState.biometricStatusChangeNotifier.addListener(_initSetup);
    _createResetPinCallback();
  }

  @override
  void dispose() {
    appState.biometricStatusChangeNotifier.removeListener(_initSetup);
    super.dispose();
  }

  void _initSetup() {
    _loginCubit.init();
    _biometricCubit.init();
  }

  Future<bool> _handleBackButton() async {
    final AppState appState = getIt.get<AppState>();
    if (appState.actionAfterLogin != null) {
      await clearUnprocessedNotifications();
      appState.actionAfterLogin = null;
    }

    return evoUtilFunction.handleBackOrGoHomeScreen();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, _) {
        if (didPop) {
          return;
        }

        _handleBackButton();
      },
      child: Scaffold(
        appBar: EvoAppBar(
          leading: EvoAppBarLeadingButton(
            onPressed: () => _handleBackButton(),
          ),
        ),
        backgroundColor: evoColors.background,
        body: SafeArea(
          child: MultiBlocProvider(
            providers: <BlocProvider<dynamic>>[
              BlocProvider<LoginOnOldDeviceCubit>(
                create: (_) => _loginCubit,
              ),
              BlocProvider<BiometricCubit>(
                create: (_) => _biometricCubit,
              ),
              BlocProvider<PinCodeCubit>(
                create: (_) => _pinCodeCubit,
              ),
            ],
            child: MultiBlocListener(
              listeners: <BlocListener<dynamic, dynamic>>[
                BlocListener<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
                  listener: (_, LoginOnOldDeviceState state) {
                    _listenLoginState(state);
                  },
                ),
                BlocListener<BiometricCubit, BiometricState>(
                  listener: (_, BiometricState state) {
                    _listenBiometricState(state);
                  },
                ),
              ],
              child: Column(
                children: <Widget>[
                  Expanded(
                      child: CustomScrollView(
                    slivers: <Widget>[
                      SliverFillRemaining(
                        hasScrollBody: false,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              _buildLoginTitleWidget(),
                              const SizedBox(height: 10),
                              _buildLoginDescWidget(),
                              _buildBiometricWidget(),
                              _buildPinCodeWidget(),
                              const Spacer(),
                              const Center(
                                  child: VersionAppWidget(
                                padding: EdgeInsets.zero,
                              )),
                            ],
                          ),
                        ),
                      ),
                    ],
                  )),
                  const SizedBox(height: 5),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: CommonButton(
                      onPressed: _loginWithAnotherAccount,
                      isWrapContent: false,
                      style: evoButtonStyles.tertiary(ButtonSize.xLarge),
                      child: Text(
                        EvoStrings.loginScreenLoginWithAnotherPhoneNumber,
                        style: evoTextStyles.h200(
                          color: evoColors.textPassive,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBiometricWidget() {
    return BlocBuilder<BiometricCubit, BiometricState>(
      builder: (_, BiometricState state) {
        if (state is BiometricHidden) {
          return const SizedBox(height: 60);
        }
        return Column(
          children: <Widget>[
            LoginByBiometricWidget(onClick: () => _actionLoginByBiometric()),
            const SizedBox(height: 20),
            EvoDividerWidget(
              height: 1,
              color: evoColors.disableTextFieldBorder,
            ),
            const SizedBox(height: 20),
            Align(
              child: Text(
                EvoStrings.loginScreenPinCodeOption,
                style: evoTextStyles.bodyMedium(evoColors.textActive),
              ),
            ),
            const SizedBox(height: 60),
          ],
        );
      },
    );
  }

  Widget _buildLoginTitleWidget() {
    return BlocBuilder<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      buildWhen: (LoginOnOldDeviceState previous, LoginOnOldDeviceState current) {
        return current is LoginOnOldDeviceInitial;
      },
      builder: (_, LoginOnOldDeviceState state) {
        bool hasBiometric = false;

        if (state is LoginOnOldDeviceInitial) {
          hasBiometric = state.isBiometricLoginAvailable;
        }
        return Text(
          hasBiometric
              ? EvoStrings.loginScreenTitleWithBiometric
              : EvoStrings.loginScreenTitleWithoutBiometric,
          style: evoTextStyles.h600(evoColors.textActive),
        );
      },
    );
  }

  Widget _buildLoginDescWidget() {
    return BlocBuilder<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      buildWhen: (LoginOnOldDeviceState previous, LoginOnOldDeviceState current) {
        return current is LoginOnOldDeviceInitial;
      },
      builder: (_, LoginOnOldDeviceState state) {
        bool hasBiometric = false;

        if (state is LoginOnOldDeviceInitial) {
          hasBiometric = state.isBiometricLoginAvailable;
          phoneNumber = state.phoneNumber;
        }

        final List<InlineSpan> content = <InlineSpan>[
          TextSpan(
              text: hasBiometric
                  ? EvoStrings.loginScreenBiometricDescriptionPrefix
                  : EvoStrings.loginScreenDescriptionPrefix),
          TextSpan(
            text: phoneNumber,
            style: evoTextStyles.h300(color: evoColors.textPassive),
          ),
          const TextSpan(text: EvoStrings.loginScreenDescriptionSuffix)
        ];

        return RichText(
          textAlign: TextAlign.left,
          text: TextSpan(
            style: evoTextStyles.bodyLarge(evoColors.textPassive).copyWith(height: 1.5),
            children: content,
          ),
        );
      },
    );
  }

  Widget _buildPinCodeWidget() {
    return BlocBuilder<PinCodeCubit, PinCodeState>(
      builder: (_, PinCodeState state) {
        String? errorMessage;
        if (state is PinCodeErrorState) {
          errorMessage = state.error ?? CommonStrings.otherGenericErrorMessage;
        }

        if (state is PinCodeClearErrorState) {
          errorMessage = null;
        }

        return Column(
          children: <Widget>[
            LoginByPinCodeWidget(
              key: _pinCodeKey,
              errorMessage: errorMessage,
              onSubmit: _loginCubit.loginByPinCode,
              onChange: (_) {
                _pinCodeCubit.clearPinCodeError();
              },
              onResetPin: () {
                _resetPinHandler.requestResetPin(_resetPinCallback, phoneNumber ?? '');
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> _listenLoginState(LoginOnOldDeviceState state) async {
    if (state is LoginOnOldDeviceLoading) {
      EvoUiUtils().showHudLoading();
      return;
    }

    EvoUiUtils().hideHudLoading();

    if (state is LoginOnOldDeviceInitial) {
      if (!state.isBiometricLoginAvailable) {
        _openPinCodeKeyboard();
      }
    }
    if (state is LoginOnOldDeviceSuccess) {
      _handleLoginSuccess(state);
    } else if (state is LoginOnOldDeviceError) {
      _handleLoginError(state);
    }
  }

  void _handleLoginError(LoginOnOldDeviceError state) {
    if (state.error.verdict == SignInOtpEntity.verdictInvalidDeviceToken) {
      _showInvalidOldDeviceWarningPopup();
      return;
    }

    switch (state.loginType) {
      case TypeLogin.pin:
        _handleLoginByPinCodeError(state);
        break;
      case TypeLogin.token:
      case TypeLogin.otp:
        handleEvoApiError(state.error);
        break;
      default:
    }
  }

  void _handleLoginByPinCodeError(LoginOnOldDeviceError state) {
    // Ticket: [EMA-1002]
    final String? errorUserMsg = state.error.userMessage;
    final bool shouldHandleDefaultError = state.error.verdict == null || errorUserMsg == null;

    if (shouldHandleDefaultError) {
      handleEvoApiError(state.error);
      return;
    }

    _pinCodeCubit.setPinCodeError(errorUserMsg);
  }

  Future<void> _listenBiometricState(BiometricState state) async {
    if (state is BiometricVisible && widget.isAutoLoginWithBiometric) {
      _actionLoginByBiometric();
      return;
    }

    if (state is BiometricAuthenticateSuccess) {
      _loginCubit.loginByBiometric();
    } else if (state is BiometricAuthenticateFail) {
      if (state.errorMessage != null) {
        showSnackBarError(state.errorMessage ?? CommonStrings.otherGenericErrorMessage);
      }

      if (state.isHideBiometric) {
        _loginCubit.init(isBiometricFailManyTime: true);
        _biometricCubit.hideBiometric();
      }
    }
  }

  Future<void> _loginWithAnotherAccount({String? lastPhoneNumberLogged}) async {
    await _loginCubit.loginWithNewAccount();
    InputPhoneNumberPage.pushReplacementNamed(lastPhoneNumberLogged: lastPhoneNumberLogged);
  }

  void _openPinCodeKeyboard() {
    _pinCodeKey.currentState?.openKeyboard();
  }

  @override
  Future<void> handleEvoApiError(ErrorUIModel? errorUIModel) async {
    /// If error code is  [CommonHttpClient.INVALID_TOKEN] we will re-initial this page.
    if (errorUIModel?.statusCode == CommonHttpClient.INVALID_TOKEN) {
      _initSetup();
    }
    super.handleEvoApiError(errorUIModel);
  }

  @override
  bool hasListenAuthorizationSessionExpired() => false;

  void _createResetPinCallback() {
    _resetPinCallback.handleResetPinError = (ErrorUIModel? errorUIModel) {
      handleEvoApiError(errorUIModel);
    };
    _resetPinCallback.handleXSessionExpired = () {
      EvoAuthenticationHelper().showDialogSessionTokenExpired(
          type: VerifyOtpType.resetPin,
          onClickPositive: () {
            navigatorContext?.pop();
          });
    };
    _resetPinCallback.handleVerifyOtpSuccess = (VerifyOtpState state) {
      _resetPinHandler.processResetPin(
        phoneNumber: phoneNumber ?? '',
        state: state,
      );
    };
  }

  void _showInvalidOldDeviceWarningPopup() {
    EvoDialogHelper().showDialogConfirm(
      title: EvoStrings.loginInvalidDeviceTokenTitle,
      content: EvoStrings.loginInvalidDeviceTokenContent,
      dialogId: EvoDialogId.loginInvalidOldDeviceWarningDialog,
      textPositive: EvoStrings.loginInvalidDeviceTokenConfirm,
      positiveButtonStyle: evoButtonStyles.primary(ButtonSize.xLarge),
      onClickPositive: () async {
        navigatorContext?.pop();
        final String? lastPhoneNumberLogged = await _loginCubit.getUserPhoneNumberFromLocal();
        _loginWithAnotherAccount(lastPhoneNumberLogged: lastPhoneNumberLogged);
      },
    );
  }

  void _actionLoginByBiometric() {
    _biometricCubit.authenticateBiometric(EvoStrings.localizedReasonForUsingBiometrics);
  }

  void _handleLoginSuccess(LoginOnOldDeviceSuccess state) {
    final String? challengeType = state.entity?.challengeType;

    /// If a user deletes their account on one device, their data is also deleted on that device,
    /// but not on any other devices they have used to access the app.
    /// If the user waits for 30 days after deleting their account to open the application on a second device,
    /// the back-end will return the challenge type as [SignInChallengeType.action].
    /// The app will then redirect the user to the [CreateEvoCardPage].
    /// Refer thread: https://trustingsocial.slack.com/archives/C0446DCNV70/p1698389625157299?thread_ts=**********.565709&cid=C0446DCNV70
    /// Refer ticket: https://trustingsocial1.atlassian.net/browse/EMA-1647
    if (challengeType == SignInChallengeType.action.value) {
      CreateEvoCardPage.pushNamed(action: state.entity?.action);
      return;
    }

    if (challengeType == SignInChallengeType.faceOTP.value) {
      final FaceOtpHandler faceOtpHandler = FaceOtpHandlerImpl(
        flowType: EkycFlowType.faceOtpSignIn,
      );
      faceOtpHandler.startFaceOtp(
        ekycSessionEntity: state.entity?.ekycCredential,
        callback: EkycFlowCallback(
          flow: faceOtpHandler.getFlowType,
          onSuccess: (_, EkycFlowPayload? payload) {
            _userLoginSuccess();
          },
          onFailed: (_, EkycFlowFailedReason reason, String? userMessage) async {
            await EvoActionHandler().openAuthenticationScreen(
              isClearNavigationStack: true,
              isAutoLoginWithBiometric: widget.isAutoLoginWithBiometric,
            );
          },
        ),
      );
      return;
    }

    if (challengeType == SignInChallengeType.faceAuth.value) {
      final FaceAuthHandler faceAuthHandler = FaceAuthHandlerImpl(
        flowType: EkycV2FlowType.signIn,
      );
      faceAuthHandler.startFaceAuth(
        sessionToken: state.entity?.sessionToken,
        callback: EkycV2FlowCallback(
          flowType: faceAuthHandler.getFlowType,
          onSuccess: (EkycV2FlowPayload? payload) {
            _userLoginSuccess();
          },
          onError: (EkycV2FlowErrorReason errorReason) {
            EvoActionHandler().openAuthenticationScreen(
              isClearNavigationStack: true,
              isAutoLoginWithBiometric: widget.isAutoLoginWithBiometric,
            );
          },
        ),
      );
      return;
    }

    _userLoginSuccess();
  }

  void _userLoginSuccess() {
    updateUserLoginStatus(true);
    MainScreen.goNamed(isLoggedIn: true);
  }
}
