import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/feature/onesignal/onesignal.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../data/response/sign_in_otp_entity.dart';
import '../../../util/functions.dart';
import '../../../util/mock_file_name_utils/mock_authentication_file_name.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/evo_authentication_helper.dart';
import '../../../util/token_utils/jwt_helper.dart';
import '../../biometric/biometric_token_module/biometrics_token_module.dart';
import '../../biometric/utils/biometrics_authenticate.dart';
import '../utils/mock_file/mock_login_pin_code_file_name.dart';
import 'login_on_old_device_base_cubit.dart';

part 'login_on_old_device_state.dart';

class LoginOnOldDeviceCubit extends LoginOnOldDeviceBaseCubit<LoginOnOldDeviceState> {
  final AuthenticationRepo _authenticationRepo;

  LoginOnOldDeviceCubit(
    this._authenticationRepo,
    EvoLocalStorageHelper localStorageHelper,
    BiometricsAuthenticate biometricsAuthenticate,
    JwtHelper jwtHelper,
    BiometricsTokenModule biometricsTokenModule,
  ) : super(
          LoginOnOldDeviceInitial(),
          localStorageHelper: localStorageHelper,
          jwtHelper: jwtHelper,
          biometricsAuthenticate: biometricsAuthenticate,
          biometricsTokenModule: biometricsTokenModule,
        );

  Future<void> init({bool isBiometricFailManyTime = false}) async {
    final bool hasBiometric = await checkCanLoginByBiometric();
    final String? phoneNumber = await getUserPhoneNumberFromLocal();

    emit(
      LoginOnOldDeviceInitial(
        isBiometricLoginAvailable: !isBiometricFailManyTime && hasBiometric,
        phoneNumber: phoneNumber,
      ),
    );
  }

  Future<void> loginByPinCode(String? pinCode) async {
    emit(LoginOnOldDeviceLoading());

    const TypeLogin loginType = TypeLogin.pin;
    final String? phoneNumber = await getUserPhoneNumberFromLocal();
    final SignInOtpEntity response = await _authenticationRepo.login(
      loginType,
      pin: pinCode,
      phoneNumber: phoneNumber,
      facialVerificationVersion: evoUtilFunction.getFacialVerificationVersion(),
      mockConfig: MockConfig(
        enable: false,
        fileName: getLoginPinCodeMockFileName(LoginPinCodeMockType.challengeTypeAction),
      ),
    );

    _handleAfterCallingAPILogin(
      entity: response,
      loginType: loginType,
    );
  }

  Future<void> loginByBiometric() async {
    emit(LoginOnOldDeviceLoading());

    const TypeLogin loginType = TypeLogin.token;
    final String? biometricToken = await localStorageHelper?.getBiometricToken();
    final SignInOtpEntity response = await _authenticationRepo.login(
      loginType,
      token: biometricToken,
      facialVerificationVersion: evoUtilFunction.getFacialVerificationVersion(),
      mockConfig: MockConfig(
        enable: false,
        fileName: getLoginMockFileName(),
        statusCode: CommonHttpClient.BAD_REQUEST,
      ),
    );

    _handleAfterCallingAPILogin(
      entity: response,
      loginType: loginType,
    );
  }

  void _handleAfterCallingAPILogin({
    required SignInOtpEntity entity,
    required TypeLogin loginType,
  }) {
    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      evoUtilFunction.updateProcessUserStatus(entity.status);
      emit(LoginOnOldDeviceSuccess(
        loginType: loginType,
        entity: entity,
      ));
    } else {
      emit(LoginOnOldDeviceError(ErrorUIModel.fromEntity(entity), loginType));
    }
  }

  /// `oneSignal` for unit test
  Future<void> loginWithNewAccount({OneSignal? oneSignal}) async {
    await EvoAuthenticationHelper().clearAllUserData(oneSignal: oneSignal);
  }
}
