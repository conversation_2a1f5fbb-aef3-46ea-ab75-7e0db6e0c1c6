import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/response/linked_card_status_checking_entity.dart';

abstract class ManualLinkCardResultState {}

class ManualLinkCardResultInitState extends ManualLinkCardResultState {}

class ManualLinkCardResultLoadingState extends ManualLinkCardResultState {}

class ManualLinkCardResultErrorState extends ManualLinkCardResultState {
  final ErrorUIModel errorUIModel;

  ManualLinkCardResultErrorState(this.errorUIModel);
}

class ManualLinkCardResultLoadedState extends ManualLinkCardResultState {
  final LinkedCardStatusCheckingEntity linkedCardStatusCheckingEntity;

  ManualLinkCardResultLoadedState(this.linkedCardStatusCheckingEntity);
}
