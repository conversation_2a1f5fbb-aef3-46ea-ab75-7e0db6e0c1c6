import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../data/repository/user_repo.dart';
import '../../data/response/linked_card_status_checking_entity.dart';
import '../../data/response/order_session_entity.dart';
import '../../data/response/payment_method_entity.dart';
import '../../data/response/payment_method_list_entity.dart';
import '../../util/functions.dart';
import '../../util/mock_file_name_utils/mock_user_file_name.dart';

part 'manual_link_card_state.dart';

class ManualLinkCardCubit extends CommonCubit<ManualLinkCardState> {
  final UserRepo userRepo;

  @visibleForTesting
  PaymentMethodListEntity? paymentMethodEntity;

  bool get isPaymentMethodLoaded => paymentMethodEntity != null;

  ManualLinkCardCubit({
    required this.userRepo,
  }) : super(ManualLinkCardInitState());

  Future<void> getPaymentMethod({bool showLoading = true}) async {
    if (showLoading) {
      emit(ManualLinkCardLoadingState());
    }
    final PaymentMethodListEntity entity = await userRepo.getPaymentMethods(
      mockConfig: MockConfig(
        enable: false,
        fileName: getPaymentMethodsMockFileName(),
      ),
    );
    paymentMethodEntity = entity;
    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      _handleResultFromGetPayment(entity);
    } else {
      emit(ManualLinkCardErrorState(ErrorUIModel.fromEntity(entity)));
    }
  }

  Future<void> checkDopCardStatus() async {
    emit(ManualLinkCardLoadingState());
    final LinkedCardStatusCheckingEntity entity = await userRepo.checkLinkedCardsStatus(
      facialVerificationVersion: evoUtilFunction.getFacialVerificationVersion(),
      mockConfig: MockConfig(
        enable: false,
        fileName: checkLinkedCardsStatusMockFileName(),
      ),
    );
    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(LinkedCardStatusCheckingLoadedState(entity));
    } else {
      emit(LinkedCardStatusCheckingErrorState(ErrorUIModel.fromEntity(entity)));
    }
  }

  bool isGettingData() {
    return state is ManualLinkCardInitState || state is ManualLinkCardLoadingState;
  }

  void _handleResultFromGetPayment(PaymentMethodListEntity entity) {
    if (entity.paymentMethods?.isNotEmpty == true) {
      emit(PaymentMethodLoadedState());
    } else {
      emit(PaymentMethodIsEmptyState());
    }
  }

  void validatePaymentMethod({OrderSessionEntity? order}) {
    final bool isPaymentMethodValid = checkPaymentMethodIsValid(order: order);
    if (isPaymentMethodValid) {
      emit(PaymentMethodLoadedState(order: order));
      return;
    }

    emit(PaymentMethodIsEmptyState());
  }

  bool checkPaymentMethodIsValid({OrderSessionEntity? order}) {
    final PaymentMethodEntity? defaultPaymentMethod = getDefaultPaymentMethod(order: order);
    return defaultPaymentMethod != null;
  }

  PaymentMethodEntity? getDefaultPaymentMethod({OrderSessionEntity? order}) {
    return evoUtilFunction.getDefaultPaymentMethod(order?.paymentInfo);
  }
}
