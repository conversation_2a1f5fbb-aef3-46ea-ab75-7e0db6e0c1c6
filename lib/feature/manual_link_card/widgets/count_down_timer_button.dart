import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../../../resources/resources.dart';

class CountDownTimerButton extends StatefulWidget {
  final void Function() onTapAction;
  final void Function() onDone;
  final int countDownTime;

  const CountDownTimerButton({
    required this.countDownTime,
    required this.onTapAction,
    required this.onDone,
    super.key,
  });

  @override
  State<CountDownTimerButton> createState() => _CountDownTimerButtonState();
}

class _CountDownTimerButtonState extends State<CountDownTimerButton> {
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _startCountDownTimer();
    });
  }

  @override
  Widget build(BuildContext context) {
    return CommonButton(
        onPressed: () {
          _stopCountDown();
          widget.onTapAction.call();
        },
        style: evoButtonStyles.primary(ButtonSize.xLarge),
        isWrapContent: false,
        child: Text(
          _getTextCountDown(),
        ));
  }

  @override
  void dispose() {
    _stopCountDown();
    super.dispose();
  }

  String _getTextCountDown() {
    final int currentTimer = _timer?.tick ?? 0;
    return EvoStrings.preFaceOtpButtonTitleManualLinkCard
        .replaceVariableByValue(<String>['${widget.countDownTime - currentTimer}']);
  }

  void _startCountDownTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (Timer timer) {
      setState(() {
        if (timer.tick >= widget.countDownTime) {
          _stopCountDown();
          widget.onDone.call();
          return;
        }
      });
    });
  }

  void _stopCountDown() {
    _timer?.cancel();
  }
}
