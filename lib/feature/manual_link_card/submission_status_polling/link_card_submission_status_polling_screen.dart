import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/ui_strings.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/user_repo.dart';
import '../../../data/response/link_card_submission_status_entity.dart';
import '../../../model/evo_action_model.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../../widget/animation/lottie_animation_widget.dart';
import '../../../widget/evo_appbar.dart';
import '../../feature_flow/feature_flow_failed_reason.dart';
import '../manual_link_card_config.dart';
import '../manual_link_card_mixin.dart';
import '../model/manual_link_card_result_model.dart';
import '../polling_handler/polling_handler.dart';
import '../polling_handler/polling_handler_impl.dart';
import '../result/manual_link_card_result_screen.dart';
import 'controller/screen_status_controller.dart';
import 'link_card_submission_status_polling_cubit.dart';
import 'link_card_submission_status_polling_state.dart';

class LinkCardSubmissionStatusPollingArg extends PageBaseArg {
  final String? linkCardRequestId;

  LinkCardSubmissionStatusPollingArg({this.linkCardRequestId});
}

class LinkCardSubmissionStatusPollingScreen extends PageBase {
  static void pushReplacementNamed({
    String? linkCardRequestId,
  }) {
    return navigatorContext?.pushReplacementNamed(
      Screen.linkCardSubmissionStatusPollingScreen.name,
      extra: LinkCardSubmissionStatusPollingArg(linkCardRequestId: linkCardRequestId),
    );
  }

  final String? linkCardRequestId;

  const LinkCardSubmissionStatusPollingScreen({
    super.key,
    this.linkCardRequestId,
  });

  @override
  State<LinkCardSubmissionStatusPollingScreen> createState() =>
      _LinkCardSubmissionStatusPollingState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.linkCardSubmissionStatusPollingScreen.routeName);
}

class _LinkCardSubmissionStatusPollingState
    extends EvoPageStateBase<LinkCardSubmissionStatusPollingScreen> with ManualLinkCardMixin {
  final LinkCardSubmissionStatusPollingCubit _submissionStatusPollingCubit =
      LinkCardSubmissionStatusPollingCubit(
    getIt.get<UserRepo>(),
    getIt.get<AppState>(),
  );

  final double _loadingHeightPercentage = 0.182;
  final double _loadingPaddingTopPercentage = 0.092;
  final double _loadingPaddingBottomPercentage = 0.079;

  final PollingHandler _pollingHandler = PollingHandlerImpl();
  final ScreenStatusController _screenStatusController = ScreenStatusController();

  final int _pollingIntervalInMs = ManualLinkCardConfig.submissionStatusPollingDefaultIntervalTime;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startGetLinkCardStatus();
    });
  }

  @override
  void dispose() {
    _pollingHandler.disposePolling();
    super.dispose();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, _) async {
        if (didPop) {
          return;
        }

        _onBackPressed();
      },
      child: BlocProvider<LinkCardSubmissionStatusPollingCubit>(
        create: (_) => _submissionStatusPollingCubit,
        child: BlocListener<LinkCardSubmissionStatusPollingCubit,
            LinkCardSubmissionStatusPollingState>(
          listener: (BuildContext context, LinkCardSubmissionStatusPollingState state) {
            if (_pollingHandler.isPollingStopped()) {
              return;
            }

            _onSubmissionStatusPollingState(state);
            _handlePollingState(state: state);
          },
          child: SafeArea(
            child: Scaffold(
              appBar: EvoAppBar(
                leading: CloseButton(
                  color: evoColors.icon,
                  onPressed: () {
                    _onBackPressed();
                  },
                ),
              ),
              body: _loadingWidget(),
            ),
          ),
        ),
      ),
    );
  }

  Widget _loadingWidget() {
    return Center(
      child: Column(
        children: <Widget>[
          SizedBox(
              height: EvoUiUtils().calculateVerticalSpace(
            context: context,
            heightPercentage: _loadingPaddingTopPercentage,
          )),
          LottieAnimationWidget(
            EvoAnimation.animationHourglassLoading,
            size: EvoUiUtils().calculateVerticalSpace(
              context: context,
              heightPercentage: _loadingHeightPercentage,
            ),
          ),
          SizedBox(
              height: EvoUiUtils().calculateVerticalSpace(
            context: context,
            heightPercentage: _loadingPaddingBottomPercentage,
          )),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              EvoStrings.linkCardInProcessing,
              style: evoTextStyles.h300(),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              EvoStrings.linkCardProcessingCanTakeSomeMinutes,
              style: evoTextStyles.bodyMedium(evoColors.textPassive),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Future<void> onResumed() {
    _screenStatusController.resume();
    _handlePollingState(state: _submissionStatusPollingCubit.state);
    return super.onResumed();
  }

  @override
  void onPaused() {
    super.onPaused();
    _screenStatusController.pause();
  }

  void _startGetLinkCardStatus() {
    _pollingHandler.startPolling(
      limitPollingTimeInMs: ManualLinkCardConfig.submissionStatusPollingLimitTime,
      intervalPollingTimeInMs: _pollingIntervalInMs,
      listenPollingTimeOut: _onDoPollingTimeOut,
    );

    // first Init call API getLinkCardStatus
    _getLinkCardStatus();
  }

  void _getLinkCardStatus() {
    _submissionStatusPollingCubit.getLinkCardStatus(requestId: widget.linkCardRequestId);
  }

  void _onBackPressed() {
    _screenStatusController.pause();
    showDialogConfirmCancelLinkCard(
        context: context,
        timeToWaitingForNextLinkCard:
            _submissionStatusPollingCubit.timeToWaitingForNextLinkCardInMin,
        onClickPositive: () {
          _pollingHandler.executePollingEvent(event: PollingEvent.cancel);

          navigatorContext?.pop();

          /// Pop to entry point
          onManualLinkCardFlowFailed(reason: FlowFailedReason.userCancelled);
        },
        onClickNegative: () {
          navigatorContext?.pop();
          _screenStatusController.resume();

          /// UX improvement: delay 500ms to avoid the screen is redirected immediately
          /// while user has chosen 'stay here'.
          Future<void>.delayed(const Duration(milliseconds: 500), () {
            _handlePollingState(state: _submissionStatusPollingCubit.state);
          });
        });
  }

  void _onSubmissionStatusPollingState(LinkCardSubmissionStatusPollingState state) {
    if (state is LinkCardSubmissionStatusPollingLoadedState) {
      /// update TimeToWaitingForNextLinkCard
      _submissionStatusPollingCubit.updateTimeToWaitingForNextLinkCard =
          state.entity?.nextRetryIfExitDurationInMinute;

      switch (state.entity?.action?.type) {
        case EvoActionModel.manualLinkCardOpenSuccessScreen:
        case EvoActionModel.manualLinkCardOpenFailedScreen:
          _pollingHandler.executePollingEvent(event: PollingEvent.done);
          break;
        case EvoActionModel.manualLinkCardInquiryStatus:
        default:
          _pollingHandler.updateIntervalPollingTime(
              intervalPollingTimeInMs: state.entity?.intervalInquiryMs);
          _pollingHandler.executePollingEvent(event: PollingEvent.reset);
          break;
      }

      return;
    }

    if (state is LinkCardSubmissionStatusPollingErrorState) {
      final ErrorUIModel errorUIModel = state.errorUIModel;

      /// Cancel polling process if this user token had been expired.
      if (errorUIModel.statusCode == CommonHttpClient.INVALID_TOKEN) {
        _pollingHandler.executePollingEvent(event: PollingEvent.cancel);
        return;
      }

      /// Cancel polling process if There are verdict need Handle when Failed
      /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3304390690/API+Error+Codes
      if (_isVerdictNeedHandleWhenFailed(errorUIModel)) {
        _pollingHandler.executePollingEvent(event: PollingEvent.done);
        return;
      }

      /// Show error message whenever internet is error (not block the pooling process)
      /// refer: https://trustingsocial1.atlassian.net/browse/EMA-4209
      if (errorUIModel.statusCode == CommonHttpClient.NO_INTERNET ||
          errorUIModel.statusCode == CommonHttpClient.SOCKET_ERRORS) {
        showSnackBarError(CommonStrings.genericNoInternetErrorMessage);
      }

      _pollingHandler.executePollingEvent(event: PollingEvent.reset);
      return;
    }
  }

  bool _isVerdictNeedHandleWhenFailed(ErrorUIModel errorUIModel) {
    switch (errorUIModel.verdict) {
      case LinkCardSubmissionStatusEntity.verdictLinkCardPermissionDenied:
      case LinkCardSubmissionStatusEntity.verdictLinkCardRecordNotFound:
      case LinkCardSubmissionStatusEntity.verdictLinkCardFailureAll:
        return true;
      default:
        return false;
    }
  }

  String? _userMessageWhenFailed(ErrorUIModel errorUIModel) {
    final String? userMessage = errorUIModel.userMessage;
    if (userMessage != null) {
      return userMessage;
    }

    switch (errorUIModel.verdict) {
      case LinkCardSubmissionStatusEntity.verdictLinkCardPermissionDenied:
        return EvoStrings.submitLinkCardWithPermissionDenied;
      case LinkCardSubmissionStatusEntity.verdictLinkCardRecordNotFound:
        return EvoStrings.submitLinkCardWithRecordNotFoundAndFailed;
      case LinkCardSubmissionStatusEntity.verdictLinkCardFailureAll:
        return EvoStrings.submitLinkCardWithRecordNotFoundAndFailed;
      default:
        return null;
    }
  }

  void _handlePollingState({LinkCardSubmissionStatusPollingState? state}) {
    if (_screenStatusController.isPaused) {
      return;
    }

    if (_pollingHandler.canPolling()) {
      _pollingHandler.doPolling(onDoPolling: _getLinkCardStatus);
      return;
    }

    if (_pollingHandler.isPollingDone()) {
      _handleWhenPoolingDone(state);
      return;
    }
  }

  void _handleWhenPoolingDone(LinkCardSubmissionStatusPollingState? state) {
    if (state is LinkCardSubmissionStatusPollingLoadedState) {
      final LinkCardSubmissionStatusEntity? entity = state.entity;
      ManualLinkCardResultScreen.pushReplacementNamed(
        resultModel: ManualLinkCardResultModel(
          status: entity?.linkCardStatus,
          userMessage: entity?.userMessage,
          verdict: entity?.verdict,
          nextRetryDurationInMinutes: entity?.nextRetryIfExitDurationInMinute,
        ),
      );
      return;
    }

    if (state is LinkCardSubmissionStatusPollingErrorState) {
      final ErrorUIModel errorUIModel = state.errorUIModel;

      /// Refer: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3304390690/API+Error+Codes
      if (_isVerdictNeedHandleWhenFailed(errorUIModel)) {
        ManualLinkCardResultScreen.pushReplacementNamed(
          resultModel: ManualLinkCardResultModel(
            status: LinkCardSubmissionStatusEntity.statusFailed,
            userMessage: _userMessageWhenFailed(errorUIModel),
            verdict: errorUIModel.verdict,
          ),
        );
        return;
      }

      handleEvoApiError(errorUIModel);
      return;
    }
  }

  void _onDoPollingTimeOut() {
    /// Open ManualLinkCardResultScreen with status processing.
    ManualLinkCardResultScreen.pushReplacementNamed(
      resultModel: ManualLinkCardResultModel(
        status: LinkCardSubmissionStatusEntity.statusProcessing,
        userMessage: EvoStrings.linkCardPollingTimeout,
        verdict: null,
      ),
    );
  }
}
