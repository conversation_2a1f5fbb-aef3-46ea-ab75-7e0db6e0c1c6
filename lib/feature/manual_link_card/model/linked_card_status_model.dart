import '../../../data/response/action_entity.dart';
import '../../../data/response/linked_card_status_checking_entity.dart';

enum LinkedCardStatusVerdict {
  verdictSuccess(LinkedCardStatusCheckingEntity.verdictSuccess),
  verdictUnfulfilledCard(LinkedCardStatusCheckingEntity.verdictUnfulfilledCard),
  verdictWaitingForCardIssuing(LinkedCardStatusCheckingEntity.verdictWaitingForCardIssuing),
  verdictUnqualifiedCard(LinkedCardStatusCheckingEntity.verdictUnqualifiedCard),
  verdictDuplicatedLinkRequest(LinkedCardStatusCheckingEntity.verdictDuplicatedLinkRequest),
  verdictUnqualifiedUserInformation(
      LinkedCardStatusCheckingEntity.verdictUnqualifiedUserInformation),

  // PREPARE LINK CARD
  verdictLinkCardInvalidPhoneNumber(
      LinkedCardStatusCheckingEntity.verdictLinkCardInvalidPhoneNumber),
  verdictLinkCardAlreadyLinked(LinkedCardStatusCheckingEntity.verdictLinkCardAlreadyLinked),
  verdictLinkCardUnqualifiedUserInformation(
      LinkedCardStatusCheckingEntity.verdictLinkCardUnqualifiedUserInformation),
  verdictLinkCardInvalidParameters(LinkedCardStatusCheckingEntity.verdictLinkCardInvalidParameters),
  verdictLinkCardLinkRequestIsProcessing(
      LinkedCardStatusCheckingEntity.verdictLinkCardLinkRequestIsProcessing),
  verdictLinkCardInvalidBankCode(LinkedCardStatusCheckingEntity.verdictLinkCardInvalidBankCode),
  verdictLinkCardNotFoundLinkInfo(LinkedCardStatusCheckingEntity.verdictLinkCardNotFoundLinkInfo),
  verdictLinkCardBankProductNotSupported(
      LinkedCardStatusCheckingEntity.verdictLinkCardBankProductNotSupported),
  verdictLinkCardFailure(LinkedCardStatusCheckingEntity.verdictLinkCardFailure),
  verdictFailureAll(LinkedCardStatusCheckingEntity.verdictFailureAll),

  /// it is not verdict from BE returned. it is defined if BE verdict is not in known values:
  /// 'success' | 'unfulfilled_card' | 'waiting_for_card_issuing' | 'unqualified_card' | 'duplicated_link_request' | 'unqualified_user_information'
  unknown('unknown');

  final String value;

  const LinkedCardStatusVerdict(this.value);

  static LinkedCardStatusVerdict formatStatusString(String? value) {
    switch (value) {
      case LinkedCardStatusCheckingEntity.verdictSuccess:
        return LinkedCardStatusVerdict.verdictSuccess;
      case LinkedCardStatusCheckingEntity.verdictUnfulfilledCard:
        return LinkedCardStatusVerdict.verdictUnfulfilledCard;
      case LinkedCardStatusCheckingEntity.verdictWaitingForCardIssuing:
        return LinkedCardStatusVerdict.verdictWaitingForCardIssuing;
      case LinkedCardStatusCheckingEntity.verdictUnqualifiedCard:
        return LinkedCardStatusVerdict.verdictUnqualifiedCard;
      case LinkedCardStatusCheckingEntity.verdictDuplicatedLinkRequest:
        return LinkedCardStatusVerdict.verdictDuplicatedLinkRequest;
      case LinkedCardStatusCheckingEntity.verdictUnqualifiedUserInformation:
        return LinkedCardStatusVerdict.verdictUnqualifiedUserInformation;

      // PREPARE LINK CARD
      case LinkedCardStatusCheckingEntity.verdictLinkCardInvalidPhoneNumber:
        return LinkedCardStatusVerdict.verdictLinkCardInvalidPhoneNumber;
      case LinkedCardStatusCheckingEntity.verdictLinkCardAlreadyLinked:
        return LinkedCardStatusVerdict.verdictLinkCardAlreadyLinked;
      case LinkedCardStatusCheckingEntity.verdictLinkCardInvalidParameters:
        return LinkedCardStatusVerdict.verdictLinkCardInvalidParameters;
      case LinkedCardStatusCheckingEntity.verdictLinkCardLinkRequestIsProcessing:
        return LinkedCardStatusVerdict.verdictLinkCardLinkRequestIsProcessing;
      case LinkedCardStatusCheckingEntity.verdictLinkCardInvalidBankCode:
        return LinkedCardStatusVerdict.verdictLinkCardInvalidBankCode;
      case LinkedCardStatusCheckingEntity.verdictLinkCardNotFoundLinkInfo:
        return LinkedCardStatusVerdict.verdictLinkCardNotFoundLinkInfo;
      case LinkedCardStatusCheckingEntity.verdictLinkCardBankProductNotSupported:
        return LinkedCardStatusVerdict.verdictLinkCardBankProductNotSupported;
      case LinkedCardStatusCheckingEntity.verdictLinkCardFailure:
        return LinkedCardStatusVerdict.verdictLinkCardFailure;
      case LinkedCardStatusCheckingEntity.verdictFailureAll:
        return LinkedCardStatusVerdict.verdictFailureAll;

      default:
        return unknown;
    }
  }
}

class LinkedCardStatusModel {
  final String? userMessage;
  final String? verdict;
  final ActionEntity? action;

  LinkedCardStatusModel({required this.verdict, this.userMessage, this.action});

  LinkedCardStatusVerdict getVerdictLinkedCardStatusModel() {
    return LinkedCardStatusVerdict.formatStatusString(verdict);
  }
}
