import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../data/repository/announcement_repo.dart';
import '../../../data/request/reward_request.dart';
import '../../../data/response/announcement_entity.dart';
import '../../../data/response/announcement_list_entity.dart';
import '../../../prepare_for_app_initiation.dart';
import '../model/announcement_info.dart';
import 'mock_file/mock_announcement_file_name.dart';

class UnreadAnnouncementChecker {
  final AppState _appState;
  final AnnouncementRepo _announcementRepo;

  UnreadAnnouncementChecker(this._appState, this._announcementRepo);

  Future<void> checkUnreadAnnouncement() async {
    if (_appState.isUserLogIn == true) {
      final AnnouncementRequest request =
          AnnouncementRequest(limit: 1, status: AnnouncementEntity.statusUnread, nextCursor: '');
      final AnnouncementListEntity entity = await _announcementRepo.getAnnouncements(request,
          mockConfig: MockConfig(
              enable: false, fileName: getAnnouncementsMockFileName(request.nextCursor)));
      if (entity.statusCode == CommonHttpClient.SUCCESS) {
        final AnnouncementStatus status = entity.announcements?.isNotEmpty == true
            ? AnnouncementStatus.hasUnreadItems
            : AnnouncementStatus.noUnreadItems;
        _appState.announcementInfo.updateStatus(status);
      } else {
        commonLog('checkUnreadAnnouncement: error=$entity');
      }
    } else {
      commonLog('checkUnreadAnnouncement: error user not logged in');
    }
  }
}
