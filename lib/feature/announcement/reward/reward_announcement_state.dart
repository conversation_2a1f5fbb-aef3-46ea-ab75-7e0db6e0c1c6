import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/response/announcement_entity.dart';
import '../../../model/type_load_list.dart';

@immutable
class RewardState extends BlocState {}

class RewardLoadingState extends RewardState {}

class RewardLoadedState extends RewardState {
  final List<AnnouncementEntity>? announcements;
  final bool allowLoadMore;

  /// if [loadListType] is null, it means that this is not a result of a get list request
  final LoadListType? loadListType;

  RewardLoadedState({this.announcements, this.allowLoadMore = false, this.loadListType});
}

class RewardErrorState extends RewardState {
  final ErrorUIModel errorUIModel;
  final bool isRefresh;

  RewardErrorState(this.errorUIModel, this.isRefresh);
}
