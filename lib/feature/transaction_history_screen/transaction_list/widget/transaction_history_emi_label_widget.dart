import 'package:flutter/material.dart';

import '../../../../resources/resources.dart';

class TransactionHistoryEmiLabelWidget extends StatelessWidget {
  /// [isOldUI] is a flag to determine the ui of the label.
  /// Old UI of transaction history screen uses the old color & text style,
  /// while the new UI uses the new color & text style.
  final bool isOldUI;

  const TransactionHistoryEmiLabelWidget({
    super.key,
    this.isOldUI = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(100),
        color: isOldUI
            ? evoColors.transactionListHistoryBgEmiLabel
            : evoColors.transactionListHistoryBgEmiNewLabel,
      ),
      child: Text(
        EvoStrings.transactionHistoryEmiLabel,
        style: isOldUI
            ? evoTextStyles.h100(
                color: evoColors.transactionListHistoryEmiLabel,
              )
            : evoTextStyles.bodySmall(
                color: evoColors.transactionListHistoryEmiNewLabel,
              ),
      ),
    );
  }
}
