import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../data/response/current_cashback_info_entity.dart';
import '../../../../resources/resources.dart';
import '../../../../util/functions.dart';
import '../../../emi_management/widgets/emi_custom_progressbar_widget.dart';

class CashbackStatisticWidget extends StatelessWidget {
  const CashbackStatisticWidget({
    required this.cashback,
    required this.onPressed,
    super.key,
  });

  final CurrentCashbackInfoEntity? cashback;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    if (cashback == null) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.0),
          color: evoColors.transactionHistoryCashbackBackground,
          boxShadow: const <BoxShadow>[
            BoxShadow(
              blurRadius: 4,
              color: Color.fromRGBO(0, 0, 0, 0.04),
            ),
            BoxShadow(
              blurRadius: 8,
              color: Color.fromRGBO(0, 0, 0, 0.04),
            ),
            BoxShadow(
              offset: Offset(0, 12),
              blurRadius: 24,
              spreadRadius: -8,
              color: Color.fromRGBO(0, 0, 0, 0.04),
            ),
          ],
        ),
        child: Material(
          borderRadius: BorderRadius.circular(16.0),
          child: InkWell(
            onTap: onPressed,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: _buildContentWidgets(context),
                    ),
                  ),
                  const SizedBox(width: 8),
                  evoImageProvider.asset(
                    EvoImages.icSmallRightEmi,
                    height: 16,
                    width: 16,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildContentWidgets(BuildContext context) {
    return <Widget>[
      Text(
        EvoStrings.transactionHistoryCashbackTitle,
        style: evoTextStyles.h300(),
      ),
      const SizedBox(height: 8),
      _buildContentCashbackText(
        month: cashback?.month,
        amount: cashback?.amount,
        limit: cashback?.limit,
      ),
      const SizedBox(height: 12),
      _buildCashbackProcess(
        context: context,
        amount: cashback?.amount,
        limit: cashback?.limit,
      ),
      const SizedBox(height: 8),
    ];
  }

  Widget _buildContentCashbackText({
    int? month,
    int? amount,
    int? limit,
  }) {
    return Row(
      children: <Widget>[
        Text(
          '${EvoStrings.transactionHistoryCashbackPeriodTitle.replaceVariableByValue(<String>[
                month?.toString() ?? ''
              ])}:',
          style: evoTextStyles.bodySmall(color: evoColors.textActive),
        ),
        const SizedBox(width: 8),
        Text(
          _formatCashbackCurrency(
            amount: amount,
            limit: limit,
          ),
          style: evoTextStyles.bodySmall(),
        ),
      ],
    );
  }

  Widget _buildCashbackProcess({
    required BuildContext context,
    required int? amount,
    required int? limit,
  }) {
    if (amount == null || limit == null) {
      return const SizedBox.shrink();
    }

    final double progressPaidAmount = amount / limit;
    return EmiCustomProgressbarWidget(
      width: context.screenWidth,
      height: 6,
      background: evoColors.transactionHistoryTotalAmountProgress,
      progressBarColor: evoColors.transactionHistoryPaidAmount,
      progress: progressPaidAmount,
    );
  }

  String _formatCashbackCurrency({
    int? amount,
    int? limit,
  }) {
    return '${_handleFormatCurrency(amount)}/${_handleFormatCurrency(limit)}';
  }

  String _handleFormatCurrency(num? value) {
    return evoUtilFunction.evoFormatCurrency(
      value?.toInt(),
      currencySymbol: vietNamCurrencySymbol,
    );
  }
}
