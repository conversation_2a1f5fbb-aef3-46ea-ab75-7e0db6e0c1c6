import 'package:flutter/material.dart';

import '../../../../data/response/payment_result_transaction_entity.dart';
import '../../../../resources/resources.dart';
import '../../../../util/functions.dart';
import 'transaction_history_emi_label_widget.dart';

class TransactionHistoryRevampUIPaymentItem extends StatelessWidget {
  final PaymentResultTransactionEntity? transaction;

  const TransactionHistoryRevampUIPaymentItem({super.key, this.transaction});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: transaction?.type == TransactionType.cashback
          ? _buildCashbackRecordWidget()
          : _buildPurchaseRecordWidget(),
    );
  }

  List<Widget> _buildCashbackRecordWidget() {
    return <Widget>[
      Text(
        '+${evoUtilFunction.evoFormatCurrency(
          transaction?.cashbackAmount,
          currencySymbol: vietNamCurrencySymbol,
        )}',
        style: evoTextStyles.h300(color: evoColors.primary),
      ),
    ];
  }

  List<Widget> _buildPurchaseRecordWidget() {
    return <Widget>[
      Text(
        evoUtilFunction.evoFormatCurrency(
          transaction?.userChargeAmount,
          currencySymbol: vietNamCurrencySymbol,
        ),
        style: evoTextStyles.h300(),
      ),
      const SizedBox(height: 4),
      _itemEmiLabel(),
    ];
  }

  Widget _itemEmiLabel() {
    final bool isEmiTransaction = transaction?.isEmiTransaction ?? false;
    return isEmiTransaction
        ? const TransactionHistoryEmiLabelWidget(isOldUI: false)
        : const SizedBox.shrink();
  }
}
