import 'package:flutter/material.dart';

import '../../../data/response/payment_result_transaction_entity.dart';
import 'widget/transaction_history_cashback_info_item.dart';
import 'widget/transaction_history_cashback_record_item.dart';
import 'widget/transaction_history_revamp_ui_payment_item.dart';

class TransactionHistoryRevampUIItemWidget extends StatelessWidget {
  final PaymentResultTransactionEntity? transaction;
  final VoidCallback? onTap;

  const TransactionHistoryRevampUIItemWidget({
    required this.transaction,
    super.key,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: transaction?.type == TransactionType.cashback ? null : () => onTap?.call(),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              child: transaction?.type == TransactionType.cashback
                  ? TransactionHistoryCashbackRecordItem(
                      transaction: transaction,
                      onTap: onTap,
                    )
                  : TransactionHistoryCashbackInfoItem(
                      transaction: transaction,
                    ),
            ),
            const SizedBox(width: 20),
            TransactionHistoryRevampUIPaymentItem(
              transaction: transaction,
            ),
          ],
        ),
      ),
    );
  }
}
