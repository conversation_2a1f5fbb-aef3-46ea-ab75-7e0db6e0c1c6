import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../data/repository/authentication_repo.dart';
import '../../data/response/reset_pin_entity.dart';
import '../../data/response/sign_in_entity.dart';
import '../../data/response/sign_in_otp_entity.dart';
import '../../util/functions.dart';
import '../../util/mock_file_name_utils/mock_authentication_file_name.dart';
import 'verify_otp_page.dart';

part 'verify_otp_state.dart';

class VerifyOtpCubit extends CommonCubit<VerifyOtpState> {
  final AuthenticationRepo authenticationRepo;

  VerifyOtpCubit(this.authenticationRepo) : super(VerifyOtpInitial());

  Future<void> verifyOtp(String otp, String? sessionToken, VerifyOtpType? type) async {
    switch (type) {
      case VerifyOtpType.signIn:
        await _verifySignInOtp(otp, sessionToken);
        break;
      case VerifyOtpType.resetPin:
        await _verifyResetPinOtp(otp, sessionToken);
        break;
      default:
    }
  }

  Future<void> _verifySignInOtp(String otp, String? sessionToken) async {
    emit(VerifyOtpLoading());
    final SignInOtpEntity entity = await authenticationRepo.login(
      TypeLogin.otp,
      otp: otp,
      sessionToken: sessionToken,
      facialVerificationVersion: evoUtilFunction.getFacialVerificationVersion(),
      mockConfig: MockConfig(
        enable: false,
        fileName: getLoginMockFileName(),
        statusCode: CommonHttpClient.BAD_REQUEST,
      ),
    );
    emit(VerifyOtpCompleted(entity));
  }

  Future<void> _verifyResetPinOtp(String otp, String? sessionToken) async {
    emit(VerifyOtpLoading());
    final ResetPinEntity entity = await authenticationRepo.requestResetPin(ResetPinType.verifyOtp,
        otp: otp,
        sessionToken: sessionToken,
        mockConfig: MockConfig(enable: false, fileName: getRequestResetPinMockFileName()));
    emit(VerifyOtpCompleted(entity));
  }

  Future<void> resendOtp(String phoneNumber, String? sessionToken,
      {VerifyOtpType? type = VerifyOtpType.signIn}) async {
    switch (type) {
      case VerifyOtpType.signIn:
        await _resendOtpSignIn(phoneNumber);
        break;
      case VerifyOtpType.resetPin:
        await _resendOtpResetPin(phoneNumber, sessionToken);
        break;
      default:
    }
  }

  Future<void> _resendOtpSignIn(String phoneNumber) async {
    final SignInEntity entity = await authenticationRepo.verifyPhone(
        phoneNumber: phoneNumber,
        type: TypeLogin.otp,
        mockConfig: MockConfig(
            enable: false,
            fileName: getVerifyPhoneMockFileName(),
            statusCode: CommonHttpClient.BAD_REQUEST));
    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(ResendOtpCompleted(entity));
    } else {
      _handleError(entity);
    }
  }

  Future<void> _resendOtpResetPin(String phoneNumber, String? sessionToken) async {
    final ResetPinEntity entity = await authenticationRepo.requestResetPin(
      ResetPinType.resendOtp,
      phoneNumber: phoneNumber,
      sessionToken: sessionToken,
      mockConfig: MockConfig(
        enable: false,
        fileName: getRequestResetPinMockFileName(),
      ),
    );
    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(ResendOtpCompleted(entity));
    } else {
      _handleError(entity);
    }
  }

  void _handleError(BaseEntity entity) {
    final ErrorUIModel errorUIModel = ErrorUIModel.fromEntity(entity);
    if (errorUIModel.verdict == SignInOtpEntity.verdictLimitExceeded) {
      emit(LimitResendOtp(errorUIModel.userMessage));
    } else {
      emit(VerifyOtpFailed(ErrorUIModel.fromEntity(entity)));
    }
  }
}
