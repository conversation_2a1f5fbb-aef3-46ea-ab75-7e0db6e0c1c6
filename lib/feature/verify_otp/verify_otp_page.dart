import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/otp/otp_widget.dart';

import '../../base/evo_page_state_base.dart';
import '../../data/repository/authentication_repo.dart';
import '../../data/response/reset_pin_entity.dart';
import '../../data/response/sign_in_entity.dart';
import '../../data/response/sign_in_otp_entity.dart';
import '../../model/evo_dialog_id.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/evo_authentication_helper.dart';
import '../../util/ui_utils/evo_dialog_helper.dart';
import '../../util/ui_utils/evo_ui_utils.dart';
import '../../widget/evo_pin_code/evo_pin_code_config.dart';
import '../main_screen/main_screen.dart';
import 'verify_otp_cubit.dart';

enum VerifyOtpType { signIn, resetPin }

class VerifyOtpPageArg extends PageBaseArg {
  final String? phoneNumber;
  final String? sessionToken;
  final int? otpResendSecs;
  final VerifyOtpType? verifyOtpType;
  final void Function(VerifyOtpState)? onPopSuccess;

  VerifyOtpPageArg(
      {this.phoneNumber,
      this.otpResendSecs,
      this.verifyOtpType,
      this.onPopSuccess,
      this.sessionToken});
}

class VerifyOtpPage extends PageBase {
  static void pushNamed({
    required String? phoneNumber,
    required int? otpResendSecs,
    void Function(VerifyOtpState)? onPopSuccess,
    String? sessionToken,
    VerifyOtpType? verifyOtpType = VerifyOtpType.signIn,
  }) {
    return navigatorContext?.pushNamed(Screen.verifyOtpScreen.name,
        extra: VerifyOtpPageArg(
          phoneNumber: phoneNumber,
          otpResendSecs: otpResendSecs,
          onPopSuccess: onPopSuccess,
          verifyOtpType: verifyOtpType,
          sessionToken: sessionToken,
        ));
  }

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.verifyOtpScreen.routeName);

  final String? phoneNumber;
  final int? otpResendSecs;
  final String? sessionToken;
  final void Function(VerifyOtpState)? onPopSuccess;
  final VerifyOtpType? verifyOtpType;

  const VerifyOtpPage(
      {super.key,
      this.phoneNumber,
      this.otpResendSecs,
      this.onPopSuccess,
      this.verifyOtpType,
      this.sessionToken});

  @override
  EvoPageStateBase<VerifyOtpPage> createState() => _SignInOtpPageState();
}

class _SignInOtpPageState extends EvoPageStateBase<VerifyOtpPage> {
  final VerifyOtpCubit _cubit = VerifyOtpCubit(getIt.get<AuthenticationRepo>());

  late int otpResendSecs;
  final FocusNode _focusNode = FocusNode();
  String? errorText;
  String? sessionToken;

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  @override
  void initState() {
    otpResendSecs = widget.otpResendSecs ?? 0;
    super.initState();
    commonUtilFunction.delayAndRequestFocus(_focusNode);
    sessionToken = widget.sessionToken;
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return _itemBody();
  }

  Widget _itemBody() => BlocProvider<VerifyOtpCubit>(
      create: (_) => _cubit,
      child: BlocConsumer<VerifyOtpCubit, VerifyOtpState>(
          listener: (BuildContext context, VerifyOtpState state) {
        _handleListener(state);
      }, builder: (BuildContext context, VerifyOtpState state) {
        return OtpWidget(
            focusNode: _focusNode,
            autoFocus: false,
            errorText: errorText,
            phoneNumber: widget.phoneNumber ?? '',
            resendInSec: otpResendSecs,
            textOtpTitle: EvoStrings.otpTitle,
            textOtpDesc: EvoStrings.otpDescription,
            pinCodeTextFieldHeight: EvoPinCodeConfig.defaultPinCodeFieldHeight,
            onResendOtp: () {
              errorText = null;
              _cubit.resendOtp(widget.phoneNumber ?? '', sessionToken, type: widget.verifyOtpType);
            },
            onSubmit: (String value) {
              errorText = null;
              _cubit.verifyOtp(value, sessionToken, widget.verifyOtpType);
            });
      }));

  void _handleSignInResendOtp(ResendOtpCompleted state) {
    if (state.entity.statusCode == CommonHttpClient.INVALID_TOKEN) {
      _handleSessionTokenExpired();
    } else {
      switch (state.entity.verdict) {
        case SignInEntity.verdictSuccess:
          if (state.entity is SignInEntity) {
            final SignInEntity entity = state.entity as SignInEntity;
            otpResendSecs = entity.otpResendSecs;
            sessionToken = entity.sessionToken;
          }
          break;
        case SignInEntity.verdictUserNotExisted:
          navigatorContext?.pushNamed(Screen.createEvoCardScreen.name);
          break;
        case SignInEntity.verdictLimitExceeded:
          _showDialogBlock(content: state.entity.userMessage);
          break;
        default:
          handleEvoApiError(ErrorUIModel.fromEntity(state.entity));
      }
    }
  }

  void _handleSignInVerifyOtp(VerifyOtpCompleted state) {
    if (state.entity.statusCode == CommonHttpClient.INVALID_TOKEN) {
      _handleSessionTokenExpired();
    } else {
      switch (state.entity.verdict) {
        case SignInOtpEntity.verdictSuccess:
          _handleOtpSuccess(state);
          break;
        case SignInOtpEntity.verdictLimitExceeded:
        case SignInOtpEntity.verdictUserNotExisted:
        case SignInOtpEntity.verdictIncorrectOtp:
        case SignInOtpEntity.verdictExpiredOTP:
          errorText = state.entity.userMessage ?? EvoStrings.otpInvalidMsg;
          break;
        default:
          handleEvoApiError(ErrorUIModel.fromEntity(state.entity));
      }
    }
  }

  void _handleResetPinResendOtp(ResendOtpCompleted state) {
    if (state.entity.statusCode == CommonHttpClient.INVALID_TOKEN) {
      _handleSessionTokenExpired();
    } else {
      switch (state.entity.verdict) {
        case ResetPinEntity.verdictSuccess:
          if (state.entity is ResetPinEntity) {
            final ResetPinEntity entity = state.entity as ResetPinEntity;
            otpResendSecs = entity.otpResendSecs ?? 0;
            sessionToken = entity.sessionToken;
          }
          break;
        case ResetPinEntity.verdictLockedResource:
        case ResetPinEntity.verdictLimitExceeded:
          _showDialogBlock(content: state.entity.userMessage);
          break;
        default:
          handleEvoApiError(ErrorUIModel.fromEntity(state.entity));
      }
    }
  }

  void _handleResetPinVerifyOtp(VerifyOtpCompleted state) {
    if (state.entity.statusCode == CommonHttpClient.INVALID_TOKEN) {
      _handleSessionTokenExpired();
    } else {
      switch (state.entity.verdict) {
        case ResetPinEntity.verdictSuccess:
          _handleOtpSuccess(state);
          break;
        case ResetPinEntity.verdictLimitExceeded:
        case ResetPinEntity.verdictInvalidCredential:
        case ResetPinEntity.verdictExpiredData:
          errorText = state.entity.userMessage ?? EvoStrings.otpInvalidMsg;
          break;
        default:
          handleEvoApiError(ErrorUIModel.fromEntity(state.entity));
      }
    }
  }

  String titleDialogBlock() {
    switch (widget.verifyOtpType) {
      case VerifyOtpType.signIn:
        return EvoStrings.limitOtp;
      case VerifyOtpType.resetPin:
        return EvoStrings.titleLimitResetPin;
      default:
        return '';
    }
  }

  String contentDialogBlock() {
    switch (widget.verifyOtpType) {
      case VerifyOtpType.signIn:
        return EvoStrings.descLimitOtp;
      case VerifyOtpType.resetPin:
        return EvoStrings.errorLimitResetPin;
      default:
        return '';
    }
  }

  void _showDialogBlock({String? content}) {
    EvoDialogHelper().showDialogConfirm(
        title: titleDialogBlock(),
        content: content ?? contentDialogBlock(),
        dialogId: EvoDialogId.otpBlockedErrorDialog,
        isDismissible: false,
        textPositive: EvoStrings.moveToHome,
        onClickPositive: () {
          MainScreen.goNamed(isLoggedIn: false);
        });
  }

  void _handleOtpSuccess(VerifyOtpState state) {
    navigatorContext?.pop();
    widget.onPopSuccess?.call(state);
  }

  void _handleShowLoading(VerifyOtpState state) {
    state is VerifyOtpLoading ? EvoUiUtils().showHudLoading() : EvoUiUtils().hideHudLoading();
  }

  void _handleSessionTokenExpired() {
    EvoAuthenticationHelper().showDialogSessionTokenExpired(type: widget.verifyOtpType);
  }

  void _handleListener(VerifyOtpState state) {
    _handleShowLoading(state);
    if (state is ResendOtpCompleted) {
      widget.verifyOtpType == VerifyOtpType.signIn
          ? _handleSignInResendOtp(state)
          : _handleResetPinResendOtp(state);
    } else if (state is VerifyOtpCompleted) {
      widget.verifyOtpType == VerifyOtpType.signIn
          ? _handleSignInVerifyOtp(state)
          : _handleResetPinVerifyOtp(state);
    } else if (state is LimitResendOtp) {
      _showDialogBlock(content: state.errorText);
    } else if (state is VerifyOtpFailed) {
      handleEvoApiError(state.errorUIModel);
    }
  }
}
