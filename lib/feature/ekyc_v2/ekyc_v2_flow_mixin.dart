import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_error_reason.dart';

import '../../prepare_for_app_initiation.dart';
import 'ekyc_v2_flow_callback.dart';
import 'ekyc_v2_state.dart';

mixin EkycV2FlowMixin {
  void onEkycSuccess({
    required EkycV2FlowPayload payload,
  }) {
    final EkycV2State ekycState = getIt.get<AppState>().ekycV2State;
    final void Function(EkycV2FlowPayload payload)? onSuccess = ekycState.callback?.onSuccess;

    //clear ekyc state
    ekycState.clear();
    onSuccess?.call(payload);
  }

  void onEkycError({
    required EkycV2FlowErrorReason errorReason,
  }) {
    final EkycV2State ekycState = getIt.get<AppState>().ekycV2State;
    final void Function(EkycV2FlowErrorReason reason)? onError = ekycState.callback?.onError;

    // clear ekyc state
    ekycState.clear();
    onError?.call(errorReason);
  }

  void handleCloseFacialVerificationIfModuleHasError({
    BaseEntity? apiErrorResponse,
    EkycBridgeErrorReason? bridgeErrorReason,
  }) {
    final int? apiErrorCode = apiErrorResponse?.statusCode;

    // handle limit exceeded
    if (apiErrorCode == CommonHttpClient.LIMIT_EXCEEDED ||
        bridgeErrorReason == EkycBridgeErrorReason.exceedLimit) {
      onEkycError(errorReason: EkycV2FlowErrorReason.exceedLimit);
      return;
    }

    // handle session error
    if (apiErrorCode == CommonHttpClient.INVALID_TOKEN ||
        bridgeErrorReason == EkycBridgeErrorReason.sessionExpired ||
        bridgeErrorReason == EkycBridgeErrorReason.initWithInvalidSession) {
      onEkycError(errorReason: EkycV2FlowErrorReason.sessionError);
      return;
    }

    // default reason: userCancelled
    onEkycError(errorReason: EkycV2FlowErrorReason.userCancelled);
  }

  bool checkIsAbleToRetryFacialVerificationIfModuleHasError({
    BaseEntity? apiErrorResponse,
    EkycBridgeErrorReason? bridgeErrorReason,
  }) {
    return bridgeErrorReason != EkycBridgeErrorReason.exceedLimit &&
        bridgeErrorReason != EkycBridgeErrorReason.sessionExpired &&
        bridgeErrorReason != EkycBridgeErrorReason.initWithInvalidSession &&
        apiErrorResponse?.statusCode != CommonHttpClient.LIMIT_EXCEEDED &&
        apiErrorResponse?.statusCode != CommonHttpClient.INVALID_TOKEN;
  }
}
