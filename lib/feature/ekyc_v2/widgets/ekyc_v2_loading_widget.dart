import 'package:flutter/material.dart';

import '../../../resources/resources.dart';
import '../../../util/ui_utils/evo_ui_utils.dart';
import '../../../widget/animation/lottie_animation_widget.dart';

/// this widget is used to show loading of eKYC process
/// such as:
///   - Prepare create eKYC Session
///   - ....
class EkycV2LoadingWidget extends StatelessWidget {
  @visibleForTesting
  final double loadingHeightPercentage = 0.182;

  @visibleForTesting
  final double loadingPaddingTopPercentage = 0.092;

  @visibleForTesting
  final double loadingPaddingBottomPercentage = 0.079;

  final String? title;
  final String? content;

  const EkycV2LoadingWidget({
    super.key,
    this.title,
    this.content,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        children: <Widget>[
          SizedBox(
            height: EvoUiUtils().calculateVerticalSpace(
              context: context,
              heightPercentage: loadingPaddingTopPercentage,
            ),
          ),
          LottieAnimationWidget(
            EvoAnimation.animationHourglassLoading,
            size: EvoUiUtils().calculateVerticalSpace(
              context: context,
              heightPercentage: loadingHeightPercentage,
            ),
          ),
          SizedBox(
            height: EvoUiUtils().calculateVerticalSpace(
              context: context,
              heightPercentage: loadingPaddingBottomPercentage,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              title ?? EvoStrings.eKYCLoadingTitle,
              style: evoTextStyles.h300(),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Text(
              content ?? EvoStrings.eKYCLoadingContent,
              style: evoTextStyles.bodyMedium(evoColors.textPassive),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
