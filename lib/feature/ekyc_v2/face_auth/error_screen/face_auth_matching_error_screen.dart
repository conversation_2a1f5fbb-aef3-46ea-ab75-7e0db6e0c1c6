import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_error_reason.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/ui_strings.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../base/evo_page_state_base.dart';
import '../../../../resources/resources.dart';
import '../../../../util/ui_utils/evo_ui_utils.dart';
import '../../../../widget/evo_appbar.dart';
import '../../ekyc_v2_flow_callback.dart';
import '../../ekyc_v2_flow_mixin.dart';
import '../../ekyc_v2_flow_type.dart';
import '../../error_screen/ekyc_v2_error_screen.dart';
import '../../widgets/ekyc_v2_block_widget.dart';
import '../face_matching/face_auth_matching_process_screen.dart';
import '../instruction/face_auth_instruction_screen.dart';
import '../models/face_auth_matching_error_type.dart';
import '../selfie_capture/face_auth_selfie_capturing_cubit.dart';

class FaceAuthMatchingErrorScreenArg extends PageBaseArg {
  final EkycV2FlowType flowType;
  final ErrorUIModel errorUIModel;
  final FaceAuthMatchingErrorType errorType;

  FaceAuthMatchingErrorScreenArg({
    required this.flowType,
    required this.errorUIModel,
    this.errorType = FaceAuthMatchingErrorType.other,
  });
}

class FaceAuthMatchingErrorScreen extends PageBase {
  static void pushReplacementNamed({
    required EkycV2FlowType flowType,
    required ErrorUIModel errorUIModel,
    FaceAuthMatchingErrorType errorType = FaceAuthMatchingErrorType.other,
  }) {
    return navigatorContext?.pushReplacementNamed(
      Screen.faceAuthMatchingErrorScreen.name,
      extra: FaceAuthMatchingErrorScreenArg(
        flowType: flowType,
        errorUIModel: errorUIModel,
        errorType: errorType,
      ),
    );
  }

  final EkycV2FlowType flowType;
  final ErrorUIModel errorUIModel;
  final FaceAuthMatchingErrorType errorType;

  const FaceAuthMatchingErrorScreen({
    required this.flowType,
    required this.errorUIModel,
    super.key,
    this.errorType = FaceAuthMatchingErrorType.other,
  });

  @override
  State<FaceAuthMatchingErrorScreen> createState() => _FaceAuthMatchingErrorScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.faceAuthMatchingErrorScreen.routeName);
}

class _FaceAuthMatchingErrorScreenState extends EvoPageStateBase<FaceAuthMatchingErrorScreen>
    with EkycV2FlowMixin {
  final FaceAuthSelfieCapturingCubit _selfieCapturingCubit = FaceAuthSelfieCapturingCubit();

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<FaceAuthSelfieCapturingCubit>(
      create: (_) => _selfieCapturingCubit,
      child: BlocConsumer<FaceAuthSelfieCapturingCubit, FaceAuthSelfieCapturingState>(
        listener: _listenSelfieCapturingCubit,
        builder: (BuildContext context, _) {
          return PopScope(
            canPop: false,
            child: Scaffold(
              backgroundColor: evoColors.background,
              appBar: EvoAppBar(
                leading: CloseButton(
                  color: evoColors.icon,
                  onPressed: () {
                    _handleCloseButton(context);
                  },
                ),
              ),
              body: SafeArea(
                child: switch (widget.errorType) {
                  FaceAuthMatchingErrorType.limitExceed => EkycV2BlockWidget(
                      title: EvoStrings.faceOtpMatchingErrorTitle,
                      description:
                          widget.errorUIModel.userMessage ?? CommonStrings.otherGenericErrorMessage,
                      imageUrl: EvoImages.imgFaceOtpError,
                      buttonTitle: EvoStrings.close,
                      onButtonTap: () {
                        _handleCTAButton(context);
                      },
                    ),
                  FaceAuthMatchingErrorType.sessionExpired => EkycV2BlockWidget(
                      title: EvoStrings.faceOtpSessionExpireTitle,
                      description: EvoStrings.faceOtpSessionExpireDescription,
                      imageUrl: EvoImages.imgFaceOtpSessionExpired,
                      buttonTitle: EvoStrings.faceOtpMatchingRetryTitle,
                      onButtonTap: () {
                        _handleCTAButton(context);
                      },
                    ),
                  FaceAuthMatchingErrorType.other => EkycV2BlockWidget(
                      title: EvoStrings.faceOtpMatchingErrorTitle,
                      description:
                          widget.errorUIModel.userMessage ?? CommonStrings.otherGenericErrorMessage,
                      imageUrl: EvoImages.imgFaceOtpError,
                      buttonTitle: EvoStrings.faceOtpMatchingRetryTitle,
                      onButtonTap: () {
                        _handleCTAButton(context);
                      },
                    ),
                },
              ),
            ),
          );
        },
      ),
    );
  }

  void _handleCloseButton(BuildContext context) {
    switch (widget.errorType) {
      case FaceAuthMatchingErrorType.limitExceed:
        onEkycError(errorReason: EkycV2FlowErrorReason.exceedLimit);
        break;
      case FaceAuthMatchingErrorType.sessionExpired:
        onEkycError(errorReason: EkycV2FlowErrorReason.sessionError);
        break;
      case FaceAuthMatchingErrorType.other:
        onEkycError(errorReason: EkycV2FlowErrorReason.userCancelled);
        break;
    }
  }

  void _handleCTAButton(BuildContext context) {
    switch (widget.errorType) {
      case FaceAuthMatchingErrorType.limitExceed:
        onEkycError(errorReason: EkycV2FlowErrorReason.exceedLimit);
        break;
      case FaceAuthMatchingErrorType.sessionExpired:
        onEkycError(errorReason: EkycV2FlowErrorReason.sessionError);
        break;
      case FaceAuthMatchingErrorType.other:
        // retry with selfie capture flow
        _selfieCapturingCubit.startSelfieCapturing();
        break;
    }
  }

  void _listenSelfieCapturingCubit(
    BuildContext context,
    FaceAuthSelfieCapturingState state,
  ) {
    if (state is FaceAuthSelfieCapturingInProgressState) {
      EvoUiUtils().showHudLoading();
      return;
    }

    EvoUiUtils().hideHudLoading();

    // e.g. 401 error for session expired, the unauthorized pop-up will be displayed
    // we should not handle error state in this case
    if (isPageAtTopOfNavigationStack() == false) {
      return;
    }

    if (state is FaceAuthSelfieCapturingSuccessState) {
      FaceAuthMatchingProcessScreen.pushReplacementNamed(
        facialVerificationResult: state.successResult,
        flowType: widget.flowType,
      );
      return;
    }

    if (state is FaceAuthSelfieCapturingErrorState) {
      _handleSelfieCapturingErrorState(
        bridgeErrorReason: state.errorResult.bridgeErrorReason,
        apiErrorResponse: state.errorResult.apiErrorResponse,
      );
      return;
    }

    if (state is FaceAuthSelfieCapturingErrorNotInitializedState) {
      _handleSelfieCapturingErrorState(isNotInitialized: true);
      return;
    }
  }

  void _handleSelfieCapturingErrorState({
    BaseEntity? apiErrorResponse,
    EkycBridgeErrorReason? bridgeErrorReason,
    bool isNotInitialized = false,
  }) {
    if (bridgeErrorReason == EkycBridgeErrorReason.userCancelled) {
      // navigate user to instruction screen after user retry error and then cancel
      // ref: https://trustingsocial1.atlassian.net/browse/EMA-4835
      FaceAuthInstructionScreen.pushReplacementNamed(flowType: widget.flowType);
      return;
    }

    // callback to exist flow, allow user retry for re-initialize again
    if (isNotInitialized) {
      showSnackBarError(CommonStrings.otherGenericErrorMessage);
      onEkycError(errorReason: EkycV2FlowErrorReason.initializeError);
      return;
    }

    // otherwise, show ekyc error screen
    EkycV2ErrorScreen.pushNamed(
      isAbleToRetry: checkIsAbleToRetryFacialVerificationIfModuleHasError(
        apiErrorResponse: apiErrorResponse,
        bridgeErrorReason: bridgeErrorReason,
      ),
      onTapRetryButton: () {
        navigatorContext?.pop();
        _selfieCapturingCubit.startSelfieCapturing();
      },
      onTapCloseButton: () {
        navigatorContext?.pop();
        handleCloseFacialVerificationIfModuleHasError(
          apiErrorResponse: apiErrorResponse,
          bridgeErrorReason: bridgeErrorReason,
        );
      },
    );
  }
}
