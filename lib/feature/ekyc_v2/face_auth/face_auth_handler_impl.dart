import 'package:flutter/cupertino.dart';

import '../../../../prepare_for_app_initiation.dart';
import '../../login/login_shared_data.dart';
import '../ekyc_v2_flow_callback.dart';
import '../ekyc_v2_flow_type.dart';
import 'face_auth_handler.dart';
import 'starter_screen/face_auth_starter_screen.dart';

class FaceAuthHandlerImpl implements FaceAuthHandler {
  final EkycV2FlowType flowType;

  FaceAuthHandlerImpl({required this.flowType});

  @override
  EkycV2FlowType get getFlowType => flowType;

  @override
  Future<void> startFaceAuth({
    required String? sessionToken,
    required EkycV2FlowCallback callback,
    bool isReplaceScreen = true,
  }) async {
    saveFaceAuthFlowCallback(faceAuthCallback: callback);
    if (isReplaceScreen) {
      FaceAuthStarterScreen.pushReplacementNamed(
        flowType: flowType,
        callback: callback,
        sessionToken: sessionToken,
      );
      return;
    }

    FaceAuthStarterScreen.pushNamed(
      flowType: flowType,
      callback: callback,
      sessionToken: sessionToken,
    );
  }

  /// save face_otp flow callback
  /// [FaceAuthCallback] is callback that will be called when manual link face-otp flow is success or failed (include cancel)
  @visibleForTesting
  void saveFaceAuthFlowCallback({
    EkycV2FlowCallback? faceAuthCallback,
  }) {
    final AppState appState = getIt.get<AppState>();
    final LoginSharedData sharedData = appState.loginSharedData;
    sharedData.faceAuthCallback = faceAuthCallback;
  }
}
