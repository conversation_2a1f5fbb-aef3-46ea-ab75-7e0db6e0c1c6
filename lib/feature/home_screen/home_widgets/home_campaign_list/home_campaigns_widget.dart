import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/ui_component/ui_component.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/shimmer/shimmer.dart';

import '../../../../data/repository/campaign_repo.dart';
import '../../../../data/response/campaign_entity.dart';
import '../../../../data/response/campaign_list_entity.dart';
import '../../../../model/evo_action_model.dart';
import '../../../../model/promotion_status_ui_model.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/evo_action_handler.dart';
import '../../../../util/mapper.dart';
import '../../../../util/promotion/status_ui_data_creator_factory/promotion_source_data.dart';
import '../../../../util/promotion/status_ui_data_creator_factory/promotion_status_ui_creator_factory.dart';
import '../../../../widget/evo_image_provider_widget.dart';
import '../../../../widget/evo_inkwell_network_image.dart';
import '../../../../widget/evo_next_action/evo_next_action_widget.dart';
import '../../../campaign_list/other_widgets/campaign_expired_time_widget.dart';
import '../../../webview/models/evo_webview_arg.dart';
import 'home_campaigns_cubit.dart';
import 'skeleton_home_campaigns_widget.dart';

/// Campaign List
/// Link: https://trustingsocial1.atlassian.net/browse/EMA-270
class HomeCampaignsWidget extends UIComponentWidget {
  final VoidCallback? onViewMore;

  const HomeCampaignsWidget({
    super.key,
    super.controller,
    this.onViewMore,
  });

  @override
  State<HomeCampaignsWidget> createState() => _CampaignsHomeWidgetState();
}

class _CampaignsHomeWidgetState extends UIComponentWidgetState<HomeCampaignsWidget> {
  final HomeCampaignsCubit _cubit = HomeCampaignsCubit(campaignRepo: getIt.get<CampaignRepo>());

  @override
  void loadData() {
    _cubit.getCampaigns();
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<HomeCampaignsCubit>.value(
      value: _cubit,
      child: BlocConsumer<HomeCampaignsCubit, UiComponentState>(
          listener: (BuildContext context, UiComponentState state) {
        handleCommonEvent(state);

        if (state is UiComponentDataLoaded<CampaignListEntity>) {
          if (state.data?.campaigns?.isEmpty ?? true) {
            widget.controller?.onEmptyData?.call();
          }
        }
      }, builder: (_, UiComponentState state) {
        CampaignListEntity? campaignListEntity;
        if (state is UiComponentDataLoaded<CampaignListEntity>) {
          campaignListEntity = state.data;
        }

        final bool isLoading = state is UiComponentLoading;
        return ShimmerWidget(
            isLoading: isLoading,
            skeletonWidget: const SkeletonHomeCampaignsWidget(),
            child: isLoading ? const SizedBox.shrink() : _content(campaignListEntity));
      }),
    );
  }

  Widget _content(CampaignListEntity? listCampaign) {
    return listCampaign?.campaigns?.isNotEmpty == true
        ? Column(crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
            _titleCampaignList(),
            const SizedBox(height: 18),
            _listCampaignContent(listCampaign!.campaigns!),
            const SizedBox(height: 20),
          ])
        : const SizedBox.shrink();
  }

  Widget _titleCampaignList() {
    const double cornerRadius = 200;
    return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: <Widget>[
      Expanded(child: Text(EvoStrings.listVoucherTitle, style: evoTextStyles.h400())),
      Material(
          color: Colors.transparent,
          child: InkWell(
              borderRadius: BorderRadius.circular(cornerRadius),
              onTap: widget.onViewMore,
              child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(cornerRadius),
                      color: evoColors.primary.withOpacity(0.08)),
                  child: Text(EvoStrings.viewMore,
                      style: evoTextStyles
                          .bodySmall(color: evoColors.primary)
                          .copyWith(fontWeight: FontWeight.w700)))))
    ]);
  }

  Widget _listCampaignContent(List<CampaignEntity> campaignList) {
    return ListView.separated(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: campaignList.length,
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        separatorBuilder: (BuildContext context, int index) => const SizedBox(height: 12),
        itemBuilder: (BuildContext context, int index) {
          final CampaignEntity item = campaignList.elementAt(index);

          return Stack(children: <Widget>[
            EvoInkWellNetworkImage(item.banner,
                width: double.infinity,
                fit: BoxFit.fitWidth,
                cornerRadius: 16,
                typeImage: TypeImage.banner, onTap: () {
              _handleCampaignItemClick(context, item);
            }),
            _campaignExpiredDateWidget(
                validFromDateTime: item.startExpireDateTime,
                validToDateTime: item.endExpireDateTime)
          ]);
        });
  }

  Widget _nextActionButton(CampaignEntity campaign) {
    return EvoNextActionWidget(
      nextAction: campaign.action?.args?.nextAction?.toEvoActionModel(),
    );
  }

  Widget _campaignExpiredDateWidget({DateTime? validFromDateTime, DateTime? validToDateTime}) {
    final PromotionStatusUIModel? campaignStatusUIModel = PromotionStatusUICreatorFactory()
        .create(
      CampaignSourceData(
        validFromDate: validFromDateTime,
        validToDate: validToDateTime,
      ),
    ).createPromotionStatusUIData();

    if (campaignStatusUIModel == null) {
      return const SizedBox.shrink();
    }

    return Align(
        alignment: Alignment.topRight,
        child: Padding(
            padding: const EdgeInsets.all(16),
            child: CampaignExpiredTimeWidget(campaignStatusUIModel: campaignStatusUIModel)));
  }

  void _handleCampaignItemClick(BuildContext context, CampaignEntity item) {
    item.action?.toEvoActionModel().let((EvoActionModel it) async {
      final EvoWebViewArg arg = EvoWebViewArg(
        title: EvoStrings.campaignDetailTitle,
        url: it.args?.link,
        nextActionWidget: _nextActionButton(item),
      );

      EvoActionHandler().handle(it, arg: arg);
    });
  }
}
