import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_component/ui_component.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/campaign_repo.dart';
import '../../../../data/response/campaign_list_entity.dart';
import '../../../../util/mock_file_name_utils/mock_campaign_file_name.dart';

class HomeCampaignsCubit extends UiComponentCubit<UiComponentState>
    with SingleTaskManager<CampaignListEntity> {
  final CampaignRepo campaignRepo;

  HomeCampaignsCubit({required this.campaignRepo}) : super(UiComponentLoading());

  Future<void> getCampaigns() async {
    emit(UiComponentLoading());
    final CampaignListEntity campaignListEntity = await run(campaignRepo.getOffers(
        flowType: FlowType.offers,
        mockConfig: MockConfig(
          enable: false,
          fileName: getOffersMockFileName(),
        )));

    if (campaignListEntity.statusCode == CommonHttpClient.SUCCESS) {
      emit(UiComponentDataLoaded<CampaignListEntity>(data: campaignListEntity));
    } else {
      emit(UiComponentFailed(ErrorUIModel.fromEntity(campaignListEntity)));
    }
  }
}
