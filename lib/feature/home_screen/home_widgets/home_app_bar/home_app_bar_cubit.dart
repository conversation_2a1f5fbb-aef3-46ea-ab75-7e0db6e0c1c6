import 'package:flutter_common_package/base/common_cubit.dart';

import 'home_app_bar_state.dart';

class HomeAppBarCubit extends CommonCubit<HomeAppBarState> {
  HomeAppBarCubit() : super(HomeAppBarInitial());

  void loaded(bool status) {
    emit(HomeAppBarLoaded(status));
  }

  void loading(bool isLoading) {
    if (isLoading) {
      emit(HomeAppBarLoading(isLoading));
      return;
    }

    emit(HomeAppBarInitial());
  }
}
