import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../../../base/evo_page_state_base.dart';
import '../../../../../../model/evo_action_model.dart';
import '../../../../../../resources/global.dart';
import '../../../../../webview/models/evo_webview_arg.dart';
import '../story_config.dart';
import 'story_footer_widget.dart';

class StoryWebViewArg extends PageBaseArg {
  final EvoActionModel? footerAction;
  final EvoWebViewArg? evoWebViewArg;

  StoryWebViewArg({
    this.footerAction,
    this.evoWebViewArg,
  });
}

class StoryWebView extends PageBase {
  final StoryWebViewArg arg;

  const StoryWebView({
    required this.arg,
    super.key,
  });

  static void pushNamed({EvoActionModel? footerAction, EvoWebViewArg? evoWebViewArg}) {
    return navigatorContext?.pushNamed(
      Screen.storyWebViewScreen.name,
      extra: StoryWebViewArg(footerAction: footerAction, evoWebViewArg: evoWebViewArg),
    );
  }

  @override
  State<StoryWebView> createState() => _StoryWebViewState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.storyWebViewScreen.routeName);
}

class _StoryWebViewState extends EvoPageStateBase<StoryWebView> {
  @override
  Widget getContentWidget(BuildContext context) {
    return _buildItemBody();
  }

  Widget _buildItemBody() {
    return Stack(
      children: <Widget>[
        _buildWebView(),
        _buildItemFooter(action: widget.arg.footerAction),
      ],
    );
  }

  Widget _buildWebView() {
    final EvoWebViewArg? evoWebViewArg = widget.arg.evoWebViewArg;
    return AspectRatio(
      aspectRatio: StoryConfig.aspectRatioStoryImage,
      child: evoWebViewArg != null
          ? CommonWebView(arg: evoWebViewArg.copyWith(safeAreaBottom: false))
          : const SizedBox.shrink(),
    );
  }

  Widget _buildItemFooter({EvoActionModel? action}) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: StoryFooterWidget(action: action),
    );
  }
}
