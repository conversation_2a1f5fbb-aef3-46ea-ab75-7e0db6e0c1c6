import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/shimmer/shimmer_style.dart';

import '../../../../prepare_for_app_initiation.dart';
import '../../../feature_toggle.dart';

class ReminderShimmer extends StatelessWidget {
  const ReminderShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[..._buildReminderWidget(context)],
    );
  }

  List<Widget> _buildReminderWidget(BuildContext context) {
    final List<Widget> widgets = <Widget>[];
    if (getIt.get<FeatureToggle>().reminderVisibility) {
      widgets.addAll(<Widget>[
        Container(
          height: 100,
          width: context.screenWidth,
          decoration: ShimmerStyle.shimmerContainerDecoration,
        ),
        const SizedBox(height: 20),
      ]);
    }

    return widgets;
  }
}
