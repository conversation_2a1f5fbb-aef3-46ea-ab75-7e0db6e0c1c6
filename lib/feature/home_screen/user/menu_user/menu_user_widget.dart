import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/ui_component/ui_component.dart';
import 'package:flutter_common_package/widget/shimmer/shimmer.dart';

import '../../../../data/repository/user_repo.dart';
import '../../../../data/response/payment_method_list_entity.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../main_screen/main_screen_controller.dart';
import '../../../payment/qrcode_scanner/qrcode_scanner_screen.dart';
import '../../../promotion_list/promotion_list_page.dart';
import '../../../webview/models/evo_webview_arg.dart';
import 'menu_user_body_widget.dart';
import 'menu_user_cubit.dart';
import 'menu_user_shimmer_widget.dart';

enum MenuUserType { cardUsageGuide, reward, scanQr, question }

class MenuUserWidget extends UIComponentWidget {
  final MainScreenController? mainScreenController;

  const MenuUserWidget({
    super.key,
    super.controller,
    this.mainScreenController,
  });

  @override
  UIComponentWidgetState<MenuUserWidget> createState() => _MenuUserWidgetState();
}

class _MenuUserWidgetState extends UIComponentWidgetState<MenuUserWidget> {
  final MenuUserCubit _cubit = MenuUserCubit(
    userRepo: getIt.get<UserRepo>(),
    appState: getIt.get<AppState>(),
  );
  PaymentMethodListEntity? _linkedCardListEntity;

  @override
  Widget build(BuildContext context) {
    return BlocProvider<MenuUserCubit>(
        create: (_) => _cubit,
        child: BlocConsumer<MenuUserCubit, UiComponentState>(
            listener: (BuildContext context, UiComponentState state) {
          handleCommonEvent(state);
        }, builder: (BuildContext context, UiComponentState state) {
          if (state is UiComponentDataLoaded<PaymentMethodListEntity>) {
            _linkedCardListEntity = state.data;
          }
          final bool isLoading = state is UiComponentLoading;
          return ShimmerWidget(
              isLoading: isLoading,
              skeletonWidget: const MenuUserShimmer(),
              child: isLoading ? const SizedBox.shrink() : _itemBody(_linkedCardListEntity));
        }));
  }

  Widget _itemBody(PaymentMethodListEntity? entity) => entity?.paymentMethods?.isNotEmpty == true
      ? MenuUserBody(onTapMenuUser: onTapMenuUser)
      : const SizedBox.shrink();

  void onTapMenuUser(MenuUserType type) {
    switch (type) {
      case MenuUserType.cardUsageGuide:
        final EvoWebViewArg arg =
        EvoWebViewArg(url: WebsiteUrl.cardUsageGuideUrl, title: EvoStrings.cardUsageGuide);
        CommonWebView.pushNamed(arg: arg);
        break;
      case MenuUserType.reward:
        widget.mainScreenController?.jumpToPromotionPage(type: PromotionTabType.myVoucher);
        break;
      case MenuUserType.scanQr:
        _preparePaymentEntryPoint();
        QrCodeScannerScreen.openSingleInstance();
        break;
      case MenuUserType.question:
        final EvoWebViewArg arg =
            EvoWebViewArg(url: WebsiteUrl.evoFaqUrl, title: EvoStrings.frequentlyQuestions);
        CommonWebView.pushNamed(arg: arg);
        break;
    }
  }

  void _preparePaymentEntryPoint() {
    _cubit.preparePaymentDataIfNeed();
  }

  @override
  void loadData() {
    _cubit.getPaymentMethods();
  }
}
