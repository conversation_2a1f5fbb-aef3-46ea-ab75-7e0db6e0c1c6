import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/shimmer/shimmer.dart';

class MenuUserShimmer extends StatelessWidget {
  final double iconSize;

  const MenuUserShimmer({super.key, this.iconSize = 48});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            _itemIcon(),
            _itemIcon(),
            _itemIcon(),
            _itemIcon(),
          ],
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _itemIcon() => Column(
        children: <Widget>[
          SkeletonContainer(
            height: iconSize,
            width: iconSize,
          ),
          const SizedBox(height: 8),
          Skeleton<PERSON>ontainer(width: iconSize),
          const SizedBox(height: 10),
        ],
      );
}
