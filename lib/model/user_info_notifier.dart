import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../data/response/user_information_entity.dart';

class UserInfoNotifier extends ValueNotifier<UserInformationEntity?> {
  UserInfoNotifier(super.value);

  void update(UserInformationEntity? newValue) {
    if (value == newValue) {
      commonLog('Ignore update because newValue is same value with current data');
      return;
    }

    value = newValue;
  }
}
