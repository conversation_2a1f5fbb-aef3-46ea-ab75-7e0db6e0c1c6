import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';

import 'colors.dart';
import 'colors_v2.dart';
import 'input_borders.dart';
import 'text_styles_v2.dart';

export 'animations.dart';
export 'colors.dart';
export 'colors_v2.dart';
export 'dimensions.dart';
export 'global.dart';
export 'images.dart';
export 'text_styles.dart';
export 'text_styles_v2.dart';
export 'ui_strings.dart';

final CommonImageProvider evoImageProvider = getIt.get<CommonImageProvider>();
final CommonTextStyles evoTextStyles = getIt.get<CommonTextStyles>();
final EvoColors evoColors = getIt.get<EvoColors>();
final EvoColorsV2 evoColorsV2 = getIt.get<EvoColorsV2>();
final EvoTextStylesV2 evoTextStylesV2 = getIt.get<EvoTextStylesV2>();
final CommonButtonStyles evoButtonStyles = getIt.get<CommonButtonStyles>();
final EvoInputBorders evoInputBorders = getIt.get<EvoInputBorders>();
