import 'package:flutter/material.dart';

import 'resources.dart';

class EvoInputBorders {
  // Default border
  InputBorder get defaultBorder => _getInputBorder(evoColors.textHint);

  InputBorder get defaultFocusedBorder => _getInputBorder(evoColors.primary);

  InputBorder get defaultErrorBorder => _getInputBorder(evoColors.error);

  // Non border
  InputBorder get nonBorder => InputBorder.none;

  InputBorder _getInputBorder(Color color) {
    return UnderlineInputBorder(
      borderSide: BorderSide(
        color: color,
        width: 1.5,
      ),
    );
  }
}
