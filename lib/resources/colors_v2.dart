// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.
import 'package:flutter/material.dart';

import 'colors.dart';

class EvoColorsV2 extends EvoColors {
  /// ------------------------------------------------------------------------
  /// EVO Design System Colors: https://www.figma.com/design/VeebsA8zVSN10H5y1jjUzu/Revamp---EVO-App?node-id=2477-12950&t=Ny9IfY59hFFNVAot-4
  /// Do not touch unless you are sure about the changes (figma design system change)
  /// Note: when designer provide a new color, please request them to update the Figma design system colors and add it here
  /// Do not use the color directly for specific UI element, always refer to this class.

  final Color transparent = const Color(0x00000000);
  final Color black = const Color(0xFF000000);
  final Color white = const Color(0xFFFFFFFF);

  /// A. Primitive colors from Figma
  /// 1. Primary
  final Color primary1 = const Color(0xFFFAFEFA);
  final Color primary2 = const Color(0xFFF4FBF5);
  final Color primary3 = const Color(0xFFE6F8E7);
  final Color primary4 = const Color(0xFFD6F2D8);
  final Color primary5 = const Color(0xFFC3EBC6);
  final Color primary6 = const Color(0xFFAAE1AF);
  final Color primary7 = const Color(0xFF88D290);
  final Color primary8 = const Color(0xFF52BE64);
  final Color primary9 = const Color(0xFF059937); // main
  final Color primary10 = const Color(0xFF0B8A32);
  final Color primary11 = const Color(0xFF00822A);
  final Color primary12 = const Color(0xFF1A3E1F);

  /// 2. Neutral
  final Color neutral1 = const Color(0xFFFCFDFC);
  final Color neutral2 = const Color(0xFFF9FBF9);
  final Color neutral3 = const Color(0xFFF1F3F1);
  final Color neutral4 = const Color(0xFFEAECEA);
  final Color neutral5 = const Color(0xFFE2E4E2);
  final Color neutral6 = const Color(0xFFDADDDA);
  final Color neutral7 = const Color(0xFFD3D5D2);
  final Color neutral8 = const Color(0xFFB9BCB8);
  final Color neutral9 = const Color(0xFF888D86);
  final Color neutral10 = const Color(0xFF7B7F7A);
  final Color neutral11 = const Color(0xFF5F645E);
  final Color neutral12 = const Color(0xFF1D211C);

  /// 3. Neutral alpha
  final Color neutralAlpha1 = const Color.fromRGBO(0, 0, 0, 1.2 / 100);
  final Color neutralAlpha2 = const Color.fromRGBO(0, 0, 0, 2.4 / 100);
  final Color neutralAlpha3 = const Color.fromRGBO(0, 0, 0, 5.5 / 100);
  final Color neutralAlpha4 = const Color.fromRGBO(0, 0, 0, 7.8 / 100);
  final Color neutralAlpha5 = const Color.fromRGBO(0, 0, 0, 10.6 / 100);
  final Color neutralAlpha6 = const Color.fromRGBO(0, 0, 0, 13.3 / 100);
  final Color neutralAlpha7 = const Color.fromRGBO(0, 0, 0, 16.9 / 100);
  final Color neutralAlpha8 = const Color.fromRGBO(0, 0, 0, 26.7 / 100);
  final Color neutralAlpha9 = const Color.fromRGBO(0, 0, 0, 44.7 / 100);
  final Color neutralAlpha10 = const Color.fromRGBO(0, 0, 0, 49.8 / 100);
  final Color neutralAlpha11 = const Color.fromRGBO(0, 0, 0, 60.8 / 100);
  final Color neutralAlpha12 = const Color.fromRGBO(0, 0, 0, 87.5 / 100);

  /// B. Semantic colors from Figma
  /// 1. Background
  /// 1.1. Background Neutral
  late final Color backgroundNeutralPage = neutral1;
  late final Color backgroundNeutralBackground = neutral12;
  late final Color backgroundNeutralContainer = neutral3;
  late final Color backgroundNeutralElement = neutral4;
  late final Color backgroundNeutralDisable = neutralAlpha2;
  late final Color backgroundNeutralFlat = neutralAlpha9;
  late final Color backgroundNeutralLoading = neutralAlpha1;

  /// 1.2. Background Primary
  late final Color backgroundPrimaryElement = primary9;
  late final Color backgroundPrimaryElement2 = primary3;
  late final Color backgroundPrimaryBackground = primary2;
  late final Color backgroundPrimaryContainer = primary2;

  /// 1.3. Background Status
  /// 1.3.1. Background Status Success
  late final Color backgroundStatusSuccessElement = neutral1;
  late final Color backgroundStatusSuccessBackground = const Color(0xFF2B9A66);

  /// 1.3.2. Background Status Warning
  late final Color backgroundStatusWarningElement = const Color(0xFFFFBA1A);
  late final Color backgroundStatusWarningBackground = const Color(0xFFFFF7C2);

  /// 1.3.3. Background Status Fail
  late final Color backgroundStatusFailElement = const Color(0xFFE54D2E);
  late final Color backgroundStatusFailBackground = const Color(0xFFFEEBE7);

  /// 1.3.3. Background Status Info
  late final Color backgroundStatusInfoElement = const Color(0xFF007AFF);
  late final Color backgroundStatusInfoBackground = const Color(0xFFF0F1FE);

  /// 2. Border
  late final Color borderPrimary = primary9;
  late final Color borderSubtle = neutral4;
  late final Color borderLine = neutral6;
  late final Color borderContainer = neutral8;
  late final Color borderActive = neutral12;

  /// 3. Text
  late final Color textPrimary = primary9;
  late final Color textWhite = neutral1;
  late final Color textTitle = neutral12;
  late final Color textLabel = neutral12;
  late final Color textSubtitle = neutral11;
  late final Color textBody = neutral11;
  late final Color textDisable = neutral8;

  /// 4. Icon
  late final Color iconGreenPrimary = primary9;
  late final Color iconBlackPrimary = neutral12;
  late final Color iconWhitePrimary = neutral1;
  late final Color iconSecondary = neutral10;
  late final Color iconDisable = neutral10;

  /// 5. Status
  late final Color statusDanger = const Color(0xFFE54D2E);
  late final Color statusWarning = const Color(0xFFFFBA1A);
  late final Color statusInfo = const Color(0xFF007AFF);
  late final Color statusSuccess = const Color(0xFF30A66D);

  /// 6. Button
  /// 6.1. Button Accent
  /// 6.1.1. Button Accent Default
  late final Color buttonAccentDefaultText = neutral1;
  late final Color buttonAccentDefaultIcon = neutral1;
  late final Color buttonAccentDefaultBackground = neutral12;

  /// 6.1.2. Button Accent Disable
  late final Color buttonAccentDisableText = neutral8;
  late final Color buttonAccentDisableIcon = neutral8;
  late final Color buttonAccentDisableBackground = neutralAlpha3;

  /// 6.2. Button Secondary
  /// 6.2.1. Button Secondary Default
  late final Color buttonSecondaryDefaultText = neutral12;
  late final Color buttonSecondaryDefaultIcon = neutral12;
  late final Color buttonSecondaryDefaultBackground = neutral4;

  /// 6.2.2. Button Secondary Disable
  late final Color buttonSecondaryDisableText = neutral8;
  late final Color buttonSecondaryDisableIcon = neutral8;
  late final Color buttonSecondaryDisableBackground = neutralAlpha3;

  /// 6.3. Button Chip
  /// 6.3.1. Button Chip Default
  late final Color buttonChipDefaultText = primary9;
  late final Color buttonChipDefaultBorder = primary9;

  /// 6.3.2. Button Chip Disable
  late final Color buttonChipDisableText = neutral8;
  late final Color buttonChipDisableBorder = neutral8;

  /// 6.4. Button Link Text
  /// 6.4.1. Button Link Text Default
  late final Color buttonLinkTextDefaultText = primary9;
  late final Color buttonLinkTextDefaultIcon = primary9;

  /// 6.4.2. Button Link Text Disable
  late final Color buttonLinkTextDisableText = neutral8;
  late final Color buttonLinkTextDisableIcon = neutral8;

  /// 7. Input Icon
  late final Color inputIconDefault = neutral12;
  late final Color inputIconDisable = neutral7;

  /// 7. CheckboxAndRadio
  late final Color checkBoxRadioDefault = neutral12;
  late final Color checkBoxRadioDisable = neutral8;

  /// ------------------------------------------------------------------------
  /// Extended colors for Base CommonColors

  // region common
  @override
  Color get primary => primary9;

  @override
  Color get foreground => neutral12;

  @override
  Color get background => backgroundNeutralPage;

  @override
  Color get error => statusDanger;

  @override
  Color get highlighted => primary;

  @override
  Color get appBarShadow => transparent;

  @override
  Color get iconColor => iconBlackPrimary;

  @override
  Color get loadingViewColor => primary;

  // endregion

  // region text
  @override
  Color get textActive => neutral12;

  @override
  Color get textPassive => neutral11;

  @override
  Color get textPassive2 => neutral8;

  @override
  Color get textHint => neutral8;

  @override
  Color get textNormal => neutral11;

  @override
  Color get icon => neutral12;
  // endregion
}
