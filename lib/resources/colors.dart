import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/colors.dart';

class EvoColors extends CommonColors {
  // region page
  @override
  Color get primary => const Color(0xFF09B364);

  @override
  Color get foreground => const Color(0xFF1D1D1D);

  @override
  Color get background => const Color(0xFFFFFFFF);

  @override
  Color get error => const Color(0xFFE54D2E);

  @override
  Color get highlighted => const Color(0xFF09B364);

  @override
  Color get appBarShadow => const Color(0x14000000);

  // bottom sheet color
  @override
  Color get bottomSheetBackground => Colors.white;

  @override
  Color get bottomSheetSelectedItem => const Color(0xFF1D1D1D);

  @override
  Color get bottomSheetUnselectedItem => const Color(0xFFC2C2C2);

  // endregion

  // region text
  @override
  Color get textActive => const Color(0xFF1D1D1D);

  @override
  Color get textPassive => const Color(0xFF5E5E5E);

  @override
  Color get textPassive2 => const Color(0xFF999999);

  @override
  Color get textHint => const Color(0xFFD1D1D1);

  @override
  Color get textNormal => const Color(0xFF505560);

  @override
  Color get icon => const Color(0xFF1D1D1D);

  // endregion

  // region TextField
  @override
  Color get focusedTextFieldBorder => const Color(0xFF09B364);

  @override
  Color get textFieldBorder => const Color(0xFFD1D1D1);

  @override
  Color get disableTextFieldBorder => const Color(0xFFE9E9E9);

  @override
  Color get disableTextFieldBg => const Color(0x0A1D1D1D);

  @override
  Color get textFieldBg => background;

  @override
  Color get textSelectedBg => const Color(0x1D1D1D14);

  // endregion

  // region button
  // primary button
  @override
  Color get primaryButtonForeground => Colors.white;

  @override
  Color get primaryButtonBg => const Color(0xFF1D1D1D);

  @override
  Color get primaryButtonForegroundDisable => Colors.white;

  @override
  Color get primaryButtonBgDisable => const Color(0xFFE9E9E9);

  // secondary button
  @override
  Color get secondaryButtonForeground => const Color(0xFF09B364);

  @override
  Color get secondaryButtonBg => const Color(0xFFECF9F3);

  @override
  Color get secondaryButtonForegroundDisable => const Color(0xFFD1D1D1);

  @override
  Color get secondaryButtonBgDisable => const Color(0xFFE9E9E9);

  // accent button
  @override
  Color get accentButtonForeground => Colors.white;

  @override
  Color get accentButtonBg => const Color(0xFF09B364);

  @override
  Color get accentButtonForegroundDisable => Colors.white;

  @override
  Color get accentButtonBgDisable => const Color(0xFFD5F6E7);

  // tertiary button
  @override
  Color get tertiaryButtonForeground => const Color(0xFF1D1D1D);

  @override
  Color get tertiaryButtonBg => const Color(0xFFFFFFFF);

  @override
  Color get tertiaryButtonForegroundDisable => const Color(0xFFD1D1D1);

  @override
  Color get tertiaryButtonBgDisable => const Color(0xFFFFFFFF);

  // negative button
  @override
  Color get negativeButtonForeground => const Color(0xFFFFFFFF);

  @override
  Color get negativeButtonBg => const Color(0xFFE73F3C);

  @override
  Color get negativeButtonBgDisable => const Color(0xFFD1D1D1);

  @override
  Color get negativeButtonForegroundDisable => const Color(0xFFFFFFFF);

  // endregion

  // otp text input field
  @override
  Color get inputFocusedColor => const Color(0xFF09B364);

  @override
  Color get inputUnfocusedColor => textHint;

  @override
  Color get selectedRadioButton => const Color(0xFF60A5FA);

  // WebView loading Progress Bg;
  @override
  Color get webViewProgressBg => const Color(0xFFC2C2C2);

  @override
  Color get webViewProgressValue => const Color(0xFF4AC58D);

  @override
  Color get iconColor => const Color(0xFFE52722);

  @override
  Color get loadingViewColor => primary;

  /// Those below colors should be removed after revamp
  /// using primitive colors or semantic colors instead
  /// EVO checkbox
  Color get checkBoxChecked => const Color(0xFF1F71F4);

  Color get checkBoxUnChecked => const Color(0xFFFFFFFF);

  Color get checkBoxBorderUnChecked => const Color(0xFFD1D1D1);

  Color get checkBoxBorderChecked => const Color(0xFF1F71F4);

  /// Define Evo's color
  Color get secondaryBackground => const Color(0xFFFAFAFA);

  Color get profileContainerBackground => const Color(0xFFECF9F3);

  Color get inActiveStepIcon => const Color(0xFFC2C2C2);

  Color get snackBarSuccessBackground => profileContainerBackground;

  Color get snackBarSuccessBorder => const Color(0xFFD7F4E5);

  Color get inactiveSwitchBackground => disableTextFieldBorder;

  Color get activeSwitchBackground => primary;

  Color get snackBarErrorBackground => const Color(0xFFFFE5DC);

  Color get snackBarErrorBorder => const Color(0xFFFFC6B2);

  // Promotion
  Color get voucherBeforeRunningOutTime => const Color(0xFF09B364);

  Color get campaignBeforeRunningOutTime => const Color(0xFFF5A70B);

  Color get promotionRunningOutTime => const Color(0xFFF5A70B);

  Color get promotionNotRunningOutTime => const Color(0xFF999999);

  Color get transactionPending => const Color(0xFFF5A70B);

  Color get snackBarWarningBackground => const Color(0xFFFEF3DC);

  Color get snackBarWarningBorder => const Color(0xFFFEE7B9);

  Color get snackBarNeutralBackground => const Color(0xFFEBF3FF);

  Color get snackBarNeutralBorder => const Color(0xFFBFDBFE);

  Color get snackBarDefaultBackground => background;

  Color get snackBarDefaultBorder => foreground;

  Color get promotionIsUsed => const Color(0xFFC2C2C2);

  Color get promotionTimeout => const Color(0xFF5E5E5E);

  Color get promotionHotTime => const Color(0xFFE54D2E);

  Color get promotionText => const Color(0xFFFFFFFF);

  Color get campaignIsComing => const Color(0xFF09B364);

  Color get textActiveReminder => Colors.black;

  Color get paymentPromotionTitle => const Color(0xFF0F0F0F);

  Color get paymentPromotionCardItemShadow => const Color(0xFF000008);

  Color get promotionItemUnqualifiedBorder => const Color(0xFFFBC760);

  Color get promotionItemUnqualifiedText => const Color(0xFFF5A70B);

  // Referral
  Color get referralNewMemberText => const Color(0xFFFFFFFF);

  // Transaction history
  Color get transactionHistoryProcessing => const Color(0xFFF5A70B);

  Color get transactionHistorySuccess => const Color(0xFF01AA4F);

  Color get transactionHistoryFailure => const Color(0xFFE54D2E);

  Color get transactionListHistoryEmiLabel => const Color(0xFF1F71F4);

  Color get transactionListHistoryEmiNewLabel => const Color(0xFF0D6D40);

  Color get transactionHistoryBgProcessing => const Color(0xFFFEF3DC);

  Color get transactionHistoryBgSuccess => const Color(0xFFECF9F3);

  Color get transactionHistoryBgFailure => const Color(0xFFFFE5DC);

  Color get transactionListHistoryBgEmiLabel => const Color(0xFFEBF3FF);

  Color get transactionListHistoryBgEmiNewLabel => const Color(0xFFECF9F3);

  Color get placeholderBorder => disableTextFieldBorder;

  Color get transactionHistoryEmiStatusReceived => const Color(0xFF999999);

  Color get transactionHistoryEmiStatusApproved => const Color(0xFF01AA4F);

  Color get transactionHistoryEmiStatusDone => const Color(0xFF01AA4F);

  Color get transactionHistoryEmiStatusRejected => const Color(0xFFE54D2E);

  Color get transactionHistoryEmiStatusUnknown => const Color(0xFF999999);

  Color get transactionHistoryEmiTitleReceived => const Color(0xFF5E5E5E);

  Color get transactionHistoryEmiTitleApproved => const Color(0xFF01AA4F);

  Color get transactionHistoryEmiTitleDone => const Color(0xFF01AA4F);

  Color get transactionHistoryEmiTitleRejected => const Color(0xFFE54D2E);

  Color get transactionHistoryEmiTitleUnknown => const Color(0xFF5E5E5E);

  Color get transactionHistoryCashbackBackground => Colors.white;

  Color get transactionHistoryTotalAmountProgress => const Color(0xFFE9E9E9);

  Color get transactionHistoryPaidAmount => const Color(0xFF09B364);

  // Card linked
  Color get linkedCardLimitationBackground => const Color(0xFF1D1D1D).withOpacity(0.04);

  Color get linkedCardLimitationTitle => const Color(0xFF5E5E5E);

  Color get linkedCardLimitationValue => Colors.black;

  Color get linkedCardLimitationPerTrans => const Color(0xFF999999);

  Color get linkedCardLine => const Color(0xFF1D1D1D).withOpacity(0.08);

  // Intro
  Color get passiveIndicator => const Color(0x331D1D1D);

  Color get activeIndicator => const Color(0xFF222222);

  // Policy
  Color get activePrivatePolicy => const Color(0xFF1F71F4);

  // Feed back
  Color get feedBackEmail => const Color(0xFF1C1C1C);

  // Card status
  Color get cardStatusRemainingSteps => const Color(0xFFF5A70B);

  Color get creditLimitAwaitingForApproval => const Color(0xFF999999);

  Color get creditLimitNotReadyForPayment => const Color(0xFF1D1D1D);

  Color get creditLimitReadyForPaymentOrOutOfSync => const Color(0xFF09B364);

  Color get creditLimitIconApprovedOrOutOfSync => const Color(0xFF1D1D1D);

  Color get creditLimitIconAwaitingForApproval => const Color(0xFF999999);

  Color get cardStatusTitleNotReadyForPayment => const Color(0xFF999999);

  Color get cardStatusTitleReadyForPaymentOrOutOfSync => const Color(0xFF09B364);

  // profile settings
  Color get settingsCardShadow => const Color(0x0A000000);

  // delete account
  Color get deleteAccountSuccessTitle => const Color(0xFF0D121C);

  Color get surveyItemBackground => const Color(0x0A1D1D1D);

  // referral QR code
  Color get referralQrCodeBackground => const Color(0xFF0D6C40);

  Color get referralQrCodeWhiteText => const Color(0xFFFFFFFF);

  // Payment V2
  Color get paymentInputAmountV2TextHint => const Color(0xFFC2C2C2);

  Color get paymentManualLinkCard => const Color(0xFFF5A70B);

  Color get pendingTransactionNoteLeftBorder => const Color(0xFF1F71F4);

  //EMI
  Color get emiTenorBackground => const Color(0xFF1C1C1C);

  Color get emiContainerBackground => const Color(0xFFECF9F3);

  Color get paymentResultEmiNoteErrorShadow => Colors.black;

  Color get paymentResultEmiMoreDetailBackground => const Color(0xFF1D1D1D).withOpacity(0.08);

  Color get emiRegularText => emiTenorBackground;

  Color get emiVoucherSelectedBackground => const Color(0xFFECF9F3);

  Color get emiInvalidVoucherSelectedBorder => const Color(0xFFF5A70B);

  Color get mwgEmiInvalidVoucherSelectedBackground => const Color(0xFFFEF3DC);

  Color get mwgEmiInvalidVoucherSelectedBorder => const Color(0xFFF5A70B);

  // Revamp Home Page for non-user
  Color get storyProgressBarBackground => Colors.white.withOpacity(0.3);

  Color get storyTitleColor => const Color(0xFF010F1D).withOpacity(0.7);

  Color get storyViewDetailColor => const Color(0xFF000000);

  Color get storyViewFooterBackgroundColor => const Color(0xFFFFFFFF);

  Color get storyViewFooterShadowColor => const Color(0xFF000000).withOpacity(0.08);

  Color get storyViewFooterTitleColor => const Color(0xFF5E5E5E);

  // Pos limit enable guide
  Color get posLimitIndicatorTextColor => const Color(0xFFFFFFFF);

  Color get posLimitIndicatorEnable => const Color(0xFFFFFFFF);

  Color get posLimitIndicatorDisable => const Color(0xFF5E5E5E);

  Color get posLimitIndicatorBackground => const Color(0xFF000000);

  // Remind POS limit enable guide
  Color get remindPosLimitTitle => const Color(0xFF000000);

  Color get remindPosLimitDescription => const Color(0xFF5E5E5E);

  Color get remindPosLimitIndicator => const Color(0xFF000000);

  Color get remindPosLimitIndicatorBackground => const Color(0xFFFFFFFF);

  Color get remindPosLimitIndicatorBorder => const Color(0xFF000000);

  Color get remindPosLimitIndicatorShadow => const Color(0xFFFFFFFF).withOpacity(0.08);

  Color get remindPosLimitIndicatorText => const Color(0xFF000000);

  /// EMI Management
  Color get emiManagementApproved => const Color(0xFF09B364);

  Color get emiManagementRejected => const Color(0xFFE54D2E);

  Color get emiManagementProcessing => const Color(0xFF999999);

  Color get emiManagementBottomBorder => const Color(0xFFE9E9E9);

  Color get emiManagementDefaultItemBackground => Colors.white;

  Color get emiManagementAmountItem => const Color(0xFF0F0F0F);

  Color get emiManagementPaidAmount => const Color(0xFF09B364);

  Color get emiManagementTotalAmountProgress => const Color(0xFFE9E9E9);

  // Active POS Limit
  Color get activatedCardGuideColor => const Color(0xFF1F71F4);

  // setup POS limit
  Color get itemPOSLimitSuggestionBg => const Color(0xFFE9E9E9);

  Color get posLimitHintTextColor => const Color(0x331D1D1D);

  /// Circular countdown
  Color get countdownTotalColor => const Color(0xFFD1D1D1);

  Color get countdownBackgroundGradientCenterColor => const Color(0xffEEFBF5);

  /// Divider
  Color get divider => const Color(0xFFE9E9E9);
}
