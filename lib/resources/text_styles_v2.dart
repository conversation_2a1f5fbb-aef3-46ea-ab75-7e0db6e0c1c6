// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.
import 'package:flutter/material.dart';

import 'resources.dart';

class EvoTextStylesV2 extends EvoTextStyles {
  EvoTextStylesV2() : super(fontFamily: 'Inter');

  /// Style for Text widgets that use the Inter font family.
  /// Figma: https://www.figma.com/design/VeebsA8zVSN10H5y1jjUzu/Revamp---EVO-App?node-id=2301-5791&t=PlqQQhK6Zh5djZkv-4
  TextStyle heading(HeadingTextType type, {Color? color}) => TextStyle(
        fontFamily: fontFamily,
        color: color ?? evoColorsV2.textTitle,
        fontSize: type.fontSize,
        height: type.height,
        fontWeight: type.fontWeight,
        letterSpacing: type.letterSpacing,
      );

  TextStyle body(BodyTextType type, {Color? color}) => TextStyle(
        fontFamily: fontFamily,
        color: color ?? evoColorsV2.textBody,
        fontSize: type.fontSize,
        height: type.height,
        fontWeight: type.fontWeight,
      );

  TextStyle caption(CaptionTextType type, {Color? color}) => TextStyle(
        fontFamily: fontFamily,
        color: color ?? evoColorsV2.textSubtitle,
        fontSize: type.fontSize,
        height: type.height,
        fontWeight: type.fontWeight,
      );

  TextStyle label(LabelTextType type, {Color? color}) => TextStyle(
        fontFamily: fontFamily,
        color: color ?? evoColorsV2.textLabel,
        fontSize: type.fontSize,
        height: type.height,
        fontWeight: type.fontWeight,
        letterSpacing: type.letterSpacing,
      );
}

enum HeadingTextType {
  h0(fontWeight: FontWeight.w700, fontSize: 40, lineHeight: 48, figmaLetterSpacing: -4),
  h1(fontWeight: FontWeight.w600, fontSize: 28, lineHeight: 36),
  h2(fontWeight: FontWeight.w600, fontSize: 24, lineHeight: 28),
  h3(fontWeight: FontWeight.w600, fontSize: 20, lineHeight: 28),
  h4(fontWeight: FontWeight.w600, fontSize: 18, lineHeight: 24),
  h5(fontWeight: FontWeight.w600, fontSize: 16, lineHeight: 24),
  h6(fontWeight: FontWeight.w500, fontSize: 14, lineHeight: 20, figmaLetterSpacing: -1),
  h7(fontWeight: FontWeight.w600, fontSize: 12, lineHeight: 18, figmaLetterSpacing: -1);

  final FontWeight fontWeight;
  final double fontSize;
  final double lineHeight;
  final double figmaLetterSpacing;

  double get height => lineHeight / fontSize;

  double get letterSpacing => figmaLetterSpacing / 100 * fontSize;

  const HeadingTextType({
    required this.fontWeight,
    required this.fontSize,
    required this.lineHeight,
    this.figmaLetterSpacing = 0,
  });
}

enum BodyTextType {
  large(fontWeight: FontWeight.w400, fontSize: 16, lineHeight: 24),
  medium(fontWeight: FontWeight.w400, fontSize: 14, lineHeight: 20),
  small(fontWeight: FontWeight.w400, fontSize: 12, lineHeight: 18),
  smallCap(fontWeight: FontWeight.w500, fontSize: 12, lineHeight: 18);

  final FontWeight fontWeight;
  final double fontSize;
  final double lineHeight;

  double get height => lineHeight / fontSize;

  const BodyTextType({
    required this.fontWeight,
    required this.fontSize,
    required this.lineHeight,
  });
}

enum CaptionTextType {
  medium(fontWeight: FontWeight.w400, fontSize: 14, lineHeight: 20),
  small(fontWeight: FontWeight.w400, fontSize: 12, lineHeight: 18);

  final FontWeight fontWeight;
  final double fontSize;
  final double lineHeight;

  double get height => lineHeight / fontSize;

  const CaptionTextType({
    required this.fontWeight,
    required this.fontSize,
    required this.lineHeight,
  });
}

enum LabelTextType {
  xLarge(fontWeight: FontWeight.w500, fontSize: 18, lineHeight: 24),
  large(fontWeight: FontWeight.w600, fontSize: 16, lineHeight: 20, figmaLetterSpacing: -1),
  medium(fontWeight: FontWeight.w600, fontSize: 14, lineHeight: 18, figmaLetterSpacing: -1),
  small(fontWeight: FontWeight.w600, fontSize: 12, lineHeight: 18),
  xSmall(fontWeight: FontWeight.w600, fontSize: 10, lineHeight: 18);

  final FontWeight fontWeight;
  final double fontSize;
  final double lineHeight;
  final double figmaLetterSpacing;

  double get height => lineHeight / fontSize;

  double get letterSpacing => figmaLetterSpacing / 100 * fontSize;

  const LabelTextType({
    required this.fontWeight,
    required this.fontSize,
    required this.lineHeight,
    this.figmaLetterSpacing = 0,
  });
}
