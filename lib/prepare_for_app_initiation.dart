import 'dart:ui';

import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import 'package:evoapp/util/interceptor/maintenance_interceptor.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/common_package/intl.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/dio_http_client_impl.dart';
import 'package:flutter_common_package/data/http_client/dio_log_interceptor.dart';
import 'package:flutter_common_package/data/http_client/dio_request_option_mapper.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/flutter_downloader/common_flutter_downloader.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_common_package/util/uuid/uuid_generator.dart';
import 'package:flutter_common_package/widget/default_widgets.dart';
import 'package:local_auth/local_auth.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:trust_vision_plugin/trust_vision_plugin.dart';
import 'package:ts_bio_detect_changed/ts_bio_detect_changed.dart';

import 'data/repository/announcement_repo.dart';
import 'data/repository/announcement_repo_impl.dart';
import 'data/repository/authentication_repo.dart';
import 'data/repository/authentication_repo_impl.dart';
import 'data/repository/campaign_repo.dart';
import 'data/repository/campaign_repo_impl.dart';
import 'data/repository/cashback_repo.dart';
import 'data/repository/cashback_repo_impl.dart';
import 'data/repository/checkout_repo.dart';
import 'data/repository/checkout_repo_impl.dart';
import 'data/repository/common_repo.dart';
import 'data/repository/common_repo_impl.dart';
import 'data/repository/decree_consent_repo.dart';
import 'data/repository/decree_consent_repo_impl.dart';
import 'data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo.dart';
import 'data/repository/dop_native_repo/dop_native_ekyc_ui_only_repo_impl.dart';
import 'data/repository/dop_native_repo/dop_native_repo.dart';
import 'data/repository/dop_native_repo/dop_native_repo_impl.dart';
import 'data/repository/ekyc_repo.dart';
import 'data/repository/ekyc_repo_impl.dart';
import 'data/repository/emi_repo.dart';
import 'data/repository/emi_repo_impl.dart';
import 'data/repository/firebase_remote_config_impl.dart';
import 'data/repository/home_repo.dart';
import 'data/repository/home_repo_impl.dart';
import 'data/repository/merchant_repo.dart';
import 'data/repository/merchant_repo_impl.dart';
import 'data/repository/mock_test_config_repo.dart';
import 'data/repository/mock_test_config_repo_impl.dart';
import 'data/repository/qr_code_repo.dart';
import 'data/repository/qr_code_repo_impl.dart';
import 'data/repository/referral_repo.dart';
import 'data/repository/referral_repo_impl.dart';
import 'data/repository/user_repo.dart';
import 'data/repository/user_repo_impl.dart';
import 'data/response/card_status_entity.dart';
import 'data/response/reset_pin_entity.dart';
import 'data/response/user_deletion_verification_entity.dart';
import 'feature/activated_pos_limit/utils/activated_pos_limit_flow/activated_pos_limit_flow.dart';
import 'feature/activated_pos_limit/utils/activated_pos_limit_state.dart';
import 'feature/alice/v2/evo_alice_chatwoot_builder/evo_alice_chatwoot_builder.dart';
import 'feature/alice/v2/evo_alice_chatwoot_builder/evo_alice_chatwoot_builder_impl.dart';
import 'feature/announcement/model/announcement_info.dart';
import 'feature/announcement/utils/unread_announcement_checker.dart';
import 'feature/appsflyer/appsflyer_handler.dart';
import 'feature/appsflyer/one_link_utils.dart';
import 'feature/authorization_session_expired/authorization_session_expired.dart';
import 'feature/authorization_session_expired/authorization_session_expired_popup.dart';
import 'feature/biometric/biometric_token_module/biometrics_token_module.dart';
import 'feature/biometric/model/biometric_status_change_notifier.dart';
import 'feature/biometric/model/biometric_ui_model.dart';
import 'feature/biometric/request_user_active_biometric/request_user_active_biometric_handler.dart';
import 'feature/biometric/request_user_active_biometric/request_user_active_biometric_handler_impl.dart';
import 'feature/biometric/request_user_active_biometric/request_user_active_biometric_util.dart';
import 'feature/biometric/utils/biometric_status_helper.dart';
import 'feature/biometric/utils/biometric_status_helper_impl.dart';
import 'feature/biometric/utils/biometric_type_helper.dart';
import 'feature/biometric/utils/biometrics_authenticate.dart';
import 'feature/biometric/utils/biometrics_authenticate_impl.dart';
import 'feature/biometric_pin_confirm/biometric_and_pin_confirmation.dart';
import 'feature/deep_link/deep_link_handler.dart';
import 'feature/deep_link/deep_link_utils.dart';
import 'feature/deep_link/model/deep_link_shared_data.dart';
import 'feature/dop_native/dialogs/input_phone_number/cubit/dop_native_input_phone_cubit.dart';
import 'feature/dop_native/dop_native_constants.dart';
import 'feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/fpt_sdk_bridge.dart';
import 'feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/fpt_sdk_bridge_impl.dart';
import 'feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/nfc_reader/nfc_reader.dart';
import 'feature/dop_native/features/ekyc_ui_only/sdk_bridge/fpt/nfc_reader/nfc_reader_fpt.dart';
import 'feature/dop_native/features/ekyc_ui_only/sdk_bridge/tv_ekyc/ekyc_ui_only_bridge.dart';
import 'feature/dop_native/features/ekyc_ui_only/sdk_bridge/tv_ekyc/ekyc_ui_only_bridge_impl.dart';
import 'feature/dop_native/features/ekyc_ui_only/utils/dop_native_ekyc_api_response_handler/dop_native_ekyc_api_response_handler.dart';
import 'feature/dop_native/features/recaptcha/recaptcha_handler.dart';
import 'feature/dop_native/features/verify_otp/cubit/dop_native_verify_otp_cubit.dart';
import 'feature/dop_native/models/dop_native_state.dart';
import 'feature/dop_native/resources/dop_native_button_styles.dart';
import 'feature/dop_native/resources/dop_native_colors.dart';
import 'feature/dop_native/resources/dop_native_text_styles.dart';
import 'feature/dop_native/util/dop_functions.dart';
import 'feature/dop_native/util/dop_native_navigation_utils.dart';
import 'feature/dop_native/util/nfc_availability_wrapper/nfc_availability_wrapper.dart';
import 'feature/ekyc/ekyc_bridge/ekyc_bridge.dart';
import 'feature/ekyc/ekyc_bridge/ekyc_bridge_impl.dart';
import 'feature/ekyc/ekyc_sdk_helper/init_ekyc_sdk_helper.dart';
import 'feature/ekyc/ekyc_sdk_helper/init_ekyc_sdk_helper_impl.dart';
import 'feature/ekyc/ekyc_state.dart';
import 'feature/ekyc_v2/ekyc_v2_state.dart';
import 'feature/emi_management/utils/emi_management_utils.dart';
import 'feature/feature_toggle.dart';
import 'feature/home_screen/non_user/v2/story/widgets/story_indicator/story_indicator_widget.dart';
import 'feature/logging/event_tracking_shared_data.dart';
import 'feature/logging/evo_event_tracking_handler.dart';
import 'feature/logging/evo_event_tracking_utils/evo_event_tracking_utils.dart';
import 'feature/logging/evo_event_tracking_utils/evo_event_tracking_utils_impl.dart';
import 'feature/logging/evo_navigator_observer.dart';
import 'feature/login/login_shared_data.dart';
import 'feature/maintenance/maintenance_handler.dart';
import 'feature/maintenance/maintenance_handler_impl.dart';
import 'feature/manual_link_card/manual_link_card_cubit.dart';
import 'feature/manual_link_card/model/manual_link_card_shared_data.dart';
import 'feature/mock_test/build_mode.dart';
import 'feature/mock_test/mock_test_helper.dart';
import 'feature/payment/base_page_payment/cubit/update_order_cubit.dart';
import 'feature/payment/confirm_payment/bloc/confirm_button_cubit/confirm_button_cubit.dart';
import 'feature/payment/confirm_payment/bloc/confirm_payment_cubit.dart';
import 'feature/payment/confirm_payment/bloc/order_info_cubit.dart';
import 'feature/payment/emi_option_screen/bloc/emi_option_screen_cubit.dart';
import 'feature/payment/payment_shared_data.dart';
import 'feature/payment/qrcode_scanner/bloc/after_parse/qr_scanner_after_parse_cubit.dart';
import 'feature/payment/qrcode_scanner/bloc/parse_qr_code/parse_qr_code_cubit.dart';
import 'feature/payment/qrcode_scanner/bloc/setup_emi_condition/setup_emi_condition_cubit.dart';
import 'feature/payment/qrcode_scanner/widget/evo_qr_code_controller.dart';
import 'feature/payment/utils/active_pos_limit_handler/active_pos_limit_handler.dart';
import 'feature/payment/utils/auto_apply_voucher_handler/auto_apply_voucher_handler.dart';
import 'feature/payment/utils/auto_apply_voucher_handler/auto_apply_voucher_handler_impl.dart';
import 'feature/payment/utils/payment_with_emi_utils.dart';
import 'feature/payment/utils/voucher_detail_action_helper.dart';
import 'feature/pin/reset_pin/reset_pin_handler.dart';
import 'feature/pin/reset_pin/reset_pin_handler_impl.dart';
import 'feature/pin/reset_pin/reset_pin_ui_handler.dart';
import 'feature/profile/profile_screen/card_status/widget/credit_limit_widget/credit_limit_amount/credit_limit_amount_cubit.dart';
import 'feature/remote_config/remote_config_helper.dart';
import 'feature/sharing/sharing_feature.dart';
import 'feature/sharing/sharing_feature_impl.dart';
import 'feature/splash_screen/utils/exit_app_feature/exit_app_feature.dart';
import 'feature/splash_screen/utils/exit_app_feature/exit_app_feature_impl.dart';
import 'feature/splash_screen/utils/secure_detection_utils/secure_detection.dart';
import 'feature/splash_screen/utils/secure_detection_utils/secure_detection_impl.dart';
import 'model/user_info_notifier.dart';
import 'resources/button_dimensions.dart';
import 'resources/button_styles.dart';
import 'resources/input_borders.dart';
import 'resources/resources.dart';
import 'util/download_file_handler/download_file_handler.dart';
import 'util/download_file_handler/download_file_handler_impl.dart';
import 'util/evo_flutter_wrapper.dart';
import 'util/evo_snackbar.dart';
import 'util/file_browser/evo_file_browser_helper.dart';
import 'util/file_browser/file_browser_helper.dart';
import 'util/functions.dart';
import 'util/http_local_monitoring.dart';
import 'util/interceptor/unauthorized_interceptor.dart';
import 'util/navigator/evo_router_navigator.dart';
import 'util/secure_storage_helper/secure_storage_helper.dart';
import 'util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'util/task_polling_handler/task_polling_handler.dart';
import 'util/token_utils/evo_jwt_helper_impl.dart';
import 'util/token_utils/jwt_helper.dart';
import 'util/url_launcher_uri_wrapper.dart';
import 'util/web_link_utils.dart';
import 'widget/evo_default_widget.dart';
import 'widget/evo_overlay/evo_overlay_util_functions.dart';

class AppState {
  AppState._();

  static AppState? _singleton;

  factory AppState() {
    _singleton ??= AppState._();
    return _singleton!;
  }

  Locale locale = const Locale('vi');

  String? appVersion;

  UserInfoNotifier userInfo = UserInfoNotifier(null);

  bool? isCheckShowUpdateOnce;

  bool isUserLogIn = false;

  BiometricTypeUIModel bioTypeInfo = BiometricTypeUIModel.faceAndFinger();

  BiometricStatusChangeNotifier biometricStatusChangeNotifier =
      BiometricStatusChangeNotifier(BiometricStatus.usable);

  AnnouncementInfo? _announcementInfo;

  AnnouncementInfo get announcementInfo {
    return _announcementInfo ??= AnnouncementInfo();
  }

  set announcementInfo(AnnouncementInfo? announcementInfo) {
    _announcementInfo = announcementInfo;
  }

  final PaymentSharedData paymentSharedData = PaymentSharedData();

  ManualLinkCardSharedData? _manualLinkCardSharedData;

  ManualLinkCardSharedData get manualLinkCardSharedData {
    return _manualLinkCardSharedData ??= ManualLinkCardSharedData();
  }

  set manualLinkCardSharedData(ManualLinkCardSharedData? manualLinkCardSharedData) {
    _manualLinkCardSharedData = manualLinkCardSharedData;
  }

  final EkycState ekycState = EkycState();
  final EkycV2State ekycV2State = EkycV2State();

  CardStatusEntity? cardStatus;

  /// When a user taps a notification, an action may need to be executed that requires the user to log in first.
  /// If the user is not logged in (e.g: Token is expired, ...), we will save the action in this field [actionAfterLogin] and redirect the user to the login screen.
  /// After the user has successfully logged in, we will execute this action in the [Screen.mainScreen].
  /// Reference: https://trustingsocial1.atlassian.net/browse/EMA-1211
  void Function()? actionAfterLogin;

  /// Delete Account Flow
  LoginSharedData? _loginSharedData;

  LoginSharedData get loginSharedData {
    return _loginSharedData ??= LoginSharedData();
  }

  /// Event Tracking
  EventTrackingSharedData? _eventTrackingSharedData;

  EventTrackingSharedData get eventTrackingSharedData {
    return _eventTrackingSharedData ??= EventTrackingSharedData();
  }

  // return the tracking screen id or undefined
  EventTrackingScreenId get currentScreenId =>
      eventTrackingSharedData.currentScreenId ?? EventTrackingScreenId.undefined;

  // return the unique_token of DOP Native or DOP Web flow
  String? get dopUniqueToken {
    // DOP Web for NFC flow
    final String? dopWebUniqueToken = dopNativeState.nfcSharedModel?.dopWebUniqueToken;
    if (dopWebUniqueToken != null) {
      return dopWebUniqueToken;
    }
    // DOP Native
    return dopNativeState.uniqueToken;
  }

  /// The [appLifecycleState] variable stores the app's life cycle state when didChangeAppLifecycleState is called in EvoPageStateBase.
  /// It can be used to check if the app is in the background OR if some system dialogs are showing.
  AppLifecycleState? appLifecycleState;

  bool get isAppOnForeground =>
      appLifecycleState == null || appLifecycleState == AppLifecycleState.resumed;

  /// DOP
  final DOPNativeState dopNativeState = DOPNativeState();

  /// Use to save DeepLink data when app opened by DeepLink
  /// Not cleared after the deep link is handled, but saved to be shared across app features
  final DeepLinkSharedData deepLinkSharedData = DeepLinkSharedData();

  /// Save Activated POS Limit State
  final ActivatedPOSLimitState activatedPOSLimitState = ActivatedPOSLimitState();
}

GetIt getIt = GetIt.instance;
const String nonAuthenticationHttpClientInstance = 'NonAuthenticationHttpClient';

Future<void> prepareForAppInitiation() async {
  _registerFeatureToggle();

  await initCommonPackage();

  _registerFlutterWrapper();

  _registerEvoUtilFunction();

  _registerEvoOverlayUtilFunctions();

  _registerSecureDetection();

  _registerExitAppFeature();

  _setUpMaintenanceHandler();

  await initAppState();

  await _registerNonAuthenticationHttpClient();

  await _registerDOPNativeHttpClient();

  _initializeFlutterDownloader();

  _setUpSessionExpiredHandler();

  _registerNotifyMessage();

  _registerLoggingFeatures();

  //init navigator
  getIt.registerLazySingleton<EvoNavigatorTypeFactory>(() => EvoNavigatorTypeFactory());
  getIt.registerLazySingleton<CommonNavigator>(() => EvoRouterNavigator());

  _registerUiComponents();

  _registerLocalStorages();

  _registerJwtHelper();

  _initFileBrowser();

  /// this method needs to be called after the [initCommonPackage] method is already called.
  await _registerRepositories();

  await registerBiometrics();

  _setUpDioInterceptor();

  _initTsRemoteConfig();

  _setUpActiveBiometricHandler();

  await _setUpBiometricStatusHelper();

  _registerResetPin();

  _registerUnreadAnnouncementChecker();

  _registerEKYC();

  _initMockTestHelper();

  _registerUrlLauncherWrapper();

  _registerSharingFeature();

  _registerCubits();

  _registerDeepLinkHandler();

  _registerVoucherDetailActionHelper();

  _registerStoryIndicatorController();

  _registerEvoAliceChatwootBuilder();

  _registerDOPNative();

  await _registerAppsflyer();

  _registerDownloadFeature();

  _registerOneLinkUtilFunction();

  _initDebugUtilsFunction();

  _registerTaskPollingHandler();

  _registerActivateCardPosLimit();

  _registerEvoMobileScanner();

  _registerInstanceFactory();
}

Future<void> _registerAppsflyer() async {
  final AppsFlyerOptions appsFlyerOptions = AppsFlyerOptions(
    afDevKey: AppsflyerHandler.appsflyerDevKey,
    appId: evoUtilFunction.iOSEvoAppId,
    showDebug: kDebugMode,
  );
  final AppsflyerSdk appsflyerSdk = AppsflyerSdk(appsFlyerOptions);

  final AppsflyerHandler appsflyerHandler = AppsflyerHandler(appsflyerSdk);
  await appsflyerHandler.init();
  getIt.registerSingleton<AppsflyerHandler>(appsflyerHandler);
}

void _registerEvoUtilFunction() {
  getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
  getIt.registerLazySingleton<EmiManagementUtilFunctions>(() => EmiManagementUtilFunctions());
}

void _registerEvoOverlayUtilFunctions() {
  getIt.registerLazySingleton<EvoOverlayUtilFunctions>(() => EvoOverlayUtilFunctions());
}

void _registerSecureDetection() {
  getIt.registerLazySingleton<SecureDetection>(() => SecureDetectionImpl());
}

void _registerExitAppFeature() {
  getIt.registerLazySingleton<ExitAppFeature>(() => ExitAppFeatureImpl());
}

Future<void> initAppState({Locale locale = const Locale('vi')}) async {
  final PackageInfo packageInfo = await getIt.getAsync<PackageInfo>();

  final AppState appState = AppState();
  appState.locale = locale;
  appState.appVersion = packageInfo.version;

  getIt.registerLazySingleton<AppState>(() => appState);

  Intl.defaultLocale = appState.locale.languageCode;
}

void _registerUiComponents() {
  getIt.registerLazySingleton<CommonButtonDimensions>(() => EvoButtonDimensions());
  getIt.registerLazySingleton<CommonButtonStyles>(() => EvoButtonStyles());
  getIt.registerLazySingleton<CommonDefaultWidgets>(() => EvoDefaultWidget());
  getIt.registerLazySingleton<EvoInputBorders>(() => EvoInputBorders());

  // Feature Toggle: enableRevampUiFeature
  if (getIt<FeatureToggle>().enableRevampUiFeature) {
    // color
    getIt.registerLazySingleton<CommonColors>(() => EvoColorsV2());
    getIt.registerLazySingleton<EvoColors>(() => EvoColorsV2());
    getIt.registerLazySingleton<EvoColorsV2>(() => EvoColorsV2());
    // text styles
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStylesV2());
    getIt.registerLazySingleton<EvoTextStyles>(() => EvoTextStylesV2());
    getIt.registerLazySingleton<EvoTextStylesV2>(() => EvoTextStylesV2());
  } else {
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<EvoColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
  }

  /// DOP native
  getIt.registerLazySingleton<DOPNativeColors>(() => DOPNativeColors());
  getIt.registerLazySingleton<DopNativeButtonStyles>(() => DopNativeButtonStyles());
  getIt.registerLazySingleton<DOPNativeTextStyles>(() => DOPNativeTextStyles());
}

void _registerEKYC() {
  getIt.registerLazySingleton<EkycBridge>(
      () => EkycBridgeImpl(TrustVisionPlugin.instance, getIt.get<LoggingRepo>()));
  getIt.registerLazySingleton<InitEKYCSdkHelper>(() => InitEKYCSdkHelperImpl());
  getIt.registerLazySingleton<NFCReader>(
    () => NFCReaderFPT(),
  );

  getIt.registerLazySingleton<EkycUiOnlyBridge>(() => EkycUiOnlyBridgeImpl(
        TrustVisionPlugin.instance,
        getIt.get<LoggingRepo>(),
      ));

  getIt.registerLazySingleton<FptSdkBridge>(() => FptSdkBridgeImpl(
        getIt.get<NFCReader>(),
      ));
}

void _registerNotifyMessage() {
  getIt.registerLazySingleton<EvoSnackBar>(() => EvoSnackBar(SnackBarWrapper()));
}

void _registerLocalStorages() {
  // Local storage
  getIt.registerLazySingleton<EvoLocalStorageHelper>(
      () => EvoSecureStorageHelperImpl(secureStorage: getIt.get<FlutterSecureStorage>()));
}

void _registerJwtHelper() {
  getIt.registerLazySingleton<JwtHelper>(() => EvoJwtHelperImpl());
}

void _initFileBrowser() {
  getIt.registerLazySingleton<FileBrowserHelper>(() => EvoFileBrowserHelperImpl(
        DevicePlatformImp(),
      ));
}

void _initMockTestHelper() {
  getIt.registerLazySingleton<MockTestHelper>(
    () => MockTestHelper(
      getIt.get<MockTestConfigRepo>(),
      EvoBuildModeImpl(),
    ),
  );
}

/// Because this method is using instances that provide by [initCommonPackage] method,
/// it needs to be called after the [initCommonPackage] method is already called.
Future<void> _registerRepositories() async {
  // User Repo
  getIt.registerLazySingleton<UserRepo>(() => UserRepoImpl(
        getIt.get<CommonHttpClient>(),
        getIt.get<EvoLocalStorageHelper>(),
      ));

  getIt.registerLazySingleton<AuthenticationRepo>(
    () => AuthenticationRepoImpl(
      evoHttpClient: getIt.get<CommonHttpClient>(),
      nonAuthenticationEvoHttpClient:
          getIt.get<CommonHttpClient>(instanceName: nonAuthenticationHttpClientInstance),
      evoLocalStorageHelper: getIt.get<EvoLocalStorageHelper>(),
    ),
  );

  getIt.registerLazySingleton<CampaignRepo>(() => CampaignRepoImpl(getIt.get<CommonHttpClient>()));

  getIt.registerLazySingleton<HomeRepo>(() => HomeRepoImpl(getIt.get<CommonHttpClient>()));

  getIt.registerLazySingleton<AnnouncementRepo>(
      () => AnnouncementRepoImpl(getIt.get<CommonHttpClient>()));

  getIt.registerLazySingleton<MerchantRepo>(() => MerchantRepoImpl(getIt.get<CommonHttpClient>()));

  getIt.registerLazySingleton<CheckOutRepo>(() => CheckoutRepoImpl(getIt.get<CommonHttpClient>()));
  getIt.registerLazySingleton<QrCodeRepo>(() => QrCodeRepoImpl(getIt.get<CommonHttpClient>()));
  getIt.registerLazySingleton<CommonRepo>(() => CommonRepoImpl(getIt.get<CommonHttpClient>()));
  getIt.registerLazySingleton<DecreeConsentRepo>(
      () => DecreeConsentRepoImpl(getIt.get<CommonHttpClient>()));
  getIt.registerLazySingleton<EKYCRepo>(() => EKYCRepoImpl(getIt.get<CommonHttpClient>()));
  getIt.registerLazySingleton<MockTestConfigRepo>(
      () => MockTestConfigRepoImpl(getIt.get<FileBrowserHelper>()));
  getIt.registerLazySingleton<ReferralRepo>(() => ReferralRepoImpl(getIt.get<CommonHttpClient>()));
  getIt.registerLazySingleton<EmiRepo>(() => EmiRepoImpl(getIt.get<CommonHttpClient>()));
  getIt.registerLazySingleton<CashbackRepo>(() => CashbackRepoImpl(getIt.get<CommonHttpClient>()));

  _registerDOPNativeRepositories();
}

const String getItInstanceNameForDOPNative = 'dop_native';

Future<void> _registerDOPNativeHttpClient() async {
  /// This http client is to server the DOP native flow.
  /// It is created to custom [HeaderKey.dopNativeAuthorization] header for DOP native flow.
  final Dio dopNativeAuthenticationDio = await getDio();

  getIt.registerLazySingleton<Dio>(
    () => dopNativeAuthenticationDio,
    instanceName: getItInstanceNameForDOPNative,
  );

  getIt.registerLazySingleton<CommonHttpClient>(
    () => DioClientImpl(
      getIt.get<Dio>(
        instanceName: getItInstanceNameForDOPNative,
      ),
      dioRequestOptionMapper: DioRequestOptionMapper(
        receiveTimeout: const Duration(seconds: DOPNativeConstants.receiveTimeoutInSecond),
        sendTimeout: const Duration(seconds: DOPNativeConstants.sendTimeoutInSecond),
      ),
    ),
    instanceName: getItInstanceNameForDOPNative,
  );

  dopNativeAuthenticationDio.interceptors.add(MaintenanceInterceptor(
    getIt.get<MaintenanceHandler>(),
  ));

  if (kDebugMode) {
    dopNativeAuthenticationDio.interceptors.add(
      DioLogInterceptor(
        responseHeaderKeyLogged: <String>[CommonHttpClient.xRequestIdHeader],
      ),
    );
  }
}

void _registerDOPNativeRepositories() {
  final CommonHttpClient dopHttpClient =
      getIt.get<CommonHttpClient>(instanceName: getItInstanceNameForDOPNative);
  getIt.registerLazySingleton<DOPNativeRepo>(() => DOPNativeRepoImpl(dopHttpClient));
  getIt.registerLazySingleton<DopNativeEkycUIOnlyRepo>(
      () => DopNativeEkycUIOnlyRepoImpl(dopHttpClient));
}

void _setUpSessionExpiredHandler() {
  getIt.registerLazySingleton<AuthorizationSessionExpiredHandler>(
      () => AuthorizationSessionExpiredHandlerImpl());

  getIt.registerLazySingleton<AuthorizationSessionExpiredPopup>(
      () => AuthorizationSessionExpiredPopup());
}

void _setUpMaintenanceHandler() {
  getIt.registerLazySingleton<MaintenanceHandler>(() => MaintenanceHandlerImpl());
}

void _setUpActiveBiometricHandler() {
  getIt.registerLazySingleton<RequestUserActiveBiometricUtil>(
      () => RequestUserActiveBiometricUtil(localStorageHelper: getIt.get<EvoLocalStorageHelper>()));

  getIt.registerFactory<RequestUserActivateBiometricHandler>(() =>
      RequestUserActiveBiometricHandlerImp(
          requestUserActiveBiometricUtil: getIt.get<RequestUserActiveBiometricUtil>(),
          biometricsAuthenticate: getIt.get<BiometricsAuthenticate>()));
}

Future<void> _setUpBiometricStatusHelper() async {
  getIt.registerLazySingleton<BiometricStatusHelper>(() => BiometricStatusHelperImpl());
}

Future<void> _registerNonAuthenticationHttpClient() async {
  /// We need a separate Dio to handle refresh token which will not apply [UnauthorizedInterceptor].
  /// Use can use it to make api call which don't need authentication (don't put access token to header)
  final Dio nonAuthenticationDio = await getDio();

  getIt.registerLazySingleton<Dio>(
    () => nonAuthenticationDio,
    instanceName: nonAuthenticationHttpClientInstance,
  );

  getIt.registerLazySingleton<CommonHttpClient>(
    () => DioClientImpl(
      getIt.get<Dio>(
        instanceName: nonAuthenticationHttpClientInstance,
      ),
      dioRequestOptionMapper: DioRequestOptionMapper(),
    ),
    instanceName: nonAuthenticationHttpClientInstance,
  );

  nonAuthenticationDio.interceptors.add(MaintenanceInterceptor(
    getIt.get<MaintenanceHandler>(),
  ));

  if (kDebugMode) {
    nonAuthenticationDio.interceptors.add(
      DioLogInterceptor(
        responseHeaderKeyLogged: <String>[CommonHttpClient.xRequestIdHeader],
      ),
    );
  }
}

/// This function allow you to add interceptors to Dio [https://pub.dev/packages/dio#interceptors]
/// Currently we add [UnauthorizedInterceptor] to handle get new access token when it's expired
void _setUpDioInterceptor() {
  final Dio dio = getIt.get<Dio>();
  dio.interceptors.addAll(<Interceptor>[
    MaintenanceInterceptor(
      getIt.get<MaintenanceHandler>(),
    ),
    UnauthorizedInterceptor(getIt<AuthorizationSessionExpiredHandler>(),
        getIt<AuthenticationRepo>(), getIt<EvoLocalStorageHelper>(), getIt<JwtHelper>(),
        ignoredRefreshTokenApiPath: <String>[
          switch (evoUtilFunction.getFacialVerificationVersion()) {
            FacialVerificationVersion.version_1 => AuthenticationRepoImpl.verifySignInUrlV1,
            FacialVerificationVersion.version_2 => AuthenticationRepoImpl.verifySignInUrlV2,
            FacialVerificationVersion.version_3 => AuthenticationRepoImpl.verifySignInUrlV3,
          },
        ],
        ignoredVerdictEmitUnauthorized: <String>[
          ResetPinEntity.verdictExpiredResetPinSession,
          ResetPinEntity.verdictInvalidResetPinSession,
          UserDeletionVerificationEntity.verdictExpiredDeleteAccountSession,
        ]),
  ]);

  if (kDebugMode) {
    dio.interceptors.add(
      DioLogInterceptor(
        responseHeaderKeyLogged: <String>[CommonHttpClient.xRequestIdHeader],
      ),
    );
  }
}

void _initTsRemoteConfig() {
  /// using Firebase as the Remote Config
  getIt.registerLazySingleton<RemoteConfigHelper>(() => RemoteConfigHelperImpl(
      remoteConfigRepo: FirebaseRemoteConfigImpl(FirebaseRemoteConfig.instance)));
}

Future<void> registerBiometrics() async {
  final BiometricsAuthenticate biometricAuthenticate =
      BiometricAuthenticateImpl(localAuth: LocalAuthentication(), isForceStrongBiometric: true);
  getIt.registerLazySingleton<BiometricsAuthenticate>(() => biometricAuthenticate);

  getIt.registerLazySingleton<TsBioDetectChanged>(() => TsBioDetectChanged());

  getIt.registerFactory<BiometricsTokenModule>(
    () => BiometricsTokenModule(
      biometricsAuthenticate: getIt.get<BiometricsAuthenticate>(),
      userRepo: getIt.get<UserRepo>(),
      secureStorageHelper: getIt.get<EvoLocalStorageHelper>(),
      bioDetectChanged: getIt.get<TsBioDetectChanged>(),
      jwtHelper: getIt.get<JwtHelper>(),
    ),
  );

  getIt.registerLazySingleton<BiometricTypeHelper>(() => BiometricTypeHelper(
      getIt.get<BiometricsAuthenticate>(),
      getIt.get<DeviceInfoPlugin>(),
      getIt.get<RemoteConfigHelper>()));
}

void _registerFeatureToggle() {
  getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());
}

void _registerResetPin() {
  getIt.registerFactory<ResetPinHandler>(
    () => ResetPinHandlerImpl(
      ResetPinUiHandler(),
      getIt.get<AuthenticationRepo>(),
    ),
  );
}

void _registerTaskPollingHandler() {
  getIt.registerFactory<TaskPollingHandler>(
    () =>
        TaskPollingHandlerImpl(sharedPreferencesHelper: getIt.get<CommonSharedPreferencesHelper>()),
  );
}

void _registerActivateCardPosLimit() {
  getIt.registerFactory<ActivatedPOSLimitFlow>(() => ActivatedPOSLimitFlowImpl());
}

void _registerUnreadAnnouncementChecker() {
  getIt.registerLazySingleton<UnreadAnnouncementChecker>(() => UnreadAnnouncementChecker(
        getIt.get<AppState>(),
        getIt.get<AnnouncementRepo>(),
      ));
}

void _registerFlutterWrapper() {
  getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());
}

void _registerUrlLauncherWrapper() {
  getIt.registerLazySingleton<UrlLauncherWrapper>(() => UrlLauncherWrapper());
}

void _registerSharingFeature() {
  getIt.registerFactory<SharingFeature>(() => SharingFeatureImpl());
}

void _initializeFlutterDownloader() {
  final CommonFlutterDownloader flutterDownloaderWrapper = getIt.get<CommonFlutterDownloader>();
  // no need to await, because this package only used in web_view, and it can initialize in parallel with the splash_screen
  flutterDownloaderWrapper.initialize();
}

void _registerCubits() {
  getIt.registerFactory<CreditLimitAmountCubit>(
    () => CreditLimitAmountCubit(getIt.get<EvoLocalStorageHelper>()),
  );
}

void _registerDeepLinkHandler() {
  getIt.registerLazySingleton<DeepLinkHandler>(() => DeepLinkHandler());
  getIt.registerLazySingleton<DeepLinkUtils>(() => DeepLinkUtils());
  getIt.registerLazySingleton<WebLinkUtils>(() => WebLinkUtils());
}

void _registerLoggingFeatures() {
  getIt.registerLazySingleton<EvoEventTrackingUtils>(() => EvoEventTrackingUtilsImpl());
  getIt.registerLazySingleton<EvoNavigatorObserver>(() => EvoNavigatorObserver());
  getIt.registerLazySingleton<EvoEventTrackingHandler>(() => EvoEventTrackingHandler());
}

void _registerVoucherDetailActionHelper() {
  getIt.registerLazySingleton<VoucherDetailActionHelper>(() => VoucherDetailActionHelperImpl());
}

void _registerStoryIndicatorController() {
  getIt.registerFactory<StoryIndicatorController>(() => StoryIndicatorController());
}

void _registerEvoAliceChatwootBuilder() {
  getIt.registerFactory<EvoAliceChatwootBuilder>(() => EvoAliceChatwootBuilderImpl());
}

void _registerDOPNative() {
  getIt.registerLazySingleton<DOPUtilFunctions>(() => DOPUtilFunctions());
  getIt.registerLazySingleton<DOPNativeNavigationUtils>(() => DOPNativeNavigationUtils());
  getIt.registerFactory<DOPNativeVerifyOtpCubit>(() => DOPNativeVerifyOtpCubit(
        dopNativeRepo: getIt.get<DOPNativeRepo>(),
        appState: getIt.get<AppState>(),
      ));
  getIt.registerFactory<DOPNativeInputPhoneNumberCubit>(() => DOPNativeInputPhoneNumberCubit(
        dopNativeRepo: getIt.get<DOPNativeRepo>(),
        appState: getIt.get<AppState>(),
      ));

  getIt.registerLazySingleton<EkycCommonApiResponsesHandler>(() => EkycCommonApiResponsesHandler());

  getIt.registerLazySingleton<RecaptchaHandler>(() => RecaptchaHandler());

  getIt.registerLazySingleton<NfcAvailabilityWrapper>(() => NfcAvailabilityWrapper());
}

void _registerDownloadFeature() {
  getIt.registerFactory<DownloadFileHandler>(
    () => DownloadFileHandlerImpl(downloader: getIt.get<CommonFlutterDownloader>()),
  );
}

void _registerOneLinkUtilFunction() {
  getIt.registerLazySingleton<OneLinkUtils>(() => OneLinkUtils());
}

void _initDebugUtilsFunction() {
  HttpLocalMonitoring().init(isDebugMode: kDebugMode);
}

void _registerEvoMobileScanner() {
  /// Make sure WidgetsFlutterBinding.ensureInitialized(); called first
  /// In this project we call it in main.dart
  final FlutterView view = WidgetsBinding.instance.platformDispatcher.views.first;
  final Size physicalSize = view.physicalSize;

  // Default ratio using on lib
  // Refer: https://github.com/juliansteenbakker/mobile_scanner/blob/develop/lib/src/mobile_scanner_controller.dart
  const double screenAspectRatio = 4 / 3;

  // fallback to 1080 width if can not get width or width = 0
  final double desiredWidth = physicalSize.width != 0 ? physicalSize.width : 1080.0;

  // Ensure calculated height is not zero or negative if aspect ratio is weird
  final double calculatedHeight = (desiredWidth * screenAspectRatio).clamp(1.0, double.infinity);

  getIt.registerFactory<EvoMobileScannerController>(
    () => EvoMobileScannerController(
      MobileScannerController(
        formats: <BarcodeFormat>[BarcodeFormat.qrCode],
        autoStart: false,
        cameraResolution: Size(desiredWidth, calculatedHeight),
      ),
    ),
  );
}

void _registerInstanceFactory() {
  getIt.registerFactory<ParseQRCodeCubit>(() => ParseQRCodeCubit(
        qrCodeRepo: getIt.get<QrCodeRepo>(),
        appState: getIt.get<AppState>(),
      ));
  getIt.registerFactory<SetupEmiConditionCubit>(() => SetupEmiConditionCubit());
  getIt.registerFactory<QrScannerAfterParseCubit>(() => QrScannerAfterParseCubit(
        checkOutRepo: getIt.get<CheckOutRepo>(),
        merchantRepo: getIt.get<MerchantRepo>(),
        paymentWithEMIUtils: PaymentWithEMIUtils(),
      ));
  getIt.registerFactory<ManualLinkCardCubit>(
      () => ManualLinkCardCubit(userRepo: getIt.get<UserRepo>()));
  getIt.registerFactory<ActivePosLimitHandler>(() => ActivePosLimitHandlerImpl());
  getIt.registerFactory<PaymentWithEMIUtils>(() => PaymentWithEMIUtils());

  getIt.registerFactory<EmiOptionScreenCubit>(() => EmiOptionScreenCubit(
        paymentWithEMIUtils: PaymentWithEMIUtils(),
      ));
  getIt.registerFactory<UpdateOrderCubit>(() => UpdateOrderCubit(
        checkOutRepo: getIt.get<CheckOutRepo>(),
        paymentWithEMIUtils: PaymentWithEMIUtils(),
      ));

  getIt.registerFactory<ConfirmPaymentCubit>(() => ConfirmPaymentCubit(
        checkOutRepo: getIt.get<CheckOutRepo>(),
        uuidGenerator: getIt.get<UUIDGenerator>(),
        evoLocalStorageHelper: getIt.get<EvoLocalStorageHelper>(),
      ));

  getIt.registerFactory<ConfirmButtonCubit>(() => ConfirmButtonCubit());
  getIt.registerFactory<BiometricAndPinConfirmation>(() => BiometricAndPinConfirmation(
        biometricsAuthenticate: getIt.get<BiometricsAuthenticate>(),
        secureStorageHelper: getIt.get<EvoLocalStorageHelper>(),
        jwtHelper: getIt.get<JwtHelper>(),
      ));

  getIt.registerFactory<OrderInfoCubit>(() => OrderInfoCubit(getIt.get<AppState>()));

  getIt.registerFactory<AutoApplyVoucherHandler>(() => AutoApplyVoucherHandlerImpl(
        getIt.get<CampaignRepo>(),
        getIt.get<AppState>(),
      ));
}
