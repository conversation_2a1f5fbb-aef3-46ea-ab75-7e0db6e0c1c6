import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'ts_bio_detect_changed_method_channel.dart';

abstract class TsBioDetectChangedPlatform extends PlatformInterface {
  /// Constructs a TsBioDetectChangedPlatform.
  TsBioDetectChangedPlatform() : super(token: _token);

  static final Object _token = Object();

  static TsBioDetectChangedPlatform _instance = MethodChannelTsBioDetectChanged();

  /// The default instance of [TsBioDetectChangedPlatform] to use.
  ///
  /// Defaults to [MethodChannelTsBioDetectChanged].
  static TsBioDetectChangedPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [TsBioDetectChangedPlatform] when
  /// they register themselves.
  static set instance(TsBioDetectChangedPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  /// return true, if biometrics on devices is added or removed one.
  /// for iOS, this function response true when adding or removing Biometrics entity
  /// for Android, this function ONLY true when adding Biometrics entity (class 3 - Strong)
  Future<bool?> isBiometricChanged() {
    throw UnimplementedError('isBiometricChanged() has not been implemented.');
  }

  /// save current Biometric entity, and using this data for detecting if biometrics changed or not
  /// this function should be called when end-user enable Biometric Authentication feature in App
  /// NOTE: this function MUST be called before using [isBiometricChanged] method
  Future<void> initialize() {
    throw UnimplementedError('init() has not been implemented.');
  }
}
