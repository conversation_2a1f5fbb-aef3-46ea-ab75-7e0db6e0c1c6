import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'nfc_plugin_flutter_method_channel.dart';

abstract class NfcPluginFlutterPlatform extends PlatformInterface {
  /// Constructs a NfcPluginFlutterPlatform.
  NfcPluginFlutterPlatform() : super(token: _token);

  static final Object _token = Object();

  static NfcPluginFlutterPlatform _instance = MethodChannelNfcPluginFlutter();

  /// The default instance of [NfcPluginFlutterPlatform] to use.
  ///
  /// Defaults to [MethodChannelNfcPluginFlutter].
  static NfcPluginFlutterPlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [NfcPluginFlutterPlatform] when
  /// they register themselves.
  static set instance(NfcPluginFlutterPlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('platformVersion() has not been implemented.');
  }
}
