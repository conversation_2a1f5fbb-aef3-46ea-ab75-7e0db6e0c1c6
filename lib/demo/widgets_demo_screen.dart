// Copyright (c) 2025 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';

import '../feature/term_and_condition/term_and_condition_utils.dart';
import '../feature/term_and_condition/widget/term_and_condition_checkbox_widget.dart';
import '../feature/term_and_condition/widget/term_and_condition_widget.dart';
import '../widget/evo_radio_widget.dart';
import '../widget/evo_checkbox_widget.dart';
import '../resources/resources.dart';

/// A demo screen showcasing various widgets from the Evo design system
class WidgetsDemoScreen extends StatefulWidget {
  const WidgetsDemoScreen({super.key});

  @override
  State<WidgetsDemoScreen> createState() => _WidgetsDemoScreenState();
}

class _WidgetsDemoScreenState extends State<WidgetsDemoScreen> {
  bool isTermsAccepted = false;
  String selectedOption = 'option1';
  bool isEvoCheckboxChecked = false;
  bool isEvoCheckboxWithTitleChecked = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Widgets Demo',
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('Terms and Condition Checkbox'),
            const SizedBox(height: 16),
            TermAndConditionWidget(
              consentValue: isTermsAccepted,
              onTap: () {
                setState(() {
                  isTermsAccepted = !isTermsAccepted;
                });
              },
            ),
            const SizedBox(height: 32),
            _buildSectionTitle('Radio Buttons'),
            const SizedBox(height: 16),
            EvoRadioWidget<String>(
              title: 'Option 1',
              value: 'option1',
              isSelected: selectedOption == 'option1',
              onChange: (value) {
                setState(() {
                  selectedOption = value;
                });
              },
            ),
            const SizedBox(height: 12),
            EvoRadioWidget<String>(
              title: 'Option 2',
              value: 'option2',
              isSelected: selectedOption == 'option2',
              onChange: (value) {
                setState(() {
                  selectedOption = value;
                });
              },
            ),
            const SizedBox(height: 12),
            EvoRadioWidget<String>(
              title: 'Disabled Option',
              value: 'option3',
              isSelected: selectedOption == 'option3',
              enable: false,
              onChange: (value) {
                setState(() {
                  selectedOption = value;
                });
              },
            ),
            const SizedBox(height: 32),
            _buildSectionTitle('Checkboxes'),
            const SizedBox(height: 16),
            EvoCheckboxWidget(
              title: 'Checkbox with Title',
              value: isEvoCheckboxWithTitleChecked,
              onTap: () {
                setState(() {
                  isEvoCheckboxWithTitleChecked = !isEvoCheckboxWithTitleChecked;
                });
              },
            ),
            ElevatedButton(onPressed: () {
              TermAndConditionUtils().showTermAndConditionOverlay();
            }, child: Text('show term and condition'))
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
    );
  }
}

