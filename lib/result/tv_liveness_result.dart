class TVLivenessResult {
  double? score;
  bool? isLive;
  String? requestId;
  String? timestamp;

  TVLivenessResult({required this.score, required this.isLive, this.requestId, this.timestamp});

  TVLivenessResult.fromMap(Map<Object?, dynamic> map) {
    String? strScore = map['score']?.toString();
    score = strScore != null ? double.tryParse(strScore) : null;
    isLive = map['isLive'];
    requestId = map['requestId'];
    timestamp = map['timestamp'];
  }
}
