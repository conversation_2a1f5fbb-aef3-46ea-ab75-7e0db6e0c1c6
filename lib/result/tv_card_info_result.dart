class TVCardInfoResult {
  List<Info>? infos;
  String? requestId;
  String? rawQR;
  List<Info>? parsedQr;
  String? timestamp;

  TVCardInfoResult({this.infos, this.requestId, this.rawQR, this.parsedQr, this.timestamp});

  TVCardInfoResult.fromMap(Map<Object?, dynamic> map) {
    infos = (map['infos'] as List?)?.map((dynamic e) => Info.fromMap(e as Map<Object?, dynamic>)).toList();
    requestId = map['requestId'];
    rawQR = map['rawQR'];
    parsedQr = (map['parsedQr'] as List?)?.map((dynamic e) => Info.fromMap(e as Map<Object?, dynamic>)).toList();
    timestamp = map['timestamp'];
  }
}

class Info {
  String? field;
  String? value;
  String? confidenceVerdict;
  double? confidenceScore;

  Info({this.field, this.value, this.confidenceVerdict, this.confidenceScore});

  Info.fromMap(Map<Object?, dynamic> map) {
    field = map['field'];
    value = map['value'];
    confidenceVerdict = map['confidenceVerdict'];

    String? strConfidenceScore = map['confidenceScore']?.toString();
    confidenceScore = strConfidenceScore != null ? double.tryParse(strConfidenceScore) : null;
  }
}
