#update_fastlane
require 'spaceship'
Spaceship::ConnectAPI::App.const_set('ESSENTIAL_INCLUDES', 'appStoreVersions')

default_platform(:ios)

platform :ios do
  desc "Push a new build to TestFlight"
  #Lane build and upload app
  lane :build_ipa_release do |options|
  commit = last_git_commit
  appFlavor = options[:flavor]
  teamId = options[:teamId] || "3U9MP5NMJW"
  keychainPass = options[:keychainPass]
  cerPath = options[:cerPath] || "../ios/fastlane/distribution.p12"
  cerPass = options[:cerPass]
  p8Content = options[:p8Content]
  p8KeyId = options[:p8KeyId]
  p8IssuerId = options[:p8IssuerId]
  keychainName = "CICD_Keychain"
  bundleId = ""
  appProvisioningName = ""
  oneSignalProvisioningName = ""
  slackMessage = ""
  codeSignIdentity = "iPhone Distribution"
  sdk = "iphoneos*"
  oneSignalNotificationServiceExtension = "OneSignalNotificationServiceExtension"

  # Install provisioning for app and onesignal also
  install_provisioning_profile(path: "./fastlane/runner_app.mobileprovision")
  install_provisioning_profile(path: "./fastlane/onesignal_extension.mobileprovision")

  # Create new keychain to import certificate (p12 file) if need
  if keychainName
   create_keychain(
     name: keychainName,
     password: keychainPass,
     default_keychain: false,
     unlock: true,
     timeout: 0,
     lock_when_sleeps: true
   )
   end

   # Import certificate (p12 file) to keychain, build process (build_app) will get this cer to build
   import_certificate(
          keychain_name: keychainName,
          keychain_password: keychainPass,
      	  certificate_path: cerPath,
          certificate_password: cerPass)

   # debug purpose: print out keychain list, make sure that the $keychainName exist
   sh("security list-keychains")
   sh("security list-keychains -d user")
   # debug purpose: print out current available certificates related to codesigning, make sure that the iPhone Distribution cert is available
   sh("security find-identity -v -p codesigning")

    # Get app bundle id base on appFlavor
   if appFlavor == "stag"
           bundleId = "vn.goevo.evo.stag"
           appProvisioningName= "EVO Stag - ios distribute appstore"
           oneSignalProvisioningName= "EVO Stag Onesignal - ios distribute appstore"
   end
   if appFlavor == "uat"
           bundleId = "vn.goevo.evo.uat"
           appProvisioningName= "EVO UAT - ios distribute appstore"
           oneSignalProvisioningName= "EVO UAT Onesignal - ios distribute appstore"
   end
   if appFlavor == "prod"
           bundleId = "vn.goevo.evo"
           appProvisioningName= "EVO Prod - ios distribute appstore"
           oneSignalProvisioningName= "EVO Prod Onesignal - ios distribute appstore"
   end

      # Get App Store connect api key
   api_key = app_store_connect_api_key(
             key_id: p8KeyId,
             issuer_id: p8IssuerId,
             key_content: p8Content,
             is_key_content_base64: true,
             duration: 1200,
             in_house: false
   )

   previous_build_number = latest_testflight_build_number(
             app_identifier: bundleId,
             api_key: api_key
      )

   # Build 692 was uploaded to TestFlight but rejected by Apple due to our failure to accept the new Agreement before uploading the build.
   # Consequently, the previous_build_number remains at 691. When CI/CD tried to set the build number to 692 and upload again, it resulted in an error.
   # See the error here: [Link to Slack message](https://trustingsocial.slack.com/archives/C04HC16L30V/p1704687364203489).
   # Workaround: If the previous_build_number is 691, we will manually set it to 692 for the next attempt.
   if previous_build_number == 691
           previous_build_number = 692
   end

   # Build 721 was uploaded to TestFlight but rejected by Apple due to missing NSMicrophoneUsageDescription in Info.plist.
   # Refer to slack thread: https://trustingsocial.slack.com/archives/C04RSC7FMR8/p1708424340573349?thread_ts=1706866198.536379&cid=C04RSC7FMR8
   #
   # Consequently, the previous_build_number remains at 720. When CI/CD tried to set the build number to 721 and upload again, it resulted in an error.
   # See the error here: [Link to Slack message](https://trustingsocial.slack.com/archives/C04HC16L30V/p1708566529231979?thread_ts=1708481596.858329&cid=C04HC16L30V).
   # Workaround: If the previous_build_number is 721, we will manually set it to 722 for the next attempt.
   if previous_build_number == 720
           previous_build_number = 721
   end

   current_build_number = previous_build_number + 1

   increment_build_number(
           xcodeproj: "Runner.xcodeproj",
           build_number: current_build_number,
      )


   update_code_signing_settings(
     targets: "Runner",
     use_automatic_signing: false,
     team_id: teamId,
     bundle_identifier: bundleId,
     code_sign_identity: codeSignIdentity,
     sdk: sdk,
     profile_name: appProvisioningName,
   )

   update_code_signing_settings(
     targets: oneSignalNotificationServiceExtension,
     use_automatic_signing: false,
     team_id: teamId,
     bundle_identifier: "#{bundleId}.#{oneSignalNotificationServiceExtension}",
     code_sign_identity:codeSignIdentity,
     sdk: sdk,
     profile_name: oneSignalProvisioningName,
   )

    # pod install process
   cocoapods(clean_install: true, use_bundle_exec: false, error_callback: true, repo_update: true)

    # Build ipa file
   build_app(
    workspace: "Runner.xcworkspace",
    scheme: appFlavor,
    export_method: "app-store",
    export_options: {
    provisioningProfiles: {
             		bundleId => appProvisioningName,
             		"#{bundleId}.#{oneSignalNotificationServiceExtension}" => oneSignalProvisioningName
           		}
         }
       )

   #Upload build to TestFlight
   pilot(api_key: api_key, skip_waiting_for_build_processing: true)
  end
 end