<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>ITSAppUsesNonExemptEncryption</key>
		<false/>
		<key>NSUserTrackingUsageDescription</key>
		<string>Thông tin được sử dụng để thông báo, cập nhật các ưu đãi dành riêng cho bạn.</string>
		<key>BGTaskSchedulerPermittedIdentifiers</key>
		<array>
			<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		</array>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>$(APP_NAME)</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>EVO</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>http</string>
			<string>https</string>
			<string>tel</string>
			<string>mailto</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>processing</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIStatusBarHidden</key>
		<false/>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>NSCameraUsageDescription</key>
        <string>Camera dùng để quét mã thanh toán và xác thực danh tính.</string>
        <key>NSPhotoLibraryUsageDescription</key>
        <string>EVO app needs your permission to access your photos.</string>
        <key>NSFaceIDUsageDescription</key>
        <string>FaceID dùng để đăng nhập và xác thực tài khoản.</string>
        <key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
        <string>Địa chỉ của bạn được dùng để cung cấp dịch vụ phù hợp</string>
        <key>NSLocationWhenInUseUsageDescription</key>
        <string>Địa chỉ của bạn được dùng để cung cấp dịch vụ phù hợp</string>
        <key>UISupportsDocumentBrowser</key>
        <true/>
        <key>CFBundleURLTypes</key>
        <array>
        	<dict>
               <key>CFBundleURLSchemes</key>
               <array>
                   <string>evoappvn</string>
               </array>
        	</dict>
        </array>
        <key>NFCReaderUsageDescription</key>
        <string>EVO app sẽ sử dụng NFC để quét thông tin của chip trên CCCD</string>

        <key>com.apple.developer.nfc.readersession.iso7816.select-identifiers</key>
            <array>
                <string>A0000002471001</string>
                <string>A0000002472001</string>
                <string>00000000000000</string>
                <string>D4100000030001</string>
            </array>
        <key>LSMinimumSystemVersion</key>
        <string>12.0</string>
	</dict>
</plist>
