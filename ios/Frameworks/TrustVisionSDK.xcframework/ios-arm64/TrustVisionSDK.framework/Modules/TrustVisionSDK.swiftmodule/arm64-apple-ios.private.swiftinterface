// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 6.0.2 effective-5.10 (swiftlang-6.0.2.1.2 clang-1600.0.26.4)
// swift-module-flags: -target arm64-apple-ios12.0 -enable-objc-interop -enable-library-evolution -swift-version 5 -enforce-exclusivity=checked -O -enable-bare-slash-regex -module-name TrustVisionSDK
// swift-module-flags-ignorable: -no-verify-emitted-module-interface
import Foundation
import Swift
import TrustVisionAPI
import TrustVisionCoreSDK
@_exported import TrustVisionSDK
import TrustVisionSDKEvo
import _Concurrency
import _StringProcessing
import _SwiftConcurrencyShims
@objc public protocol TrustVisionClientProtocolType : TrustVisionCoreSDK.TrustVisionSdkFullProtocol, TrustVisionCoreSDK.TrustVisionSdkUiOnlyProtocol {
}
@_hasMissingDesignatedInitializers public class TrustVisionSdk : TrustVisionSDK.TrustVisionClientProtocolType {
  @objc public static let shared: any TrustVisionSDK.TrustVisionClientProtocolType
  @objc public func initialize(baseUrl: Swift.String?, localizationFiles: [TrustVisionCoreSDK.TVLocalizationFile]?, languageCode: Swift.String?, theme: TrustVisionCoreSDK.TVTheme, xRequestId: Swift.String?, xRequestId2: Swift.String?, isForced: Swift.Bool, enableDebuggingLog: Swift.Bool, imageEncryptionKey: Swift.String?, securityPublicKey: Swift.String?, logServerAccess: Swift.String?, logServerSecret: Swift.String?, logServerBaseUrl: Swift.String?, success: @escaping () -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, onEvent: @escaping (TrustVisionCoreSDK.TVTrackingEvent) -> Swift.Void)
  @objc public func initialize(accessKeyId: Swift.String, accessKeySecret: Swift.String, baseUrl: Swift.String?, localizationFiles: [TrustVisionCoreSDK.TVLocalizationFile]?, languageCode: Swift.String?, theme: TrustVisionCoreSDK.TVTheme, xRequestId: Swift.String?, xRequestId2: Swift.String?, isForced: Swift.Bool, enableDebuggingLog: Swift.Bool, imageEncryptionKey: Swift.String?, securityPublicKey: Swift.String?, logServerAccess: Swift.String?, logServerSecret: Swift.String?, logServerBaseUrl: Swift.String?, success: @escaping () -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, onEvent: @escaping (TrustVisionCoreSDK.TVTrackingEvent) -> Swift.Void)
  @objc public func initialize(accessKeyId: Swift.String, accessKeySecret: Swift.String, baseUrl: Swift.String?, localizationFiles: [TrustVisionCoreSDK.TVLocalizationFile]?, languageCode: Swift.String?, theme: TrustVisionCoreSDK.TVTheme, xRequestId: Swift.String?, xRequestId2: Swift.String?, isForced: Swift.Bool, enableDebuggingLog: Swift.Bool, tvCertificate: TrustVisionAPI.TVCertificate?, imageEncryptionKey: Swift.String?, securityPublicKey: Swift.String?, logServerAccess: Swift.String?, logServerSecret: Swift.String?, logServerBaseUrl: Swift.String?, success: @escaping () -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, onEvent: @escaping (TrustVisionCoreSDK.TVTrackingEvent) -> Swift.Void)
  @objc public func initialize(accessKeyId: Swift.String?, accessKeySecret: Swift.String?, baseUrl: Swift.String?, clientSettingsJsonString: Swift.String?, localizationFiles: [TrustVisionCoreSDK.TVLocalizationFile]?, languageCode: Swift.String?, theme: TrustVisionCoreSDK.TVTheme, xRequestId: Swift.String?, xRequestId2: Swift.String?, isForced: Swift.Bool, enableDebuggingLog: Swift.Bool, tvCertificate: TrustVisionAPI.TVCertificate?, imageEncryptionKey: Swift.String?, securityPublicKey: Swift.String?, logServerAccess: Swift.String?, logServerSecret: Swift.String?, logServerBaseUrl: Swift.String?, success: @escaping () -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, onEvent: @escaping (TrustVisionCoreSDK.TVTrackingEvent) -> Swift.Void)
  @objc public func initialize(accessKeyId: Swift.String?, accessKeySecret: Swift.String?, baseUrl: Swift.String?, clientSettings: TrustVisionAPI.TVClientSettingsResponse?, localizationFiles: [TrustVisionCoreSDK.TVLocalizationFile]?, languageCode: Swift.String?, theme: TrustVisionCoreSDK.TVTheme, xRequestId: Swift.String?, xRequestId2: Swift.String?, isForced: Swift.Bool, enableDebuggingLog: Swift.Bool, tvCertificate: TrustVisionAPI.TVCertificate?, imageEncryptionKey: Swift.String?, securityPublicKey: Swift.String?, logServerAccess: Swift.String?, logServerSecret: Swift.String?, logServerBaseUrl: Swift.String?, success: @escaping () -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, onEvent: @escaping (TrustVisionCoreSDK.TVTrackingEvent) -> Swift.Void)
  @objc public func initialize(clientSettingsJsonString: Swift.String?, localizationFiles: [TrustVisionCoreSDK.TVLocalizationFile]?, languageCode: Swift.String?, theme: TrustVisionCoreSDK.TVTheme, enableDebuggingLog: Swift.Bool, imageEncryptionKey: Swift.String?, securityPublicKey: Swift.String?, logServerAccess: Swift.String?, logServerSecret: Swift.String?, logServerBaseUrl: Swift.String?, xRequestId: Swift.String?, success: @escaping () -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, onEvent: @escaping (TrustVisionCoreSDK.TVTrackingEvent) -> Swift.Void)
  @objc public func initialize(clientSettings: TrustVisionAPI.TVClientSettingsResponse?, localizationFiles: [TrustVisionCoreSDK.TVLocalizationFile]?, languageCode: Swift.String?, theme: TrustVisionCoreSDK.TVTheme, enableDebuggingLog: Swift.Bool, imageEncryptionKey: Swift.String?, securityPublicKey: Swift.String?, logServerAccess: Swift.String?, logServerSecret: Swift.String?, logServerBaseUrl: Swift.String?, xRequestId: Swift.String?, success: @escaping () -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, onEvent: @escaping (TrustVisionCoreSDK.TVTrackingEvent) -> Swift.Void)
  @objc public func initialize(accessKeyId: Swift.String, accessKeySecret: Swift.String, baseUrl: Swift.String?, localizationFiles: [TrustVisionCoreSDK.TVLocalizationFile]?, languageCode: Swift.String?, theme: TrustVisionCoreSDK.TVTheme, xRequestId: Swift.String?, xRequestId2: Swift.String?, isForced: Swift.Bool, enableDebuggingLog: Swift.Bool, imageEncryptionKey: Swift.String?, securityPublicKey: Swift.String?, sslCertificates: [TrustVisionAPI.TVCertificate]?, flowId: Swift.String?, headers: [Swift.String : Swift.String]?, logServerAccess: Swift.String?, logServerSecret: Swift.String?, logServerBaseUrl: Swift.String?, success: @escaping () -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, onEvent: @escaping (TrustVisionCoreSDK.TVTrackingEvent) -> Swift.Void)
  @objc public func initialize(config: TrustVisionCoreSDK.TVInitializeConfiguration, success: @escaping () -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, onEvent: @escaping (TrustVisionCoreSDK.TVTrackingEvent) -> Swift.Void)
  @objc public func startFullFlow(config: TrustVisionCoreSDK.TVSDKConfig, readIdCardNumber: ((TrustVisionCoreSDK.TVImageClass) -> Swift.String)?, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public func startFullFlow(config: TrustVisionCoreSDK.TVSDKConfig, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public func startFullFlow(config: TrustVisionCoreSDK.TVSDKConfig, framesRecordedCallback: @escaping TrustVisionCoreSDK.FramesRecordedCallback, readIdCardNumber: ((TrustVisionCoreSDK.TVImageClass) -> Swift.String)?, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public func startFullFlow(config: TrustVisionCoreSDK.TVSDKConfig, framesRecordedCallback: @escaping TrustVisionCoreSDK.FramesRecordedCallback, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public func getSelfieCameraMode() -> TrustVisionCoreSDK.TVCameraOption
  @objc public func getLivenessOptions() -> [Swift.String]
  @objc public func getIdCardSanityCheckingEnable() -> Swift.Bool
  @objc public func getSelfieSanityCheckingEnable() -> Swift.Bool
  @objc public func getSupportTransaction() -> Swift.Bool
  @objc public func startTransaction(referenceId: Swift.String?, success: @escaping (Swift.String?) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void)
  @objc public func endTransaction(success: @escaping () -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void)
  @objc public func startIdCapturing(configuration: TrustVisionCoreSDK.TVIdCardConfiguration, readIdCardNumber: ((TrustVisionCoreSDK.TVImageClass) -> Swift.String)?, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public func startSelfieCapturing(configuration: TrustVisionCoreSDK.TVSelfieConfiguration, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public func matchFace(image1Id: Swift.String, image2Id: Swift.String, success: @escaping (TrustVisionCoreSDK.TVCompareFacesResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void)
  @objc public func downloadImage(imageId: Swift.String, success: @escaping (UIKit.UIImage?) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void)
  @objc public func pushServerLog(tag: Swift.String, event: Swift.String, log: [Swift.String : Any])
  @objc public func faceAuthRegister(cusUserId: Swift.String, faceType: Swift.String?, selfieType: Swift.String?, selfieImages: [TrustVisionCoreSDK.TVGestureImage], videos: [TrustVisionAPI.TVRequestVideo], success: @escaping (TrustVisionCoreSDK.TVFaceAuthRegisterResult?) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void)
  @objc public func faceAuth(cusUserId: Swift.String, authType: Swift.String?, selfieType: Swift.String?, selfieImages: [TrustVisionCoreSDK.TVGestureImage], videos: [TrustVisionAPI.TVRequestVideo], success: @escaping (TrustVisionCoreSDK.TVFaceAuthResult?) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void)
  @objc public func startFaceAuthen(config: TrustVisionCoreSDK.TVFaceAuthenConfiguration, onLoading: (() -> Swift.Void)?, framesRecordedCallback: @escaping TrustVisionCoreSDK.FramesRecordedCallback, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public func startIdCapturing(configuration: TrustVisionCoreSDK.TVIdCardConfiguration, framesRecordedCallback: @escaping TrustVisionCoreSDK.FramesRecordedCallback, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public func startIdCapturing(configuration: TrustVisionCoreSDK.TVIdCardConfiguration, framesRecordedCallback: @escaping TrustVisionCoreSDK.FramesRecordedCallback, readIdCardNumber: ((TrustVisionCoreSDK.TVImageClass) -> Swift.String)?, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public func startSelfieCapturing(configuration: TrustVisionCoreSDK.TVSelfieConfiguration, framesRecordedCallback: @escaping TrustVisionCoreSDK.FramesRecordedCallback, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public func startNfcScanning(configuration: TrustVisionCoreSDK.TVIdNfcConfiguration, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public func startQRScanning(configuration: TrustVisionCoreSDK.TVQRConfiguration, framesRecordedCallback: @escaping TrustVisionCoreSDK.FramesRecordedCallback, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public func getLogFilePaths() -> [Swift.String]
  @objc public func changeLanguageCode(languageCode: Swift.String)
  @objc public func getLanguageCode() -> Swift.String?
  @objc public func getSupportedLanguageCodes() -> [Swift.String]
  @objc public func getCardTypes() -> [TrustVisionCoreSDK.TVCardType]
  @objc public func startIdCapturing(configuration: TrustVisionCoreSDK.TVIdCardConfiguration, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public func startQRScanning(configuration: TrustVisionCoreSDK.TVQRConfiguration, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public func isNfcSupport() -> Swift.Bool
  @objc public func getDeviceInfo() -> TrustVisionCoreSDK.TVDeviceInfoProvider?
  @objc deinit
}
@_inheritsConvenienceInitializers @objc public class TrustVisionSdkWrapper : ObjectiveC.NSObject {
  @objc public static func initialize(clientSettingsJsonString: Swift.String?, localizationFiles: [TrustVisionCoreSDK.TVLocalizationFile]?, languageCode: Swift.String?, theme: TrustVisionCoreSDK.TVTheme, enableDebuggingLog: Swift.Bool, logServerAccess: Swift.String?, logServerSecret: Swift.String?, logServerBaseUrl: Swift.String?, xRequestId: Swift.String?, success: @escaping () -> Swift.Void, failure: @escaping (_ error: TrustVisionCoreSDK.TVError) -> Swift.Void, onEvent: @escaping (_ event: TrustVisionCoreSDK.TVTrackingEvent) -> Swift.Void)
  @objc public static func initialize(clientSettingsJsonString: Swift.String?, localizationFiles: [TrustVisionCoreSDK.TVLocalizationFile]?, languageCode: Swift.String?, theme: TrustVisionCoreSDK.TVTheme, enableDebuggingLog: Swift.Bool, imageEncryptionKey: Swift.String?, securityPublicKey: Swift.String?, logServerAccess: Swift.String?, logServerSecret: Swift.String?, logServerBaseUrl: Swift.String?, xRequestId: Swift.String?, success: @escaping () -> Swift.Void, failure: @escaping (_ error: TrustVisionCoreSDK.TVError) -> Swift.Void, onEvent: @escaping (_ event: TrustVisionCoreSDK.TVTrackingEvent) -> Swift.Void)
  @objc public static func initialize(clientSettings: TrustVisionAPI.TVClientSettingsResponse?, localizationFiles: [TrustVisionCoreSDK.TVLocalizationFile]?, languageCode: Swift.String?, theme: TrustVisionCoreSDK.TVTheme, enableDebuggingLog: Swift.Bool, imageEncryptionKey: Swift.String?, securityPublicKey: Swift.String?, logServerAccess: Swift.String?, logServerSecret: Swift.String?, logServerBaseUrl: Swift.String?, xRequestId: Swift.String?, success: @escaping () -> Swift.Void, failure: @escaping (_ error: TrustVisionCoreSDK.TVError) -> Swift.Void, onEvent: @escaping (_ event: TrustVisionCoreSDK.TVTrackingEvent) -> Swift.Void)
  @objc public static func initialize(config: TrustVisionCoreSDK.TVInitializeConfiguration, success: @escaping () -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, onEvent: @escaping (TrustVisionCoreSDK.TVTrackingEvent) -> Swift.Void)
  @objc public static func startIdCapturing(configuration: TrustVisionCoreSDK.TVIdCardConfiguration, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (_ error: TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public static func startIdCapturing(configuration: TrustVisionCoreSDK.TVIdCardConfiguration, framesRecordedCallback: @escaping TrustVisionCoreSDK.FramesRecordedCallback, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (_ error: TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public static func startIdCapturing(configuration: TrustVisionCoreSDK.TVIdCardConfiguration, framesRecordedCallback: @escaping TrustVisionCoreSDK.FramesRecordedCallback, readIdCardNumber: ((TrustVisionCoreSDK.TVImageClass) -> Swift.String)?, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (_ error: TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public static func startIdCapturing(configuration: TrustVisionCoreSDK.TVIdCardConfiguration, readIdCardNumber: ((TrustVisionCoreSDK.TVImageClass) -> Swift.String)?, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (_ error: TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public static func startSelfieCapturing(configuration: TrustVisionCoreSDK.TVSelfieConfiguration, framesRecordedCallback: @escaping TrustVisionCoreSDK.FramesRecordedCallback, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (_ error: TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public static func startNfcScanning(configuration: TrustVisionCoreSDK.TVIdNfcConfiguration, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public static func startQRScanning(configuration: TrustVisionCoreSDK.TVQRConfiguration, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (_ error: TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public static func startQRScanning(configuration: TrustVisionCoreSDK.TVQRConfiguration, framesRecordedCallback: @escaping TrustVisionCoreSDK.FramesRecordedCallback, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public static func startFaceAuthen(config: TrustVisionCoreSDK.TVFaceAuthenConfiguration, onLoading: (() -> Swift.Void)?, framesRecordedCallback: @escaping TrustVisionCoreSDK.FramesRecordedCallback, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (_ error: TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public static func getCardTypes() -> [TrustVisionCoreSDK.TVCardType]
  @objc public static func isNfcSupport() -> Swift.Bool
  @objc public static func getLogFilePaths() -> [Swift.String]
  @objc public static func changeLanguageCode(languageCode: Swift.String)
  @objc public static func getLanguageCode() -> Swift.String?
  @objc public static func getSupportedLanguageCodes() -> [Swift.String]
  @objc public static func initialize(accessKeyId: Swift.String?, accessKeySecret: Swift.String?, baseUrl: Swift.String?, clientSettingsJsonString: Swift.String?, localizationFiles: [TrustVisionCoreSDK.TVLocalizationFile]?, languageCode: Swift.String?, theme: TrustVisionCoreSDK.TVTheme, xRequestId: Swift.String?, xRequestId2: Swift.String?, isForced: Swift.Bool, enableDebuggingLog: Swift.Bool, tvCertificate: TrustVisionAPI.TVCertificate?, imageEncryptionKey: Swift.String?, securityPublicKey: Swift.String?, logServerAccess: Swift.String?, logServerSecret: Swift.String?, logServerBaseUrl: Swift.String?, success: @escaping () -> Swift.Void, failure: @escaping (_ error: TrustVisionCoreSDK.TVError) -> Swift.Void, onEvent: @escaping (_ event: TrustVisionCoreSDK.TVTrackingEvent) -> Swift.Void)
  @objc public static func initialize(accessKeyId: Swift.String?, accessKeySecret: Swift.String?, baseUrl: Swift.String?, clientSettings: TrustVisionAPI.TVClientSettingsResponse?, localizationFiles: [TrustVisionCoreSDK.TVLocalizationFile]?, languageCode: Swift.String?, theme: TrustVisionCoreSDK.TVTheme, xRequestId: Swift.String?, xRequestId2: Swift.String?, isForced: Swift.Bool, enableDebuggingLog: Swift.Bool, tvCertificate: TrustVisionAPI.TVCertificate?, imageEncryptionKey: Swift.String?, securityPublicKey: Swift.String?, logServerAccess: Swift.String?, logServerSecret: Swift.String?, logServerBaseUrl: Swift.String?, success: @escaping () -> Swift.Void, failure: @escaping (_ error: TrustVisionCoreSDK.TVError) -> Swift.Void, onEvent: @escaping (_ event: TrustVisionCoreSDK.TVTrackingEvent) -> Swift.Void)
  @objc public static func startFullFlow(config: TrustVisionCoreSDK.TVSDKConfig, readIdCardNumber: ((TrustVisionCoreSDK.TVImageClass) -> Swift.String)?, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public static func startFullFlow(config: TrustVisionCoreSDK.TVSDKConfig, success: @escaping (TrustVisionCoreSDK.TVDetectionResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void, cancellation: @escaping () -> Swift.Void) throws -> UIKit.UINavigationController
  @objc public static func getSelfieCameraMode() -> TrustVisionCoreSDK.TVCameraOption
  @objc public static func getLivenessOptions() -> [Swift.String]
  @objc public static func getIdCardSanityCheckingEnable() -> Swift.Bool
  @objc public static func getSelfieSanityCheckingEnable() -> Swift.Bool
  @objc public static func getSupportTransaction() -> Swift.Bool
  @objc public static func startTransaction(referenceId: Swift.String?, success: @escaping (Swift.String?) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void)
  @objc public static func endTransaction(success: @escaping () -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void)
  @objc public static func matchFace(image1Id: Swift.String, image2Id: Swift.String, success: @escaping (TrustVisionCoreSDK.TVCompareFacesResult) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void)
  @objc public static func downloadImage(imageId: Swift.String, success: @escaping (UIKit.UIImage?) -> Swift.Void, failure: @escaping (TrustVisionCoreSDK.TVError) -> Swift.Void)
  @objc public static func pushServerLog(tag: Swift.String, event: Swift.String, log: [Swift.String : Any])
  @objc public static func getDeviceInfo() -> TrustVisionCoreSDK.TVDeviceInfoProvider?
  @objc override dynamic public init()
  @objc deinit
}
