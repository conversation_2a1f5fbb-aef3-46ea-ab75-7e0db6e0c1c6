<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/TrustVisionSDK-Swift.h</key>
		<data>
		2xqHst+SZz6j3mFTnANcKgXk+Xo=
		</data>
		<key>Headers/TrustVisionSDK-umbrella.h</key>
		<data>
		lTO2gnrm0y3VCXKq+v+Sk22pplo=
		</data>
		<key>Headers/TrustVisionSDK.h</key>
		<data>
		TEacEeK9eW2J9sg8Fno0HteLXRc=
		</data>
		<key>Info.plist</key>
		<data>
		Yv0z8ePGE1YlCoaT6/RUhNdSxyc=
		</data>
		<key>Modules/TrustVisionSDK.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		h73/AYSevZLR8+WKIuarCx0p2K4=
		</data>
		<key>Modules/TrustVisionSDK.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		HbjD0KT/KfvbGCglAy9xFQuy0Q8=
		</data>
		<key>Modules/TrustVisionSDK.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		2IaW63f/qw9K/JEXvnAQnqE1/cU=
		</data>
		<key>Modules/TrustVisionSDK.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		9xL3Jtre0DFe62mXgvzefDERMV8=
		</data>
		<key>Modules/TrustVisionSDK.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		2IaW63f/qw9K/JEXvnAQnqE1/cU=
		</data>
		<key>Modules/TrustVisionSDK.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		CFwc2z6MBrmUWB5V+e8Y9tlTY9A=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		OLB2yGWaWvgz4Ah1WAerNfbv32A=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/TrustVisionSDK-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			2xqHst+SZz6j3mFTnANcKgXk+Xo=
			</data>
			<key>hash2</key>
			<data>
			962uYOyAUfq373+WQMvLTgBFFLMUhJBbsonwgxTTMiY=
			</data>
		</dict>
		<key>Headers/TrustVisionSDK-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			lTO2gnrm0y3VCXKq+v+Sk22pplo=
			</data>
			<key>hash2</key>
			<data>
			GAN7DB+Sj6OKKQbPZpeftwwkVP2hp61BUiMKWtTni2c=
			</data>
		</dict>
		<key>Headers/TrustVisionSDK.h</key>
		<dict>
			<key>hash</key>
			<data>
			TEacEeK9eW2J9sg8Fno0HteLXRc=
			</data>
			<key>hash2</key>
			<data>
			3ZyLFM9gztCXiqKJ7U4QQuXm6hFqCy2vLLPR08mbPrw=
			</data>
		</dict>
		<key>Modules/TrustVisionSDK.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			h73/AYSevZLR8+WKIuarCx0p2K4=
			</data>
			<key>hash2</key>
			<data>
			bJEZMftC4pR8zrdOdmAimbHjTbY7A0KLDQR4HE5J+lU=
			</data>
		</dict>
		<key>Modules/TrustVisionSDK.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			HbjD0KT/KfvbGCglAy9xFQuy0Q8=
			</data>
			<key>hash2</key>
			<data>
			pUZCPjXfg6NZM0rmL5yNjQo45ZKc/ynF+zCAB6GILTI=
			</data>
		</dict>
		<key>Modules/TrustVisionSDK.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			2IaW63f/qw9K/JEXvnAQnqE1/cU=
			</data>
			<key>hash2</key>
			<data>
			fSazGblK1vWB8nD8eyqZipKFXf7b4Ynvrv2F1WB+pxQ=
			</data>
		</dict>
		<key>Modules/TrustVisionSDK.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			9xL3Jtre0DFe62mXgvzefDERMV8=
			</data>
			<key>hash2</key>
			<data>
			viZdgsvbfbLzI4l6zINzwUzCpxZv3KoJzL4ezqsij9M=
			</data>
		</dict>
		<key>Modules/TrustVisionSDK.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			2IaW63f/qw9K/JEXvnAQnqE1/cU=
			</data>
			<key>hash2</key>
			<data>
			fSazGblK1vWB8nD8eyqZipKFXf7b4Ynvrv2F1WB+pxQ=
			</data>
		</dict>
		<key>Modules/TrustVisionSDK.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			CFwc2z6MBrmUWB5V+e8Y9tlTY9A=
			</data>
			<key>hash2</key>
			<data>
			O9CSwjDKvD1aBEeV0fxhkj8lkKsrOf2Ub8/D5WrrZBI=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			OLB2yGWaWvgz4Ah1WAerNfbv32A=
			</data>
			<key>hash2</key>
			<data>
			GyLAIzghBQizo4m3P0pvoIEJaXTIl1PS5oQ+GNlvb2Q=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
