<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/TrustVisionAPI-Swift.h</key>
		<data>
		HGHx7QxXdGFrCsTxusKHN3oz2BI=
		</data>
		<key>Headers/TrustVisionAPI-umbrella.h</key>
		<data>
		QQejSSelxVkjr1fn6HxQTM37D+s=
		</data>
		<key>Headers/TrustVisionAPI.h</key>
		<data>
		fuLklVdVwOweilt6SGqS/LSaSyA=
		</data>
		<key>Info.plist</key>
		<data>
		1RaO8O2WZfk2tjBPTeWL2VfrNxM=
		</data>
		<key>Modules/TrustVisionAPI.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		XMvMUAOaQtNZ3KtMKni1/sC4rXo=
		</data>
		<key>Modules/TrustVisionAPI.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		V0LTxum2U7xqtGoHbUcCsXsibgY=
		</data>
		<key>Modules/TrustVisionAPI.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		09+JRnmIChXArjZNsn4F5/aO4H8=
		</data>
		<key>Modules/TrustVisionAPI.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		rhLUTK6M/T4HWplNx/9k/fA5dcg=
		</data>
		<key>Modules/TrustVisionAPI.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		09+JRnmIChXArjZNsn4F5/aO4H8=
		</data>
		<key>Modules/TrustVisionAPI.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		1pn6eXsFLNQVWqpJqr4BwPmeIx0=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		hkqRO8QEGp3ZuznhPYCxsUnP50E=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/TrustVisionAPI-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			HGHx7QxXdGFrCsTxusKHN3oz2BI=
			</data>
			<key>hash2</key>
			<data>
			4CqkwNzyU0z3DOWdt7p3ACjxIkMJTGvKy+DarPOyN+M=
			</data>
		</dict>
		<key>Headers/TrustVisionAPI-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			QQejSSelxVkjr1fn6HxQTM37D+s=
			</data>
			<key>hash2</key>
			<data>
			Lpv9Nl/U8MIhjjhaqHXdC3GozICwS/RrPysxux0sSG8=
			</data>
		</dict>
		<key>Headers/TrustVisionAPI.h</key>
		<dict>
			<key>hash</key>
			<data>
			fuLklVdVwOweilt6SGqS/LSaSyA=
			</data>
			<key>hash2</key>
			<data>
			b/D1pjJs6xs3DINTLgiipN+GzDjMto5Do+BJuElD4X8=
			</data>
		</dict>
		<key>Modules/TrustVisionAPI.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			XMvMUAOaQtNZ3KtMKni1/sC4rXo=
			</data>
			<key>hash2</key>
			<data>
			pisyqlV3tdgMR/PhoYGcwf6TNceNqfbH/YX2dj0rrzY=
			</data>
		</dict>
		<key>Modules/TrustVisionAPI.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			V0LTxum2U7xqtGoHbUcCsXsibgY=
			</data>
			<key>hash2</key>
			<data>
			wMkkYxiK55bEgmMBxUt5+5Pi+D2ndutdp/mF0FZDPr0=
			</data>
		</dict>
		<key>Modules/TrustVisionAPI.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			09+JRnmIChXArjZNsn4F5/aO4H8=
			</data>
			<key>hash2</key>
			<data>
			Rn3Qrk4QZNk5jzdsZodgoNi2vmrXO3URVahnRclEvy8=
			</data>
		</dict>
		<key>Modules/TrustVisionAPI.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			rhLUTK6M/T4HWplNx/9k/fA5dcg=
			</data>
			<key>hash2</key>
			<data>
			ADFxv20u4XLn0rvYtle9ER36FdMgOEVs+qGBVd+IMu0=
			</data>
		</dict>
		<key>Modules/TrustVisionAPI.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			09+JRnmIChXArjZNsn4F5/aO4H8=
			</data>
			<key>hash2</key>
			<data>
			Rn3Qrk4QZNk5jzdsZodgoNi2vmrXO3URVahnRclEvy8=
			</data>
		</dict>
		<key>Modules/TrustVisionAPI.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			1pn6eXsFLNQVWqpJqr4BwPmeIx0=
			</data>
			<key>hash2</key>
			<data>
			AThmnEnz8sL7sWq8HNAbTHLhIjZsKEy+uUk/qMoRhHc=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			hkqRO8QEGp3ZuznhPYCxsUnP50E=
			</data>
			<key>hash2</key>
			<data>
			BVDA2ewl0KwqS61EOtjZaEi+cxMPWppzKV1unWlbg1Y=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
