@startuml

autonumber

title Manual Link Card - Inquiry Status Flow

actor User
participant EvoA<PERSON> as app
participant EvoGateway as gateway

== LinkCardSubmissionStatusPollingScreen ==

opt user clicks button (X) OR device back button
    app --> app: pause polling
    activate app
    app --> User: show warning dialog
    deactivate app
    User -> app: do action on warning dialog
    alt user clicks button "Ở lại"
        app --> User: hide warning dialog
        activate app
        app --> app: continue polling
        deactivate app
    else user clicks button "Thoát"
        app --> app: stop polling
        activate app
        app --> User: hide warning dialog & back to entry point
        deactivate app
    end
end opt

par Delay 120s to limit Polling
    app --> app: start timer
    activate app
    ...Delay **120s limit time**...
    app --> app: stop polling
    app --> User: redirect to **ManualLinkCardResultScreen** case timeout
    deactivate app
else Polling submit-link-card-status

app -> gateway: GET /user/card/submit-link-card-status
note over gateway
    request:
        - param: link_card_request_id
    get from response of check linking status
   Refer: [[https://portal-evo-vn-staging-internal.tsengineering.io/docs/#/Cards/handleCheckCardLinkingStatusV1 API Docs]]
end note
    activate gateway
    gateway --> app: return submit-link-card-status response
   deactivate gateway

alt #Salmon invalid token response
    app --> app: stop polling
    activate app
        app --> User: display request login dialog
    deactivate app
else #Salmon verdict != success
    app --> app: check verdict
        alt error verdicts that user can't do Manual Link Card
            note over app
                error verdicts that user can't do Manual Link Card include:
                    - permission_denied
                    - record_not_found
                    - failure
            end note
            app --> app: stop polling
            activate app
            app --> User: redirect to **ManualLinkCardResultScreen** with error verdicts
            deactivate app
        else other error verdicts
            ...Delay **interval_inquiry_ms** OR default **5000 ms**...
            note over app
                The **interval_inquiry_ms** is gotten from API submit-link-card-status response,
                used for delaying to next polling API.
                Default value is **5000 ms**.
            end note
            app --> app: Go to **Step 11**
            activate app
            deactivate app
        end
    else #LightGreen verdict == success
        activate app
        note over app
            submit-link-card-status response:
                - action
                - interval_inquiry_ms
                - link_card_request_id
                - link_card_status
                - next_retry_if_exit_duration_in_minute
        end note

        app --> app: update **next_retry_if_exit_duration_in_minute**
        activate app
        note over app
             **next_retry_if_exit_duration_in_minute** is the next time
             that user can do manual link card again. **Default value is 15 minutes**
             Used to display warning message if user cancel manual link card
        end note
        app --> app: check **action.type**
        deactivate app

        alt #LightGreen action.type == INQUIRY_STATUS
            app --> app: update **interval_inquiry_ms**
            activate app
            note over app
                update **interval_inquiry_ms** if it is not NULL.
                The **interval_inquiry_ms** is used for delaying to next polling API
                Default value is **5000 ms**
            end note
            ...Delay **interval_inquiry_ms** OR default **5000 ms**...
            app --> app: Go to **Step 11**
            deactivate app
        else action.type == OPEN_APP_SCREEN_FAILED || OPEN_APP_SCREEN_SUCCESS
            app --> app: stop polling
            activate app
            app --> User: redirect to **ManualLinkCardResultScreen** case fail || success
            deactivate app
        end
    end
end par
@enduml