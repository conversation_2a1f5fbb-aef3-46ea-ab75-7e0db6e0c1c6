@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

note right app
    initial: **needPollingProcessingStatus** = true
end note

== PaymentResultScreen ==
opt #azure user click CTA button
    user -> app: user click **Về trang chủ** button
    app --> user: redirect to **MainScreen**
end

opt #azure Internet connection status change and hasInternet == true
    app -> app: go to step **Check conditions and Polling API**
end

par #Azure Start timer with duration = 300s
    == Timer 300s ==
    app --> app: start timer
    activate app
        ...**Delay 300s seconds**...
        app --> app: set flag **_needPollingProcessingStatus** = false
    deactivate app
else
    == Check conditions and Polling API ==
    app --> app: delay 1s
    alt _needPollingProcessingStatus == true and isn't calling API
        app -> be: GET /transactions/{id}
        note over be
            //params://
            **- transaction id**
        end note
        be --> app: return response

        alt #LightGreen status code == 200
            app --> app: save response to **savedTransaction**

            alt #white transaction lastError.verdict == "tpb_card_declined"
                app --> user: show bottom sheet **EnablePosLimit**
                note right
                    ref: **[[https://trustingsocial1.atlassian.net/browse/EMA-1777 EMA-1777]]**
                end note
            end alt

            alt #Orange transaction.status == processing
                app --> user: show UI payment result processing
            else #Cyan transaction.status == success || failure || reach_max_retries
                app --> app: set flag **_needPollingProcessingStatus** = false
                app --> user: show UI payment result success || failure || reach_max_retries

                alt #white transaction.status == success && is_first_transaction = true
                   app --> user: Request show **Rating/Review Dialog**
                   note right
                       ref: **[[https://trustingsocial1.atlassian.net/browse/EMA-4432 EMA-4432]]**
                   end note
                end

                alt #white transaction.status == success && emi_conversion_status != null
                    app --> user: show UI EMI Conversion Status
                    note right
                        Ref: [[https://trustingsocial1.atlassian.net/browse/EMA-2427 EMA-2427]]
                    end note
                    opt #azure press emi conversion status widget
                       user -> app: press emi conversion status widget
                       app -> user: redirect to **EmiManagementDetailScreen**
                    end
                end alt
            end
        else #Salmon status code != 200
            app --> user: show toast error
            app --> user: show UI  payment result with **savedTransaction** data
        end
    end alt
end
====
@enduml