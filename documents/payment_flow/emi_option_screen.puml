@startuml
autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

title EvoApp - EMIOptionScreen
note right user
user enter this screen from
- **EMIPaymentInputAmountScreen**
- Choosing pay with EMI in **FullPaymentInputAmountScreen**
end note

== EmiOptionScreenFlow ==
app --> user: show UI with list tenor of EMI packages for user to select (e.g: 3, 6, 9, 12)
app --> user: default selected is the package with **recommended = true**
alt #lightcyan User has pre-selected voucher
    ref over user, app, be: UpdateOrderApiFlow
else #linen User has not pre-selected voucher
    app -> app: Save current selected emi package in memory cached
end alt

opt #cyan
    user -> app: press **back** button
    app -> user: pop back to previous screen
end
loop #azure until user press "Tiếp tục" button
  user -> app: select an emi package
  alt #beige Selected Emi package data not available in cache
    ref over user, app : UpdateOrderApiFlow
  else #khaki Selected Emi package data available in cache
    app -> app: Update current selected emi package in memory cached
    app -> user: update package detail
  end
  opt #aqua
    user -> app: press **Xem thêm thông tin** button
    app --> user: show TenorInfoBottomSheet with selected tenor info
  end
  opt #aqua
    user -> app: press **Phí chuyển đổi** tooltip
    app --> user: show Tooltip info
  end
  opt #aqua
    user -> app: press "Tiếp tục" button
    ref over user, app, be: UpdateOrderApiFlow
  end
end

====

autonumber 1
== UpdateOrderApiFlow ==
app -> be: [Update Order] PATCH /checkout/orderSessionId
be --> app: return response
note left app
    Ref: **[[https://portal-evo-vn-staging-internal.tsengineering.io/docs/#/Checkout/create_session API docs]]**
end note
alt #LightGreen statusCode == SUCCESS
  app -> app: update current selected emi package
  app -> app: update cached emi packages on memory
  app -> user: show package detail
  alt #azure all vouchers valid
     app -> user: redirect to **UpdateConfirmPaymentScreen** with emiPackage
  else #coral Invalid Vouchers
     alt #azure User select Tenor of Emi Packages
          app -> user: show warning snack bar
     else #ivory User presses "Tiếp tục" CTA
          app -> user: show **InvalidPromotionPopup**
     end alt
  end alt
else #Salmon statusCode != success
  ref over user, app, be: UpdateOrderApiErrorFlow
end
====

autonumber 1
== UpdateOrderApiErrorFlow ==
note right user
   this flow to handle api error when updating order,
   the param is api response
end note
alt #LightSkyBlue verdict == emi_unqualified
   app --> user: show **UnqualifiedOrderBottomSheet**
else #LightBlue verdict == session_not_opened || verdict == session_expired || verdict == order_expired
   app --> user: show **ExpiredOrderErrorBottomSheet**
else #azure status_Code == 401
   app -> app: evoUtilFunction.clearDataOnTokenInvalid()
   app -> user: show snack bar error with message from api
else #Salmon other errors
   app -> user: show snack bar error with message from api
end alt
====

autonumber 1
== UnqualifiedOrderBottomSheetFlow ==
app -> user: show **UnqualifiedOrderBottomSheet**
alt #beige user press 'Chuyển sang trả thẳng'
    app -> be: [Update Order] PATCH /checkout/orderSessionId
        note right
            body: selectedOfferId = ''
        end note
    be --> app: return response
        note left
            response:
                - updatedOrder
            Ref: **[[https://portal-evo-vn-staging-internal.tsengineering.io/docs/#/Checkout/create_session API docs]]**
        end note
    alt #lightGreen statusCode == success
        alt #cyan invalid vouchers
            app -> user: show snack bar error
        else #azure voucher is valid
            app -> user: redirect to **UpdateConfirmPaymentScreen** with vouchers
        end
    else #Salmon statusCode != success
        ref over user, app, be: UpdateOrderApiErrorFlow
    end
else #khaki user press 'Trả góp không dùng ưu đãi'
    app -> be: [Update Order] PATCH /checkout/orderSessionId
    note right
        body: vouchers = []
    end note
    be --> app: return response
        note left
            response:
                - updatedOrder
            Ref: **[[https://portal-evo-vn-staging-internal.tsengineering.io/docs/#/Checkout/create_session API docs]]**
        end note
    alt #lightGreen success
        app -> user: redirect to **UpdateConfirmPaymentScreen**
    else #Salmon error or invalid vouchers
        ref over user, app, be: UpdateOrderApiErrorFlow
    end
end

@enduml