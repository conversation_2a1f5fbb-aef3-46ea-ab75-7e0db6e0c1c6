@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"

actor User as user
participant <PERSON><PERSON><PERSON><PERSON> as app
participant EvoGateway as gateway

title Face OTP - Mock Flow

user -> app: click **Start Selfie** button
app --> app: check if MockTestFlow is enabled

note left
    mockTestFlow conditions:
        - isDebugMode
        - flavor == UAT or STAG
end note
alt #Salmon mockTestFlow is NOT enabled
    app --> user: back to normal flow
    user -> app: do FaceOTP - normal flow
else #LightGreen mockTestFlow is enabled
    app --> user: show popup to request Storage permission
    user -> app: User perform Grant or Deny

    alt #Salmon deny Storage permission
        app --> user: back to normal flow
        user -> app: do FaceOTP - normal flow

    else #LightGreen  grant Storage permission
        app --> app: read MockFaceOTPData from local storage
        note left of app
            * manual_link_card_flow:
                - **enable**: true/false
                - **image_id**: string
            * sign_in_flow:
                - **enable**: true/false
                - **image_id**: string
        end note
        alt #lightBlue MockFaceOTPData: enable == true && image_id is not empty
            app --> user: go to step **Handle FaceOTP Result** of FaceOTPFlow.puml with image_id
        else #salmon
            user -> app: do FaceOTP - normal flow
        end alt

    end
end
@enduml
