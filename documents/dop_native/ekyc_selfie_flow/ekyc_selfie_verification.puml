@startuml
'https://plantuml.com/sequence-diagram

autonumber

actor User as user
participant Evo<PERSON><PERSON> as app
participant TrustVisionEKYC as ekyc
participant EvoGateway as gateway

title DOP Native - Selfie Verification Flow


note over of app
After user done EKYC Flash Selfie successfully,
the app will show the **DOPNativeSelfieVerificationScreen**
end note

app --> user: show **DOPNativeSelfieVerificationScreen**
app -> gateway: **POST dop/selfie/upload**
note over of gateway
    params:
    * batch_id
    * close_face_image
    * far_face_image
    * selfie_type:
        * [Android]: flash_32
        * [iOS]: flash_16
    Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3633316003/KH+th+c+hi+n+Selfie+Active#6.-Upload-image API docs]]**
end note

gateway --> app: return upload response
note over of app
    response:
    * verdict
    * job_id
end note
alt #LightYellow verdict == success
   loop verdict == success && (status == pending || status == processing)
        app -> gateway: **GET "dop/api/ekyc/status"**
        note over of gateway
            params:
            * job_id
            Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#GET-Ekyc-status API docs]]**
        end note
        gateway --> app: return status response
        note over of app
            response:
            * verdict
            * status
        end note
        alt #LightGreen verdict == success && status == success
            note over of app
               move to step #10
            end note
        else #LightYellow received HTTP error
            app --> user: show **Common Error Screen**
        else #LightPink other cases
            app --> user: goto **Verification Failure Flow**
        end
   end
else #LightYellow received HTTP error
    app --> user: show **Common Error Screen**
else #LightPink other cases
    app --> user: goto **Verification Failure Flow**
end


app -> gateway: **GET /dop/application/state**
gateway --> app: return ApplicationState response

note over of app
    Response:
    * current_step
    * ui_version
    ...
    Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Get-Application-State-API API docs]]**
end note

alt #LightPink current_step == failure
    ref over app, user
    **Verification Failed Matching flow**
    end ref

else #LightPink current_step == locked
    ref over app, user
    **Verification Locked flow**
    end ref
else #LightGreen current_step == other_steps
    ref over app, user
        **Verification Success flow**
    end ref
else #LightYellow received HTTP error
    app --> user: show **Common Error Screen**
end

@enduml