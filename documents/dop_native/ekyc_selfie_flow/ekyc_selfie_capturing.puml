@startuml
'https://plantuml.com/sequence-diagram

autonumber

actor User as user
participant <PERSON><PERSON><PERSON><PERSON> as app
participant TrustVisionEKYC as ekyc
participant Evo<PERSON>ate<PERSON> as gateway

title DOP Native - Selfie Capturing (EKYC Flash Selfie)


note over of user
2 way to open this feature:
   * In DOP native flow
   * In previous time, user drop-off at this step & comeback
end note
user -> app: navigate to Flash Selfie feature
app --> user: show **DOPNativeSelfieCaptureIntroductionScreen**

== Initialization ==

opt #LightYellow EKYC SDK is NOT initialized
app -> gateway: **GET dop/api/ekyc/client_settings**
note right
    Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#GET-Ekyc-client-settings API docs]]**
end note
gateway --> app: return client_settings response
    alt #LightPink Failure cases
        app --> user: show **Common Error Screen**
        user -> app: click CTA **Close** & goto entry point
    else #LightGreen client_settings is AVA<PERSON>ABLE
        app -> ekyc: initialize
        note over of ekyc
            params:
            * client_settings
        end note
    end
end

user -> app: click CT<PERSON> "Bắt Đầu Chụp"
app --> app: check Camera permission

alt #LightPink Permission is denied permanently
    app --> user: show warning popup
    alt Click CTA **Bỏ Qua**
        app --> user: close popup & move to entry point
    else click CTA **Settings**
        app --> user: close popup & goto System Setting app to adjust permission
    end

else #LightYellow need to ask user permission
    app --> user: system dialog is showed & ask user permission
    alt Deny
        user -> app: click CTA **Deny**
        note over of app
            back to step **#10**
        end note
    else Grant
        user -> app: click CTA **Grant**
        note over of app
            goto **Capturing Selfie Image** steps
        end note
    end
else #LightGreen Permission is granted
   note over of app
        goto **Capturing Selfie Image** steps
   end note
end

== Capturing Selfie Image ==
app --> app: initialize selfie session
note over of app
generate new **batch_id (uuid_v4)**
end note
app -> ekyc: start ** Flash Selfie Capturing**
ekyc --> user: show **Flash Selfie Capturing** screen


user -> ekyc: user follow instruction to capture selfie

loop #LightYellow n times based on SDK Config
ekyc --> app: return **selfie_batch_frame**
app -> gateway: **POST dop/api/ekyc/upload_frames**
note over of gateway
params:
* batch_id
* sequences_id (count from 0)
* selfie_batch_frame
Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#POST-Ekyc-upload-frames API docs]]**
end note
end loop

ekyc --> app: notify **Flash Selfie Capturing** completed

note over of app
response:
* close_face_image
* far_face_image
* fail_reason
end note

alt #LightGreen fail_reason == null
    app --> user: goto **Verification** steps
else #LightPink fail_reason != null
    app --> user: show **Common Error Screen**
end

== Verification ==
ref over app, user
    **Ekyc Selfie Verification flow**
end ref
@enduml