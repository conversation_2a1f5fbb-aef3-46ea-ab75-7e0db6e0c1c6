@startuml
'https://plantuml.com/sequence-diagram

autonumber

autonumber

actor User as user
participant <PERSON><PERSON><PERSON><PERSON> as app
participant Evo<PERSON><PERSON><PERSON> as gateway

title EKYC Flash Selfie Verification is Failure \n (this is result after the user has taken the selfie & completed the verification process)

app --> user: show **Failure Screen**

opt
    user -> app: click **Close** button at Action Bar
    app --> user: show Popup to confirm that drop-off flow
    alt user click "YES" button
        app --> user: close Popup & goto **EntryPoint**
   else user click "NO" button
        app --> user: close Popup
    end
end

user --> app: click on CTA **Thử Lại**

app -> gateway: **GET /dop/application/state**
gateway --> app: return ApplicationState response

note over of app
    Response:
    * current_step
    * ui_version
    ...
    Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Get-Application-State-API API docs]]**
end note

alt #LightGreen current_step == ekyc.selfie.flash
    app --> user: goto **DOPNativeSelfieCaptureIntroductionScreen**
    note over of user
       goto Ekyc-Selfie Flow
    end note
else #LightYellow current_step == other_steps
    app --> user: goto **Corresponding Screen**
    ref over app
    DOPNativeNavigationStep
    end ref
else #LightPink received HTTP error
    app --> user: goto **CommonError Screen**
end

@enduml