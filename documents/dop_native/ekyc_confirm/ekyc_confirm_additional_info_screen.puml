@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

title DOP Native - eKYC Confirm Additional Info

app --> user:  redirect user to **DOPNativeEkycConfirmAdditionalInfoScreen**

opt
    user -> app: presses CTA (X) on app bar
    ref over user,app
        **Close DOP flow**
    end ref
end opt

opt user select Địa chỉ nơi ở hiện tại input
    app --> user:  open address dialog

    ref over user, app
       **DOP Native - Address Dialog**
    end ref

end opt`

opt user select Tình trạng việc làm và Nghề nghiệp
    app --> user: open employment dialog

    ref over user, app
         **DOP Native - Employment Dialog**
    end ref

    alt #lightpink employment code or employment status code is invalid
        app --> user: display error message
        else #lightyellow employment code and employment status is valid
            app --> user: go to **Get payment method**
    end alt
    app --> user: go to **Check form validity**
end opt

opt user input Email
    app -> app: validate email
    alt #lightpink invalid email
        app --> user: display error message
    end alt
    app --> user: go to **Check form validity**
end opt

opt user input Thu nhập hàng tháng
    app -> app: validate income
    alt #lightpink invalid income
        app --> user: display error message
    end alt
    app --> user: redirect to **Check form validity**
end opt

==Get payment method==
app -> be: GET dop/api/metadata
note right
    params: {
        "type": PAYMENT_TYPE,
        "parentCode": employment code,
        "parentType": EMPLOYMENT,
    }
    Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Metadata API docs]]**
end note

alt #lightpink payments data is empty
    app --> user: hide Hình thức nhận lương
    else #lightyellow payments data is not empty
    app --> user: show Hình thức nhận lương
    user -> app: select payment type
end alt

==Check form validity==
alt #lightpink has invalid input
    app --> user: disable submit cta
    else #lightyellow all input are valid
        app --> user: enable submit cta
        user -> app: submit form
        app --> user: redirect to **Submit form**
end alt

==Submit form==
app -> be: POST dop/api/ekyc/confirm

note left
body:
{
    "updated_fields": {
        "contactInfo": {
            "familyAddress": String,
            "familyBookAddressDistId": String,
            "familyBookAddressProvinceId": String,
            "familyBookAddressWardId": String,
            "curAddressDistId": String,
            "curAddressProvinceId": String,
            "curAddressWardId": String,
    },
        "personalInfo": {
            "birthday": String,
            "fullName": String,
            "gender": String,
            "idCard": String,
            "idIssueDate": String,
            "idIssuePlaceId": String,
            "oldIDCard": String,
            "email": String,
    },
        "workingInfo": {
            "employmentId": String,
            "employmentStatusId": String,
            "income": Int,
            "salaryPaymentMethodId": String
    }
  }
}
Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#POST-Ekyc-confirm API docs]]**
end note

alt #lightpink statusCode != 200
    app --> user: show **common error screen**
    else #lightyellow
        app --> user: redirect to **DOP Native - Quick and Second apprising** flow
end alt

@enduml