@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

user -> app: Input "Tên công ty"
loop User inputs "Tên công ty"
    alt Length of Text inputted >= 3
        alt User stops to input >= 300ms
            alt Has previous request API Search Company name
               app --> app: Cancel previous request
            end alt
            app -> be: GET dop/api/metadata?type=COMPANY&search_prefix=$input_search_prefix
            be --> app: Response
            note left
                 Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533603031/S6+KH+i+n+contact+info+additional+form#5.-Get-company-metadata-suggestion API docs]]**
            end note
        end alt
    end alt
end loop

alt #coral statusCode != 200
    alt #white Has previous company metadata suggestions on Memory
        app --> user: Show previous company metadata suggestions
    else #white Has not previous company metadata suggestions on Memory
        app --> user: Do not show company metadata suggestions
    end alt
else #lightgreen statusCode == 200
    app --> app: Save company metadata suggestions
    app --> user: show company metadata suggestions
    opt #cyan User does not select any suggestion
        app --> user: Display value to "Tên công ty"
    end opt
    user -> app: Select "Tên công ty" from suggestions
    app --> user: Display value to "Tên công ty"
    app --> user: Hide company metadata suggestions
end alt
@enduml