@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

title DOP Native - Additional Form - Secret Question Step

app --> user: Show UI screen with form_step == "af.addinfo.secret_question"
alt secret_question data is not empty
    app --> user: Enable CTA button
    app --> user: Display value to secret_question_text_field
end alt

user -> app: Input **secret question**
alt #coral secret question is empty
    app --> user: Show error text
    app --> user: Disable CTA button
else #physical secret question is not empty
    user -> app: Press CTA button
    alt secret_question data is not null && the user does not change secret_question
        app --> user: Go to next Tab, show UI screen with form_step == "af.addinfo.address"
    else the user changes secret_question
        app -> be: POST dop/api/application/form/submit
        note right
            **Request Body:**
            Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533603031/S6+KH+i+n+contact+info+additional+form#1.-Submit-form-data%3A-secret_question API docs]]**
        end note
        be --> app: Response
        note left
            Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533603031/S6+KH+i+n+contact+info+additional+form#1.-Submit-form-data%3A-secret_question API docs]]**
        end note
        alt #coral statusCode != 200
            app --> user: Show error
            note right
                Refer File **dop_native_error_screen.puml**
            end note
        else #limegreen statusCode == 200
            app --> user: Go to next Tab, show UI screen with form_step == "af.addinfo.address"
        end alt
    end alt
end alt
@enduml