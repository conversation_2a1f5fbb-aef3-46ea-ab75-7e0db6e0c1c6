@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

title DOP Native - Additional Form - Address Info Step

app --> user: Show UI screen with form_step == "af.addinfo.address"
opt #cyan The user presses back to previous step
    user -> app: Press back button
    app --> user: Go to previous tab, show UI screen with form_step == "af.addinfo.secret_question"
end opt
alt Init "Địa chỉ nhận thẻ" data
    app -> be: GET dop/api/metadata?type=CARD_DELIVERY_TYPE
    be --> app: Response
    note left
       Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533603031/S6+KH+i+n+contact+info+additional+form#4.-Get-metadata-CARD_DELIVERY_TYPE API docs]]**
    end note
    alt #coral statusCode != 200
       app --> user: "Địa chỉ nhận thẻ" is hidden
       app --> user: Disable CTA button
    else #lightgreen statusCode == 200
       app --> user: Show "Địa chỉ nhận thẻ"
       app --> user: First item is selected
    end alt
end alt
alt address_data is not empty
    app --> user: Display value to "Số nhà, tên đường" của "Địa chỉ hiện tại"
    app --> user: Display value to "Tên công ty"
    app --> user: Display value to "Địa chỉ công ty (khu vực)"
    app --> user: Display value to "Địa chỉ công ty (số nhà, tên đường)"
    app --> user: Display value to "Địa chỉ nhận thẻ"
    app --> user: Enable CTA button
    opt #cyan User press CTA button
        user -> app: Press CTA button
        app --> user: Go to next tab, show UI screen with form_step == "af.addinfo.emergency_contact"
    end opt
end alt

alt address_data "Địa chỉ hiện tại" (PROVINCE/DISTRICT/WARD) is empty
    app --> user: "Khu vực Địa chỉ hiện tại" is empty
else address_data "Địa chỉ hiện tại" (PROVINCE/DISTRICT/WARD) is not empty
    app -> be: GET dop/api/metadata?type=PROVINCE&metadata_code=$metadata_code
    be --> app: Response
    note left
        Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533603031/S6+KH+i+n+contact+info+additional+form#3.-Get-metadata-with-code API docs]]**
    end note
    alt #coral statusCode != 200
        app --> user: "Khu vực Địa chỉ hiện tại" is empty
    else #lightgreen statusCode == 200
        app -> be: GET dop/api/metadata?type=DISTRICT&metadata_code=$metadata_code
        be --> app: Response
        note left
            Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533603031/S6+KH+i+n+contact+info+additional+form#3.-Get-metadata-with-code API docs]]**
        end note
        alt #coral statusCode != 200
            app --> user: "Khu vực Địa chỉ hiện tại" is empty
        else #lightgreen statusCode == 200
            app -> be: GET dop/api/metadata?type=WARD&metadata_code=$metadata_code
            be --> app: Response
            note left
                Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533603031/S6+KH+i+n+contact+info+additional+form#3.-Get-metadata-with-code API docs]]**
            end note
            alt #coral statusCode != 200
                app --> user: "Khu vực Địa chỉ hiện tại" is empty
            else #lightgreen statusCode == 200
                app --> user: Show "Khu vực Địa chỉ hiện tại" (format: "WARD_name, DISTRICT_name, PROVINCE_name")
            end alt
        end alt
    end alt
end alt

loop User inputs data
alt User inputs invalid data
    user -> app: Input invalid data
    app --> user: Disable CTA button
    app --> user: Show inline error on field
else User inputs valid data
    alt User inputs **Số nhà, tên đường**
    user -> app: Input **Số nhà, tên đường**
    app --> app: Validate **Số nhà, tên đường**
        alt **Số nhà, tên đường** is invalid
            app --> user: Disable CTA button
            app --> user: Show inline error
        else **Số nhà, tên đường** is valid
            app --> user: Display value to "Số nhà, tên đường"
        end alt
    end alt
    alt User inputs **Tên công ty**
    user -> app: Input **Tên công ty**
    note right
         Refer **search_company_name.puml** file
    end note
    app --> app: Validate **Tên công ty**
        alt **Tên công ty** is invalid
            app --> user: Disable CTA button
            app --> user: Show inline error
        else **Tên công ty** is valid
            app --> user: Display value to "Tên công ty"
        end alt
    end alt
    alt User selects **Địa chỉ khu vực của công ty**
    user -> app: Taps **Địa chỉ khu vực của công ty**
    note right
       Refer **address_dialog.puml** file
    end note
    app --> user: Show address dialog
    user -> app: Choose **Địa chỉ khu vực của công ty**
    app --> app: Validate **Địa chỉ khu vực của công ty**
        alt **Địa chỉ khu vực của công ty** is invalid
            app --> user: Disable CTA button
            app --> user: Show inline error
        else **Tên công ty** is valid
            app --> user: Display value to "Địa chỉ khu vực của công ty"
        end alt
    end alt
    alt User inputs **Địa chỉ cụ thể của công ty**
    user -> app: Input **Địa chỉ cụ thể của công ty**
    app --> app: Validate **Địa chỉ khu vực của công ty**
        alt **Địa chỉ khu vực của công ty** is invalid
            app --> user: Disable CTA button
            app --> user: Show inline error
        else **Địa chỉ cụ thể của công ty** is valid
            app --> user: Display value to "Địa chỉ cụ thể của công ty"
        end alt
    end alt

    app --> user: Enable CTA button
    user -> app: Press CTA button
   alt address_data is not null && the user does not change address_data
        app --> user: Go to next tab, show UI screen with form_step == "af.addinfo.emergency_contact"
    else the user changes address_data
        app -> be: POST dop/api/application/form/submit
        note right
            **Request Body:**
            Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533603031/S6+KH+i+n+contact+info+additional+form#6.-Submit-form-data%3A-address API docs]]**
        end note
        be --> app: Response
        note left
            Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533603031/S6+KH+i+n+contact+info+additional+form#6.-Submit-form-data%3A-address API docs]]**
        end note
        alt #coral statusCode != 200
            app --> user: Show error
            note right
                Refer File **dop_native_error_screen.puml**
            end note
        else #lightgreen statusCode == 200
            app --> user: Go to next tab, show UI screen with form_step == "af.addinfo.emergency_contact"
        end alt
    end alt
end alt
end loop
@enduml