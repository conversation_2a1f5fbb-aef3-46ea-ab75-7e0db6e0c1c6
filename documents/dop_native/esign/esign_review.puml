@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"

actor "**User**" as User
participant "**EvoApp**" as App
participant "**EvoGateWay**" as API

title DOP Native - E-Sign review flow

note right User
User go to DOP flow with
    * state: esign.review
    * ui_version: v9.4.1.0 || v9.4.2.0 || v9.4.3.0
These ui_version corresponding to screen name: Screen.dopNativeESignReviewScreen

* default state of two checkbox:
    "Khách hàng không có yếu tố hoa kỳ": checked
    "Tôi đồng ý liên kết thẻ vào EVO app": checked
end note

App --> User: redirect to **DOPNativeESignReviewScreen**

opt User presses CTA (X) on app bar
    ref over User, App
        **Close DOP flow** diagram
    end ref
end opt

loop get e-sign state
    App -> API: GET dop/api/esign/state
    note over of API
        Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#GET-esign-state API docs]]**
    end note
    API --> App: API Response
    note right App
       response: {
         status: ready || pending
         renderUrl: ...
       }
    end note

    alt success && status is pending
        ...Wait for 1s...
        App -> App: Continue loop
    else #LightGreen success && status is not pending
       App -> App: Stop loop
       App --> User: Load pdf from [renderUrl]
    else #LightPink http error
       App -> App: Stop loop
       App --> User: Redirect to **Error screen**
    end alt
end loop

User -> App: Press button **Ký hợp đồng**
App -> API: POST dop/api/esign/form/submit
note right App
   Request body:
    "form_data": {
        "isUSNationality": false,
        "isBornInUS": false,
        "isInstructDepositToOrWithdrawFromUSAddress": false,
        "isHaveUSAddressOrUSNumber": false,
        "isDelegateToUSAddress": false,
        "isHaveUniqueUSMailAddress": false,
        "payment": {
          "link_token": true
        }
      },
      Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3544449025/S7+KH+k+h+p+ng+i+n+t+v+x+c+nh+n+tu+n+th+FATCA#Submit-Esign API docs]]**
end note
API --> App:  API Response
alt #LightGreen success
    App -> API: POST dop/api/esign/prepare
    API --> App:  API Response
    note right App
       Response: {
          retries: 3,
          validSeconds: 180
        }
       Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3544449025/S7+KH+k+h+p+ng+i+n+t+v+x+c+nh+n+tu+n+th+FATCA#Prepare-Esign API docs]]**
    end note
    alt #Application success
        App --> User: Goto **Get Application State step**
    else #LightYellow http_code is 409 && verdict is invalid_state
        note right User: Refer ticket **EMA-2887**
        App --> User: Goto **Get Application State step**
    else #LightPink http error
        App --> User: Redirect to **Error screen**
    end alt
else #LightPink http error
    App --> User: Redirect to **Error screen**
end alt

opt User press hyperlink 'Điều kiện và điều khoản'
User -> App: Press hyperlink:'Điều kiện và điều khoản'
App --> User: Show PDF view load pdf from link:\n <prod/non-prod domain>/T&C_EVO_FPT_CA.pdf
end opt

opt User press 'Nhấn vào đây để xem chi tiết'
    User -> App: Tap 'Nhấn vào đây để xem chi tiết'
    App --> User: Redirect to **View PDF screen**
end opt

opt User press checkbox "Khách hàng không có yếu tố hoa kỳ"
    User -> App: Tap checkbox "Khách hàng không có yếu tố hoa kỳ"
    App --> User: Redirect to **Select America Element Screen**
    note right User
       App navigate to Screen.dopNativeAmericaCitizenScreen
       This screen have several option, user can select or un-select them
    end note

    alt #LightYellow user checks at least 1 America Citizen option
        App --> User: back to **DOPNativeESignReviewScreen**
        App --> User: uncheck checkbox "Khách hàng không có yếu tố hoa kỳ"
    else #Application user does not check any America Citizen option
        App --> User: back to **DOPNativeESignReviewScreen**
        App --> User: check checkbox "Khách hàng không có yếu tố hoa kỳ"
    end alt
end opt

opt User press checkbox "Tôi đồng ý liên kết thẻ vào EVO app"
    User -> App: Tap checkbox "Tôi đồng ý liên kết thẻ vào EVO app"

    alt #LightYellow checkbox is checked
        App --> User: Show dialog ask user
        note right User
            App popup a dialog, contain 2 button:
            * Agree
            * Not Agree
        end note

        alt #Technology User pressed Agree button or User dismiss dialog
            App --> User: keep checkbox checked & Close dialog
        else #Azure User pressed Not Agree button
            App --> User: mark checkbox un-checked & close dialog
        end alt
    else #Application checkbox is unchecked
        App --> User: mark checkbox checked
    end alt
end opt

==Get Application State step==
App -> API: GET dop/api/application/state
note over of API
   Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Get-Application-State-API API docs]]**
end note
API --> App: API Response

alt success

   alt #LightGreen  current_step == esign.otp
        App --> User: redirect to **DOPNativeVerifyOtpScreen**
        note right User
            Navigate with params:{
               verifyOtpType: DOPNativeVerifyOtpType.eSign,
               validSeconds: int,
               retries: int,
            }

            Refer to **DOP Native - DOPNativeVerifyOtpScreen** diagram
        end note
   else #Azure current_step != esign.otp
        App --> User: Navigate to next state
   end alt
else #Pink http error
    App --> User: Display Error screens
end alt

deactivate App
@enduml