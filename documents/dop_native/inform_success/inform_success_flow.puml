@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as User
participant "**EvoApp**" as App
participant "**EvoGateWay**" as API

title DOP Native - Inform success flow

note right of User
User go to DOP flow with
* state: inform.success
* ui_version: ...

With each ui_version corresponding to screen name as follows
- v9.4.1.0: Screen.dopNativeInformSuccessSemiScreen (SEMI),
- v9.4.2.0: Screen.dopNativeInformSuccessAutoPCBScreen (Auto PCB),
- v9.4.3.0: Screen.dopNativeInformSuccessAutoCICScreen (Auto CIC),
end note

activate App
alt #AliceBlue ui_version: Auto CIC or Auto PCB
    App -> API: GET /appraising/credit_assignment
    note over API
        Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#GET-Credit-assignment API docs]]**
    end note
    activate API
    API --> App: API Response
    deactivate API

    alt #LightGreen success
        note left of App
         eg. response:
             * credit_amount: 1000000
        end note
        App --> User: Display credit on UI
    else #LightPink http error
        App --> User: Display Error screens
    end
end

par #Application Countdown 10s
    App -> App: Start countdown
    ...After 10s countdown...
    App -> App: Countdown Finished
    App -> API: go to **Get Application Next State step**

else #LightSteelBlue Next Button Press
    User -> App: Press next button

    App -> App: Stop Countdown
    App -> API: go to **Get Application Next State step**
end



== Get Application Next State step ==
group #LightYellow Get Application Next State
App -> API: GET /application/state/next
note right of API
   params: {
      "action": "evo_next"
    }
   Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#POST-Application-next-state API docs]]**
end note

activate API
API --> App: API Response
deactivate API

alt #LightGreen success
    App -> API: GET /application/state
    note right of API
       Ref: **[[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3533242433/DOP+-+API+SPEC#Get-Application-State-API API docs]]**
    end note
    activate API
    API --> App: API Response
    deactivate API
    alt #LightGreen success
        App --> User: Navigate to next state
    else #Pink http error
        App --> User: Display Error screens
    end alt
else #Pink http error
    App --> User: Display Error screens
end alt
end group
deactivate App

@enduml