@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"

actor "**User**" as User
participant "**EvoApp**" as App
participant "**EvoGateWay**" as BE

title DOP Native - Determine lead source


note right User
    When user scan QR code, Evo App will extract screen_name and additional params from deeplink

    Example deeplink:
    evoappvn://mobile/deeplinking?
        screen_name=dop_native_introduction_screen&utm_source=evo_hoanghamobile
        &utm_campaign=offline_hhm_270524&lead_source=mwg

    if screen_name = dop_native_introduction_screen
        app will redirect to **DOPNativeIntroductionScreen** with additional params

   //Refer diagram: **onboarding_by_deeplinking_device_camera.puml**//
              //  & **onboarding_by_deeplinking_evo_camera.puml**//
end note

App --> User: Redirect to **DOPNativeIntroductionScreen**

note left
    additionalParams: {
        ...
        lead_source: ...,
        ...
    }
end note

opt User press CTA (X) on app bar
    ref over User, App
        **close_dop_flow.puml** diagram
    end ref
end opt

App --> App: Get **lead_source** from additionalParams

alt lead_source is not null
    App -> BE: GET api/registration/campaign?partner=<lead_source>
else lead_source is null
    App -> BE: GET api/registration/campaign?partner=**evo_native**
end alt

BE --> App: API response
note left
  //response//:
        campaign_code: ...;
        source: ...;
  Refer to: [[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3517677780/S1+KH+nh+p+s+i+n+tho+i#APIs GET registration/campaign API SPEC]]
end note

alt status_code == 200
    App --> App: Save **campaign_code & source**
    note left
        Save **campaign_code & source** to **AppState**
        to use when call API **registration/register** at Input phone step

        Refer to: [[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/3517677780/S1+KH+nh+p+s+i+n+tho+i#For-api/registration/campaign:~:text=2-,POST%20api/registration/register,-NO_AUTH POST registration/register API SPEC]]
    end note

    opt User press CTA "Mở thẻ ngay"
      ref over User, App
          Refer to **input_phone.puml** diagram
      end ref
    end opt
end alt

@enduml