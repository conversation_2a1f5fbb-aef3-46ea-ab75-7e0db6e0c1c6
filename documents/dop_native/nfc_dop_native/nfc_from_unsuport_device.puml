@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"

actor "**User**" as User
participant "**EvoApp**" as App

title DOP Native - NFC from unsupported device

== Evo App on the unsupported NFC device ==
note right User
Condition:
* User goto NFC step from one of these entry point:
    1. DOE flow
    2. DOP in-app WebView by scan QR on Web
* User's device not support NFC feature
end note

alt entry point is DOP WebView
    App -> App: get & save current DOP URL
end alt

App --> User: Redirect to **DOPNativeNFCReaderIntroductionScreen**
note left
   Refer: nfc_dop_native_flow.puml
end note

User -> App: Press CTA **Bắt đầu xác thực**
App -> App: check NFC support

alt device support NFC
   App --> User: Continue scan NFC
   note right
   Refer: nfc_dop_native_flow.puml
   end note
else device is not supported NFC

App --> User: redirect to **NFC Device unsupported screen**

alt Entry point is DOP in-app WebView
    App --> User: Show appbar title: **Đăng ký mở thẻ**
else entry point is DOE
    App --> User: Show appbar title: **Đăng ký mở thẻ tại cửa hàng**
end alt

App -> App: Generate QR code from OneLink
note right
    **If entry point is DOP in-app WebView:**
        OneLink will contain saved **DOP URL**
    **If entry point is DOE:**
        OneLink will trigger **DOP Introduction Screen** on DOE

    Refer OneLink format: [[https://trustingsocial1.atlassian.net/browse/EMA-3010 EMA-3010]]
end note

App --> User: Display QR on screen
App -> BE: send log event to API /events
note right
    event_id: 01.6501.000
    **loading_time**: int
    **unique_token**: string
    [[https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3771858945/Product+-+Screen+flow+-+scan+NFC+flow Refer docs]]
end note

opt Press **"Chia sẻ tới thiết bị khác"** CTA
    User --> App: User press **"Chia sẻ tới thiết bị khác"** CTA
    App --> User: Show share panel
    note left
        Show share panel, allowing the user to share the Onelink
        with format content defined in ticket:  [[https://trustingsocial1.atlassian.net/browse/EMA-3010 EMA-3010]]
    end note
    App -> BE: send log event to API /events
    note right
        **event_id**: 01.6501.001
        **unique_token**: string
        [[https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3771858945/Product+-+Screen+flow+-+scan+NFC+flow Refer docs]]
    end note
end opt

User -> User: Use other device which supported NFC to scan QR to complete open card

note left
Refer: **onboarding_by_deeplinking_device_camera.puml**
          **onboarding_by_deeplinking_evo_camera.puml**

Ticket: [[https://trustingsocial1.atlassian.net/browse/EMA-1891 EMA-1891]]
end note
end alt
@enduml
