@startuml
'https://plantuml.com/sequence-diagram

autonumber "<b>[0]"
actor "**User**" as User
participant "**EvoApp**" as App
participant "**TPBank**" as TPB

title DOP Native - 3D Secure

opt User press CTA (X) on app bar
    ref over User, App
        **Close DOP flow** diagram
    end ref
end opt

note over App
After user activate card in **setup POS limit** (Refer to **setup_pos_limit.puml**)

App get the response:
//response:// {
    redirect_url: ...
}

Refer to: [[https://trustingsocial1.atlassian.net/wiki/spaces/TEK/pages/**********/Underwriting+sub+flow+Card+activation#POST-Activate-card Activate card API specs]]
end note

App --> User: Redirect to **DOPNativeThreeDSecureScreen**
App --> App: Load redirect_url
alt  Load redirect_url fail || redirect_url is Empty
    App --> User: Show empty page

else Load redirect_url success
    App --> User: Show 3D secure page loaded from redirect_url

    User -> App: Enter OTP
    note right
        User enter OTP on **3D secure** page, loaded on **EvoApp**
    end note
    App -> TPB: Send OTP
    TPB --> TPB: Verify OTP

    alt TPB verify OTP success
        TPB --> App: redirect url **(evo://dop-card-activating)**
        App --> User: redirect to **DOPNativeCardStatusInformationScreen**
        note right
            Refer to **DOP Native - Card Activation Processing**
        end note
    end alt
end alt
@enduml
