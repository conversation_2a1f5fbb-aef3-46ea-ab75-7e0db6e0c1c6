{"data": {"challenge_type": "face_auth", "ekyc_client_settings": {"card_types": [{"back_qr": {"exist": false}, "code": "vn.national_id", "front_qr": {"exist": false}, "has_back_side": true, "name": "Thẻ CC / CCCD / CMND cũ / CMND mới / Hộ chiếu", "orientation": "horizontal"}, {"back_qr": {"exist": true}, "code": "vn.tcc", "front_qr": {"exist": false}, "has_back_side": true, "name": "Thẻ căn cước", "orientation": "horizontal"}, {"back_qr": {"exist": false}, "code": "vn.cccd_new", "front_qr": {"exist": true}, "has_back_side": true, "name": "<PERSON><PERSON><PERSON> c<PERSON> công dân chip", "orientation": "horizontal"}], "country": "vn", "settings": {"enable_compare_faces": true, "enable_convert_pdf": false, "enable_detect_id_card_tampering": true, "enable_encryption": false, "enable_face_authentication": true, "enable_face_registration": true, "enable_face_retrieval": true, "enable_read_id_card_info": true, "enable_verify_face_liveness": true, "enable_verify_id_card_sanity": true, "enable_verify_nfc": true, "enable_verify_portrait_sanity": true, "flashing_modes": {"flash": {"colors": "r r r rr", "colors_android": " r rrr r", "colors_ios": " r r r r", "colors_web": "  rr rrr", "frames_per_color": 4, "frames_per_color_android": 4, "frames_per_color_ios": 4, "frames_per_color_web": 4}, "flash_16": {"colors": " r r ", "colors_android": "r r r", "colors_ios": "r r r", "colors_web": " r r ", "frames_per_color": 3, "frames_per_color_android": 3, "frames_per_color_ios": 3, "frames_per_color_web": 3}, "flash_32": {"colors": "  r r  r r", "colors_android": "rr rrr r  ", "colors_ios": "rrr  r r r", "colors_web": "  rr rr rr", "frames_per_color": 3, "frames_per_color_android": 3, "frames_per_color_ios": 3, "frames_per_color_web": 3}, "flash_8": {"colors": "rr ", "colors_android": " r ", "colors_ios": " rr", "colors_web": " r ", "frames_per_color": 3, "frames_per_color_android": 3, "frames_per_color_ios": 3, "frames_per_color_web": 3}, "flash_advanced": {"colors": "  rr  rr", "colors_android": " r r r  ", "colors_ios": " r  rr  ", "colors_web": "  r rr r", "frames_per_color": 4, "frames_per_color_android": 4, "frames_per_color_ios": 4, "frames_per_color_web": 4}, "flash_edge": {"colors": "r ", "colors_android": "r ", "colors_ios": "r ", "colors_web": "r ", "frames_per_color": 4, "frames_per_color_android": 4, "frames_per_color_ios": 4, "frames_per_color_web": 4}}, "liveness_modes": ["active", "passive"], "scan_qr": "none", "sdk_settings": {"active_liveness_settings": {"face_tracking_setting": {"android_terminate_threshold": 0.002847, "android_warning_threshold": 0.001474, "enable": true, "ios_terminate_threshold": 0.003393, "ios_warning_threshold": 0.002176, "limit_for": "all_flow", "max_interval_ms": 2000, "max_warning_time": 5, "web_terminate_threshold": 0.0030152991993743408, "web_warning_threshold": 0.0017317430600108828}, "flow_interval_time_ms": 3000, "limit_time_liveness_check": {"enable": true, "limit_time_second": 45}, "record_video": {"enable": false}, "save_encoded_frames": {"enable": true, "enable_logging": false, "frames_batch_len": 20, "frames_interval_ms": 180, "quality_android": 90, "quality_ios": 80}, "show_gesture_arrow": false, "terminate_if_no_face": {"enable": true, "max_invalid_frame": 5, "max_time_ms": 1000}}, "flash_liveness_settings": {"blur_close_settings": {"blurry_threshold": 0.3, "blurry_threshold_android": 0.4, "blurry_threshold_ios": 0.35, "enable": true, "wait_for_best_image_time_ms": 400}, "blur_far_settings": {"blurry_threshold": 0.55, "blurry_threshold_android": 0.5, "blurry_threshold_ios": 0.35, "enable": true, "wait_for_best_image_time_ms": 400}, "capture_frame_settings": {"enable": true, "enable_ios": true, "frame_quality": 0.99, "frame_quality_android": 99, "frame_quality_ios": 0.99, "frame_scale": 1, "frames_batch_length": 10, "frames_batch_length_android": 10, "frames_batch_length_ios": 10, "frames_interval_time": 10, "frames_interval_time_android": 100, "frames_interval_time_ios": 100}, "close_eyes_settings": {"enable": true, "enable_ios": true, "threshold_ios": 0.342, "timeout": 5, "timeout_ios": 5}, "exif_data_settings": {"enable": true, "enable_ios": true}, "face_settings": {"chin_height": 200, "chin_height_android": 200, "chin_height_ios": 100, "close_face_ratio": 0.7, "close_face_ratio_android": 0.43, "close_face_ratio_ios": 0.55, "max_far_face_ratio": 0.85, "max_far_face_ratio_android": 0.67, "max_far_face_ratio_ios": 0.67, "min_far_face_ratio": 0.5, "min_far_face_ratio_android": 0.23, "min_far_face_ratio_ios": 0.17}, "flash_settings": {"colors_length": 8, "colors_length_android": 8, "colors_length_ios": 8, "delay_between_colors": 350, "delay_between_colors_android": 400, "delay_between_colors_ios": 450, "flash_color": "rgb(255, 0, 0)", "flash_color_android": "#ff0000", "flash_color_ios": {"blue": 0, "green": 0, "red": 255}, "flash_frame_interval": 0, "flash_frame_interval_android": 10, "flash_frame_interval_ios": 10, "flash_intensity": 1, "flash_intensity_android": 1, "flash_intensity_ios": 1, "frames_per_color": 4, "frames_per_color_android": 4, "frames_per_color_ios": 4, "min_brightness": 0.9, "min_brightness_android": 0.9, "min_brightness_ios": 1, "use_face_detector_when_flashing": false}, "image_settings": {"crop_center_close": false, "crop_center_close_ios": false, "crop_center_far": true, "crop_center_far_ios": true, "image_quality": 1, "image_quality_android": 100, "image_quality_ios": 1, "valid_face_ratio": 0.6, "valid_face_ratio_android": 0.45, "valid_face_ratio_ios": 0.2}, "liveness_settings": {"enable_far_step": true, "enable_far_step_ios": true, "timeout": 30}, "mask_settings": {"background_color": "#cccccc", "large_scale": 0.85, "large_scale_android": 0.85, "large_scale_ios": 0.85, "mobile": {"chin_to_mask_bottom_padding": 200, "large_scale": 0.85, "oval_padding": {"bottom": 20, "left": 30, "right": 30, "top": 0}, "oval_vertical_offset": 0, "small_scale": 0.6}, "oval_horizontal_offset": 0, "oval_horizontal_offset_android": 0, "oval_horizontal_offset_ios": 0, "oval_horizontal_padding": 40, "oval_horizontal_padding_android": 40, "oval_horizontal_padding_ios": 20, "oval_padding": {"bottom": 20, "left": 30, "right": 30, "top": 0}, "oval_vertical_offset": 0, "oval_vertical_offset_android": 0, "oval_vertical_offset_ios": 0, "oval_vertical_padding": 50, "oval_vertical_padding_android": 30, "oval_vertical_padding_ios": 15, "small_scale": 0.6, "small_scale_android": 0.6, "small_scale_ios": 0.65}, "timeout_settings": {"countdown": 10, "countdown_ios": 10, "enable": true, "enable_ios": true, "total": 45, "total_ios": 45}}, "id_detection_settings": {"auto_capture": {"enable": false, "show_capture_button": true, "wait_for_best_image_time_ms": 1500, "wait_for_best_image_time_ms_web": 1000}, "blur_check": {"enable": true, "threshold": 0.29, "threshold_web": 0.82}, "disable_capture_button_if_alert": true, "exif_data_settings": {"enable": true}, "flow_interval_time_ms": 2000, "glare_check": {"enable": true, "threshold": 0.002}, "id_detection": {"enable": true}, "limit_time_settings": {"enable": true, "limit_time_second": 10, "video_partial_length_seconds": 10}, "save_frame_settings": {"enable": true, "frames_batch_len": 20, "frames_interval_ms": 190, "quality_android": 80, "quality_ios": 70, "quality_web": 80}, "scan_nfc_settings": {"enable": true, "enable_call_api_ocr_android": true, "enable_call_api_ocr_ios": true, "enable_show_max_retries_screen_android": true, "enable_show_max_retries_screen_ios": true, "enable_show_timeout_screen_android": true, "enable_skip_button_android": true, "enable_skip_button_ios": true, "enable_validate_full_card_number": true, "guideline_time_second": 3, "limit_loading_time_second": 60, "limit_time_second": 30, "max_retries_android": 1000, "max_retries_ios": 1000, "request_clone_detection": true, "request_integrity_check": true, "request_read_image": true}, "scan_qr_settings": {"enable": true, "limit_time_second": 20}, "virtual_cam_hashes": {"mobile": true, "web": true}}, "license_settings": {"license_key": "fake_license_key", "license_key_android": "fake_license_key_android"}, "license_settings_android": {"com.trustingsocial.tvsdkexample.faceAuth.staging": "fake_data", "license_key_android": "fake_license_key_android"}, "license_settings_ios": {"com.trustingsocial.trustvision.ekyc": "fake_data", "license_key": "fake_license_key_android"}, "server_log_settings": {"enable": true}}, "selfie_camera_options": ["front"], "selfie_enable_detect_multiple_face": true, "support_transaction": false, "utilities": {"length_video_sec": 5, "num_of_photo_taken": 3, "photo_res": "640x640", "timing_take_photo_sec": "1,2.5,4"}, "web_app_crop_face": "none"}}, "remain_attempt": 0, "session_token": "fake_session_token"}, "message": "verify successfully", "status_code": 200, "verdict": "success"}